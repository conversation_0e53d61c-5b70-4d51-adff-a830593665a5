# 加密货币交易所后端系统

## 项目简介

这是一个基于Spring Boot 3.x开发的加密货币交易所后端系统，提供用户管理、钱包管理、交易撮合、市场数据等核心功能。

## 技术栈

- **框架**: Spring Boot 3.2.0
- **安全**: Spring Security 6.x + JWT
- **数据库**: MySQL 8.0 + MyBatis Plus 3.5.x
- **缓存**: Redis 7.x + Redisson
- **连接池**: Druid
- **文档**: Knife4j (Swagger 3)
- **工具**: Hutool, Lombok, FastJSON2
- **存储**: Min<PERSON> (对象存储)
- **构建**: Maven 3.x
- **JDK**: Java 17

## 项目结构

```
crypto-exchange/
├── src/main/java/com/cryptoexchange/
│   ├── common/                 # 通用类
│   │   ├── Result.java        # 统一响应结果
│   │   ├── PageResult.java    # 分页响应结果
│   │   └── BusinessException.java # 业务异常
│   ├── config/                # 配置类
│   │   ├── SecurityConfig.java    # 安全配置
│   │   ├── RedisConfig.java       # Redis配置
│   │   ├── MybatisPlusConfig.java # MyBatis Plus配置
│   │   └── SwaggerConfig.java     # Swagger配置
│   ├── controller/            # 控制器
│   │   └── AuthController.java    # 认证控制器
│   ├── dto/                   # 数据传输对象
│   │   ├── request/           # 请求DTO
│   │   └── response/          # 响应DTO
│   ├── entity/                # 实体类
│   │   ├── User.java          # 用户实体
│   │   └── UserWallet.java    # 用户钱包实体
│   ├── enums/                 # 枚举类
│   │   └── ResultCode.java    # 响应码枚举
│   ├── exception/             # 异常处理
│   │   └── GlobalExceptionHandler.java # 全局异常处理器
│   ├── mapper/                # 数据访问层
│   │   └── UserMapper.java    # 用户Mapper
│   ├── security/              # 安全相关
│   │   ├── JwtAuthenticationEntryPoint.java # JWT认证入口点
│   │   └── JwtAuthenticationFilter.java     # JWT认证过滤器
│   ├── service/               # 服务层
│   │   ├── AuthService.java       # 认证服务接口
│   │   ├── UserService.java       # 用户服务接口
│   │   └── impl/                  # 服务实现
│   │       ├── AuthServiceImpl.java   # 认证服务实现
│   │       └── UserServiceImpl.java   # 用户服务实现
│   ├── util/                  # 工具类
│   │   ├── JwtUtil.java       # JWT工具类
│   │   └── RedisUtil.java     # Redis工具类
│   └── CryptoExchangeApplication.java # 启动类
├── src/main/resources/
│   ├── application.yml        # 应用配置
│   └── mapper/               # MyBatis映射文件
├── pom.xml                   # Maven配置
└── README.md                 # 项目说明
```

## 核心功能

### 1. 用户管理
- 用户注册/登录
- 邮箱/短信验证
- 密码重置
- 用户信息管理
- KYC认证
- 两步验证

### 2. 钱包管理
- 多币种钱包
- 充值/提现
- 资产查询
- 交易记录
- 地址管理

### 3. 安全机制
- JWT令牌认证
- 密码加密存储
- 登录失败限制
- IP白名单
- 操作日志记录

### 4. 系统监控
- 健康检查
- 性能监控
- 数据库连接池监控
- Redis连接监控

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 7.0+

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-repo/crypto-exchange.git
cd crypto-exchange
```

2. **创建数据库**
```sql
CREATE DATABASE crypto_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. **修改配置**

编辑 `src/main/resources/application.yml`，修改数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: *******************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password
```

4. **编译运行**
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

5. **访问应用**

- 应用地址: http://localhost:8080/api
- API文档: http://localhost:8080/api/doc.html
- 数据库监控: http://localhost:8080/api/druid/index.html

## API文档

项目集成了Knife4j，启动后可通过以下地址访问API文档：

- Swagger UI: http://localhost:8080/api/doc.html
- OpenAPI JSON: http://localhost:8080/api/v3/api-docs

## 配置说明

### JWT配置

```yaml
app:
  jwt:
    secret: your-jwt-secret-key
    expiration: 86400        # 访问令牌过期时间（秒）
    refresh-expiration: 604800 # 刷新令牌过期时间（秒）
```

### 安全配置

```yaml
app:
  security:
    max-login-attempts: 5     # 最大登录尝试次数
    account-lock-duration: 30 # 账户锁定时间（分钟）
    password-min-length: 8    # 密码最小长度
```

### 交易配置

```yaml
app:
  trading:
    fee-rate: 1              # 手续费率（千分之几）
    min-trade-amount: 10     # 最小交易金额
    max-trade-amount: 1000000 # 最大交易金额
    price-precision: 8       # 价格精度
    quantity-precision: 8    # 数量精度
```

## 开发指南

### 代码规范

1. 使用Lombok简化代码
2. 统一使用Result包装响应结果
3. 异常统一通过GlobalExceptionHandler处理
4. 使用MyBatis Plus简化数据库操作
5. 重要操作记录日志

### 数据库设计

- 使用逻辑删除，不物理删除数据
- 重要表添加版本号字段，支持乐观锁
- 统一添加创建时间、更新时间、创建者、更新者字段
- 金额字段使用DECIMAL类型，确保精度

### 安全考虑

- 敏感信息加密存储
- API接口权限控制
- 输入参数校验
- SQL注入防护
- XSS攻击防护

## 部署说明

### Docker部署

```bash
# 构建镜像
docker build -t crypto-exchange:latest .

# 运行容器
docker run -d -p 8080:8080 --name crypto-exchange crypto-exchange:latest
```

### 生产环境配置

1. 修改数据库连接池配置
2. 调整JVM参数
3. 配置日志输出
4. 设置监控告警
5. 配置负载均衡

## 监控运维

### 健康检查

- 应用健康: http://localhost:8080/api/actuator/health
- 应用信息: http://localhost:8080/api/actuator/info
- 性能指标: http://localhost:8080/api/actuator/metrics

### 日志管理

- 日志文件: logs/crypto-exchange.log
- 日志级别: 开发环境DEBUG，生产环境INFO
- 日志轮转: 单文件最大10MB，保留30天

## 常见问题

### Q: 启动时提示数据库连接失败？
A: 检查数据库服务是否启动，配置信息是否正确。

### Q: Redis连接失败？
A: 检查Redis服务是否启动，端口和密码配置是否正确。

### Q: JWT令牌验证失败？
A: 检查JWT密钥配置，确保前后端使用相同的密钥。

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

- 邮箱: <EMAIL>
- 官网: https://www.cryptoexchange.com
- 文档: https://docs.cryptoexchange.com