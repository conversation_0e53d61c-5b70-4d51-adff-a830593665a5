# 测试环境配置
spring:
  application:
    name: crypto-exchange-test
  
  # 数据源配置 - 使用H2内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    hikari:
      maximum-pool-size: 5
  
  # H2数据库配置
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # Redis配置 - 使用嵌入式Redis
  data:
    redis:
      host: localhost
      port: 6370  # 使用不同端口避免冲突
      database: 15
      timeout: 3000ms
  
  # 邮件配置 - 测试环境禁用
  mail:
    host: localhost
    port: 25
    username: test
    password: test
    properties:
      mail:
        smtp:
          auth: false
          starttls:
            enable: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  secret: test-secret-key-for-crypto-exchange-testing-environment
  access-token-expiration: 3600000   # 1小时
  refresh-token-expiration: 7200000   # 2小时
  issuer: CryptoExchange-Test

# 日志配置
logging:
  level:
    com.cryptoexchange: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
    org.springframework.test: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'

# 应用自定义配置
app:
  # 安全配置
  security:
    max-login-attempts: 3
    account-lock-duration: 5
    password-min-length: 6
  
  # 验证码配置
  captcha:
    expiration: 1
    length: 4
    type: numeric
  
  # 交易配置
  trading:
    min-trade-amount: 1
    max-trade-amount: 10000
    fee-rate: 0.001
    price-precision: 8
    quantity-precision: 8