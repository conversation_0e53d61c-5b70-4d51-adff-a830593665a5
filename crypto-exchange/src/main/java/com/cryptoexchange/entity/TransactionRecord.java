package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("transaction_record")
public class TransactionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 交易类型：DEPOSIT-充值，WITHDRAW-提现，TRADE_BUY-买入，TRADE_SELL-卖出，TRANSFER_IN-转入，TRANSFER_OUT-转出，FEE-手续费，REWARD-奖励，REFUND-退款
     */
    @TableField("transaction_type")
    private String transactionType;

    /**
     * 交易金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 交易前余额
     */
    @TableField("balance_before")
    private BigDecimal balanceBefore;

    /**
     * 交易后余额
     */
    @TableField("balance_after")
    private BigDecimal balanceAfter;

    /**
     * 交易状态：PENDING-待处理，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 交易哈希
     */
    @TableField("tx_hash")
    private String txHash;

    /**
     * 确认数
     */
    @TableField("confirmations")
    private Integer confirmations;

    /**
     * 地址
     */
    @TableField("address")
    private String address;

    /**
     * 网络类型
     */
    @TableField("network")
    private String network;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 关联订单ID
     */
    @TableField("related_order_id")
    private Long relatedOrderId;

    /**
     * 关联交易ID
     */
    @TableField("related_trade_id")
    private Long relatedTradeId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 完成时间
     */
    @TableField("complete_time")
    private LocalDateTime completeTime;

    /**
     * 账户类型：SPOT-现货，FUTURES-期货，MARGIN-杠杆
     */
    @TableField("account_type")
    private String accountType;

    /**
     * 业务类型：NORMAL-正常交易，LIQUIDATION-强制平仓，FUNDING-资金费用，ADL-自动减仓
     */
    @TableField("business_type")
    private String businessType;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}