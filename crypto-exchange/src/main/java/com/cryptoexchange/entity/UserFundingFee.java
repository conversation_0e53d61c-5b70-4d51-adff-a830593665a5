package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户资金费用实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_funding_fee")
public class UserFundingFee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资金费用ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 持仓方向：1-多头，2-空头
     */
    @TableField("position_side")
    private Integer positionSide;

    /**
     * 持仓数量
     */
    @TableField("position_size")
    private BigDecimal positionSize;

    /**
     * 资金费率
     */
    @TableField("funding_rate")
    private BigDecimal fundingRate;

    /**
     * 资金费用金额（正数表示收入，负数表示支出）
     */
    @TableField("funding_fee")
    private BigDecimal fundingFee;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 名义价值
     */
    @TableField("notional_value")
    private BigDecimal notionalValue;

    /**
     * 费用类型：1-支付，2-收取
     */
    @TableField("fee_type")
    private Integer feeType;

    /**
     * 资金费用时间
     */
    @TableField("funding_time")
    private LocalDateTime fundingTime;

    /**
     * 结算状态：1-待结算，2-已结算
     */
    @TableField("settlement_status")
    private Integer settlementStatus;

    /**
     * 结算时间
     */
    @TableField("settlement_time")
    private LocalDateTime settlementTime;

    /**
     * 交易哈希（如果有）
     */
    @TableField("transaction_hash")
    private String transactionHash;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}