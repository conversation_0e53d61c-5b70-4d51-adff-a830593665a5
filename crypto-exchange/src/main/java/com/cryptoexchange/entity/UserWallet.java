package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户钱包实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_wallets")
public class UserWallet implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 钱包ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 币种ID
     */
    @TableField("currency_id")
    private Long currencyId;

    /**
     * 币种符号
     */
    @TableField("currency")
    private String currency;

    /**
     * 钱包类型：1-现货钱包，2-合约钱包，3-理财钱包，4-挖矿钱包
     */
    @TableField("wallet_type")
    private Integer walletType;

    /**
     * 可用余额
     */
    @TableField("available_balance")
    private BigDecimal availableBalance;

    /**
     * 冻结余额
     */
    @TableField("frozen_balance")
    private BigDecimal frozenBalance;

    /**
     * 总余额
     */
    @TableField("total_balance")
    private BigDecimal totalBalance;

    /**
     * 钱包地址
     */
    @TableField("address")
    private String address;

    /**
     * 私钥（加密存储）
     */
    @TableField("private_key")
    private String privateKey;

    /**
     * 公钥
     */
    @TableField("public_key")
    private String publicKey;

    /**
     * 助记词（加密存储）
     */
    @TableField("mnemonic")
    private String mnemonic;

    /**
     * 钱包状态：0-禁用，1-正常，2-冻结
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否为主钱包
     */
    @TableField("is_main")
    private Boolean isMain;

    /**
     * 钱包标签
     */
    @TableField("label")
    private String label;

    /**
     * 最后交易时间
     */
    @TableField("last_transaction_time")
    private LocalDateTime lastTransactionTime;

    /**
     * 累计充值金额
     */
    @TableField("total_deposit")
    private BigDecimal totalDeposit;

    /**
     * 累计提现金额
     */
    @TableField("total_withdraw")
    private BigDecimal totalWithdraw;

    /**
     * 累计交易金额
     */
    @TableField("total_trade")
    private BigDecimal totalTrade;

    /**
     * 累计手续费
     */
    @TableField("total_fee")
    private BigDecimal totalFee;

    /**
     * 版本号（乐观锁）
     */
    @Version
    @TableField("version")
    private Integer version;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}