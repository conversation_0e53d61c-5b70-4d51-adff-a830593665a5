package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("trades")
public class Trade implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;

    /**
     * 交易对
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 交易方向：BUY-买入，SELL-卖出
     */
    @TableField("side")
    private String side;

    /**
     * 成交价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 成交数量
     */
    @TableField("qty")
    private BigDecimal qty;

    /**
     * 成交金额
     */
    @TableField("quote_qty")
    private BigDecimal quoteQty;

    /**
     * 手续费
     */
    @TableField("commission")
    private BigDecimal commission;

    /**
     * 手续费币种
     */
    @TableField("commission_asset")
    private String commissionAsset;

    /**
     * 成交时间
     */
    @TableField("time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    /**
     * 是否为买方
     */
    @TableField("is_buyer")
    private Boolean isBuyer;

    /**
     * 是否为挂单方
     */
    @TableField("is_maker")
    private Boolean isMaker;

    /**
     * 是否为最优价格匹配
     */
    @TableField("is_best_match")
    private Boolean isBestMatch;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 对手方用户ID
     */
    @TableField("counterparty_user_id")
    private Long counterpartyUserId;

    /**
     * 交易类型：SPOT-现货，FUTURES-合约
     */
    @TableField("trade_type")
    private String tradeType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}