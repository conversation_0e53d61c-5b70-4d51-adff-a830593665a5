package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保证金账户实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("margin_account")
public class MarginAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 账户余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 冻结余额
     */
    @TableField("frozen_balance")
    private BigDecimal frozenBalance;

    /**
     * 已用保证金
     */
    @TableField("used_margin")
    private BigDecimal usedMargin;

    /**
     * 可用保证金
     */
    @TableField("available_margin")
    private BigDecimal availableMargin;

    /**
     * 未实现盈亏
     */
    @TableField("unrealized_pnl")
    private BigDecimal unrealizedPnl;

    /**
     * 已实现盈亏
     */
    @TableField("realized_pnl")
    private BigDecimal realizedPnl;

    /**
     * 总权益
     */
    @TableField("total_equity")
    private BigDecimal totalEquity;

    /**
     * 维持保证金
     */
    @TableField("maintenance_margin")
    private BigDecimal maintenanceMargin;

    /**
     * 风险率
     */
    @TableField("risk_ratio")
    private BigDecimal riskRatio;

    /**
     * 账户状态：NORMAL-正常，LIQUIDATION-强制平仓，FROZEN-冻结
     */
    @TableField("status")
    private String status;

    /**
     * 最大杠杆倍数
     */
    @TableField("max_leverage")
    private Integer maxLeverage;

    /**
     * 当前杠杆倍数
     */
    @TableField("current_leverage")
    private BigDecimal currentLeverage;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}