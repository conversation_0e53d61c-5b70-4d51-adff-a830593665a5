package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("deposit")
public class Deposit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 充值记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 充值金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 实际到账金额
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 充值地址
     */
    @TableField("address")
    private String address;

    /**
     * 交易哈希
     */
    @TableField("tx_hash")
    private String txHash;

    /**
     * 网络类型
     */
    @TableField("network")
    private String network;

    /**
     * 确认数
     */
    @TableField("confirmations")
    private Integer confirmations;

    /**
     * 需要确认数
     */
    @TableField("required_confirmations")
    private Integer requiredConfirmations;

    /**
     * 充值状态：PENDING-待处理，CONFIRMING-确认中，COMPLETED-已完成，FAILED-失败
     */
    @TableField("status")
    private String status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}