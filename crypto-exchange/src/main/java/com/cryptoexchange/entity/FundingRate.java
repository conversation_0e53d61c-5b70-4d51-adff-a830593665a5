package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资金费率实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("funding_rate")
public class FundingRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资金费率ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 资金费率
     */
    @TableField("rate")
    private BigDecimal rate;

    /**
     * 预测资金费率
     */
    @TableField("predicted_rate")
    private BigDecimal predictedRate;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 指数价格
     */
    @TableField("index_price")
    private BigDecimal indexPrice;

    /**
     * 溢价指数
     */
    @TableField("premium_index")
    private BigDecimal premiumIndex;

    /**
     * 利率
     */
    @TableField("interest_rate")
    private BigDecimal interestRate;

    /**
     * 资金费用收取时间
     */
    @TableField("funding_time")
    private LocalDateTime fundingTime;

    /**
     * 下次资金费用时间
     */
    @TableField("next_funding_time")
    private LocalDateTime nextFundingTime;

    /**
     * 资金费率计算周期（小时）
     */
    @TableField("funding_interval")
    private Integer fundingInterval;

    /**
     * 最大资金费率
     */
    @TableField("max_funding_rate")
    private BigDecimal maxFundingRate;

    /**
     * 最小资金费率
     */
    @TableField("min_funding_rate")
    private BigDecimal minFundingRate;

    /**
     * 状态：1-正常，2-暂停
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}