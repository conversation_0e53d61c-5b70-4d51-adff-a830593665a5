package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货合约实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_contract")
public class FuturesContract implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合约ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 基础资产
     */
    @TableField("base_asset")
    private String baseAsset;

    /**
     * 计价资产
     */
    @TableField("quote_asset")
    private String quoteAsset;

    /**
     * 合约类型：PERPETUAL-永续合约，DELIVERY-交割合约
     */
    @TableField("contract_type")
    private String contractType;

    /**
     * 合约大小
     */
    @TableField("contract_size")
    private BigDecimal contractSize;

    /**
     * 最小价格变动
     */
    @TableField("tick_size")
    private BigDecimal tickSize;

    /**
     * 最小数量变动
     */
    @TableField("step_size")
    private BigDecimal stepSize;

    /**
     * 最小订单数量
     */
    @TableField("min_qty")
    private BigDecimal minQty;

    /**
     * 最大订单数量
     */
    @TableField("max_qty")
    private BigDecimal maxQty;

    /**
     * 最大杠杆倍数
     */
    @TableField("max_leverage")
    private Integer maxLeverage;

    /**
     * 维持保证金率
     */
    @TableField("maintenance_margin_rate")
    private BigDecimal maintenanceMarginRate;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 指数价格
     */
    @TableField("index_price")
    private BigDecimal indexPrice;

    /**
     * 资金费率
     */
    @TableField("funding_rate")
    private BigDecimal fundingRate;

    /**
     * 下次资金费用时间
     */
    @TableField("next_funding_time")
    private LocalDateTime nextFundingTime;

    /**
     * 24小时价格变化
     */
    @TableField("price_change_24h")
    private BigDecimal priceChange24h;

    /**
     * 24小时成交量
     */
    @TableField("volume_24h")
    private BigDecimal volume24h;

    /**
     * 交割时间（仅交割合约）
     */
    @TableField("delivery_time")
    private LocalDateTime deliveryTime;

    /**
     * 合约状态：ACTIVE-活跃，SUSPENDED-暂停，DELISTED-下架
     */
    @TableField("status")
    private String status;

    /**
     * 是否热门
     */
    @TableField("is_hot")
    private Boolean isHot;

    /**
     * 是否新上线
     */
    @TableField("is_new")
    private Boolean isNew;

    /**
     * 排序顺序
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}