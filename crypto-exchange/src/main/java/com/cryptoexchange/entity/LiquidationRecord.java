package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 强平记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("liquidation_record")
public class LiquidationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 强平记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 强平编号
     */
    @TableField("liquidation_id")
    private String liquidationId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 强平方向：1-多头强平，2-空头强平
     */
    @TableField("side")
    private Integer side;

    /**
     * 强平数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 强平价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 强平金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 强平时保证金
     */
    @TableField("margin")
    private BigDecimal margin;

    /**
     * 强平手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 强平损失
     */
    @TableField("loss")
    private BigDecimal loss;

    /**
     * 保险基金补偿
     */
    @TableField("insurance_compensation")
    private BigDecimal insuranceCompensation;

    /**
     * 强平原因：1-维持保证金不足，2-ADL自动减仓，3-系统强平
     */
    @TableField("liquidation_reason")
    private Integer liquidationReason;

    /**
     * 强平状态：1-强平中，2-强平完成，3-强平失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 风险率
     */
    @TableField("risk_ratio")
    private BigDecimal riskRatio;

    /**
     * 标记价格
     */
    @TableField("mark_price")
    private BigDecimal markPrice;

    /**
     * 破产价格
     */
    @TableField("bankruptcy_price")
    private BigDecimal bankruptcyPrice;

    /**
     * 强平开始时间
     */
    @TableField("liquidation_start_time")
    private LocalDateTime liquidationStartTime;

    /**
     * 强平完成时间
     */
    @TableField("liquidation_end_time")
    private LocalDateTime liquidationEndTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}