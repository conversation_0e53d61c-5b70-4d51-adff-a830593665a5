package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("orders")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 订单号（唯一）
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 交易对ID
     */
    @TableField("trading_pair_id")
    private Long tradingPairId;

    /**
     * 交易对符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 订单类型：1-限价单，2-市价单，3-止损单，4-止盈单
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 买卖方向：1-买入，2-卖出
     */
    @TableField("side")
    private Integer side;

    /**
     * 委托价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 委托数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 委托金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 已成交数量
     */
    @TableField("filled_quantity")
    private BigDecimal filledQuantity;

    /**
     * 已成交金额
     */
    @TableField("filled_amount")
    private BigDecimal filledAmount;

    /**
     * 剩余数量
     */
    @TableField("remaining_quantity")
    private BigDecimal remainingQuantity;

    /**
     * 平均成交价格
     */
    @TableField("avg_price")
    private BigDecimal avgPrice;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 手续费币种
     */
    @TableField("fee_currency")
    private String feeCurrency;

    /**
     * 手续费率
     */
    @TableField("fee_rate")
    private BigDecimal feeRate;

    /**
     * 订单状态：1-待成交，2-部分成交，3-完全成交，4-已撤销，5-已拒绝
     */
    @TableField("status")
    private Integer status;

    /**
     * 时间有效性：1-GTC（撤销前有效），2-IOC（立即成交或撤销），3-FOK（全部成交或撤销）
     */
    @TableField("time_in_force")
    private Integer timeInForce;

    /**
     * 止损价格
     */
    @TableField("stop_price")
    private BigDecimal stopPrice;

    /**
     * 冰山订单显示数量
     */
    @TableField("iceberg_qty")
    private BigDecimal icebergQty;

    /**
     * 客户端订单ID
     */
    @TableField("client_order_id")
    private String clientOrderId;

    /**
     * 订单来源：1-WEB，2-APP，3-API
     */
    @TableField("source")
    private Integer source;

    /**
     * 是否为做市商订单
     */
    @TableField("is_maker")
    private Boolean isMaker;

    /**
     * 订单优先级
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 撤销原因
     */
    @TableField("cancel_reason")
    private String cancelReason;

    /**
     * 拒绝原因
     */
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 成交时间
     */
    @TableField("filled_time")
    private LocalDateTime filledTime;

    /**
     * 撤销时间
     */
    @TableField("cancel_time")
    private LocalDateTime cancelTime;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 订单备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 扩展字段（JSON格式）
     */
    @TableField("extra_data")
    private String extraData;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;
}