package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货交易记录实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_trade")
public class FuturesTrade implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易编号
     */
    @TableField("trade_id")
    private String tradeId;

    /**
     * 买方订单ID
     */
    @TableField("buyer_order_id")
    private String buyerOrderId;

    /**
     * 卖方订单ID
     */
    @TableField("seller_order_id")
    private String sellerOrderId;

    /**
     * 买方用户ID
     */
    @TableField("buyer_user_id")
    private Long buyerUserId;

    /**
     * 卖方用户ID
     */
    @TableField("seller_user_id")
    private Long sellerUserId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 交易价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 交易数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 交易金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 买方手续费
     */
    @TableField("buyer_fee")
    private BigDecimal buyerFee;

    /**
     * 卖方手续费
     */
    @TableField("seller_fee")
    private BigDecimal sellerFee;

    /**
     * 买方手续费币种
     */
    @TableField("buyer_fee_currency")
    private String buyerFeeCurrency;

    /**
     * 卖方手续费币种
     */
    @TableField("seller_fee_currency")
    private String sellerFeeCurrency;

    /**
     * 交易方向：1-买入，2-卖出（相对于taker）
     */
    @TableField("side")
    private Integer side;

    /**
     * 交易类型：1-开仓，2-平仓，3-强平
     */
    @TableField("trade_type")
    private Integer tradeType;

    /**
     * 是否为Maker交易
     */
    @TableField("is_maker")
    private Boolean isMaker;

    /**
     * 交易时间
     */
    @TableField("trade_time")
    private LocalDateTime tradeTime;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}