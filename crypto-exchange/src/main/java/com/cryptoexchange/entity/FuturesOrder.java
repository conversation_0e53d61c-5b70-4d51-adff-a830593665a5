package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货订单实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("futures_order")
public class FuturesOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 合约符号
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 订单类型：MARKET-市价单，LIMIT-限价单，STOP-止损单，TAKE_PROFIT-止盈单
     */
    @TableField("order_type")
    private String orderType;

    /**
     * 订单方向：BUY-买入，SELL-卖出
     */
    @TableField("side")
    private String side;

    /**
     * 持仓方向：LONG-多头，SHORT-空头
     */
    @TableField("position_side")
    private String positionSide;

    /**
     * 订单数量
     */
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 订单价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 已成交数量
     */
    @TableField("filled_quantity")
    private BigDecimal filledQuantity;

    /**
     * 平均成交价格
     */
    @TableField("avg_price")
    private BigDecimal avgPrice;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 杠杆倍数
     */
    @TableField("leverage")
    private Integer leverage;

    /**
     * 保证金
     */
    @TableField("margin")
    private BigDecimal margin;

    /**
     * 止损价格
     */
    @TableField("stop_price")
    private BigDecimal stopPrice;

    /**
     * 止盈价格
     */
    @TableField("take_profit_price")
    private BigDecimal takeProfitPrice;

    /**
     * 订单状态：PENDING-待成交，PARTIAL-部分成交，FILLED-完全成交，CANCELLED-已取消，REJECTED-已拒绝
     */
    @TableField("status")
    private String status;

    /**
     * 时效类型：GTC-撤销前有效，IOC-立即成交或取消，FOK-全部成交或取消
     */
    @TableField("time_in_force")
    private String timeInForce;

    /**
     * 是否只做Maker
     */
    @TableField("post_only")
    private Boolean postOnly;

    /**
     * 是否减仓单
     */
    @TableField("reduce_only")
    private Boolean reduceOnly;

    /**
     * 客户端订单ID
     */
    @TableField("client_order_id")
    private String clientOrderId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}