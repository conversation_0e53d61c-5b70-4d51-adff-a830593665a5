package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易对实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("trading_pairs")
public class TradingPair implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易对ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 交易对符号（如：BTCUSDT、ETHUSDT）
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 基础货币ID（交易货币）
     */
    @TableField("base_currency_id")
    private Long baseCurrencyId;

    /**
     * 基础货币符号
     */
    @TableField("base_currency")
    private String baseCurrency;

    /**
     * 计价货币ID（定价货币）
     */
    @TableField("quote_currency_id")
    private Long quoteCurrencyId;

    /**
     * 计价货币符号
     */
    @TableField("quote_currency")
    private String quoteCurrency;

    /**
     * 交易对名称
     */
    @TableField("name")
    private String name;

    /**
     * 交易对状态：0-禁用，1-正常，2-维护中，3-即将上线
     */
    @TableField("status")
    private Integer status;

    /**
     * 价格精度（小数点后几位）
     */
    @TableField("price_precision")
    private Integer pricePrecision;

    /**
     * 数量精度（小数点后几位）
     */
    @TableField("quantity_precision")
    private Integer quantityPrecision;

    /**
     * 最小交易数量
     */
    @TableField("min_quantity")
    private BigDecimal minQuantity;

    /**
     * 最大交易数量
     */
    @TableField("max_quantity")
    private BigDecimal maxQuantity;

    /**
     * 最小交易金额
     */
    @TableField("min_amount")
    private BigDecimal minAmount;

    /**
     * 最大交易金额
     */
    @TableField("max_amount")
    private BigDecimal maxAmount;

    /**
     * 价格步长（最小价格变动单位）
     */
    @TableField("price_step")
    private BigDecimal priceStep;

    /**
     * 数量步长（最小数量变动单位）
     */
    @TableField("quantity_step")
    private BigDecimal quantityStep;

    /**
     * 买方手续费率（千分之几）
     */
    @TableField("buy_fee_rate")
    private BigDecimal buyFeeRate;

    /**
     * 卖方手续费率（千分之几）
     */
    @TableField("sell_fee_rate")
    private BigDecimal sellFeeRate;

    /**
     * 当前价格
     */
    @TableField("current_price")
    private BigDecimal currentPrice;

    /**
     * 开盘价（24小时）
     */
    @TableField("open_price")
    private BigDecimal openPrice;

    /**
     * 最高价（24小时）
     */
    @TableField("high_price")
    private BigDecimal highPrice;

    /**
     * 最低价（24小时）
     */
    @TableField("low_price")
    private BigDecimal lowPrice;

    /**
     * 收盘价（前一日）
     */
    @TableField("close_price")
    private BigDecimal closePrice;

    /**
     * 价格变动
     */
    @TableField("price_change")
    private BigDecimal priceChange;

    /**
     * 价格变动百分比
     */
    @TableField("price_change_percent")
    private BigDecimal priceChangePercent;

    /**
     * 24小时交易量（基础货币）
     */
    @TableField("volume_24h")
    private BigDecimal volume24h;

    /**
     * 24小时交易额（计价货币）
     */
    @TableField("amount_24h")
    private BigDecimal amount24h;

    /**
     * 24小时交易笔数
     */
    @TableField("count_24h")
    private Long count24h;

    /**
     * 排序权重
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否为热门交易对
     */
    @TableField("is_hot")
    private Boolean isHot;

    /**
     * 是否为新币交易对
     */
    @TableField("is_new")
    private Boolean isNew;

    /**
     * 是否支持杠杆交易
     */
    @TableField("leverage_enabled")
    private Boolean leverageEnabled;

    /**
     * 最大杠杆倍数
     */
    @TableField("max_leverage")
    private Integer maxLeverage;

    /**
     * 是否支持合约交易
     */
    @TableField("contract_enabled")
    private Boolean contractEnabled;

    /**
     * 上线时间
     */
    @TableField("launch_time")
    private LocalDateTime launchTime;

    /**
     * 交易对分类：1-主流区，2-创新区，3-观察区
     */
    @TableField("category")
    private Integer category;

    /**
     * 风险等级：1-低风险，2-中风险，3-高风险
     */
    @TableField("risk_level")
    private Integer riskLevel;

    /**
     * 交易对描述
     */
    @TableField("description")
    private String description;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}