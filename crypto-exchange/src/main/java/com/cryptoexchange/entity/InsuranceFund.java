package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保险基金实体类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("insurance_fund")
public class InsuranceFund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 保险基金ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 基金余额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 冻结余额
     */
    @TableField("frozen_balance")
    private BigDecimal frozenBalance;

    /**
     * 可用余额
     */
    @TableField("available_balance")
    private BigDecimal availableBalance;

    /**
     * 累计收入
     */
    @TableField("total_income")
    private BigDecimal totalIncome;

    /**
     * 累计支出
     */
    @TableField("total_expense")
    private BigDecimal totalExpense;

    /**
     * 今日收入
     */
    @TableField("daily_income")
    private BigDecimal dailyIncome;

    /**
     * 今日支出
     */
    @TableField("daily_expense")
    private BigDecimal dailyExpense;

    /**
     * 最后更新余额时间
     */
    @TableField("last_balance_update_time")
    private LocalDateTime lastBalanceUpdateTime;

    /**
     * 风险阈值
     */
    @TableField("risk_threshold")
    private BigDecimal riskThreshold;

    /**
     * 状态：1-正常，2-预警，3-紧急
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否启用自动补充
     */
    @TableField("auto_replenish_enabled")
    private Boolean autoReplenishEnabled;

    /**
     * 自动补充阈值
     */
    @TableField("auto_replenish_threshold")
    private BigDecimal autoReplenishThreshold;

    /**
     * 自动补充金额
     */
    @TableField("auto_replenish_amount")
    private BigDecimal autoReplenishAmount;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}