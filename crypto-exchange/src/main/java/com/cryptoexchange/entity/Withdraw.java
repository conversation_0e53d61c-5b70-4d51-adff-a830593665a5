package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("withdraw")
public class Withdraw implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 提现记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 币种
     */
    @TableField("currency")
    private String currency;

    /**
     * 提现金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 手续费
     */
    @TableField("fee")
    private BigDecimal fee;

    /**
     * 实际提现金额（扣除手续费后）
     */
    @TableField("actual_amount")
    private BigDecimal actualAmount;

    /**
     * 提现地址
     */
    @TableField("to_address")
    private String toAddress;

    /**
     * 地址标签
     */
    @TableField("address_tag")
    private String addressTag;

    /**
     * 交易哈希
     */
    @TableField("tx_hash")
    private String txHash;

    /**
     * 网络类型
     */
    @TableField("network")
    private String network;

    /**
     * 确认数
     */
    @TableField("confirmations")
    private Integer confirmations;

    /**
     * 需要确认数
     */
    @TableField("required_confirmations")
    private Integer requiredConfirmations;

    /**
     * 提现状态：PENDING-待处理，PROCESSING-处理中，COMPLETED-已完成，FAILED-失败，CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 审核状态：PENDING-待审核，APPROVED-已通过，REJECTED-已拒绝
     */
    @TableField("audit_status")
    private String auditStatus;

    /**
     * 审核人ID
     */
    @TableField("auditor_id")
    private Long auditorId;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    @TableField("audit_remark")
    private String auditRemark;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;
}