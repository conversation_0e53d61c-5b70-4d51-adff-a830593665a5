package com.cryptoexchange.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 币种实体类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("currencies")
public class Currency implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 币种ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 币种符号（如：BTC、ETH、USDT）
     */
    @TableField("symbol")
    private String symbol;

    /**
     * 币种名称（如：Bitcoin、Ethereum、Tether）
     */
    @TableField("name")
    private String name;

    /**
     * 币种全称
     */
    @TableField("full_name")
    private String fullName;

    /**
     * 币种图标URL
     */
    @TableField("icon")
    private String icon;

    /**
     * 币种类型：1-主流币，2-山寨币，3-稳定币，4-平台币
     */
    @TableField("type")
    private Integer type;

    /**
     * 区块链网络（如：Bitcoin、Ethereum、BSC、Polygon）
     */
    @TableField("network")
    private String network;

    /**
     * 合约地址（代币合约地址）
     */
    @TableField("contract_address")
    private String contractAddress;

    /**
     * 小数位数
     */
    @TableField("decimals")
    private Integer decimals;

    /**
     * 价格精度（小数点后几位）
     */
    @TableField("price_precision")
    private Integer pricePrecision;

    /**
     * 数量精度（小数点后几位）
     */
    @TableField("quantity_precision")
    private Integer quantityPrecision;

    /**
     * 最小交易数量
     */
    @TableField("min_trade_amount")
    private BigDecimal minTradeAmount;

    /**
     * 最大交易数量
     */
    @TableField("max_trade_amount")
    private BigDecimal maxTradeAmount;

    /**
     * 最小提现数量
     */
    @TableField("min_withdraw_amount")
    private BigDecimal minWithdrawAmount;

    /**
     * 最大提现数量
     */
    @TableField("max_withdraw_amount")
    private BigDecimal maxWithdrawAmount;

    /**
     * 提现手续费
     */
    @TableField("withdraw_fee")
    private BigDecimal withdrawFee;

    /**
     * 提现手续费类型：1-固定费用，2-按比例
     */
    @TableField("withdraw_fee_type")
    private Integer withdrawFeeType;

    /**
     * 充值确认数
     */
    @TableField("deposit_confirmations")
    private Integer depositConfirmations;

    /**
     * 提现确认数
     */
    @TableField("withdraw_confirmations")
    private Integer withdrawConfirmations;

    /**
     * 是否支持充值：0-不支持，1-支持
     */
    @TableField("deposit_enabled")
    private Boolean depositEnabled;

    /**
     * 是否支持提现：0-不支持，1-支持
     */
    @TableField("withdraw_enabled")
    private Boolean withdrawEnabled;

    /**
     * 是否支持交易：0-不支持，1-支持
     */
    @TableField("trade_enabled")
    private Boolean tradeEnabled;

    /**
     * 币种状态：0-禁用，1-正常，2-维护中
     */
    @TableField("status")
    private Integer status;

    /**
     * 排序权重
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否为基础货币（计价货币）
     */
    @TableField("is_base_currency")
    private Boolean isBaseCurrency;

    /**
     * 是否为法币
     */
    @TableField("is_fiat")
    private Boolean isFiat;

    /**
     * 总发行量
     */
    @TableField("total_supply")
    private BigDecimal totalSupply;

    /**
     * 流通量
     */
    @TableField("circulating_supply")
    private BigDecimal circulatingSupply;

    /**
     * 市值
     */
    @TableField("market_cap")
    private BigDecimal marketCap;

    /**
     * 当前价格（USDT）
     */
    @TableField("current_price")
    private BigDecimal currentPrice;

    /**
     * 24小时涨跌幅
     */
    @TableField("price_change_24h")
    private BigDecimal priceChange24h;

    /**
     * 24小时交易量
     */
    @TableField("volume_24h")
    private BigDecimal volume24h;

    /**
     * 币种介绍
     */
    @TableField("description")
    private String description;

    /**
     * 官网地址
     */
    @TableField("website")
    private String website;

    /**
     * 白皮书地址
     */
    @TableField("whitepaper")
    private String whitepaper;

    /**
     * 区块浏览器地址
     */
    @TableField("explorer")
    private String explorer;

    /**
     * 上线时间
     */
    @TableField("launch_time")
    private LocalDateTime launchTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新者
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}