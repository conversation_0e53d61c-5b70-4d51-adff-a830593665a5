package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.TradingPair;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易对Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TradingPairMapper extends BaseMapper<TradingPair> {

    /**
     * 根据交易对符号查询
     */
    @Select("SELECT * FROM trading_pairs WHERE symbol = #{symbol}")
    TradingPair selectBySymbol(@Param("symbol") String symbol);

    /**
     * 查询所有活跃的交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' ORDER BY sort_order ASC, symbol ASC")
    List<TradingPair> selectActiveTradingPairs();

    /**
     * 根据基础币种查询交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE base_asset = #{baseAsset} AND status = 'TRADING' ORDER BY symbol ASC")
    List<TradingPair> selectByBaseAsset(@Param("baseAsset") String baseAsset);

    /**
     * 根据计价币种查询交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE quote_asset = #{quoteAsset} AND status = 'TRADING' ORDER BY symbol ASC")
    List<TradingPair> selectByQuoteAsset(@Param("quoteAsset") String quoteAsset);

    /**
     * 更新交易对价格信息
     */
    @Update("UPDATE trading_pairs SET last_price = #{lastPrice}, price_change = #{priceChange}, " +
            "price_change_percent = #{priceChangePercent}, high_price = #{highPrice}, low_price = #{lowPrice}, " +
            "volume = #{volume}, quote_volume = #{quoteVolume}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updatePriceInfo(@Param("symbol") String symbol, @Param("lastPrice") BigDecimal lastPrice, 
                       @Param("priceChange") BigDecimal priceChange, @Param("priceChangePercent") BigDecimal priceChangePercent,
                       @Param("highPrice") BigDecimal highPrice, @Param("lowPrice") BigDecimal lowPrice,
                       @Param("volume") BigDecimal volume, @Param("quoteVolume") BigDecimal quoteVolume,
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新交易对状态
     */
    @Update("UPDATE trading_pairs SET status = #{status}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateStatus(@Param("symbol") String symbol, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 搜索交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE (symbol LIKE CONCAT('%', #{keyword}, '%') OR " +
            "base_asset LIKE CONCAT('%', #{keyword}, '%') OR quote_asset LIKE CONCAT('%', #{keyword}, '%')) " +
            "AND status = 'TRADING' ORDER BY symbol ASC")
    List<TradingPair> searchTradingPairs(@Param("keyword") String keyword);

    /**
     * 查询热门交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' ORDER BY quote_volume DESC LIMIT #{limit}")
    List<TradingPair> selectHotTradingPairs(@Param("limit") Integer limit);

    /**
     * 查询新上线的交易对
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' AND create_time >= #{startTime} ORDER BY create_time DESC")
    List<TradingPair> selectNewTradingPairs(@Param("startTime") LocalDateTime startTime);

    /**
     * 查询涨幅榜
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' AND price_change_percent IS NOT NULL ORDER BY price_change_percent DESC LIMIT #{limit}")
    List<TradingPair> selectTopGainers(@Param("limit") Integer limit);

    /**
     * 查询跌幅榜
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' AND price_change_percent IS NOT NULL ORDER BY price_change_percent ASC LIMIT #{limit}")
    List<TradingPair> selectTopLosers(@Param("limit") Integer limit);

    /**
     * 查询成交量排行
     */
    @Select("SELECT * FROM trading_pairs WHERE status = 'TRADING' ORDER BY quote_volume DESC LIMIT #{limit}")
    List<TradingPair> selectTopVolume(@Param("limit") Integer limit);
}