package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.Currency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 币种Mapper接口
 * 提供币种相关的数据库操作
 */
@Mapper
public interface CurrencyMapper extends BaseMapper<Currency> {

    /**
     * 根据币种符号查询
     */
    @Select("SELECT * FROM currency WHERE symbol = #{symbol}")
    Currency findBySymbol(@Param("symbol") String symbol);

    /**
     * 查询所有活跃币种
     */
    @Select("SELECT * FROM currency WHERE status = 'ACTIVE' ORDER BY sort_order ASC, symbol ASC")
    List<Currency> findActiveCurrencies();

    /**
     * 查询支持充值的币种
     */
    @Select("SELECT * FROM currency WHERE deposit_enabled = true AND status = 'ACTIVE' ORDER BY sort_order ASC")
    List<Currency> findDepositEnabledCurrencies();

    /**
     * 查询支持提现的币种
     */
    @Select("SELECT * FROM currency WHERE withdraw_enabled = true AND status = 'ACTIVE' ORDER BY sort_order ASC")
    List<Currency> findWithdrawEnabledCurrencies();

    /**
     * 查询支持交易的币种
     */
    @Select("SELECT * FROM currency WHERE trading_enabled = true AND status = 'ACTIVE' ORDER BY sort_order ASC")
    List<Currency> findTradingEnabledCurrencies();

    /**
     * 根据网络类型查询币种
     */
    @Select("SELECT * FROM currency WHERE network = #{network} AND status = 'ACTIVE'")
    List<Currency> findByNetwork(@Param("network") String network);

    /**
     * 更新币种价格
     */
    @Update("UPDATE currency SET current_price = #{price}, price_change_24h = #{priceChange24h}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updatePrice(@Param("symbol") String symbol, @Param("price") BigDecimal price, 
                   @Param("priceChange24h") BigDecimal priceChange24h, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新币种状态
     */
    @Update("UPDATE currency SET status = #{status}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateStatus(@Param("symbol") String symbol, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新充值状态
     */
    @Update("UPDATE currency SET deposit_enabled = #{enabled}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateDepositStatus(@Param("symbol") String symbol, @Param("enabled") Boolean enabled, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新提现状态
     */
    @Update("UPDATE currency SET withdraw_enabled = #{enabled}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateWithdrawStatus(@Param("symbol") String symbol, @Param("enabled") Boolean enabled, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新交易状态
     */
    @Update("UPDATE currency SET trading_enabled = #{enabled}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateTradingStatus(@Param("symbol") String symbol, @Param("enabled") Boolean enabled, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新提现手续费
     */
    @Update("UPDATE currency SET withdraw_fee = #{fee}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateWithdrawFee(@Param("symbol") String symbol, @Param("fee") BigDecimal fee, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新最小提现金额
     */
    @Update("UPDATE currency SET min_withdraw_amount = #{amount}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateMinWithdrawAmount(@Param("symbol") String symbol, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新最大提现金额
     */
    @Update("UPDATE currency SET max_withdraw_amount = #{amount}, update_time = #{updateTime} WHERE symbol = #{symbol}")
    int updateMaxWithdrawAmount(@Param("symbol") String symbol, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 搜索币种
     */
    @Select("SELECT * FROM currency WHERE (symbol LIKE CONCAT('%', #{keyword}, '%') OR full_name LIKE CONCAT('%', #{keyword}, '%')) AND status = 'ACTIVE'")
    List<Currency> searchCurrencies(@Param("keyword") String keyword);

    /**
     * 查询热门币种
     */
    @Select("SELECT * FROM currency WHERE is_hot = true AND status = 'ACTIVE' ORDER BY sort_order ASC LIMIT #{limit}")
    List<Currency> findHotCurrencies(@Param("limit") Integer limit);

    /**
     * 查询新上线币种
     */
    @Select("SELECT * FROM currency WHERE is_new = true AND status = 'ACTIVE' ORDER BY create_time DESC LIMIT #{limit}")
    List<Currency> findNewCurrencies(@Param("limit") Integer limit);

    /**
     * 查询涨幅榜
     */
    @Select("SELECT * FROM currency WHERE status = 'ACTIVE' AND price_change_24h > 0 ORDER BY price_change_24h DESC LIMIT #{limit}")
    List<Currency> findTopGainers(@Param("limit") Integer limit);

    /**
     * 查询跌幅榜
     */
    @Select("SELECT * FROM currency WHERE status = 'ACTIVE' AND price_change_24h < 0 ORDER BY price_change_24h ASC LIMIT #{limit}")
    List<Currency> findTopLosers(@Param("limit") Integer limit);

    /**
     * 查询币种统计信息
     */
    @Select("SELECT COUNT(*) as total_count, COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_count, COUNT(CASE WHEN deposit_enabled = true THEN 1 END) as deposit_enabled_count, COUNT(CASE WHEN withdraw_enabled = true THEN 1 END) as withdraw_enabled_count FROM currency")
    Map<String, Object> getCurrencyStatistics();
}