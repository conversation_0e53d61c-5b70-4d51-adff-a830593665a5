package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.UserFundingFee;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户资金费用Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserFundingFeeMapper extends BaseMapper<UserFundingFee> {

    /**
     * 查询用户资金费用记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的资金费用记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} AND symbol = #{symbol} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的用户资金费用
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectByUserIdAndTimeRange(@Param("userId") Long userId,
                                                   @Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定合约的所有资金费用记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE symbol = #{symbol} AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectBySymbol(@Param("symbol") String symbol);

    /**
     * 查询待结算的资金费用
     */
    @Select("SELECT * FROM user_funding_fee WHERE settlement_status = 1 AND is_deleted = 0 ORDER BY funding_time ASC")
    List<UserFundingFee> selectPendingSettlement();

    /**
     * 查询用户待结算的资金费用
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} AND settlement_status = 1 " +
            "AND is_deleted = 0 ORDER BY funding_time ASC")
    List<UserFundingFee> selectPendingSettlementByUser(@Param("userId") Long userId);

    /**
     * 更新结算状态
     */
    @Update("UPDATE user_funding_fee SET settlement_status = #{status}, settlement_time = #{settlementTime}, " +
            "transaction_hash = #{transactionHash}, update_time = #{updateTime} WHERE id = #{id}")
    int updateSettlementStatus(@Param("id") Long id,
                              @Param("status") Integer status,
                              @Param("settlementTime") LocalDateTime settlementTime,
                              @Param("transactionHash") String transactionHash,
                              @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新结算状态
     */
    @Update("UPDATE user_funding_fee SET settlement_status = #{status}, settlement_time = #{settlementTime}, " +
            "update_time = #{updateTime} WHERE user_id = #{userId} AND settlement_status = 1")
    int batchUpdateSettlementStatusByUser(@Param("userId") Long userId,
                                         @Param("status") Integer status,
                                         @Param("settlementTime") LocalDateTime settlementTime,
                                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 统计用户资金费用总额
     */
    @Select("SELECT COALESCE(SUM(funding_fee), 0) FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND settlement_status = 2 AND is_deleted = 0")
    BigDecimal sumFundingFeeByUser(@Param("userId") Long userId,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户支付的资金费用
     */
    @Select("SELECT COALESCE(SUM(ABS(funding_fee)), 0) FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND fee_type = 1 AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND settlement_status = 2 AND is_deleted = 0")
    BigDecimal sumPaidFeeByUser(@Param("userId") Long userId,
                               @Param("startTime") LocalDateTime startTime,
                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户收取的资金费用
     */
    @Select("SELECT COALESCE(SUM(funding_fee), 0) FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND fee_type = 2 AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND settlement_status = 2 AND is_deleted = 0")
    BigDecimal sumReceivedFeeByUser(@Param("userId") Long userId,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定合约的资金费用总额
     */
    @Select("SELECT COALESCE(SUM(funding_fee), 0) FROM user_funding_fee WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND settlement_status = 2 AND is_deleted = 0")
    BigDecimal sumFundingFeeBySymbol(@Param("symbol") String symbol,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户最近的资金费用记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC LIMIT #{limit}")
    List<UserFundingFee> selectRecentFeesByUser(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询指定资金费用时间的记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE funding_time = #{fundingTime} " +
            "AND is_deleted = 0 ORDER BY user_id ASC")
    List<UserFundingFee> selectByFundingTime(@Param("fundingTime") LocalDateTime fundingTime);

    /**
     * 查询用户指定持仓方向的资金费用
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} AND symbol = #{symbol} " +
            "AND position_side = #{positionSide} AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectByUserAndSymbolAndSide(@Param("userId") Long userId,
                                                     @Param("symbol") String symbol,
                                                     @Param("positionSide") Integer positionSide);

    /**
     * 统计用户资金费用记录数量
     */
    @Select("SELECT COUNT(*) FROM user_funding_fee WHERE user_id = #{userId} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    Long countFeeRecordsByUser(@Param("userId") Long userId,
                              @Param("startTime") LocalDateTime startTime,
                              @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户指定费用类型的记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE user_id = #{userId} AND fee_type = #{feeType} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<UserFundingFee> selectByUserAndFeeType(@Param("userId") Long userId,
                                               @Param("feeType") Integer feeType,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定合约和资金费用时间的记录
     */
    @Select("SELECT * FROM user_funding_fee WHERE symbol = #{symbol} AND funding_time = #{fundingTime} " +
            "AND is_deleted = 0")
    List<UserFundingFee> selectBySymbolAndFundingTime(@Param("symbol") String symbol,
                                                     @Param("fundingTime") LocalDateTime fundingTime);
}