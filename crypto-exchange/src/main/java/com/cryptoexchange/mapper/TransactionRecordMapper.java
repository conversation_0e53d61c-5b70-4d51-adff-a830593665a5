package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.TransactionRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 交易记录Mapper接口
 * 提供交易记录相关的数据库操作
 */
@Mapper
public interface TransactionRecordMapper extends BaseMapper<TransactionRecord> {

    /**
     * 根据用户ID查询交易记录
     */
    @Select("SELECT * FROM transaction_record WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<TransactionRecord> findByUserId(@Param("userId") Long userId);

    /**
     * 根据交易哈希查询记录
     */
    @Select("SELECT * FROM transaction_record WHERE tx_hash = #{txHash}")
    TransactionRecord findByTxHash(@Param("txHash") String txHash);

    /**
     * 根据用户ID和币种查询记录
     */
    @Select("SELECT * FROM transaction_record WHERE user_id = #{userId} AND currency = #{currency} ORDER BY create_time DESC")
    List<TransactionRecord> findByUserIdAndCurrency(@Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 根据交易类型查询记录
     */
    @Select("SELECT * FROM transaction_record WHERE transaction_type = #{transactionType} ORDER BY create_time DESC")
    List<TransactionRecord> findByTransactionType(@Param("transactionType") String transactionType);

    /**
     * 根据状态查询记录
     */
    @Select("SELECT * FROM transaction_record WHERE status = #{status} ORDER BY create_time DESC")
    List<TransactionRecord> findByStatus(@Param("status") String status);

    /**
     * 更新交易状态
     */
    @Update("UPDATE transaction_record SET status = #{status}, complete_time = #{completeTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("completeTime") LocalDateTime completeTime);

    /**
     * 更新确认数
     */
    @Update("UPDATE transaction_record SET confirmations = #{confirmations} WHERE id = #{id}")
    int updateConfirmations(@Param("id") Long id, @Param("confirmations") Integer confirmations);

    /**
     * 查询用户交易统计
     */
    @Select("SELECT COUNT(*) as total_count, SUM(amount) as total_amount FROM transaction_record WHERE user_id = #{userId} AND transaction_type = #{transactionType}")
    Map<String, Object> getUserTransactionStats(@Param("userId") Long userId, @Param("transactionType") String transactionType);

    /**
     * 查询待处理的交易记录
     */
    @Select("SELECT * FROM transaction_record WHERE status = 'PENDING' ORDER BY create_time ASC")
    List<TransactionRecord> findPendingTransactions();

    /**
     * 查询指定时间范围内的交易记录
     */
    @Select("SELECT * FROM transaction_record WHERE create_time BETWEEN #{startTime} AND #{endTime} ORDER BY create_time DESC")
    List<TransactionRecord> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大额交易记录
     */
    @Select("SELECT * FROM transaction_record WHERE amount >= #{minAmount} ORDER BY amount DESC")
    List<TransactionRecord> findLargeTransactions(@Param("minAmount") BigDecimal minAmount);

    /**
     * 查询用户今日交易总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM transaction_record WHERE user_id = #{userId} AND DATE(create_time) = CURDATE() AND transaction_type = #{transactionType}")
    BigDecimal getTodayTransactionAmount(@Param("userId") Long userId, @Param("transactionType") String transactionType);

    /**
     * 查询系统交易统计
     */
    @Select("SELECT transaction_type, COUNT(*) as count, SUM(amount) as total_amount FROM transaction_record WHERE DATE(create_time) = CURDATE() GROUP BY transaction_type")
    List<Map<String, Object>> getTodaySystemStats();
}