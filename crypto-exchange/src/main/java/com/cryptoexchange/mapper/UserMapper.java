package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cryptoexchange.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     * 
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = 0")
    User findByPhone(@Param("phone") String phone);

    /**
     * 根据推荐码查询用户
     * 
     * @param referralCode 推荐码
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE referral_code = #{referralCode} AND deleted = 0")
    User findByReferralCode(@Param("referralCode") String referralCode);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET status = #{status}, update_time = #{updateTime} WHERE id = #{userId}")
    int updateUserStatus(@Param("userId") Long userId, 
                        @Param("status") Integer status, 
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param password 密码
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET password = #{password}, update_time = #{updateTime} WHERE id = #{userId}")
    int updatePassword(@Param("userId") Long userId, 
                      @Param("password") String password, 
                      @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新交易密码
     * 
     * @param userId 用户ID
     * @param tradingPassword 交易密码
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET trading_password = #{tradingPassword}, update_time = #{updateTime} WHERE id = #{userId}")
    int updateTradingPassword(@Param("userId") Long userId, 
                             @Param("tradingPassword") String tradingPassword, 
                             @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新最后登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @param loginTime 登录时间
     * @param userAgent 用户代理
     * @return 影响行数
     */
    @Update("UPDATE users SET last_login_ip = #{loginIp}, last_login_time = #{loginTime}, " +
            "last_login_user_agent = #{userAgent}, update_time = #{loginTime} WHERE id = #{userId}")
    int updateLastLoginInfo(@Param("userId") Long userId, 
                           @Param("loginIp") String loginIp, 
                           @Param("loginTime") LocalDateTime loginTime, 
                           @Param("userAgent") String userAgent);

    /**
     * 更新用户交易量
     * 
     * @param userId 用户ID
     * @param volume 交易量
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET total_trading_volume = total_trading_volume + #{volume}, " +
            "update_time = #{updateTime} WHERE id = #{userId}")
    int updateTradingVolume(@Param("userId") Long userId, 
                           @Param("volume") BigDecimal volume, 
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新KYC状态
     * 
     * @param userId 用户ID
     * @param kycStatus KYC状态
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET kyc_status = #{kycStatus}, update_time = #{updateTime} WHERE id = #{userId}")
    int updateKycStatus(@Param("userId") Long userId, 
                       @Param("kycStatus") Integer kycStatus, 
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 软删除用户
     * 
     * @param userId 用户ID
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET deleted = 1, update_time = #{updateTime} WHERE id = #{userId}")
    int softDeleteUser(@Param("userId") Long userId, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 恢复已删除用户
     * 
     * @param userId 用户ID
     * @param updateTime 更新时间
     * @return 影响行数
     */
    @Update("UPDATE users SET deleted = 0, update_time = #{updateTime} WHERE id = #{userId}")
    int restoreUser(@Param("userId") Long userId, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND deleted = 0")
    int countByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     * 
     * @param email 邮箱
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email} AND deleted = 0")
    int countByEmail(@Param("email") String email);

    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE phone = #{phone} AND deleted = 0")
    int countByPhone(@Param("phone") String phone);

    /**
     * 检查推荐码是否存在
     * 
     * @param referralCode 推荐码
     * @return 数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE referral_code = #{referralCode} AND deleted = 0")
    int countByReferralCode(@Param("referralCode") String referralCode);

    /**
     * 分页查询用户列表
     * 
     * @param page 分页对象
     * @param keyword 关键词
     * @param status 状态
     * @param userType 用户类型
     * @param kycStatus KYC状态
     * @return 用户列表
     */
    IPage<User> findUsers(Page<User> page, 
                         @Param("keyword") String keyword, 
                         @Param("status") Integer status, 
                         @Param("userType") Integer userType, 
                         @Param("kycStatus") Integer kycStatus);

    /**
     * 获取用户推荐列表
     * 
     * @param page 分页对象
     * @param referrerId 推荐人ID
     * @return 推荐用户列表
     */
    @Select("SELECT * FROM users WHERE referrer_id = #{referrerId} AND deleted = 0 ORDER BY create_time DESC")
    IPage<User> findReferralUsers(Page<User> page, @Param("referrerId") Long referrerId);

    /**
     * 获取用户统计信息
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getUserStatistics(@Param("userId") Long userId);

    /**
     * 获取用户总数
     * 
     * @return 用户总数
     */
    @Select("SELECT COUNT(*) FROM users WHERE deleted = 0")
    Long getTotalUserCount();

    /**
     * 获取今日新增用户数
     * 
     * @return 今日新增用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE DATE(create_time) = CURDATE() AND deleted = 0")
    Long getTodayNewUserCount();

    /**
     * 获取活跃用户数（最近30天登录）
     * 
     * @return 活跃用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE last_login_time >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND deleted = 0")
    Long getActiveUserCount();

    /**
     * 获取已认证用户数
     * 
     * @return 已认证用户数
     */
    @Select("SELECT COUNT(*) FROM users WHERE kyc_status = 2 AND deleted = 0")
    Long getVerifiedUserCount();
}