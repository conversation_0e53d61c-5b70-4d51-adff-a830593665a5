package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.Withdraw;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 提现记录Mapper接口
 * 提供提现相关的数据库操作
 */
@Mapper
public interface WithdrawMapper extends BaseMapper<Withdraw> {

    /**
     * 根据用户ID查询提现记录
     */
    @Select("SELECT * FROM withdraw WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Withdraw> findByUserId(@Param("userId") Long userId);

    /**
     * 根据交易哈希查询提现记录
     */
    @Select("SELECT * FROM withdraw WHERE tx_hash = #{txHash}")
    Withdraw findByTxHash(@Param("txHash") String txHash);

    /**
     * 根据提现地址查询记录
     */
    @Select("SELECT * FROM withdraw WHERE to_address = #{toAddress} ORDER BY create_time DESC")
    List<Withdraw> findByToAddress(@Param("toAddress") String toAddress);

    /**
     * 根据状态查询提现记录
     */
    @Select("SELECT * FROM withdraw WHERE status = #{status} ORDER BY create_time DESC")
    List<Withdraw> findByStatus(@Param("status") String status);

    /**
     * 根据币种查询提现记录
     */
    @Select("SELECT * FROM withdraw WHERE currency = #{currency} ORDER BY create_time DESC")
    List<Withdraw> findByCurrency(@Param("currency") String currency);

    /**
     * 根据用户ID和币种查询提现记录
     */
    @Select("SELECT * FROM withdraw WHERE user_id = #{userId} AND currency = #{currency} ORDER BY create_time DESC")
    List<Withdraw> findByUserIdAndCurrency(@Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 更新提现状态
     */
    @Update("UPDATE withdraw SET status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新交易哈希
     */
    @Update("UPDATE withdraw SET tx_hash = #{txHash}, update_time = #{updateTime} WHERE id = #{id}")
    int updateTxHash(@Param("id") Long id, @Param("txHash") String txHash, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新确认数
     */
    @Update("UPDATE withdraw SET confirmations = #{confirmations}, update_time = #{updateTime} WHERE id = #{id}")
    int updateConfirmations(@Param("id") Long id, @Param("confirmations") Integer confirmations, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新审核状态和审核人
     */
    @Update("UPDATE withdraw SET audit_status = #{auditStatus}, auditor_id = #{auditorId}, audit_time = #{auditTime}, audit_remark = #{auditRemark}, update_time = #{updateTime} WHERE id = #{id}")
    int updateAuditInfo(@Param("id") Long id, @Param("auditStatus") String auditStatus, 
                       @Param("auditorId") Long auditorId, @Param("auditTime") LocalDateTime auditTime,
                       @Param("auditRemark") String auditRemark, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询待审核的提现记录
     */
    @Select("SELECT * FROM withdraw WHERE audit_status = 'PENDING' ORDER BY create_time ASC")
    List<Withdraw> findPendingAuditWithdraws();

    /**
     * 查询待处理的提现记录
     */
    @Select("SELECT * FROM withdraw WHERE status IN ('PENDING', 'PROCESSING') ORDER BY create_time ASC")
    List<Withdraw> findPendingWithdraws();

    /**
     * 查询用户提现统计
     */
    @Select("SELECT COUNT(*) as total_count, COALESCE(SUM(amount), 0) as total_amount, COALESCE(SUM(fee), 0) as total_fee FROM withdraw WHERE user_id = #{userId} AND status = 'COMPLETED'")
    Map<String, Object> getUserWithdrawStats(@Param("userId") Long userId);

    /**
     * 查询用户今日提现总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw WHERE user_id = #{userId} AND DATE(create_time) = CURDATE() AND status IN ('PENDING', 'PROCESSING', 'COMPLETED')")
    BigDecimal getTodayWithdrawAmount(@Param("userId") Long userId);

    /**
     * 查询用户指定币种提现总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw WHERE user_id = #{userId} AND currency = #{currency} AND status = 'COMPLETED'")
    BigDecimal getUserCurrencyWithdrawAmount(@Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 查询指定时间范围内的提现记录
     */
    @Select("SELECT * FROM withdraw WHERE create_time BETWEEN #{startTime} AND #{endTime} ORDER BY create_time DESC")
    List<Withdraw> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大额提现记录
     */
    @Select("SELECT * FROM withdraw WHERE amount >= #{minAmount} ORDER BY amount DESC")
    List<Withdraw> findLargeWithdraws(@Param("minAmount") BigDecimal minAmount);

    /**
     * 查询系统提现统计
     */
    @Select("SELECT currency, COUNT(*) as count, SUM(amount) as total_amount, SUM(fee) as total_fee FROM withdraw WHERE DATE(create_time) = CURDATE() AND status = 'COMPLETED' GROUP BY currency")
    List<Map<String, Object>> getTodaySystemWithdrawStats();

    /**
     * 查询需要确认的提现记录
     */
    @Select("SELECT * FROM withdraw WHERE status = 'PROCESSING' AND confirmations < required_confirmations ORDER BY create_time ASC")
    List<Withdraw> findConfirmingWithdraws();

    /**
     * 查询异常提现记录
     */
    @Select("SELECT * FROM withdraw WHERE status = 'FAILED' OR (status = 'PENDING' AND create_time < #{timeThreshold}) ORDER BY create_time DESC")
    List<Withdraw> findAbnormalWithdraws(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 查询用户最近一次提现记录
     */
    @Select("SELECT * FROM withdraw WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT 1")
    Withdraw findLatestByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定时间内提现次数
     */
    @Select("SELECT COUNT(*) FROM withdraw WHERE user_id = #{userId} AND create_time >= #{startTime} AND status IN ('PENDING', 'PROCESSING', 'COMPLETED')")
    int countUserWithdrawsInPeriod(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询用户风险提现记录
     */
    @Select("SELECT * FROM withdraw WHERE user_id = #{userId} AND (amount >= #{riskAmount} OR to_address IN (SELECT address FROM risk_address)) ORDER BY create_time DESC")
    List<Withdraw> findRiskWithdraws(@Param("userId") Long userId, @Param("riskAmount") BigDecimal riskAmount);
}