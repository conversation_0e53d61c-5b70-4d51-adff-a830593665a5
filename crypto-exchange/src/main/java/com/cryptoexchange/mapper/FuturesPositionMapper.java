package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesPosition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货持仓Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesPositionMapper extends BaseMapper<FuturesPosition> {

    /**
     * 根据用户ID和合约符号查询持仓
     */
    @Select("SELECT * FROM futures_position WHERE user_id = #{userId} AND symbol = #{symbol} AND side = #{side} AND is_deleted = 0")
    FuturesPosition selectByUserAndSymbolAndSide(@Param("userId") Long userId, 
                                                  @Param("symbol") String symbol, 
                                                  @Param("side") Integer side);

    /**
     * 查询用户所有持仓
     */
    @Select("SELECT * FROM futures_position WHERE user_id = #{userId} AND quantity > 0 AND is_deleted = 0 ORDER BY create_time DESC")
    List<FuturesPosition> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的持仓
     */
    @Select("SELECT * FROM futures_position WHERE user_id = #{userId} AND symbol = #{symbol} AND quantity > 0 AND is_deleted = 0")
    List<FuturesPosition> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询所有活跃持仓
     */
    @Select("SELECT * FROM futures_position WHERE quantity > 0 AND status = 1 AND is_deleted = 0")
    List<FuturesPosition> selectActivePositions();

    /**
     * 查询指定合约的所有活跃持仓
     */
    @Select("SELECT * FROM futures_position WHERE symbol = #{symbol} AND quantity > 0 AND status = 1 AND is_deleted = 0")
    List<FuturesPosition> selectActivePositionsBySymbol(@Param("symbol") String symbol);

    /**
     * 查询需要强平的持仓
     */
    @Select("SELECT * FROM futures_position WHERE status = 1 AND quantity > 0 AND " +
            "(unrealized_pnl + margin) <= maintenance_margin AND is_deleted = 0")
    List<FuturesPosition> selectLiquidationPositions();

    /**
     * 更新持仓标记价格和未实现盈亏
     */
    @Update("UPDATE futures_position SET mark_price = #{markPrice}, unrealized_pnl = #{unrealizedPnl}, " +
            "roe = #{roe}, last_price_update_time = #{updateTime}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateMarkPriceAndPnl(@Param("id") Long id, 
                              @Param("markPrice") BigDecimal markPrice,
                              @Param("unrealizedPnl") BigDecimal unrealizedPnl,
                              @Param("roe") BigDecimal roe,
                              @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新持仓数量和保证金
     */
    @Update("UPDATE futures_position SET quantity = #{quantity}, available_quantity = #{availableQuantity}, " +
            "avg_price = #{avgPrice}, margin = #{margin}, update_time = #{updateTime} WHERE id = #{id}")
    int updatePositionQuantityAndMargin(@Param("id") Long id,
                                        @Param("quantity") BigDecimal quantity,
                                        @Param("availableQuantity") BigDecimal availableQuantity,
                                        @Param("avgPrice") BigDecimal avgPrice,
                                        @Param("margin") BigDecimal margin,
                                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新持仓状态
     */
    @Update("UPDATE futures_position SET status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int updatePositionStatus(@Param("id") Long id, 
                            @Param("status") Integer status, 
                            @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新持仓标记价格
     */
    @Update("UPDATE futures_position SET mark_price = #{markPrice}, last_price_update_time = #{updateTime}, " +
            "update_time = #{updateTime} WHERE symbol = #{symbol} AND is_deleted = 0")
    int batchUpdateMarkPrice(@Param("symbol") String symbol, 
                            @Param("markPrice") BigDecimal markPrice,
                            @Param("updateTime") LocalDateTime updateTime);

    /**
     * 统计用户持仓总价值
     */
    @Select("SELECT COALESCE(SUM(ABS(quantity * mark_price)), 0) FROM futures_position " +
            "WHERE user_id = #{userId} AND quantity != 0 AND is_deleted = 0")
    BigDecimal sumPositionValue(@Param("userId") Long userId);

    /**
     * 统计用户总保证金
     */
    @Select("SELECT COALESCE(SUM(margin), 0) FROM futures_position " +
            "WHERE user_id = #{userId} AND quantity != 0 AND is_deleted = 0")
    BigDecimal sumPositionMargin(@Param("userId") Long userId);

    /**
     * 统计用户未实现盈亏
     */
    @Select("SELECT COALESCE(SUM(unrealized_pnl), 0) FROM futures_position " +
            "WHERE user_id = #{userId} AND quantity != 0 AND is_deleted = 0")
    BigDecimal sumUnrealizedPnl(@Param("userId") Long userId);
}