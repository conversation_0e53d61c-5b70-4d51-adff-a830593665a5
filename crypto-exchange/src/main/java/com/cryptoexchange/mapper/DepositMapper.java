package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.Deposit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 充值记录Mapper接口
 * 提供充值相关的数据库操作
 */
@Mapper
public interface DepositMapper extends BaseMapper<Deposit> {

    /**
     * 根据用户ID查询充值记录
     */
    @Select("SELECT * FROM deposit WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Deposit> findByUserId(@Param("userId") Long userId);

    /**
     * 根据交易哈希查询充值记录
     */
    @Select("SELECT * FROM deposit WHERE tx_hash = #{txHash}")
    Deposit findByTxHash(@Param("txHash") String txHash);

    /**
     * 根据充值地址查询记录
     */
    @Select("SELECT * FROM deposit WHERE address = #{address} ORDER BY create_time DESC")
    List<Deposit> findByAddress(@Param("address") String address);

    /**
     * 根据状态查询充值记录
     */
    @Select("SELECT * FROM deposit WHERE status = #{status} ORDER BY create_time DESC")
    List<Deposit> findByStatus(@Param("status") String status);

    /**
     * 根据币种查询充值记录
     */
    @Select("SELECT * FROM deposit WHERE currency = #{currency} ORDER BY create_time DESC")
    List<Deposit> findByCurrency(@Param("currency") String currency);

    /**
     * 根据用户ID和币种查询充值记录
     */
    @Select("SELECT * FROM deposit WHERE user_id = #{userId} AND currency = #{currency} ORDER BY create_time DESC")
    List<Deposit> findByUserIdAndCurrency(@Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 更新充值状态
     */
    @Update("UPDATE deposit SET status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新确认数
     */
    @Update("UPDATE deposit SET confirmations = #{confirmations}, update_time = #{updateTime} WHERE id = #{id}")
    int updateConfirmations(@Param("id") Long id, @Param("confirmations") Integer confirmations, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新实际到账金额
     */
    @Update("UPDATE deposit SET actual_amount = #{actualAmount}, update_time = #{updateTime} WHERE id = #{id}")
    int updateActualAmount(@Param("id") Long id, @Param("actualAmount") BigDecimal actualAmount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询待处理的充值记录
     */
    @Select("SELECT * FROM deposit WHERE status IN ('PENDING', 'CONFIRMING') ORDER BY create_time ASC")
    List<Deposit> findPendingDeposits();

    /**
     * 查询用户充值统计
     */
    @Select("SELECT COUNT(*) as total_count, COALESCE(SUM(amount), 0) as total_amount FROM deposit WHERE user_id = #{userId} AND status = 'COMPLETED'")
    Map<String, Object> getUserDepositStats(@Param("userId") Long userId);

    /**
     * 查询用户今日充值总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM deposit WHERE user_id = #{userId} AND DATE(create_time) = CURDATE() AND status = 'COMPLETED'")
    BigDecimal getTodayDepositAmount(@Param("userId") Long userId);

    /**
     * 查询用户指定币种充值总额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM deposit WHERE user_id = #{userId} AND currency = #{currency} AND status = 'COMPLETED'")
    BigDecimal getUserCurrencyDepositAmount(@Param("userId") Long userId, @Param("currency") String currency);

    /**
     * 查询指定时间范围内的充值记录
     */
    @Select("SELECT * FROM deposit WHERE create_time BETWEEN #{startTime} AND #{endTime} ORDER BY create_time DESC")
    List<Deposit> findByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大额充值记录
     */
    @Select("SELECT * FROM deposit WHERE amount >= #{minAmount} ORDER BY amount DESC")
    List<Deposit> findLargeDeposits(@Param("minAmount") BigDecimal minAmount);

    /**
     * 查询系统充值统计
     */
    @Select("SELECT currency, COUNT(*) as count, SUM(amount) as total_amount FROM deposit WHERE DATE(create_time) = CURDATE() AND status = 'COMPLETED' GROUP BY currency")
    List<Map<String, Object>> getTodaySystemDepositStats();

    /**
     * 查询需要确认的充值记录
     */
    @Select("SELECT * FROM deposit WHERE status = 'CONFIRMING' AND confirmations < required_confirmations ORDER BY create_time ASC")
    List<Deposit> findConfirmingDeposits();

    /**
     * 查询异常充值记录
     */
    @Select("SELECT * FROM deposit WHERE status = 'FAILED' OR (status = 'PENDING' AND create_time < #{timeThreshold}) ORDER BY create_time DESC")
    List<Deposit> findAbnormalDeposits(@Param("timeThreshold") LocalDateTime timeThreshold);

    /**
     * 查询用户最近一次充值记录
     */
    @Select("SELECT * FROM deposit WHERE user_id = #{userId} ORDER BY create_time DESC LIMIT 1")
    Deposit findLatestByUserId(@Param("userId") Long userId);
}