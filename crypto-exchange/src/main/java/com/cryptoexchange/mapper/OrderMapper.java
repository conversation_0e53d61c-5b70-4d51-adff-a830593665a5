package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 根据用户ID和交易对查询活跃订单
     */
    @Select("SELECT * FROM orders WHERE user_id = #{userId} AND symbol = #{symbol} AND status IN ('NEW', 'PARTIALLY_FILLED') ORDER BY create_time DESC")
    List<Order> selectActiveOrdersByUserAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 根据交易对查询买单深度
     */
    @Select("SELECT price, SUM(orig_qty - executed_qty) as quantity FROM orders WHERE symbol = #{symbol} AND side = 'BUY' AND status IN ('NEW', 'PARTIALLY_FILLED') GROUP BY price ORDER BY price DESC LIMIT #{limit}")
    List<Object[]> selectBuyDepth(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 根据交易对查询卖单深度
     */
    @Select("SELECT price, SUM(orig_qty - executed_qty) as quantity FROM orders WHERE symbol = #{symbol} AND side = 'SELL' AND status IN ('NEW', 'PARTIALLY_FILLED') GROUP BY price ORDER BY price ASC LIMIT #{limit}")
    List<Object[]> selectSellDepth(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 更新订单状态
     */
    @Update("UPDATE orders SET status = #{status}, update_time = #{updateTime} WHERE id = #{orderId}")
    int updateOrderStatus(@Param("orderId") Long orderId, @Param("status") String status, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新订单执行数量
     */
    @Update("UPDATE orders SET executed_qty = #{executedQty}, cummulative_quote_qty = #{cummulativeQuoteQty}, avg_price = #{avgPrice}, update_time = #{updateTime} WHERE id = #{orderId}")
    int updateOrderExecution(@Param("orderId") Long orderId, @Param("executedQty") BigDecimal executedQty, 
                           @Param("cummulativeQuoteQty") BigDecimal cummulativeQuoteQty, 
                           @Param("avgPrice") BigDecimal avgPrice, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询用户订单统计
     */
    @Select("SELECT COUNT(*) as total_orders, SUM(CASE WHEN status = 'FILLED' THEN 1 ELSE 0 END) as filled_orders FROM orders WHERE user_id = #{userId} AND create_time >= #{startTime}")
    Object[] selectUserOrderStats(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询交易对24小时统计
     */
    @Select("SELECT COUNT(*) as order_count, SUM(cummulative_quote_qty) as volume FROM orders WHERE symbol = #{symbol} AND status = 'FILLED' AND create_time >= #{startTime}")
    Object[] selectSymbol24hStats(@Param("symbol") String symbol, @Param("startTime") LocalDateTime startTime);
}