package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FuturesTrade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货交易记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FuturesTradeMapper extends BaseMapper<FuturesTrade> {

    /**
     * 根据交易ID查询交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE trade_id = #{tradeId} AND is_deleted = 0")
    FuturesTrade selectByTradeId(@Param("tradeId") String tradeId);

    /**
     * 查询用户交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND is_deleted = 0 ORDER BY trade_time DESC")
    List<FuturesTrade> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND symbol = #{symbol} AND is_deleted = 0 ORDER BY trade_time DESC")
    List<FuturesTrade> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的用户交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} AND is_deleted = 0 ORDER BY trade_time DESC")
    List<FuturesTrade> selectByUserIdAndTimeRange(@Param("userId") Long userId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询合约的最新交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY trade_time DESC LIMIT #{limit}")
    List<FuturesTrade> selectLatestTradesBySymbol(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 查询指定订单的交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE (buyer_order_id = #{orderId} OR seller_order_id = #{orderId}) " +
            "AND is_deleted = 0 ORDER BY trade_time DESC")
    List<FuturesTrade> selectByOrderId(@Param("orderId") String orderId);

    /**
     * 统计用户交易量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM futures_trade " +
            "WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumTradeVolumeByUser(@Param("userId") Long userId,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户交易金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM futures_trade " +
            "WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumTradeAmountByUser(@Param("userId") Long userId,
                                   @Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户手续费
     */
    @Select("SELECT COALESCE(SUM(CASE WHEN buyer_user_id = #{userId} THEN buyer_fee ELSE seller_fee END), 0) " +
            "FROM futures_trade WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumTradeFeeByUser(@Param("userId") Long userId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计合约24小时交易量
     */
    @Select("SELECT COALESCE(SUM(quantity), 0) FROM futures_trade " +
            "WHERE symbol = #{symbol} AND trade_time >= #{startTime} AND is_deleted = 0")
    BigDecimal sum24hVolumeBySymbol(@Param("symbol") String symbol, @Param("startTime") LocalDateTime startTime);

    /**
     * 统计合约24小时交易金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM futures_trade " +
            "WHERE symbol = #{symbol} AND trade_time >= #{startTime} AND is_deleted = 0")
    BigDecimal sum24hAmountBySymbol(@Param("symbol") String symbol, @Param("startTime") LocalDateTime startTime);

    /**
     * 获取合约最新成交价
     */
    @Select("SELECT price FROM futures_trade WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY trade_time DESC LIMIT 1")
    BigDecimal getLatestPriceBySymbol(@Param("symbol") String symbol);

    /**
     * 统计用户交易次数
     */
    @Select("SELECT COUNT(*) FROM futures_trade " +
            "WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} AND is_deleted = 0")
    Long countTradesByUser(@Param("userId") Long userId,
                          @Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户最近的交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE (buyer_user_id = #{userId} OR seller_user_id = #{userId}) " +
            "AND is_deleted = 0 ORDER BY trade_time DESC LIMIT #{limit}")
    List<FuturesTrade> selectRecentTradesByUser(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询指定价格范围内的交易记录
     */
    @Select("SELECT * FROM futures_trade WHERE symbol = #{symbol} " +
            "AND price >= #{minPrice} AND price <= #{maxPrice} " +
            "AND trade_time >= #{startTime} AND trade_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY trade_time DESC")
    List<FuturesTrade> selectTradesByPriceRange(@Param("symbol") String symbol,
                                               @Param("minPrice") BigDecimal minPrice,
                                               @Param("maxPrice") BigDecimal maxPrice,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);
}