package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.InsuranceFund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 保险基金Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface InsuranceFundMapper extends BaseMapper<InsuranceFund> {

    /**
     * 根据币种查询保险基金
     */
    @Select("SELECT * FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0 ORDER BY update_time DESC LIMIT 1")
    InsuranceFund selectByCurrency(@Param("currency") String currency);

    /**
     * 查询所有保险基金
     */
    @Select("SELECT * FROM insurance_fund WHERE is_deleted = 0 ORDER BY currency ASC")
    List<InsuranceFund> selectAllFunds();

    /**
     * 查询活跃的保险基金
     */
    @Select("SELECT * FROM insurance_fund WHERE status = 1 AND is_deleted = 0 ORDER BY currency ASC")
    List<InsuranceFund> selectActiveFunds();

    /**
     * 查询预警状态的保险基金
     */
    @Select("SELECT * FROM insurance_fund WHERE status IN (2, 3) AND is_deleted = 0 ORDER BY status DESC, currency ASC")
    List<InsuranceFund> selectWarningFunds();

    /**
     * 更新基金余额
     */
    @Update("UPDATE insurance_fund SET balance = #{balance}, available_balance = #{availableBalance}, " +
            "last_balance_update_time = #{updateTime}, update_time = #{updateTime} WHERE id = #{id}")
    int updateBalance(@Param("id") Long id,
                     @Param("balance") BigDecimal balance,
                     @Param("availableBalance") BigDecimal availableBalance,
                     @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新冻结余额
     */
    @Update("UPDATE insurance_fund SET frozen_balance = #{frozenBalance}, " +
            "available_balance = balance - #{frozenBalance}, update_time = #{updateTime} WHERE id = #{id}")
    int updateFrozenBalance(@Param("id") Long id,
                           @Param("frozenBalance") BigDecimal frozenBalance,
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 增加收入
     */
    @Update("UPDATE insurance_fund SET balance = balance + #{amount}, " +
            "available_balance = available_balance + #{amount}, " +
            "total_income = total_income + #{amount}, " +
            "daily_income = daily_income + #{amount}, " +
            "last_balance_update_time = #{updateTime}, update_time = #{updateTime} WHERE id = #{id}")
    int addIncome(@Param("id") Long id,
                 @Param("amount") BigDecimal amount,
                 @Param("updateTime") LocalDateTime updateTime);

    /**
     * 增加支出
     */
    @Update("UPDATE insurance_fund SET balance = balance - #{amount}, " +
            "available_balance = available_balance - #{amount}, " +
            "total_expense = total_expense + #{amount}, " +
            "daily_expense = daily_expense + #{amount}, " +
            "last_balance_update_time = #{updateTime}, update_time = #{updateTime} WHERE id = #{id}")
    int addExpense(@Param("id") Long id,
                  @Param("amount") BigDecimal amount,
                  @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新基金状态
     */
    @Update("UPDATE insurance_fund SET status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id,
                    @Param("status") Integer status,
                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 重置每日统计
     */
    @Update("UPDATE insurance_fund SET daily_income = 0, daily_expense = 0, update_time = #{updateTime} WHERE id = #{id}")
    int resetDailyStats(@Param("id") Long id, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量重置每日统计
     */
    @Update("UPDATE insurance_fund SET daily_income = 0, daily_expense = 0, update_time = #{updateTime} WHERE is_deleted = 0")
    int batchResetDailyStats(@Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询余额低于阈值的基金
     */
    @Select("SELECT * FROM insurance_fund WHERE available_balance < risk_threshold " +
            "AND status = 1 AND is_deleted = 0")
    List<InsuranceFund> selectLowBalanceFunds();

    /**
     * 查询需要自动补充的基金
     */
    @Select("SELECT * FROM insurance_fund WHERE auto_replenish_enabled = 1 " +
            "AND available_balance < auto_replenish_threshold " +
            "AND status = 1 AND is_deleted = 0")
    List<InsuranceFund> selectFundsNeedReplenish();

    /**
     * 统计总余额
     */
    @Select("SELECT COALESCE(SUM(balance), 0) FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    BigDecimal sumBalanceByCurrency(@Param("currency") String currency);

    /**
     * 统计总收入
     */
    @Select("SELECT COALESCE(SUM(total_income), 0) FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    BigDecimal sumTotalIncomeByCurrency(@Param("currency") String currency);

    /**
     * 统计总支出
     */
    @Select("SELECT COALESCE(SUM(total_expense), 0) FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    BigDecimal sumTotalExpenseByCurrency(@Param("currency") String currency);

    /**
     * 统计今日收入
     */
    @Select("SELECT COALESCE(SUM(daily_income), 0) FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    BigDecimal sumDailyIncomeByCurrency(@Param("currency") String currency);

    /**
     * 统计今日支出
     */
    @Select("SELECT COALESCE(SUM(daily_expense), 0) FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    BigDecimal sumDailyExpenseByCurrency(@Param("currency") String currency);

    /**
     * 查询所有币种
     */
    @Select("SELECT DISTINCT currency FROM insurance_fund WHERE is_deleted = 0 ORDER BY currency ASC")
    List<String> selectAllCurrencies();

    /**
     * 更新自动补充配置
     */
    @Update("UPDATE insurance_fund SET auto_replenish_enabled = #{enabled}, " +
            "auto_replenish_threshold = #{threshold}, auto_replenish_amount = #{amount}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    int updateAutoReplenishConfig(@Param("id") Long id,
                                 @Param("enabled") Boolean enabled,
                                 @Param("threshold") BigDecimal threshold,
                                 @Param("amount") BigDecimal amount,
                                 @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新风险阈值
     */
    @Update("UPDATE insurance_fund SET risk_threshold = #{threshold}, update_time = #{updateTime} WHERE id = #{id}")
    int updateRiskThreshold(@Param("id") Long id,
                           @Param("threshold") BigDecimal threshold,
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询基金历史记录
     */
    @Select("SELECT * FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0 ORDER BY update_time DESC")
    List<InsuranceFund> selectHistoryByCurrency(@Param("currency") String currency);

    /**
     * 检查基金是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM insurance_fund WHERE currency = #{currency} AND is_deleted = 0")
    boolean existsByCurrency(@Param("currency") String currency);
}