package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.FundingRate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资金费率Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface FundingRateMapper extends BaseMapper<FundingRate> {

    /**
     * 查询指定合约的当前资金费率
     */
    @Select("SELECT * FROM funding_rate WHERE symbol = #{symbol} AND status = 1 " +
            "AND is_deleted = 0 ORDER BY funding_time DESC LIMIT 1")
    FundingRate selectCurrentBySymbol(@Param("symbol") String symbol);

    /**
     * 查询指定合约的历史资金费率
     */
    @Select("SELECT * FROM funding_rate WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY funding_time DESC")
    List<FundingRate> selectHistoryBySymbol(@Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的资金费率
     */
    @Select("SELECT * FROM funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY funding_time DESC")
    List<FundingRate> selectBySymbolAndTimeRange(@Param("symbol") String symbol,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 查询所有活跃合约的当前资金费率
     */
    @Select("SELECT fr.* FROM funding_rate fr " +
            "INNER JOIN (SELECT symbol, MAX(funding_time) as max_time FROM funding_rate " +
            "WHERE status = 1 AND is_deleted = 0 GROUP BY symbol) latest " +
            "ON fr.symbol = latest.symbol AND fr.funding_time = latest.max_time " +
            "WHERE fr.status = 1 AND fr.is_deleted = 0")
    List<FundingRate> selectAllCurrentRates();

    /**
     * 查询即将到期的资金费率
     */
    @Select("SELECT * FROM funding_rate WHERE next_funding_time <= #{time} " +
            "AND status = 1 AND is_deleted = 0")
    List<FundingRate> selectExpiringSoon(@Param("time") LocalDateTime time);

    /**
     * 更新资金费率
     */
    @Update("UPDATE funding_rate SET rate = #{rate}, predicted_rate = #{predictedRate}, " +
            "mark_price = #{markPrice}, index_price = #{indexPrice}, premium_index = #{premiumIndex}, " +
            "next_funding_time = #{nextFundingTime}, update_time = #{updateTime} WHERE id = #{id}")
    int updateFundingRate(@Param("id") Long id,
                         @Param("rate") BigDecimal rate,
                         @Param("predictedRate") BigDecimal predictedRate,
                         @Param("markPrice") BigDecimal markPrice,
                         @Param("indexPrice") BigDecimal indexPrice,
                         @Param("premiumIndex") BigDecimal premiumIndex,
                         @Param("nextFundingTime") LocalDateTime nextFundingTime,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新预测资金费率
     */
    @Update("UPDATE funding_rate SET predicted_rate = #{predictedRate}, " +
            "premium_index = #{premiumIndex}, update_time = #{updateTime} " +
            "WHERE symbol = #{symbol} AND status = 1")
    int updatePredictedRate(@Param("symbol") String symbol,
                           @Param("predictedRate") BigDecimal predictedRate,
                           @Param("premiumIndex") BigDecimal premiumIndex,
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新标记价格和指数价格
     */
    @Update("UPDATE funding_rate SET mark_price = #{markPrice}, index_price = #{indexPrice}, " +
            "update_time = #{updateTime} WHERE symbol = #{symbol} AND status = 1")
    int updatePrices(@Param("symbol") String symbol,
                    @Param("markPrice") BigDecimal markPrice,
                    @Param("indexPrice") BigDecimal indexPrice,
                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询指定合约的平均资金费率
     */
    @Select("SELECT AVG(rate) FROM funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    BigDecimal getAverageRateBySymbol(@Param("symbol") String symbol,
                                     @Param("startTime") LocalDateTime startTime,
                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定合约的最高资金费率
     */
    @Select("SELECT MAX(rate) FROM funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    BigDecimal getMaxRateBySymbol(@Param("symbol") String symbol,
                                 @Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定合约的最低资金费率
     */
    @Select("SELECT MIN(rate) FROM funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    BigDecimal getMinRateBySymbol(@Param("symbol") String symbol,
                                 @Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近N期的资金费率
     */
    @Select("SELECT * FROM funding_rate WHERE symbol = #{symbol} AND is_deleted = 0 " +
            "ORDER BY funding_time DESC LIMIT #{limit}")
    List<FundingRate> selectRecentRatesBySymbol(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 统计资金费率记录数量
     */
    @Select("SELECT COUNT(*) FROM funding_rate WHERE symbol = #{symbol} " +
            "AND funding_time >= #{startTime} AND funding_time <= #{endTime} " +
            "AND is_deleted = 0")
    Long countRatesBySymbol(@Param("symbol") String symbol,
                           @Param("startTime") LocalDateTime startTime,
                           @Param("endTime") LocalDateTime endTime);

    /**
     * 查询需要更新的资金费率（下次资金费用时间已到）
     */
    @Select("SELECT * FROM funding_rate WHERE next_funding_time <= #{currentTime} " +
            "AND status = 1 AND is_deleted = 0")
    List<FundingRate> selectRatesNeedUpdate(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量更新资金费率状态
     */
    @Update("UPDATE funding_rate SET status = #{status}, update_time = #{updateTime} " +
            "WHERE symbol = #{symbol} AND is_deleted = 0")
    int batchUpdateStatusBySymbol(@Param("symbol") String symbol,
                                 @Param("status") Integer status,
                                 @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询指定合约的下次资金费用时间
     */
    @Select("SELECT next_funding_time FROM funding_rate WHERE symbol = #{symbol} " +
            "AND status = 1 AND is_deleted = 0 ORDER BY funding_time DESC LIMIT 1")
    LocalDateTime getNextFundingTimeBySymbol(@Param("symbol") String symbol);
}