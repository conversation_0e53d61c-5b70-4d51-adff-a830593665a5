package com.cryptoexchange.mapper;

import com.cryptoexchange.entity.MarginAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 保证金账户Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface MarginAccountMapper extends BaseMapper<MarginAccount> {

    /**
     * 根据用户ID查询保证金账户
     * 
     * @param userId 用户ID
     * @return 保证金账户
     */
    @Select("SELECT * FROM margin_account WHERE user_id = #{userId}")
    MarginAccount selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和币种查询保证金账户
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @return 保证金账户
     */
    @Select("SELECT * FROM margin_account WHERE user_id = #{userId} AND symbol = #{symbol}")
    MarginAccount selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询用户所有保证金账户
     * 
     * @param userId 用户ID
     * @return 保证金账户列表
     */
    @Select("SELECT * FROM margin_account WHERE user_id = #{userId}")
    List<MarginAccount> selectAllByUserId(@Param("userId") Long userId);

    /**
     * 更新账户余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param balance 余额
     * @param frozenBalance 冻结余额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET balance = #{balance}, frozen_balance = #{frozenBalance}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol}")
    int updateBalance(@Param("userId") Long userId, 
                     @Param("symbol") String symbol, 
                     @Param("balance") BigDecimal balance, 
                     @Param("frozenBalance") BigDecimal frozenBalance);

    /**
     * 增加账户余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param amount 金额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET balance = balance + #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol}")
    int addBalance(@Param("userId") Long userId, 
                  @Param("symbol") String symbol, 
                  @Param("amount") BigDecimal amount);

    /**
     * 减少账户余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param amount 金额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET balance = balance - #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol} AND balance >= #{amount}")
    int subtractBalance(@Param("userId") Long userId, 
                       @Param("symbol") String symbol, 
                       @Param("amount") BigDecimal amount);

    /**
     * 冻结账户余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param amount 金额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET balance = balance - #{amount}, " +
            "frozen_balance = frozen_balance + #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol} AND balance >= #{amount}")
    int freezeBalance(@Param("userId") Long userId, 
                     @Param("symbol") String symbol, 
                     @Param("amount") BigDecimal amount);

    /**
     * 解冻账户余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param amount 金额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET balance = balance + #{amount}, " +
            "frozen_balance = frozen_balance - #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol} AND frozen_balance >= #{amount}")
    int unfreezeBalance(@Param("userId") Long userId, 
                       @Param("symbol") String symbol, 
                       @Param("amount") BigDecimal amount);

    /**
     * 扣除冻结余额
     * 
     * @param userId 用户ID
     * @param symbol 币种
     * @param amount 金额
     * @return 更新行数
     */
    @Update("UPDATE margin_account SET frozen_balance = frozen_balance - #{amount}, " +
            "update_time = NOW() WHERE user_id = #{userId} AND symbol = #{symbol} AND frozen_balance >= #{amount}")
    int deductFrozenBalance(@Param("userId") Long userId, 
                           @Param("symbol") String symbol, 
                           @Param("amount") BigDecimal amount);

    /**
     * 查询账户总价值
     * 
     * @param userId 用户ID
     * @return 总价值
     */
    @Select("SELECT COALESCE(SUM(balance * price + frozen_balance * price), 0) " +
            "FROM margin_account ma LEFT JOIN symbol_price sp ON ma.symbol = sp.symbol " +
            "WHERE ma.user_id = #{userId}")
    BigDecimal selectTotalValue(@Param("userId") Long userId);

    /**
     * 查询有余额的账户
     * 
     * @param userId 用户ID
     * @return 保证金账户列表
     */
    @Select("SELECT * FROM margin_account WHERE user_id = #{userId} AND (balance > 0 OR frozen_balance > 0)")
    List<MarginAccount> selectAccountsWithBalance(@Param("userId") Long userId);

    /**
     * 批量更新账户状态
     * 
     * @param userIds 用户ID列表
     * @param status 状态
     * @return 更新行数
     */
    @Update("<script>" +
            "UPDATE margin_account SET status = #{status}, update_time = NOW() " +
            "WHERE user_id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            "</script>")
    int batchUpdateStatus(@Param("userIds") List<Long> userIds, @Param("status") String status);
}