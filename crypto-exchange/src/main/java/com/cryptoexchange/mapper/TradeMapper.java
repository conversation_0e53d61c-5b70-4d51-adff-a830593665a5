package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.Trade;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易记录Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TradeMapper extends BaseMapper<Trade> {

    /**
     * 根据用户ID查询交易记录
     */
    @Select("SELECT * FROM trades WHERE user_id = #{userId} ORDER BY time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Trade> selectTradesByUser(@Param("userId") Long userId, @Param("limit") Integer limit, @Param("offset") Integer offset);

    /**
     * 根据交易对查询最新成交记录
     */
    @Select("SELECT * FROM trades WHERE symbol = #{symbol} ORDER BY time DESC LIMIT #{limit}")
    List<Trade> selectRecentTradesBySymbol(@Param("symbol") String symbol, @Param("limit") Integer limit);

    /**
     * 查询交易对24小时统计
     */
    @Select("SELECT COUNT(*) as trade_count, SUM(quote_qty) as volume, MIN(price) as low_price, MAX(price) as high_price, " +
            "(SELECT price FROM trades WHERE symbol = #{symbol} AND time >= #{startTime} ORDER BY time ASC LIMIT 1) as open_price, " +
            "(SELECT price FROM trades WHERE symbol = #{symbol} AND time >= #{startTime} ORDER BY time DESC LIMIT 1) as close_price " +
            "FROM trades WHERE symbol = #{symbol} AND time >= #{startTime}")
    Object[] selectSymbol24hTradeStats(@Param("symbol") String symbol, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询用户交易统计
     */
    @Select("SELECT COUNT(*) as total_trades, SUM(quote_qty) as total_volume, SUM(commission) as total_commission " +
            "FROM trades WHERE user_id = #{userId} AND time >= #{startTime}")
    Object[] selectUserTradeStats(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询交易对价格历史
     */
    @Select("SELECT price, time FROM trades WHERE symbol = #{symbol} AND time BETWEEN #{startTime} AND #{endTime} ORDER BY time ASC")
    List<Object[]> selectPriceHistory(@Param("symbol") String symbol, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 查询大额交易
     */
    @Select("SELECT * FROM trades WHERE symbol = #{symbol} AND quote_qty >= #{minAmount} AND time >= #{startTime} ORDER BY time DESC")
    List<Trade> selectLargeTrades(@Param("symbol") String symbol, @Param("minAmount") BigDecimal minAmount, @Param("startTime") LocalDateTime startTime);

    /**
     * 查询用户在指定交易对的交易记录
     */
    @Select("SELECT * FROM trades WHERE user_id = #{userId} AND symbol = #{symbol} ORDER BY time DESC LIMIT #{limit} OFFSET #{offset}")
    List<Trade> selectTradesByUserAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol, @Param("limit") Integer limit, @Param("offset") Integer offset);
}