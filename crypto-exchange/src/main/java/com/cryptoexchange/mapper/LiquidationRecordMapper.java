package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.LiquidationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 强平记录Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface LiquidationRecordMapper extends BaseMapper<LiquidationRecord> {

    /**
     * 根据强平ID查询记录
     */
    @Select("SELECT * FROM liquidation_record WHERE liquidation_id = #{liquidationId} AND is_deleted = 0")
    LiquidationRecord selectByLiquidationId(@Param("liquidationId") String liquidationId);

    /**
     * 查询用户强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE user_id = #{userId} AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询用户指定合约的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE user_id = #{userId} AND symbol = #{symbol} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectByUserIdAndSymbol(@Param("userId") Long userId, @Param("symbol") String symbol);

    /**
     * 查询指定时间范围内的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE create_time >= #{startTime} AND create_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                             @Param("endTime") LocalDateTime endTime);

    /**
     * 查询指定合约的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE symbol = #{symbol} AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectBySymbol(@Param("symbol") String symbol);

    /**
     * 查询进行中的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE status = 1 AND is_deleted = 0 ORDER BY create_time ASC")
    List<LiquidationRecord> selectOngoingLiquidations();

    /**
     * 查询用户指定状态的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE user_id = #{userId} AND status = #{status} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新强平状态
     */
    @Update("UPDATE liquidation_record SET status = #{status}, liquidation_end_time = #{endTime}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    int updateLiquidationStatus(@Param("id") Long id,
                               @Param("status") Integer status,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新强平结果
     */
    @Update("UPDATE liquidation_record SET price = #{price}, amount = #{amount}, fee = #{fee}, " +
            "loss = #{loss}, insurance_compensation = #{insuranceCompensation}, " +
            "liquidation_end_time = #{endTime}, status = #{status}, update_time = #{updateTime} WHERE id = #{id}")
    int updateLiquidationResult(@Param("id") Long id,
                               @Param("price") BigDecimal price,
                               @Param("amount") BigDecimal amount,
                               @Param("fee") BigDecimal fee,
                               @Param("loss") BigDecimal loss,
                               @Param("insuranceCompensation") BigDecimal insuranceCompensation,
                               @Param("endTime") LocalDateTime endTime,
                               @Param("status") Integer status,
                               @Param("updateTime") LocalDateTime updateTime);

    /**
     * 统计用户强平次数
     */
    @Select("SELECT COUNT(*) FROM liquidation_record WHERE user_id = #{userId} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} AND is_deleted = 0")
    Long countLiquidationsByUser(@Param("userId") Long userId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户强平损失
     */
    @Select("SELECT COALESCE(SUM(loss), 0) FROM liquidation_record WHERE user_id = #{userId} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumLiquidationLossByUser(@Param("userId") Long userId,
                                       @Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计保险基金补偿总额
     */
    @Select("SELECT COALESCE(SUM(insurance_compensation), 0) FROM liquidation_record " +
            "WHERE create_time >= #{startTime} AND create_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumInsuranceCompensation(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 统计指定合约的强平次数
     */
    @Select("SELECT COUNT(*) FROM liquidation_record WHERE symbol = #{symbol} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} AND is_deleted = 0")
    Long countLiquidationsBySymbol(@Param("symbol") String symbol,
                                  @Param("startTime") LocalDateTime startTime,
                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最近的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE is_deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<LiquidationRecord> selectRecentLiquidations(@Param("limit") Integer limit);

    /**
     * 查询指定强平原因的记录
     */
    @Select("SELECT * FROM liquidation_record WHERE liquidation_reason = #{reason} " +
            "AND create_time >= #{startTime} AND create_time <= #{endTime} " +
            "AND is_deleted = 0 ORDER BY create_time DESC")
    List<LiquidationRecord> selectByLiquidationReason(@Param("reason") Integer reason,
                                                      @Param("startTime") LocalDateTime startTime,
                                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计强平总金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM liquidation_record " +
            "WHERE create_time >= #{startTime} AND create_time <= #{endTime} AND is_deleted = 0")
    BigDecimal sumLiquidationAmount(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime);

    /**
     * 查询用户最近的强平记录
     */
    @Select("SELECT * FROM liquidation_record WHERE user_id = #{userId} " +
            "AND is_deleted = 0 ORDER BY create_time DESC LIMIT #{limit}")
    List<LiquidationRecord> selectRecentLiquidationsByUser(@Param("userId") Long userId, @Param("limit") Integer limit);
}