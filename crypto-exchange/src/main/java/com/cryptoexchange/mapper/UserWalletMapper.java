package com.cryptoexchange.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cryptoexchange.entity.UserWallet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户钱包Mapper接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface UserWalletMapper extends BaseMapper<UserWallet> {

    /**
     * 根据用户ID和币种查询钱包
     */
    @Select("SELECT * FROM user_wallets WHERE user_id = #{userId} AND currency = #{currency} AND wallet_type = #{walletType}")
    UserWallet selectByUserAndCurrency(@Param("userId") Long userId, @Param("currency") String currency, @Param("walletType") String walletType);

    /**
     * 根据用户ID查询所有钱包
     */
    @Select("SELECT * FROM user_wallets WHERE user_id = #{userId} ORDER BY currency ASC")
    List<UserWallet> selectByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和账户类型查询钱包
     */
    @Select("SELECT * FROM user_wallets WHERE user_id = #{userId} AND wallet_type = #{walletType} ORDER BY currency ASC")
    List<UserWallet> selectByUserAndWalletType(@Param("userId") Long userId, @Param("walletType") String walletType);

    /**
     * 更新可用余额
     */
    @Update("UPDATE user_wallets SET available_balance = #{availableBalance}, update_time = #{updateTime} WHERE id = #{walletId}")
    int updateAvailableBalance(@Param("walletId") Long walletId, @Param("availableBalance") BigDecimal availableBalance, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新冻结余额
     */
    @Update("UPDATE user_wallets SET frozen_balance = #{frozenBalance}, update_time = #{updateTime} WHERE id = #{walletId}")
    int updateFrozenBalance(@Param("walletId") Long walletId, @Param("frozenBalance") BigDecimal frozenBalance, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新余额（可用+冻结）
     */
    @Update("UPDATE user_wallets SET available_balance = #{availableBalance}, frozen_balance = #{frozenBalance}, update_time = #{updateTime} WHERE id = #{walletId}")
    int updateBalance(@Param("walletId") Long walletId, @Param("availableBalance") BigDecimal availableBalance, 
                     @Param("frozenBalance") BigDecimal frozenBalance, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 冻结余额（从可用转到冻结）
     */
    @Update("UPDATE user_wallets SET available_balance = available_balance - #{amount}, frozen_balance = frozen_balance + #{amount}, update_time = #{updateTime} " +
            "WHERE user_id = #{userId} AND currency = #{currency} AND account_type = #{accountType} AND available_balance >= #{amount}")
    int freezeBalance(@Param("userId") Long userId, @Param("currency") String currency, 
                     @Param("accountType") String accountType, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 解冻余额（从冻结转到可用）
     */
    @Update("UPDATE user_wallets SET available_balance = available_balance + #{amount}, frozen_balance = frozen_balance - #{amount}, update_time = #{updateTime} " +
            "WHERE user_id = #{userId} AND currency = #{currency} AND account_type = #{accountType} AND frozen_balance >= #{amount}")
    int unfreezeBalance(@Param("userId") Long userId, @Param("currency") String currency, 
                       @Param("accountType") String accountType, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 扣减可用余额
     */
    @Update("UPDATE user_wallets SET available_balance = available_balance - #{amount}, update_time = #{updateTime} " +
            "WHERE user_id = #{userId} AND currency = #{currency} AND account_type = #{accountType} AND available_balance >= #{amount}")
    int deductAvailableBalance(@Param("userId") Long userId, @Param("currency") String currency, 
                              @Param("accountType") String accountType, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 增加可用余额
     */
    @Update("UPDATE user_wallets SET available_balance = available_balance + #{amount}, update_time = #{updateTime} " +
            "WHERE user_id = #{userId} AND currency = #{currency} AND account_type = #{accountType}")
    int addAvailableBalance(@Param("userId") Long userId, @Param("currency") String currency, 
                           @Param("accountType") String accountType, @Param("amount") BigDecimal amount, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 查询用户总资产（按USDT计价）
     */
    @Select("SELECT SUM((available_balance + frozen_balance) * COALESCE(rate.price, 1)) as total_usdt_value " +
            "FROM user_wallets w LEFT JOIN exchange_rates rate ON w.currency = rate.from_currency AND rate.to_currency = 'USDT' " +
            "WHERE w.user_id = #{userId}")
    BigDecimal selectUserTotalUsdtValue(@Param("userId") Long userId);

    /**
     * 查询有余额的钱包
     */
    @Select("SELECT * FROM user_wallets WHERE user_id = #{userId} AND (available_balance > 0 OR frozen_balance > 0) ORDER BY currency ASC")
    List<UserWallet> selectNonZeroBalanceWallets(@Param("userId") Long userId);

    /**
     * 根据用户ID和钱包类型查询钱包
     */
    @Select("SELECT * FROM user_wallets WHERE user_id = #{userId} AND wallet_type = #{walletType} ORDER BY currency ASC")
    List<UserWallet> selectByUserIdAndType(@Param("userId") Long userId, @Param("walletType") Integer walletType);
}