package com.cryptoexchange.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger配置类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class SwaggerConfig {

    /**
     * OpenAPI配置
     * 
     * @return OpenAPI
     */
    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                    new Server().url("http://localhost:8080").description("开发环境"),
                    new Server().url("https://api.cryptoexchange.com").description("生产环境")
                ))
                .components(new Components()
                    .addSecuritySchemes("Bearer", securityScheme())
                )
                .addSecurityItem(new SecurityRequirement().addList("Bearer"));
    }

    /**
     * API信息
     * 
     * @return Info
     */
    private Info apiInfo() {
        return new Info()
                .title("加密货币交易所API")
                .description("加密货币交易所后端API文档")
                .version("1.0.0")
                .contact(new Contact()
                    .name("CryptoExchange Team")
                    .email("<EMAIL>")
                    .url("https://www.cryptoexchange.com")
                )
                .license(new License()
                    .name("MIT License")
                    .url("https://opensource.org/licenses/MIT")
                );
    }

    /**
     * 安全方案配置
     * 
     * @return SecurityScheme
     */
    private SecurityScheme securityScheme() {
        return new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .description("请输入JWT令牌，格式：Bearer {token}");
    }
}