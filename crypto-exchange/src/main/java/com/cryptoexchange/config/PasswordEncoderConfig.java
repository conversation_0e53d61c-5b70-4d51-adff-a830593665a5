package com.cryptoexchange.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码编码器配置类
 * 单独配置以避免循环依赖
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class PasswordEncoderConfig {

    /**
     * 密码编码器
     * 使用BCrypt算法进行密码加密
     * 
     * @return PasswordEncoder实例
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
