package com.cryptoexchange.enums;

/**
 * 备份类型枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BackupType {
    
    /**
     * 数据库备份
     */
    DATABASE("DATABASE", "数据库备份", "db"),
    
    /**
     * Redis备份
     */
    REDIS("REDIS", "Redis备份", "redis"),
    
    /**
     * 文件系统备份
     */
    FILESYSTEM("FILESYSTEM", "文件系统备份", "fs"),
    
    /**
     * 配置文件备份
     */
    CONFIG("CONFIG", "配置文件备份", "config"),
    
    /**
     * 日志文件备份
     */
    LOGS("LOGS", "日志文件备份", "logs"),
    
    /**
     * 完整系统备份
     */
    FULL_SYSTEM("FULL_SYSTEM", "完整系统备份", "full"),
    
    /**
     * 增量备份
     */
    INCREMENTAL("INCREMENTAL", "增量备份", "inc"),
    
    /**
     * 差异备份
     */
    DIFFERENTIAL("DIFFERENTIAL", "差异备份", "diff");
    
    private final String code;
    private final String description;
    private final String shortCode;
    
    BackupType(String code, String description, String shortCode) {
        this.code = code;
        this.description = description;
        this.shortCode = shortCode;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getShortCode() {
        return shortCode;
    }
    
    /**
     * 根据代码获取枚举
     * 
     * @param code 类型代码
     * @return 备份类型枚举
     */
    public static BackupType fromCode(String code) {
        for (BackupType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的备份类型代码: " + code);
    }
    
    /**
     * 根据短代码获取枚举
     * 
     * @param shortCode 短代码
     * @return 备份类型枚举
     */
    public static BackupType fromShortCode(String shortCode) {
        for (BackupType type : values()) {
            if (type.getShortCode().equals(shortCode)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的备份类型短代码: " + shortCode);
    }
    
    /**
     * 检查是否为数据备份类型
     * 
     * @return 是否为数据备份类型
     */
    public boolean isDataBackup() {
        return this == DATABASE || this == REDIS;
    }
    
    /**
     * 检查是否为文件备份类型
     * 
     * @return 是否为文件备份类型
     */
    public boolean isFileBackup() {
        return this == FILESYSTEM || this == CONFIG || this == LOGS;
    }
    
    /**
     * 检查是否为系统级备份类型
     * 
     * @return 是否为系统级备份类型
     */
    public boolean isSystemBackup() {
        return this == FULL_SYSTEM || this == INCREMENTAL || this == DIFFERENTIAL;
    }
    
    /**
     * 获取默认文件扩展名
     * 
     * @return 文件扩展名
     */
    public String getDefaultFileExtension() {
        switch (this) {
            case DATABASE:
                return ".sql";
            case REDIS:
                return ".rdb";
            case FILESYSTEM:
            case FULL_SYSTEM:
            case INCREMENTAL:
            case DIFFERENTIAL:
                return ".tar.gz";
            case CONFIG:
            case LOGS:
                return ".zip";
            default:
                return ".bak";
        }
    }
    
    /**
     * 获取备份文件前缀
     * 
     * @return 文件前缀
     */
    public String getFilePrefix() {
        return getShortCode() + "_backup_";
    }
}