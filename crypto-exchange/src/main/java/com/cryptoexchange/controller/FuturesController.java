package com.cryptoexchange.controller;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.service.FuturesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.util.List;

/**
 * 合约交易控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "合约交易", description = "合约和杠杆交易相关接口")
@RestController
@RequestMapping("/api/futures")
@RequiredArgsConstructor
@Validated
public class FuturesController {

    private final FuturesService futuresService;

    @Operation(summary = "获取合约产品", description = "获取所有或指定的合约产品信息")
    @GetMapping("/contracts")
    public Result<List<Object>> getContracts(
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String contractType) {
        Result<List<Object>> result = futuresService.getContracts();
        return result;
    }

    @Operation(summary = "获取杠杆账户信息", description = "获取用户的杠杆账户信息")
    @GetMapping("/account")
    public Result<Object> getMarginAccount(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = futuresService.getMarginAccount(userId);
        return result;
    }

    @Operation(summary = "创建合约订单", description = "创建合约交易订单")
    @PostMapping("/orders")
    public Result<Object> createFuturesOrder(
            @Valid @RequestBody CreateFuturesOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        // 将CreateFuturesOrderRequest转换为FuturesOrderRequest
        FuturesOrderRequest orderRequest = FuturesOrderRequest.builder()
                .userId(userId)
                .symbol(request.getSymbol())
                .side(request.getSide())
                .positionSide(request.getPositionSide())
                .type(request.getType())
                .quantity(request.getQuantity())
                .price(request.getPrice())
                .stopPrice(request.getStopPrice())
                .timeInForce(request.getTimeInForce())
                .leverage(request.getLeverage())
                .marginType(request.getMarginType())
                .reduceOnly(request.getReduceOnly())
                .postOnly(request.getPostOnly())
                .clientOrderId(request.getClientOrderId())
                .triggerPrice(request.getTriggerPrice())
                .triggerType(request.getTriggerType())
                .workingType(request.getWorkingType())
                .activationPrice(request.getActivationPrice())
                .callbackRate(request.getCallbackRate())
                .orderSource(request.getOrderSource())
                .remark(request.getRemark())
                .build();
        Result<Object> result = futuresService.createOrder(orderRequest);
        return result;
    }

    @Operation(summary = "取消合约订单", description = "取消指定的合约订单")
    @DeleteMapping("/orders/{orderId}")
    public Result<Void> cancelFuturesOrder(
            @PathVariable @NotNull(message = "订单ID不能为空") Long orderId,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = futuresService.cancelOrder(userId, String.valueOf(orderId));
        return result;
    }

    @Operation(summary = "批量取消合约订单", description = "批量取消合约订单")
    @DeleteMapping("/orders/batch")
    public Result<Void> batchCancelFuturesOrders(
            @Valid @RequestBody BatchCancelFuturesOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = futuresService.cancelAllOrders(userId, request.getSymbol());
        return result;
    }

    @Operation(summary = "查询合约订单", description = "查询用户的合约订单")
    @GetMapping("/orders")
    public Result<PageResult<Object>> getFuturesOrders(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String side,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        Result<PageResult<Object>> result = futuresService.getUserOrders(userId, symbol, 
                status != null ? Integer.valueOf(status) : null, pageNum, pageSize);
        return result;
    }

    @Operation(summary = "获取用户持仓", description = "获取用户的合约持仓信息")
    @GetMapping("/positions")
    public Result<List<FuturesPositionResponse>> getFuturesPositions(
            @RequestParam(required = false) String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<List<FuturesPositionResponse>> result = futuresService.getUserPositions(userId, symbol);
        return result;
    }

    @Operation(summary = "调整保证金", description = "调整合约持仓的保证金")
    @PostMapping("/positions/margin")
    public Result<Void> adjustMargin(
            @Valid @RequestBody AdjustMarginRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = futuresService.adjustMargin(userId, request.getSymbol(), request.getAmount(), request.getType());
        return result;
    }

    @Operation(summary = "调整杠杆倍数", description = "调整合约的杠杆倍数")
    @PostMapping("/positions/leverage")
    public Result<Void> adjustLeverage(
            @Valid @RequestBody AdjustLeverageRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = futuresService.adjustLeverage(userId, request.getSymbol(), request.getLeverage());
        return result;
    }

    @Operation(summary = "获取合约交易记录", description = "分页获取用户的合约交易记录")
    @GetMapping("/trades")
    public Result<PageResult<FuturesTradeResponse>> getFuturesTrades(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String side,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        Result<PageResult<FuturesTradeResponse>> result = futuresService.getUserTrades(userId, symbol, pageNum, pageSize);
        return result;
    }

    @Operation(summary = "获取强平记录", description = "分页获取用户的强平记录")
    @GetMapping("/liquidations")
    public Result<PageResult<LiquidationResponse>> getLiquidationRecords(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        Result<PageResult<LiquidationResponse>> result = futuresService.getLiquidationRecords(userId, symbol, pageNum, pageSize);
        return result;
    }

    @Operation(summary = "获取资金费率", description = "获取合约的资金费率信息")
    @GetMapping("/funding-rate")
    public Result<FundingRateResponse> getFundingRate(
            @RequestParam String symbol) {
        
        Result<FundingRateResponse> result = futuresService.getFundingRate(symbol);
        return result;
    }

    @Operation(summary = "获取用户资金费用", description = "分页获取用户的资金费用记录")
    @GetMapping("/funding-fees")
    public Result<PageResult<UserFundingFeeResponse>> getUserFundingFees(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        Result<PageResult<UserFundingFeeResponse>> result = futuresService.getUserFundingFees(userId, symbol, pageNum, pageSize);
        return result;
    }

    @Operation(summary = "计算保证金", description = "计算开仓所需的保证金")
    @PostMapping("/calculate-margin")
    public Result<BigDecimal> calculateMargin(
            @Valid @RequestBody MarginCalculationRequest request) {
        Result<BigDecimal> result = futuresService.calculateRequiredMargin(request.getSymbol(), 
                request.getQuantity(), request.getEntryPrice(), request.getLeverage());
        return result;
    }

    @Operation(summary = "计算强平价格", description = "计算持仓的强平价格")
    @PostMapping("/calculate-liquidation-price")
    public Result<Object> calculateLiquidationPrice(
            @Valid @RequestBody LiquidationPriceRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = futuresService.calculateLiquidationPrice(userId, request.getSymbol(), request.getQuantity(), request.getEntryPrice());
        return result;
    }

    @Operation(summary = "获取风险等级", description = "获取用户的风险等级信息")
    @GetMapping("/risk-level")
    public Result<Object> getRiskLevel(
            @RequestParam @NotBlank(message = "合约代码不能为空") String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = futuresService.getRiskLevel(userId, symbol);
        return result;
    }

    @Operation(summary = "获取合约市场深度", description = "获取指定合约的市场深度信息")
    @GetMapping("/depth")
    public Result<Object> getMarketDepth(
            @RequestParam @NotBlank(message = "合约代码不能为空") String symbol,
            @RequestParam(defaultValue = "20") @Positive(message = "深度级别必须大于0") Integer limit) {
        Result<Object> result = futuresService.getMarketDepth(symbol, limit);
        return result;
    }

    @Operation(summary = "获取合约最新成交", description = "获取指定合约的最新成交记录")
    @GetMapping("/recent-trades")
    public Result<List<Object>> getRecentTrades(
            @RequestParam @NotBlank(message = "合约代码不能为空") String symbol,
            @RequestParam(defaultValue = "50") @Positive(message = "记录数量必须大于0") Integer limit) {
        Result<List<Object>> result = futuresService.getRecentTrades(symbol, limit);
        return result;
    }

    @Operation(summary = "获取合约24小时行情", description = "获取指定合约的24小时行情统计")
    @GetMapping("/ticker/24hr")
    public Result<Object> get24hrTicker(
            @RequestParam String symbol) {
        Result<Object> result = futuresService.get24hrTicker(symbol);
        return result;
    }

    @Operation(summary = "获取合约K线数据", description = "获取指定合约的K线数据")
    @GetMapping("/klines")
    public Result<List<Object>> getKlines(
            @RequestParam @NotBlank(message = "合约代码不能为空") String symbol,
            @RequestParam @NotBlank(message = "时间间隔不能为空") String interval,
            @RequestParam(defaultValue = "500") @Positive(message = "记录数量必须大于0") Integer limit) {
        
        Result<List<Object>> result = futuresService.getKlines(symbol, interval, limit);
        return result;
    }

    @Operation(summary = "获取标记价格", description = "获取合约的标记价格")
    @GetMapping("/mark-price")
    public Result<BigDecimal> getMarkPrice(
            @RequestParam String symbol) {
        return futuresService.getMarkPrice(symbol);
    }

    @Operation(summary = "获取指数价格", description = "获取合约的指数价格")
    @GetMapping("/index-price")
    public Result<BigDecimal> getIndexPrice(
            @RequestParam String symbol) {
        return futuresService.getIndexPrice(symbol);
    }

    @Operation(summary = "资产划转", description = "在现货和合约账户间进行资产划转")
    @PostMapping("/transfer")
    public Result<Void> transfer(
            @Valid @RequestBody FuturesTransferRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        // TODO: 需要将FuturesTransferRequest转换为合适的参数
        Result<Void> result = futuresService.transfer(userId, request.getAsset(), request.getAmount(), request.getType());
        return result;
    }

    @Operation(summary = "获取账户余额", description = "获取用户的合约账户余额")
    @GetMapping("/balance")
    public Result<List<Object>> getBalance(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<List<Object>> result = futuresService.getBalance(userId);
        return result;
    }

    @Operation(summary = "获取持仓风险", description = "获取用户持仓的风险信息")
    @GetMapping("/position-risk")
    public Result<List<Object>> getPositionRisk(
            @RequestParam(required = false) String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<List<Object>> result = futuresService.getPositionRisk(userId, symbol);
        return result;
    }

    @Operation(summary = "设置止盈止损", description = "为持仓设置止盈止损")
    @PostMapping("/positions/stop-order")
    public Result<Void> setStopOrder(
            @Valid @RequestBody SetStopOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        // TODO: 需要将SetStopOrderRequest转换为合适的参数
        Result<Void> result = futuresService.setStopOrder(userId, request.getSymbol(), request.getStopPrice(), request.getTakeProfitPrice());
        return result;
    }

    @Operation(summary = "获取交易统计", description = "获取用户的合约交易统计信息")
    @GetMapping("/trading-statistics")
    public Result<Object> getTradingStatistics(
            @RequestParam(required = false) String period,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = futuresService.getTradingStatistics(userId, period);
        return result;
    }

    @Operation(summary = "获取保险基金余额", description = "获取保险基金的余额信息")
    @GetMapping("/insurance-fund")
    public Result<Object> getInsuranceFund(
            @RequestParam(required = false) String symbol) {
        Result<Object> result = futuresService.getInsuranceFund(symbol);
        return result;
    }

    @Operation(summary = "获取ADL队列排名", description = "获取用户在ADL队列中的排名")
    @GetMapping("/adl-quantile")
    public Result<Object> getAdlQuantile(
            @RequestParam(required = false) String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = futuresService.getAdlQuantile(userId, symbol);
        return result;
    }

    @Operation(summary = "获取合约规格", description = "获取合约的详细规格信息")
    @GetMapping("/contract-specs")
    public Result<List<Object>> getContractSpecs(
            @RequestParam(required = false) String symbol) {
        Result<List<Object>> result = futuresService.getContractSpecs(symbol);
        return result;
    }

    @Operation(summary = "获取杠杆配置", description = "获取合约的杠杆配置信息")
    @GetMapping("/leverage-brackets")
    public Result<List<Object>> getLeverageBrackets(
            @RequestParam(required = false) String symbol) {
        Result<List<Object>> result = futuresService.getLeverageBrackets(symbol);
        return result;
    }

    @Operation(summary = "测试合约订单", description = "测试合约订单创建，不会实际提交")
    @PostMapping("/orders/test")
    public Result<Object> testOrder(
            @Valid @RequestBody CreateFuturesOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        // TODO: 需要将CreateFuturesOrderRequest转换为FuturesOrderRequest
        Result<Object> result = futuresService.testOrder(userId, null); // 需要转换参数
        return result;
    }
}