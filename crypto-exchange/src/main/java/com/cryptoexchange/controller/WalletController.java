package com.cryptoexchange.controller;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.service.WalletService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 钱包控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "钱包管理", description = "钱包管理相关接口")
@RestController
@RequestMapping("/api/wallet")
@RequiredArgsConstructor
@Validated
public class WalletController {

    private final WalletService walletService;

    @Operation(summary = "获取用户钱包列表", description = "获取用户所有币种的钱包信息")
    @GetMapping("/list")
    public Result<List<WalletResponse>> getUserWallets(
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) Integer walletType,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        List<WalletResponse> response = walletService.getUserWallets(userId, currency, walletType);
        return Result.success(response);
    }

    @Operation(summary = "获取指定币种钱包", description = "获取用户指定币种的钱包信息")
    @GetMapping("/{currency}")
    public Result<WalletResponse> getUserWallet(
            @PathVariable @NotBlank(message = "币种不能为空") String currency,
            @RequestParam(defaultValue = "1") Integer walletType,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return walletService.getUserWallet(userId, currency, walletType);
    }

    @Operation(summary = "创建用户钱包", description = "为用户创建指定币种的钱包")
    @PostMapping("/create")
    public Result<WalletResponse> createUserWallet(
            @Valid @RequestBody CreateWalletRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        // Convert walletType string to integer
        Integer walletType = getWalletTypeFromString(request.getWalletType());
        Result<WalletResponse> result = walletService.createUserWallet(userId, "BTC", walletType); // Default currency for now
        if (result.isSuccess()) {
            return Result.success("钱包创建成功", result.getData());
        }
        return result;
    }

    @Operation(summary = "用户充值", description = "用户充值到指定币种钱包")
    @PostMapping("/deposit")
    public Result<DepositResponse> deposit(
            @Valid @RequestBody DepositRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<TransactionResponse> result = walletService.deposit(request);
        if (result.isSuccess()) {
            // Convert TransactionResponse to DepositResponse
            DepositResponse response = convertToDepositResponse(result.getData(), userId);
            return Result.success("充值申请提交成功", response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "用户提现", description = "用户从指定币种钱包提现")
    @PostMapping("/withdraw")
    public Result<WithdrawResponse> withdraw(
            @Valid @RequestBody WithdrawRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<TransactionResponse> result = walletService.withdraw(request);
        if (result.isSuccess()) {
            // Convert TransactionResponse to WithdrawResponse
            WithdrawResponse response = convertToWithdrawResponse(result.getData(), userId);
            return Result.success("提现申请提交成功", response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "资产转账", description = "用户内部资产转账")
    @PostMapping("/transfer")
    public Result<TransferResponse> transfer(
            @Valid @RequestBody TransferRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = walletService.transfer(request);
        if (result.isSuccess()) {
            // Create a simple TransferResponse
            TransferResponse response = createTransferResponse(request, userId);
            return Result.success("转账成功", response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "获取充值记录", description = "分页获取用户的充值记录")
    @GetMapping("/deposits")
    public Result<PageResult<DepositRecordResponse>> getDepositRecords(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());

        DepositQueryRequest request = new DepositQueryRequest();
        request.setUserId(userId);
        request.setCurrency(currency);
        if (status != null && !status.isEmpty()) {
            try {
                request.setStatus(Integer.valueOf(status));
            } catch (NumberFormatException e) {
                // 忽略状态转换错误
            }
        }
        request.setStartTime(parseDateTime(startTime));
        request.setEndTime(parseDateTime(endTime));
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);

        Result<PageResult<DepositRecordResponse>> result = walletService.getDepositRecords(request);
        return result;
    }

    @Operation(summary = "获取提现记录", description = "分页获取用户的提现记录")
    @GetMapping("/withdrawals")
    public Result<PageResult<WithdrawRecordResponse>> getWithdrawRecords(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());

        WithdrawQueryRequest request = new WithdrawQueryRequest();
        request.setUserId(userId);
        request.setCurrency(currency);
        if (status != null && !status.isEmpty()) {
            try {
                request.setStatus(Integer.valueOf(status));
            } catch (NumberFormatException e) {
                // 忽略状态转换错误
            }
        }
        request.setStartTime(parseDateTime(startTime));
        request.setEndTime(parseDateTime(endTime));
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);

        Result<PageResult<WithdrawRecordResponse>> result = walletService.getWithdrawRecords(request);
        return result;
    }

    @Operation(summary = "获取转账记录", description = "分页获取用户的转账记录")
    @GetMapping("/transfers")
    public Result<PageResult<TransferRecordResponse>> getTransferRecords(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());

        TransferQueryRequest request = new TransferQueryRequest();
        request.setUserId(userId);
        request.setCurrency(currency);
        if (type != null && !type.isEmpty()) {
            try {
                request.setTransferType(Integer.valueOf(type));
            } catch (NumberFormatException e) {
                // 忽略类型转换错误
            }
        }
        request.setStartTime(parseDateTime(startTime));
        request.setEndTime(parseDateTime(endTime));
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);

        Result<PageResult<TransferRecordResponse>> result = walletService.getTransferRecords(request);
        return result;
    }

    @Operation(summary = "获取用户总资产", description = "获取用户的总资产信息")
    @GetMapping("/total-assets")
    public Result<TotalAssetsResponse> getTotalAssets(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<TotalAssetsResponse> result = walletService.getTotalAssets(userId);
        return result;
    }

    @Operation(summary = "获取资产分布", description = "获取用户的资产分布信息")
    @GetMapping("/asset-distribution")
    public Result<List<AssetDistributionResponse>> getAssetDistribution(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<List<AssetDistributionResponse>> result = walletService.getAssetDistribution(userId);
        return result;
    }

    @Operation(summary = "生成充值地址", description = "为用户生成指定币种的充值地址")
    @PostMapping("/deposit-address")
    public Result<DepositAddressResponse> generateDepositAddress(
            @Valid @RequestBody GenerateAddressRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<String> result = walletService.generateDepositAddress(userId, request.getCurrency());
        if (result.isSuccess()) {
            DepositAddressResponse response = createDepositAddressResponse(userId, request.getCurrency(), result.getData(), request.getNetwork());
            return Result.success("充值地址生成成功", response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "获取充值地址", description = "获取用户指定币种的充值地址")
    @GetMapping("/deposit-address")
    public Result<List<DepositAddressResponse>> getDepositAddresses(
            @RequestParam @NotBlank(message = "币种不能为空") String currency,
            @RequestParam(required = false) String network,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<List<DepositAddressResponse>> result = walletService.getDepositAddresses(userId, currency, network);
        return result;
    }

    @Operation(summary = "验证提现地址", description = "验证提现地址的有效性")
    @PostMapping("/validate-address")
    public Result<AddressValidationResponse> validateWithdrawAddress(
            @Valid @RequestBody ValidateAddressRequest request) {
        Result<Boolean> result = walletService.validateWithdrawAddress(request.getCurrency(), request.getAddress());
        if (result.isSuccess()) {
            AddressValidationResponse response = createAddressValidationResponse(request.getAddress(), request.getCurrency(), result.getData());
            return Result.success(response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "获取币种汇率", description = "获取指定币种的汇率信息")
    @GetMapping("/exchange-rates")
    public Result<List<ExchangeRateResponse>> getExchangeRates(
            @RequestParam(required = false) String fromCurrency,
            @RequestParam(required = false) String toCurrency) {
        Result<List<ExchangeRateResponse>> result = walletService.getExchangeRates(fromCurrency, toCurrency);
        return result;
    }

    @Operation(summary = "创建资产快照", description = "创建用户的资产快照")
    @PostMapping("/asset-snapshot")
    public Result<AssetSnapshotResponse> createAssetSnapshot(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Object> result = walletService.createAssetSnapshot(userId);
        if (result.isSuccess()) {
            AssetSnapshotResponse response = createAssetSnapshotResponse(userId, result.getData());
            return Result.success(response);
        }
        return Result.error(result.getMessage());
    }

    @Operation(summary = "获取资产快照历史", description = "分页获取用户的资产快照历史")
    @GetMapping("/asset-snapshots")
    public Result<PageResult<AssetSnapshotResponse>> getAssetSnapshots(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());

        AssetSnapshotQueryRequest request = new AssetSnapshotQueryRequest();
        request.setUserId(userId);
        // Convert string dates to LocalDateTime if needed
        request.setStartTime(parseDateTime(startTime));
        request.setEndTime(parseDateTime(endTime));
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);

        Result<PageResult<AssetSnapshotResponse>> result = walletService.getAssetSnapshots(request);
        return result;
    }

    @Operation(summary = "获取钱包统计信息", description = "获取用户钱包的统计信息")
    @GetMapping("/statistics")
    public Result<WalletStatisticsResponse> getWalletStatistics(
            @RequestParam(required = false) String period,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<WalletStatisticsResponse> result = walletService.getWalletStatistics(userId, period);
        return result;
    }

    @Operation(summary = "冻结用户余额", description = "冻结用户指定币种的余额")
    @PostMapping("/freeze")
    public Result<Void> freezeBalance(
            @Valid @RequestBody FreezeBalanceRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        String currency = request.getSymbol(); // 使用 symbol 字段
        Result<Void> result = walletService.freezeBalance(userId, currency, request.getAmount(), request.getReason());
        if (result.isSuccess()) {
            return Result.success("余额冻结成功");
        }
        return result;
    }

    @Operation(summary = "解冻用户余额", description = "解冻用户指定币种的余额")
    @PostMapping("/unfreeze")
    public Result<Void> unfreezeBalance(
            @Valid @RequestBody UnfreezeBalanceRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<Void> result = walletService.unfreezeBalance(userId, request.getCurrency(), request.getAmount(), request.getReason());
        if (result.isSuccess()) {
            return Result.success("余额解冻成功");
        }
        return result;
    }

    @Operation(summary = "获取余额变动记录", description = "分页获取用户的余额变动记录")
    @GetMapping("/balance-changes")
    public Result<PageResult<BalanceChangeResponse>> getBalanceChanges(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());

        BalanceChangeQueryRequest request = new BalanceChangeQueryRequest();
        request.setUserId(userId);
        request.setCurrency(currency);
        if (type != null && !type.isEmpty()) {
            try {
                request.setType(Integer.valueOf(type));
            } catch (NumberFormatException e) {
                // 忽略类型转换错误
            }
        }
        request.setStartTime(parseDateTime(startTime));
        request.setEndTime(parseDateTime(endTime));
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);

        Result<PageResult<BalanceChangeResponse>> result = walletService.getBalanceChanges(request);
        return result;
    }

    @Operation(summary = "获取支持的币种列表", description = "获取平台支持的所有币种信息")
    @GetMapping("/currencies")
    public Result<List<CurrencyResponse>> getSupportedCurrencies() {
        return walletService.getSupportedCurrenciesWithDetails();
    }

    @Operation(summary = "获取网络配置", description = "获取指定币种的网络配置信息")
    @GetMapping("/networks")
    public Result<List<NetworkConfigResponse>> getNetworkConfigs(
            @RequestParam @NotBlank(message = "币种不能为空") String currency) {
        Result<List<NetworkConfigResponse>> result = walletService.getNetworkConfigs(currency);
        return result;
    }

    @Operation(summary = "获取提现限额", description = "获取用户的提现限额信息")
    @GetMapping("/withdraw-limits")
    public Result<WithdrawLimitResponse> getWithdrawLimits(
            @RequestParam @NotBlank(message = "币种不能为空") String currency,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<WithdrawLimitResponse> result = walletService.getWithdrawLimits(userId, currency);
        return result;
    }

    @Operation(summary = "获取手续费信息", description = "获取充值提现的手续费信息")
    @GetMapping("/fees")
    public Result<List<FeeInfoResponse>> getFeeInfo(
            @RequestParam(required = false) String currency,
            @RequestParam(required = false) String type) {
        Result<List<FeeInfoResponse>> result = walletService.getFeeInfo(currency, type);
        return result;
    }

    // Helper methods for conversion and utility functions

    private Integer getWalletTypeFromString(String walletType) {
        switch (walletType) {
            case "SPOT": return 1;
            case "FUTURES": return 2;
            case "MARGIN": return 3;
            case "SAVINGS": return 4;
            default: return 1; // Default to SPOT
        }
    }

    private DepositResponse convertToDepositResponse(TransactionResponse transaction, Long userId) {
        return DepositResponse.builder()
                .id(transaction.getTransactionId())
                .userId(userId)
                .currency(transaction.getCurrency())
                .amount(transaction.getAmount())
                .status("PENDING")
                .build();
    }

    private WithdrawResponse convertToWithdrawResponse(TransactionResponse transaction, Long userId) {
        return WithdrawResponse.builder()
                .id(transaction.getTransactionId())
                .userId(userId)
                .currency(transaction.getCurrency())
                .amount(transaction.getAmount())
                .status("PENDING")
                .build();
    }

    private TransferResponse createTransferResponse(TransferRequest request, Long userId) {
        return TransferResponse.builder()
                .id(System.currentTimeMillis()) // Simple ID generation
                .fromUserId(userId)
                .currency(request.getCurrency())
                .amount(request.getAmount())
                .fromWalletType(request.getFromAccountType())
                .toWalletType(request.getToAccountType())
                .status("SUCCESS")
                .build();
    }

    private DepositAddressResponse createDepositAddressResponse(Long userId, String currency, String address, String network) {
        return DepositAddressResponse.builder()
                .id(System.currentTimeMillis())
                .userId(userId)
                .currency(currency)
                .address(address)
                .network(network)
                .status("ACTIVE")
                .isDefault(true)
                .build();
    }

    private AddressValidationResponse createAddressValidationResponse(String address, String currency, Boolean isValid) {
        AddressValidationResponse.ValidationDetails details = AddressValidationResponse.ValidationDetails.builder()
                .formatValid(isValid)
                .checksumValid(isValid)
                .networkCompatible(isValid)
                .build();

        return AddressValidationResponse.builder()
                .address(address)
                .currency(currency)
                .isValid(isValid)
                .validationDetails(details)
                .riskLevel(isValid ? "LOW" : "HIGH")
                .build();
    }

    private AssetSnapshotResponse createAssetSnapshotResponse(Long userId, Object snapshotData) {
        return AssetSnapshotResponse.builder()
                .snapshotId(System.currentTimeMillis())
                .userId(userId)
                .snapshotType("MANUAL")
                .snapshotTime(java.time.LocalDateTime.now())
                .createTime(java.time.LocalDateTime.now())
                .build();
    }

    private java.time.LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return java.time.LocalDateTime.parse(dateTimeStr.replace(" ", "T"));
        } catch (Exception e) {
            return null;
        }
    }
}