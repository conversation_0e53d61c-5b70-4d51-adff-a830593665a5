package com.cryptoexchange.controller;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "用户管理", description = "用户信息管理、安全设置、KYC认证等相关接口")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
@Validated
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/profile")
    public Result<UserProfileResponse> getUserProfile(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUserProfile(userId);
    }

    @Operation(summary = "更新用户信息", description = "更新用户的基本信息")
    @PutMapping("/profile")
    public Result<Void> updateUserProfile(
            @Valid @RequestBody UpdateUserProfileRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.updateUserProfile(userId, request);
        return Result.success("用户信息更新成功");
    }

    @Operation(summary = "修改登录密码", description = "修改用户的登录密码")
    @PutMapping("/password")
    public Result<Void> changePassword(
            @Valid @RequestBody ChangePasswordRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.changePassword(userId, request);
        return Result.success("密码修改成功");
    }

    @Operation(summary = "设置交易密码", description = "设置或修改用户的交易密码")
    @PutMapping("/trading-password")
    public Result<Void> setTradingPassword(
            @Valid @RequestBody SetTradingPasswordRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.setTradingPassword(userId, request);
        return Result.success("交易密码设置成功");
    }

    @Operation(summary = "验证交易密码", description = "验证用户的交易密码")
    @PostMapping("/verify-trading-password")
    public Result<Void> verifyTradingPassword(
            @Valid @RequestBody VerifyTradingPasswordRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.verifyTradingPassword(userId, request.getTradingPassword());
        return Result.success("交易密码验证成功");
    }

    @Operation(summary = "绑定邮箱", description = "绑定或更换用户邮箱")
    @PostMapping("/bind-email")
    public Result<Void> bindEmail(
            @Valid @RequestBody BindEmailRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.bindEmail(userId, request);
        return Result.success("邮箱绑定成功");
    }

    @Operation(summary = "绑定手机号", description = "绑定或更换用户手机号")
    @PostMapping("/bind-phone")
    public Result<Void> bindPhone(
            @Valid @RequestBody BindPhoneRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.bindPhone(userId, request);
        return Result.success("手机号绑定成功");
    }

    @Operation(summary = "设置双因子认证", description = "启用或禁用双因子认证")
    @PostMapping("/2fa")
    public Result<TwoFactorAuthResponse> setupTwoFactorAuth(
            @Valid @RequestBody SetupTwoFactorAuthRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        TwoFactorAuthResponse response = userService.setupTwoFactorAuth(userId, request);
        return Result.success("双因子认证设置成功", response);
    }

    @Operation(summary = "验证双因子认证", description = "验证双因子认证码")
    @PostMapping("/verify-2fa")
    public Result<Void> verifyTwoFactorAuth(
            @Valid @RequestBody VerifyTwoFactorAuthRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.verifyTwoFactorAuth(userId, request.getCode());
        return Result.success("双因子认证验证成功");
    }

    @Operation(summary = "获取安全设置", description = "获取用户的安全设置信息")
    @GetMapping("/security-settings")
    public Result<SecuritySettingsResponse> getSecuritySettings(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getSecuritySettings(userId);
    }

    @Operation(summary = "更新安全设置", description = "更新用户的安全设置")
    @PutMapping("/security-settings")
    public Result<Void> updateSecuritySettings(
            @Valid @RequestBody UpdateSecuritySettingsRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.updateSecuritySettings(userId, request);
        return Result.success("安全设置更新成功");
    }

    @Operation(summary = "申请KYC认证", description = "提交KYC身份认证申请")
    @PostMapping("/kyc/apply")
    public Result<Void> applyKyc(
            @Valid @RequestBody KycApplicationRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.applyKyc(userId, request);
    }

    @Operation(summary = "获取KYC状态", description = "获取用户的KYC认证状态")
    @GetMapping("/kyc/status")
    public Result<KycStatusResponse> getKycStatus(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getKycStatus(userId);
    }

    @Operation(summary = "上传头像", description = "上传用户头像")
    @PostMapping("/avatar")
    public Result<AvatarUploadResponse> uploadAvatar(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        AvatarUploadResponse response = userService.uploadAvatar(userId, file);
        return Result.success("头像上传成功", response);
    }

    @Operation(summary = "获取登录历史", description = "分页获取用户的登录历史记录")
    @GetMapping("/login-history")
    public Result<PageResult<LoginHistoryResponse>> getLoginHistory(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        Result<LoginHistoryResponse> result = userService.getLoginHistory(userId, pageNum, pageSize);
        // Convert single response to PageResult
        PageResult<LoginHistoryResponse> pageResult = PageResult.of((long)pageNum, (long)pageSize, 1L,
            result.isSuccess() ? List.of(result.getData()) : List.of());
        return Result.success(pageResult);
    }

    @Operation(summary = "获取操作日志", description = "分页获取用户的操作日志")
    @GetMapping("/operation-logs")
    public Result<PageResult<OperationLogResponse>> getOperationLogs(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String operation,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        OperationLogQueryRequest request = new OperationLogQueryRequest();
        request.setUserId(userId);
        request.setOperationType(operation);
        if (startTime != null) {
            request.setStartTime(LocalDateTime.parse(startTime));
        }
        if (endTime != null) {
            request.setEndTime(LocalDateTime.parse(endTime));
        }
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        
        PageResult<OperationLogResponse> response = userService.getOperationLogs(request);
        return Result.success(response);
    }

    @Operation(summary = "获取用户等级", description = "获取用户的等级信息")
    @GetMapping("/level")
    public Result<UserLevelResponse> getUserLevel(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUserLevel(userId);
    }

    @Operation(summary = "获取推荐信息", description = "获取用户的推荐信息")
    @GetMapping("/referral")
    public Result<ReferralInfoResponse> getReferralInfo(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getReferralInfo(userId);
    }

    @Operation(summary = "获取推荐用户列表", description = "分页获取用户推荐的用户列表")
    @GetMapping("/referral/users")
    public Result<ReferralUserResponse> getReferralUsers(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getReferralUsers(userId, pageNum, pageSize);
    }

    @Operation(summary = "获取用户权限", description = "获取用户的权限信息")
    @GetMapping("/permissions")
    public Result<UserPermissionsResponse> getUserPermissions(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUserPermissions(userId);
    }

    @Operation(summary = "实名认证", description = "提交实名认证信息")
    @PostMapping("/real-name-auth")
    public Result<Void> realNameAuth(
            @Valid @RequestBody RealNameAuthRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.realNameAuth(userId, request);
    }

    @Operation(summary = "获取用户资产概览", description = "获取用户的资产概览信息")
    @GetMapping("/asset-overview")
    public Result<UserAssetOverviewResponse> getUserAssetOverview(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUserAssetOverview(userId);
    }

    @Operation(summary = "获取用户交易概览", description = "获取用户的交易概览信息")
    @GetMapping("/trading-overview")
    public Result<UserTradingOverviewResponse> getUserTradingOverview(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUserTradingOverview(userId);
    }

    @Operation(summary = "用户签到", description = "用户每日签到")
    @PostMapping("/check-in")
    public Result<CheckInResponse> checkIn(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.checkIn(userId);
    }

    @Operation(summary = "获取签到记录", description = "分页获取用户的签到记录")
    @GetMapping("/check-in/records")
    public Result<CheckInRecordResponse> getCheckInRecords(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getCheckInRecords(userId, pageNum, pageSize);
    }

    @Operation(summary = "获取用户通知", description = "分页获取用户的通知消息")
    @GetMapping("/notifications")
    public Result<PageResult<UserNotificationResponse>> getUserNotifications(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Boolean isRead,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        UserNotificationQueryRequest request = new UserNotificationQueryRequest();
        request.setUserId(userId);
        request.setType(type);
        request.setIsRead(isRead);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        
        PageResult<UserNotificationResponse> response = userService.getUserNotifications(request);
        return Result.success(response);
    }

    @Operation(summary = "标记通知已读", description = "标记指定通知为已读")
    @PutMapping("/notifications/{notificationId}/read")
    public Result<Void> markNotificationAsRead(
            @PathVariable @NotNull(message = "通知ID不能为空") Long notificationId,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.markNotificationAsRead(userId, notificationId);
        return Result.success("通知已标记为已读");
    }

    @Operation(summary = "批量标记通知已读", description = "批量标记通知为已读")
    @PutMapping("/notifications/batch-read")
    public Result<Void> batchMarkNotificationsAsRead(
            @Valid @RequestBody BatchMarkNotificationsRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        userService.batchMarkNotificationsAsRead(userId, request.getNotificationIds());
        return Result.success("通知已批量标记为已读");
    }

    @Operation(summary = "获取未读通知数量", description = "获取用户的未读通知数量")
    @GetMapping("/notifications/unread-count")
    public Result<UnreadNotificationCountResponse> getUnreadNotificationCount(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        return userService.getUnreadNotificationCount(userId);
    }

    // 管理员接口
    @Operation(summary = "获取用户列表", description = "管理员分页获取用户列表")
    @GetMapping("/admin/users")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<PageResult<AdminUserResponse>> getUsers(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String kycStatus,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {
        
        AdminUserQueryRequest request = new AdminUserQueryRequest();
        request.setUsername(username);
        request.setEmail(email);
        request.setPhone(phone);
        if (status != null) {
            request.setStatus(AdminUserQueryRequest.UserStatus.valueOf(status.toUpperCase()));
        }
        if (kycStatus != null) {
            request.setKycStatus(AdminUserQueryRequest.KycStatus.valueOf(kycStatus.toUpperCase()));
        }
        if (startTime != null) {
            request.setRegistrationTimeStart(LocalDateTime.parse(startTime));
        }
        if (endTime != null) {
            request.setRegistrationTimeEnd(LocalDateTime.parse(endTime));
        }
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        
        PageResult<AdminUserResponse> response = userService.getUsers(request);
        return Result.success(response);
    }

    @Operation(summary = "冻结用户", description = "管理员冻结用户账户")
    @PostMapping("/admin/users/{userId}/freeze")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> freezeUser(
            @PathVariable @NotNull(message = "用户ID不能为空") Long userId,
            @Valid @RequestBody FreezeUserRequest request) {
        userService.freezeUser(userId, request.getReason());
        return Result.success("用户冻结成功");
    }

    @Operation(summary = "解冻用户", description = "管理员解冻用户账户")
    @PostMapping("/admin/users/{userId}/unfreeze")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> unfreezeUser(
            @PathVariable @NotNull(message = "用户ID不能为空") Long userId) {
        userService.unfreezeUser(userId);
        return Result.success("用户解冻成功");
    }

    @Operation(summary = "审核KYC", description = "管理员审核用户KYC申请")
    @PostMapping("/admin/kyc/{kycId}/review")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<Void> reviewKyc(
            @PathVariable @NotNull(message = "KYC ID不能为空") Long kycId,
            @Valid @RequestBody KycReviewRequest request) {
        userService.reviewKyc(kycId, request);
        return Result.success("KYC审核完成");
    }

    @Operation(summary = "获取用户统计", description = "管理员获取用户统计信息")
    @GetMapping("/admin/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<UserStatisticsResponse> getUserStatistics(
            @RequestParam(required = false) String period) {
        return userService.getUserStatistics(period);
    }
}