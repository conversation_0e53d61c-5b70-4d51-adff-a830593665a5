package com.cryptoexchange.controller;

import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.LoginRequest;
import com.cryptoexchange.dto.request.RegisterRequest;
import com.cryptoexchange.dto.request.ResetPasswordRequest;
import com.cryptoexchange.dto.response.LoginResponse;
import com.cryptoexchange.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "认证管理", description = "用户认证相关接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthService authService;

    @Operation(summary = "用户注册", description = "用户注册接口")
    @PostMapping("/register")
    public Result<Void> register(@Valid @RequestBody RegisterRequest request, HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        authService.register(request, clientIp);
        return Result.success("注册成功");
    }

    @Operation(summary = "用户登录", description = "用户登录接口")
    @PostMapping("/login")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");
        LoginResponse response = authService.login(request, clientIp, userAgent);
        return Result.success("登录成功", response);
    }

    @Operation(summary = "用户登出", description = "用户登出接口")
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        authService.logout(token);
        return Result.success("登出成功");
    }

    @Operation(summary = "刷新Token", description = "刷新访问令牌")
    @PostMapping("/refresh")
    public Result<LoginResponse> refresh(@RequestParam @NotBlank(message = "刷新令牌不能为空") String refreshToken) {
        LoginResponse response = authService.refreshToken(refreshToken);
        return Result.success("刷新成功", response);
    }

    @Operation(summary = "发送邮箱验证码", description = "发送邮箱验证码")
    @PostMapping("/send-email-code")
    public Result<Void> sendEmailCode(
            @RequestParam @Email(message = "邮箱格式不正确") String email,
            @RequestParam @NotBlank(message = "验证码类型不能为空") String type) {
        authService.sendEmailCode(email, type);
        return Result.success("验证码发送成功");
    }

    @Operation(summary = "发送短信验证码", description = "发送短信验证码")
    @PostMapping("/send-sms-code")
    public Result<Void> sendSmsCode(
            @RequestParam @NotBlank(message = "手机号不能为空") String phone,
            @RequestParam @NotBlank(message = "验证码类型不能为空") String type) {
        authService.sendSmsCode(phone, type);
        return Result.success("验证码发送成功");
    }

    @Operation(summary = "验证邮箱", description = "验证邮箱地址")
    @PostMapping("/verify-email")
    public Result<Void> verifyEmail(
            @RequestParam @Email(message = "邮箱格式不正确") String email,
            @RequestParam @NotBlank(message = "验证码不能为空") String code) {
        authService.verifyEmail(email, code);
        return Result.success("邮箱验证成功");
    }

    @Operation(summary = "验证手机号", description = "验证手机号码")
    @PostMapping("/verify-phone")
    public Result<Void> verifyPhone(
            @RequestParam @NotBlank(message = "手机号不能为空") String phone,
            @RequestParam @NotBlank(message = "验证码不能为空") String code) {
        authService.verifyPhone(phone, code);
        return Result.success("手机号验证成功");
    }

    @Operation(summary = "忘记密码", description = "通过邮箱重置密码")
    @PostMapping("/forgot-password")
    public Result<Void> forgotPassword(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        authService.forgotPassword(email);
        return Result.success("重置密码邮件已发送");
    }

    @Operation(summary = "重置密码", description = "重置用户密码")
    @PostMapping("/reset-password")
    public Result<Void> resetPassword(@Valid @RequestBody ResetPasswordRequest request) {
        authService.resetPassword(request);
        return Result.success("密码重置成功");
    }

    @Operation(summary = "检查用户名是否可用", description = "检查用户名是否已被使用")
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam @NotBlank(message = "用户名不能为空") String username) {
        boolean available = authService.isUsernameAvailable(username);
        return Result.success(available);
    }

    @Operation(summary = "检查邮箱是否可用", description = "检查邮箱是否已被使用")
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam @Email(message = "邮箱格式不正确") String email) {
        boolean available = authService.isEmailAvailable(email);
        return Result.success(available);
    }

    @Operation(summary = "检查手机号是否可用", description = "检查手机号是否已被使用")
    @GetMapping("/check-phone")
    public Result<Boolean> checkPhone(@RequestParam @NotBlank(message = "手机号不能为空") String phone) {
        boolean available = authService.isPhoneAvailable(phone);
        return Result.success(available);
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 从请求中获取Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}