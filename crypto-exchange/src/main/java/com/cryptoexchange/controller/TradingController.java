package com.cryptoexchange.controller;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.service.TradingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "现货交易", description = "现货交易相关接口")
@RestController
@RequestMapping("/api/trading")
@RequiredArgsConstructor
@Validated
public class TradingController {

    private final TradingService tradingService;

    @Operation(summary = "创建订单", description = "创建现货交易订单")
    @PostMapping("/orders")
    public Result<OrderResponse> createOrder(
            @Valid @RequestBody CreateOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        OrderResponse response = tradingService.createOrder(userId, request);
        return Result.success("订单创建成功", response);
    }

    @Operation(summary = "取消订单", description = "取消指定的交易订单")
    @DeleteMapping("/orders/{orderId}")
    public Result<Void> cancelOrder(
            @PathVariable @NotNull(message = "订单ID不能为空") Long orderId,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        tradingService.cancelOrder(userId, orderId);
        return Result.success("订单取消成功");
    }

    @Operation(summary = "批量取消订单", description = "批量取消交易订单")
    @DeleteMapping("/orders/batch")
    public Result<BatchCancelOrderResponse> batchCancelOrders(
            @Valid @RequestBody BatchCancelOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        BatchCancelOrderResponse response = tradingService.batchCancelOrders(userId, request);
        return Result.success("批量取消订单成功", response);
    }

    @Operation(summary = "获取用户订单", description = "分页获取用户的交易订单")
    @GetMapping("/orders")
    public Result<PageResult<OrderResponse>> getUserOrders(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String side,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        OrderQueryRequest request = new OrderQueryRequest();
        request.setUserId(userId);
        request.setSymbol(symbol);
        request.setSide(side);
        request.setType(type);
        request.setStatus(status);
        
        // 处理时间字段转换
        if (startTime != null && !startTime.isEmpty()) {
            try {
                request.setStartTime(LocalDateTime.parse(startTime.replace(" ", "T")));
            } catch (Exception e) {
                // 忽略时间解析错误
            }
        }
        if (endTime != null && !endTime.isEmpty()) {
            try {
                request.setEndTime(LocalDateTime.parse(endTime.replace(" ", "T")));
            } catch (Exception e) {
                // 忽略时间解析错误
            }
        }
        
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        
        PageResult<OrderResponse> response = tradingService.getUserOrders(request);
        return Result.success(response);
    }

    @Operation(summary = "获取订单详情", description = "获取指定订单的详细信息")
    @GetMapping("/orders/{orderId}")
    public Result<OrderResponse> getOrderDetail(
            @PathVariable @NotNull(message = "订单ID不能为空") Long orderId,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        OrderResponse response = tradingService.getOrderDetail(userId, orderId);
        return Result.success(response);
    }

    @Operation(summary = "获取用户交易记录", description = "分页获取用户的交易记录")
    @GetMapping("/trades")
    public Result<PageResult<TradeResponse>> getUserTrades(
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String side,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        TradeQueryRequest request = new TradeQueryRequest();
        request.setUserId(userId.toString());
        request.setSymbol(symbol);
        request.setSide(side);
        // 注意：TradeQueryRequest使用page和size字段，而不是pageNum和pageSize
        request.setPage(pageNum);
        request.setSize(pageSize);
        
        // 如果需要设置时间，需要转换为LocalDateTime格式
        if (startTime != null && !startTime.isEmpty()) {
            try {
                request.setStartTime(LocalDateTime.parse(startTime.replace(" ", "T")));
            } catch (Exception e) {
                // 忽略时间解析错误
            }
        }
        if (endTime != null && !endTime.isEmpty()) {
            try {
                request.setEndTime(LocalDateTime.parse(endTime.replace(" ", "T")));
            } catch (Exception e) {
                // 忽略时间解析错误
            }
        }
        
        PageResult<TradeResponse> response = tradingService.getUserTrades(request);
        return Result.success(response);
    }

    @Operation(summary = "获取市场深度", description = "获取指定交易对的市场深度信息")
    @GetMapping("/depth")
    public Result<MarketDepthResponse> getMarketDepth(
            @RequestParam @NotBlank(message = "交易对不能为空") String symbol,
            @RequestParam(defaultValue = "20") @Positive(message = "深度级别必须大于0") Integer limit) {
        MarketDepthResponse response = tradingService.getMarketDepth(symbol, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取最新成交", description = "获取指定交易对的最新成交记录")
    @GetMapping("/recent-trades")
    public Result<List<RecentTradeResponse>> getRecentTrades(
            @RequestParam @NotBlank(message = "交易对不能为空") String symbol,
            @RequestParam(defaultValue = "50") @Positive(message = "记录数量必须大于0") Integer limit) {
        List<RecentTradeResponse> response = tradingService.getRecentTrades(symbol, limit);
        return Result.success(response);
    }

    @Operation(summary = "获取24小时行情", description = "获取指定交易对的24小时行情统计")
    @GetMapping("/ticker/24hr")
    public Result<Ticker24hrResponse> get24hrTicker(
            @RequestParam(required = false) String symbol) {
        Ticker24hrResponse response = tradingService.get24hrTicker(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取K线数据", description = "获取指定交易对的K线数据")
    @GetMapping("/klines")
    public Result<List<KlineResponse>> getKlines(
            @RequestParam @NotBlank(message = "交易对不能为空") String symbol,
            @RequestParam @NotBlank(message = "时间间隔不能为空") String interval,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "500") @Positive(message = "记录数量必须大于0") Integer limit) {
        
        KlineQueryRequest request = new KlineQueryRequest();
        request.setSymbol(symbol);
        request.setInterval(interval);
        // 注意：startTime和endTime字段是LocalDateTime类型，这里暂时不设置
        // 如果需要设置时间，应该将String转换为LocalDateTime
        request.setLimit(limit);
        
        List<KlineResponse> response = tradingService.getKlines(request);
        return Result.success(response);
    }

    @Operation(summary = "获取交易对信息", description = "获取所有或指定交易对的信息")
    @GetMapping("/symbols")
    public Result<List<SymbolResponse>> getSymbols(
            @RequestParam(required = false) String symbol) {
        List<SymbolResponse> response = tradingService.getSymbols(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取当前价格", description = "获取指定交易对的当前价格")
    @GetMapping("/price")
    public Result<PriceResponse> getCurrentPrice(
            @RequestParam @NotBlank(message = "交易对不能为空") String symbol) {
        PriceResponse response = tradingService.getCurrentPrice(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取最优挂单", description = "获取指定交易对的最优买卖挂单")
    @GetMapping("/book-ticker")
    public Result<BookTickerResponse> getBookTicker(
            @RequestParam(required = false) String symbol) {
        BookTickerResponse response = tradingService.getBookTicker(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取账户信息", description = "获取用户的账户信息")
    @GetMapping("/account")
    public Result<AccountResponse> getAccount(
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        AccountResponse response = tradingService.getAccount(userId);
        return Result.success(response);
    }

    @Operation(summary = "获取交易费率", description = "获取用户的交易费率信息")
    @GetMapping("/trading-fee")
    public Result<TradingFeeResponse> getTradingFee(
            @RequestParam(required = false) String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        TradingFeeResponse response = tradingService.getTradingFee(userId, symbol);
        return Result.success(response);
    }

    @Operation(summary = "测试订单", description = "测试订单创建，不会实际提交")
    @PostMapping("/orders/test")
    public Result<OrderTestResponse> testOrder(
            @Valid @RequestBody CreateOrderRequest request,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        OrderTestResponse response = tradingService.testOrder(userId, request);
        return Result.success("订单测试成功", response);
    }

    @Operation(summary = "获取订单簿", description = "获取指定交易对的完整订单簿")
    @GetMapping("/order-book")
    public Result<OrderBookResponse> getOrderBook(
            @RequestParam @NotBlank(message = "交易对不能为空") String symbol) {
        OrderBookResponse response = tradingService.getOrderBook(symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取交易统计", description = "获取用户的交易统计信息")
    @GetMapping("/statistics")
    public Result<TradingStatisticsResponse> getTradingStatistics(
            @RequestParam(required = false) String period,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        TradingStatisticsResponse response = tradingService.getTradingStatistics(userId, period);
        return Result.success(response);
    }

    @Operation(summary = "获取持仓信息", description = "获取用户的持仓信息")
    @GetMapping("/positions")
    public Result<List<PositionResponse>> getPositions(
            @RequestParam(required = false) String symbol,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        List<PositionResponse> response = tradingService.getPositions(userId, symbol);
        return Result.success(response);
    }

    @Operation(summary = "获取资产快照", description = "获取用户的资产快照")
    @GetMapping("/asset-snapshot")
    public Result<AssetSnapshotResponse> getAssetSnapshot(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") @Positive(message = "页码必须大于0") Integer pageNum,
            @RequestParam(defaultValue = "20") @Positive(message = "页面大小必须大于0") Integer pageSize,
            @AuthenticationPrincipal UserDetails userDetails) {
        Long userId = Long.valueOf(userDetails.getUsername());
        
        AssetSnapshotQueryRequest request = new AssetSnapshotQueryRequest();
        request.setUserId(userId);
        request.setAssetType(type);
        // TODO: 需要将String类型的时间转换为LocalDateTime
        // request.setStartTime(startTime);
        // request.setEndTime(endTime);
        request.setPageNum(pageNum);
        request.setPageSize(pageSize);
        
        AssetSnapshotResponse response = tradingService.getAssetSnapshot(request);
        return Result.success(response);
    }
}