package com.cryptoexchange.security;

import com.cryptoexchange.util.JwtUtil;
import com.cryptoexchange.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 令牌验证服务实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
public class TokenValidationServiceImpl implements TokenValidationService {

    private static final Logger log = LoggerFactory.getLogger(TokenValidationServiceImpl.class);

    private final JwtUtil jwtUtil;
    private final RedisUtil redisUtil;

    // Redis键前缀
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";

    @Override
    public Long validateToken(String token) {
        try {
            // 验证JWT令牌格式和签名
            if (!jwtUtil.validateAccessToken(token)) {
                log.debug("JWT令牌验证失败: 格式或签名无效");
                return null;
            }

            // 检查令牌是否在黑名单中
            if (isTokenBlacklisted(token)) {
                log.debug("JWT令牌验证失败: 令牌已被列入黑名单");
                return null;
            }

            // 从令牌中提取用户ID
            Long userId = jwtUtil.getUserIdFromToken(token);
            log.debug("JWT令牌验证成功: userId={}", userId);
            return userId;

        } catch (Exception e) {
            log.warn("JWT令牌验证过程中发生异常: {}", e.getMessage());
            return null;
        }
    }

    @Override
    public boolean isTokenBlacklisted(String token) {
        try {
            return redisUtil.hasKey(TOKEN_BLACKLIST_PREFIX + token);
        } catch (Exception e) {
            log.warn("检查令牌黑名单状态时发生异常: {}", e.getMessage());
            // 出现异常时，为了安全起见，认为令牌已被列入黑名单
            return true;
        }
    }
}
