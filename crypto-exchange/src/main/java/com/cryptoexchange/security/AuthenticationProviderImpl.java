package com.cryptoexchange.security;

import com.cryptoexchange.entity.User;
import com.cryptoexchange.service.UserService;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 认证提供者实现类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AuthenticationProviderImpl implements AuthenticationProvider {

    private static final Logger log = LoggerFactory.getLogger(AuthenticationProviderImpl.class);

    private final UserService userService;
    private final PasswordEncoder passwordEncoder;

    /**
     * 构造函数，使用@Lazy注解解决循环依赖
     */
    public AuthenticationProviderImpl(@Lazy UserService userService,
                                    PasswordEncoder passwordEncoder) {
        this.userService = userService;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public User findUserByUsername(String username) {
        try {
            return userService.findByUsername(username);
        } catch (Exception e) {
            log.error("根据用户名查找用户失败: username={}, error={}", username, e.getMessage());
            return null;
        }
    }

    @Override
    public User findUserByEmail(String email) {
        try {
            return userService.findByEmail(email);
        } catch (Exception e) {
            log.error("根据邮箱查找用户失败: email={}, error={}", email, e.getMessage());
            return null;
        }
    }

    @Override
    public User findUserById(Long userId) {
        try {
            return userService.getById(userId);
        } catch (Exception e) {
            log.error("根据用户ID查找用户失败: userId={}, error={}", userId, e.getMessage());
            return null;
        }
    }

    @Override
    public boolean verifyPassword(User user, String rawPassword) {
        try {
            if (user == null || user.getPassword() == null) {
                return false;
            }
            return passwordEncoder.matches(rawPassword, user.getPassword());
        } catch (Exception e) {
            log.error("验证用户密码失败: userId={}, error={}", 
                     user != null ? user.getId() : null, e.getMessage());
            return false;
        }
    }

    @Override
    public void updateLastLoginTime(Long userId, String loginIp) {
        try {
            userService.updateLastLoginInfo(userId, loginIp, null);
        } catch (Exception e) {
            log.error("更新用户最后登录时间失败: userId={}, loginIp={}, error={}",
                     userId, loginIp, e.getMessage());
        }
    }
}
