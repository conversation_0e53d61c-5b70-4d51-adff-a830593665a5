package com.cryptoexchange.security;

import com.cryptoexchange.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.ArrayList;

/**
 * JWT认证过滤器
 * 从请求中提取JWT令牌并验证，如果有效则设置Spring Security上下文
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);

    private final JwtUtil jwtUtil;
    private final TokenValidationService tokenValidationService;

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request, 
                                   @NonNull HttpServletResponse response, 
                                   @NonNull FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 从请求中提取JWT令牌
            String token = extractTokenFromRequest(request);
            
            // 如果令牌存在且当前没有认证信息
            if (StringUtils.hasText(token) && SecurityContextHolder.getContext().getAuthentication() == null) {

                // 验证令牌并获取用户ID
                Long userId = tokenValidationService.validateToken(token);

                if (userId != null) {
                    // 创建用户详情对象
                    UserDetails userDetails = createUserDetails(userId);

                    // 创建认证令牌
                    UsernamePasswordAuthenticationToken authToken =
                        new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                        );

                    // 设置认证详情
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    // 设置到Spring Security上下文
                    SecurityContextHolder.getContext().setAuthentication(authToken);

                    log.debug("JWT认证成功: userId={}, uri={}", userId, request.getRequestURI());
                } else {
                    log.debug("JWT令牌验证失败: uri={}", request.getRequestURI());
                }
            }
        } catch (Exception e) {
            log.error("JWT认证过程中发生错误: {}", e.getMessage(), e);
            // 清除可能存在的认证信息
            SecurityContextHolder.clearContext();
        }
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中提取JWT令牌
     * 
     * @param request HTTP请求
     * @return JWT令牌，如果不存在则返回null
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从Authorization头中提取
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.hasText(authHeader)) {
            return jwtUtil.extractTokenFromHeader(authHeader);
        }
        
        // 从查询参数中提取（用于WebSocket等场景）
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }
        
        return null;
    }

    /**
     * 创建用户详情对象
     * 
     * @param userId 用户ID
     * @return 用户详情
     */
    private UserDetails createUserDetails(Long userId) {
        // 这里简化处理，实际项目中可能需要从数据库加载用户详细信息和权限
        return User.builder()
                .username(userId.toString())
                .password("") // 密码不需要，因为已经通过JWT验证
                .authorities(new ArrayList<>()) // 权限列表，可以从数据库加载
                .accountExpired(false)
                .accountLocked(false)
                .credentialsExpired(false)
                .disabled(false)
                .build();
    }

    /**
     * 判断是否应该跳过此过滤器
     * 
     * @param request HTTP请求
     * @return 是否跳过
     */
    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String path = request.getRequestURI();
        
        // 跳过公开接口
        return path.startsWith("/api/auth/") ||
               path.startsWith("/api/public/") ||
               path.startsWith("/api/market/") ||
               path.startsWith("/ws/") ||
               path.startsWith("/actuator/health") ||
               path.startsWith("/doc.html") ||
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs/") ||
               path.startsWith("/webjars/") ||
               path.equals("/favicon.ico") ||
               path.equals("/error");
    }
}