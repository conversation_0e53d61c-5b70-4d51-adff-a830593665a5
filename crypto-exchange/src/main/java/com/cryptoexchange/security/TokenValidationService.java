package com.cryptoexchange.security;

/**
 * 令牌验证服务接口
 * 用于解耦JwtAuthenticationFilter和AuthService之间的循环依赖
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TokenValidationService {

    /**
     * 验证访问令牌
     * 
     * @param token 访问令牌
     * @return 用户ID，如果令牌无效则返回null
     */
    Long validateToken(String token);

    /**
     * 检查令牌是否在黑名单中
     * 
     * @param token 令牌
     * @return 是否在黑名单中
     */
    boolean isTokenBlacklisted(String token);
}
