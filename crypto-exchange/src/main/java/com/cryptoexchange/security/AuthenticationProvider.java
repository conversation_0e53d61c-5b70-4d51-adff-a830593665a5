package com.cryptoexchange.security;

import com.cryptoexchange.entity.User;

/**
 * 认证提供者接口
 * 用于解耦认证逻辑，避免循环依赖
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuthenticationProvider {

    /**
     * 根据用户名查找用户
     * 
     * @param username 用户名
     * @return 用户信息，如果不存在则返回null
     */
    User findUserByUsername(String username);

    /**
     * 根据邮箱查找用户
     * 
     * @param email 邮箱
     * @return 用户信息，如果不存在则返回null
     */
    User findUserByEmail(String email);

    /**
     * 根据用户ID查找用户
     * 
     * @param userId 用户ID
     * @return 用户信息，如果不存在则返回null
     */
    User findUserById(Long userId);

    /**
     * 验证用户密码
     * 
     * @param user 用户信息
     * @param rawPassword 原始密码
     * @return 是否匹配
     */
    boolean verifyPassword(User user, String rawPassword);

    /**
     * 更新用户最后登录时间
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     */
    void updateLastLoginTime(Long userId, String loginIp);
}
