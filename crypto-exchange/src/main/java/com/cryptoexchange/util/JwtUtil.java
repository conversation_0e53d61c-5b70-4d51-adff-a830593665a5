package com.cryptoexchange.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.*;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * JWT工具类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class JwtUtil {

    private static final Logger log = LoggerFactory.getLogger(JwtUtil.class);

    @Value("${app.jwt.secret}")
    private String secret;

    @Value("${app.jwt.access-token-expiration}")
    private Long accessTokenExpiration;

    @Value("${app.jwt.refresh-token-expiration}")
    private Long refreshTokenExpiration;

    @Value("${app.jwt.issuer}")
    private String issuer;

    /**
     * 生成访问令牌
     * 
     * @param userId 用户ID
     * @return 访问令牌
     */
    public String generateAccessToken(Long userId) {
        return generateToken(userId, accessTokenExpiration, "access");
    }

    /**
     * 生成刷新令牌
     * 
     * @param userId 用户ID
     * @return 刷新令牌
     */
    public String generateRefreshToken(Long userId) {
        return generateToken(userId, refreshTokenExpiration, "refresh");
    }

    /**
     * 生成令牌
     * 
     * @param userId 用户ID
     * @param expiration 过期时间（毫秒）
     * @param tokenType 令牌类型
     * @return 令牌
     */
    private String generateToken(Long userId, Long expiration, String tokenType) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        Algorithm algorithm = Algorithm.HMAC512(secret.getBytes(StandardCharsets.UTF_8));
        
        return JWT.create()
                .withSubject(userId.toString())
                .withIssuer(issuer)
                .withIssuedAt(now)
                .withExpiresAt(expiryDate)
                .withClaim("userId", userId)
                .withClaim("tokenType", tokenType)
                .sign(algorithm);
    }

    /**
     * 从令牌中获取用户ID
     * 
     * @param token 令牌
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            Claim userIdClaim = decodedJWT.getClaim("userId");
            if (userIdClaim.isNull()) {
                return null;
            }
            return userIdClaim.asLong();
        } catch (Exception e) {
            log.error("从令牌中获取用户ID失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取令牌类型
     * 
     * @param token 令牌
     * @return 令牌类型
     */
    public String getTokenTypeFromToken(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            Claim tokenTypeClaim = decodedJWT.getClaim("tokenType");
            return tokenTypeClaim.asString();
        } catch (Exception e) {
            log.error("从令牌中获取令牌类型失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取过期时间
     * 
     * @param token 令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return decodedJWT.getExpiresAt();
        } catch (Exception e) {
            log.error("从令牌中获取过期时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从令牌中获取签发时间
     * 
     * @param token 令牌
     * @return 签发时间
     */
    public Date getIssuedAtFromToken(String token) {
        try {
            DecodedJWT decodedJWT = verifyToken(token);
            return decodedJWT.getIssuedAt();
        } catch (Exception e) {
            log.error("从令牌中获取签发时间失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 验证令牌
     * 
     * @param token 令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            verifyToken(token);
            return true;
        } catch (TokenExpiredException e) {
            log.warn("令牌已过期: {}", e.getMessage());
        } catch (SignatureVerificationException e) {
            log.warn("令牌签名验证失败: {}", e.getMessage());
        } catch (JWTDecodeException e) {
            log.warn("令牌格式错误: {}", e.getMessage());
        } catch (AlgorithmMismatchException e) {
            log.warn("算法不匹配: {}", e.getMessage());
        } catch (InvalidClaimException e) {
            log.warn("令牌声明无效: {}", e.getMessage());
        } catch (Exception e) {
            log.error("令牌验证失败: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 验证访问令牌
     * 
     * @param token 访问令牌
     * @return 是否有效
     */
    public boolean validateAccessToken(String token) {
        if (!validateToken(token)) {
            return false;
        }
        String tokenType = getTokenTypeFromToken(token);
        return "access".equals(tokenType);
    }

    /**
     * 验证刷新令牌
     * 
     * @param token 刷新令牌
     * @return 是否有效
     */
    public boolean validateRefreshToken(String token) {
        if (!validateToken(token)) {
            return false;
        }
        String tokenType = getTokenTypeFromToken(token);
        return "refresh".equals(tokenType);
    }

    /**
     * 检查令牌是否过期
     * 
     * @param token 令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            return expiration != null && expiration.before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 获取令牌剩余有效时间（秒）
     * 
     * @param token 令牌
     * @return 剩余有效时间
     */
    public Long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            if (expiration == null) {
                return 0L;
            }
            long remaining = expiration.getTime() - System.currentTimeMillis();
            return Math.max(0L, remaining / 1000);
        } catch (Exception e) {
            return 0L;
        }
    }

    /**
     * 验证并解码令牌
     * 
     * @param token 令牌
     * @return 解码后的JWT
     */
    private DecodedJWT verifyToken(String token) {
        Algorithm algorithm = Algorithm.HMAC512(secret.getBytes(StandardCharsets.UTF_8));
        JWTVerifier verifier = JWT.require(algorithm)
                .withIssuer(issuer)
                .build();
        return verifier.verify(token);
    }

    /**
     * 从请求头中提取令牌
     * 
     * @param authHeader 授权头
     * @return 令牌
     */
    public String extractTokenFromHeader(String authHeader) {
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        return null;
    }

    /**
     * 获取访问令牌过期时间（秒）
     * 
     * @return 过期时间
     */
    public Long getAccessTokenExpiration() {
        return accessTokenExpiration / 1000;
    }

    /**
     * 获取刷新令牌过期时间（秒）
     * 
     * @return 过期时间
     */
    public Long getRefreshTokenExpiration() {
        return refreshTokenExpiration / 1000;
    }
}