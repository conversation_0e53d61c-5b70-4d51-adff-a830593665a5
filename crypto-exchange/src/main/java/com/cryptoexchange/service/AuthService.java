package com.cryptoexchange.service;

import com.cryptoexchange.dto.request.LoginRequest;
import com.cryptoexchange.dto.request.RegisterRequest;
import com.cryptoexchange.dto.request.ResetPasswordRequest;
import com.cryptoexchange.dto.response.LoginResponse;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface AuthService {

    /**
     * 用户注册
     * 
     * @param request 注册请求
     * @param clientIp 客户端IP
     */
    void register(RegisterRequest request, String clientIp);

    /**
     * 用户登录
     * 
     * @param request 登录请求
     * @param clientIp 客户端IP
     * @param userAgent 用户代理
     * @return 登录响应
     */
    LoginResponse login(LoginRequest request, String clientIp, String userAgent);

    /**
     * 用户登出
     * 
     * @param token 访问令牌
     */
    void logout(String token);

    /**
     * 刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 登录响应
     */
    LoginResponse refreshToken(String refreshToken);

    /**
     * 发送邮箱验证码
     * 
     * @param email 邮箱地址
     * @param type 验证码类型（register、login、reset_password等）
     */
    void sendEmailCode(String email, String type);

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号码
     * @param type 验证码类型（register、login、reset_password等）
     */
    void sendSmsCode(String phone, String type);

    /**
     * 验证邮箱
     * 
     * @param email 邮箱地址
     * @param code 验证码
     */
    void verifyEmail(String email, String code);

    /**
     * 验证手机号
     * 
     * @param phone 手机号码
     * @param code 验证码
     */
    void verifyPhone(String phone, String code);

    /**
     * 忘记密码
     * 
     * @param email 邮箱地址
     */
    void forgotPassword(String email);

    /**
     * 重置密码
     * 
     * @param request 重置密码请求
     */
    void resetPassword(ResetPasswordRequest request);

    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 是否可用
     */
    boolean isUsernameAvailable(String username);

    /**
     * 检查邮箱是否可用
     * 
     * @param email 邮箱地址
     * @return 是否可用
     */
    boolean isEmailAvailable(String email);

    /**
     * 检查手机号是否可用
     * 
     * @param phone 手机号码
     * @return 是否可用
     */
    boolean isPhoneAvailable(String phone);

    /**
     * 验证访问令牌
     * 
     * @param token 访问令牌
     * @return 用户ID
     */
    Long validateToken(String token);

    /**
     * 验证刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 用户ID
     */
    Long validateRefreshToken(String refreshToken);

    /**
     * 生成访问令牌
     * 
     * @param userId 用户ID
     * @return 访问令牌
     */
    String generateAccessToken(Long userId);

    /**
     * 生成刷新令牌
     * 
     * @param userId 用户ID
     * @return 刷新令牌
     */
    String generateRefreshToken(Long userId);

    /**
     * 使令牌失效
     * 
     * @param token 令牌
     */
    void invalidateToken(String token);

    /**
     * 验证验证码
     * 
     * @param key 验证码键
     * @param code 验证码
     * @param type 验证码类型
     * @return 是否有效
     */
    boolean verifyCode(String key, String code, String type);

    /**
     * 生成验证码
     * 
     * @param key 验证码键
     * @param type 验证码类型
     * @return 验证码
     */
    String generateCode(String key, String type);

    /**
     * 清除验证码
     * 
     * @param key 验证码键
     * @param type 验证码类型
     */
    void clearCode(String key, String type);
}