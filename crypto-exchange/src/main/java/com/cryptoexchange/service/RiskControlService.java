package com.cryptoexchange.service;

import com.cryptoexchange.dto.request.FuturesOrderRequest;
import com.cryptoexchange.dto.response.RiskAssessmentResponse;
import com.cryptoexchange.entity.FuturesOrder;
import com.cryptoexchange.entity.User;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 风险控制服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RiskControlService {

    /**
     * 订单风险检查
     * 
     * @param userId 用户ID
     * @param orderRequest 订单请求
     * @return 风险评估结果
     */
    RiskAssessmentResponse checkOrderRisk(Long userId, FuturesOrderRequest orderRequest);

    /**
     * 用户风险评估
     * 
     * @param userId 用户ID
     * @return 风险评估结果
     */
    RiskAssessmentResponse assessUserRisk(Long userId);

    /**
     * 检查用户交易限制
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @param orderType 订单类型
     * @param quantity 数量
     * @param price 价格
     * @return 是否通过检查
     */
    boolean checkTradingLimits(Long userId, String symbol, String orderType, BigDecimal quantity, BigDecimal price);

    /**
     * 检查保证金充足性
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @param quantity 数量
     * @param price 价格
     * @param leverage 杠杆倍数
     * @return 是否充足
     */
    boolean checkMarginSufficiency(Long userId, String symbol, BigDecimal quantity, BigDecimal price, Integer leverage);

    /**
     * 检查持仓风险
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @return 风险等级
     */
    String checkPositionRisk(Long userId, String symbol);

    /**
     * 计算强制平仓价格
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @return 强制平仓价格
     */
    BigDecimal calculateLiquidationPrice(Long userId, String symbol);

    /**
     * 检查是否需要强制平仓
     * 
     * @param userId 用户ID
     * @param currentPrice 当前价格
     * @return 是否需要强制平仓
     */
    boolean checkLiquidationRequired(Long userId, BigDecimal currentPrice);

    /**
     * 执行强制平仓
     * 
     * @param userId 用户ID
     * @param symbol 交易对
     * @param reason 平仓原因
     * @return 是否成功
     */
    boolean executeLiquidation(Long userId, String symbol, String reason);

    /**
     * 检查用户KYC状态
     * 
     * @param userId 用户ID
     * @return 是否通过KYC
     */
    boolean checkKycStatus(Long userId);

    /**
     * 检查用户账户状态
     * 
     * @param userId 用户ID
     * @return 账户是否正常
     */
    boolean checkAccountStatus(Long userId);

    /**
     * 检查交易频率限制
     * 
     * @param userId 用户ID
     * @return 是否超过限制
     */
    boolean checkTradingFrequency(Long userId);

    /**
     * 检查单笔交易限额
     * 
     * @param userId 用户ID
     * @param amount 交易金额
     * @return 是否超过限额
     */
    boolean checkSingleTradeLimit(Long userId, BigDecimal amount);

    /**
     * 检查日交易限额
     * 
     * @param userId 用户ID
     * @param amount 交易金额
     * @return 是否超过限额
     */
    boolean checkDailyTradeLimit(Long userId, BigDecimal amount);

    /**
     * 检查月交易限额
     * 
     * @param userId 用户ID
     * @param amount 交易金额
     * @return 是否超过限额
     */
    boolean checkMonthlyTradeLimit(Long userId, BigDecimal amount);

    /**
     * 获取用户风险等级
     * 
     * @param userId 用户ID
     * @return 风险等级
     */
    String getUserRiskLevel(Long userId);

    /**
     * 更新用户风险等级
     * 
     * @param userId 用户ID
     * @param riskLevel 风险等级
     * @return 是否成功
     */
    boolean updateUserRiskLevel(Long userId, String riskLevel);

    /**
     * 获取风险控制参数
     * 
     * @param userId 用户ID
     * @return 风险控制参数
     */
    Map<String, Object> getRiskControlParams(Long userId);

    /**
     * 记录风险事件
     * 
     * @param userId 用户ID
     * @param eventType 事件类型
     * @param description 描述
     * @param riskLevel 风险等级
     */
    void recordRiskEvent(Long userId, String eventType, String description, String riskLevel);

    /**
     * 检查异常交易行为
     * 
     * @param userId 用户ID
     * @param order 订单信息
     * @return 是否异常
     */
    boolean checkAbnormalTrading(Long userId, FuturesOrder order);

    /**
     * 检查市场风险
     * 
     * @param symbol 交易对
     * @return 市场风险等级
     */
    String checkMarketRisk(String symbol);

    /**
     * 获取系统风险状态
     * 
     * @return 系统风险状态
     */
    Map<String, Object> getSystemRiskStatus();

    /**
     * 暂停用户交易
     * 
     * @param userId 用户ID
     * @param reason 暂停原因
     * @param duration 暂停时长(分钟)
     * @return 是否成功
     */
    boolean suspendUserTrading(Long userId, String reason, Integer duration);

    /**
     * 恢复用户交易
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean resumeUserTrading(Long userId);
}