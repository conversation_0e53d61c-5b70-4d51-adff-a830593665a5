package com.cryptoexchange.service;

import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;
import com.cryptoexchange.entity.Order;
import com.cryptoexchange.entity.Trade;
import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易服务接口
 * 负责订单管理、交易撮合等核心交易功能
 */
public interface TradingService {
    
    /**
     * 创建订单
     * @param userId 用户ID
     * @param request 创建订单请求
     * @return 订单响应
     */
    OrderResponse createOrder(Long userId, CreateOrderRequest request);
    
    /**
     * 取消订单
     * @param userId 用户ID
     * @param orderId 订单ID
     */
    void cancelOrder(Long userId, Long orderId);
    
    /**
     * 批量取消订单
     * @param userId 用户ID
     * @param request 批量取消订单请求
     * @return 批量取消订单响应
     */
    BatchCancelOrderResponse batchCancelOrders(Long userId, BatchCancelOrderRequest request);
    
    /**
     * 获取用户订单列表
     * @param request 订单查询请求
     * @return 订单列表
     */
    PageResult<OrderResponse> getUserOrders(OrderQueryRequest request);
    
    /**
     * 获取订单详情
     * @param userId 用户ID
     * @param orderId 订单ID
     * @return 订单详情
     */
    OrderResponse getOrderDetail(Long userId, Long orderId);
    
    /**
     * 获取用户交易记录
     * @param request 交易查询请求
     * @return 交易记录列表
     */
    PageResult<TradeResponse> getUserTrades(TradeQueryRequest request);
    
    /**
     * 获取市场深度
     * @param symbol 交易对符号
     * @param limit 深度限制
     * @return 市场深度数据
     */
    MarketDepthResponse getMarketDepth(String symbol, Integer limit);
    
    /**
     * 获取最新成交记录
     * @param symbol 交易对符号
     * @param limit 记录数量限制
     * @return 最新成交记录
     */
    List<RecentTradeResponse> getRecentTrades(String symbol, Integer limit);
    
    /**
     * 获取24小时行情统计
     * @param symbol 交易对符号
     * @return 24小时行情统计
     */
    Ticker24hrResponse get24hrTicker(String symbol);
    
    /**
     * 获取K线数据
     * @param request K线查询请求
     * @return K线数据
     */
    List<KlineResponse> getKlines(KlineQueryRequest request);
    
    /**
     * 获取交易对信息
     * @param symbol 交易对符号（可选）
     * @return 交易对信息列表
     */
    List<SymbolResponse> getSymbols(String symbol);
    
    /**
     * 获取当前价格
     * @param symbol 交易对符号
     * @return 当前价格
     */
    PriceResponse getCurrentPrice(String symbol);
    
    /**
     * 获取最优挂单
     * @param symbol 交易对符号
     * @return 最优挂单信息
     */
    BookTickerResponse getBookTicker(String symbol);
    
    /**
     * 获取账户信息
     * @param userId 用户ID
     * @return 账户信息
     */
    AccountResponse getAccount(Long userId);
    
    /**
     * 获取交易手续费
     * @param userId 用户ID
     * @param symbol 交易对符号
     * @return 交易手续费信息
     */
    TradingFeeResponse getTradingFee(Long userId, String symbol);
    
    /**
     * 测试订单
     * @param userId 用户ID
     * @param request 创建订单请求
     * @return 测试订单响应
     */
    OrderTestResponse testOrder(Long userId, CreateOrderRequest request);
    
    /**
     * 获取订单簿
     * @param symbol 交易对符号
     * @return 订单簿信息
     */
    OrderBookResponse getOrderBook(String symbol);
    
    /**
     * 获取交易统计
     * @param userId 用户ID
     * @param symbol 交易对符号
     * @return 交易统计信息
     */
    TradingStatisticsResponse getTradingStatistics(Long userId, String symbol);
    
    /**
     * 获取持仓信息
     * @param userId 用户ID
     * @param symbol 交易对符号
     * @return 持仓信息
     */
    List<PositionResponse> getPositions(Long userId, String symbol);
    
    /**
     * 获取K线数据
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param limit 数据数量限制
     * @return K线数据
     */
    List<KlineResponse> getKlineData(String symbol, String interval, Integer limit);
    
    /**
     * 执行订单撮合
     * @param symbol 交易对符号
     * @return 撮合结果
     */
    Object executeMatching(String symbol);
    
    /**
     * 处理订单成交
     * @param buyOrder 买单
     * @param sellOrder 卖单
     * @param tradePrice 成交价格
     * @param tradeQuantity 成交数量
     * @return 成交记录
     */
    Trade processTrade(Order buyOrder, Order sellOrder, BigDecimal tradePrice, BigDecimal tradeQuantity);
    
    /**
     * 更新用户资产
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 金额变化
     * @param type 变化类型（1-增加，2-减少）
     */
    void updateUserAsset(Long userId, String currency, BigDecimal amount, Integer type);
    
    /**
     * 冻结用户资产
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 冻结金额
     */
    void freezeUserAsset(Long userId, String currency, BigDecimal amount);
    
    /**
     * 解冻用户资产
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 解冻金额
     */
    void unfreezeUserAsset(Long userId, String currency, BigDecimal amount);
    
    /**
     * 验证订单合法性
     * @param order 订单
     * @return 验证结果
     */
    boolean validateOrder(Order order);
    
    /**
     * 检查用户资产是否足够
     * @param userId 用户ID
     * @param currency 币种
     * @param amount 所需金额
     * @return 检查结果
     */
    boolean checkUserBalance(Long userId, String currency, BigDecimal amount);
    
    /**
     * 获取资产快照
     * @param request 查询请求
     * @return 资产快照响应
     */
    AssetSnapshotResponse getAssetSnapshot(AssetSnapshotQueryRequest request);
}