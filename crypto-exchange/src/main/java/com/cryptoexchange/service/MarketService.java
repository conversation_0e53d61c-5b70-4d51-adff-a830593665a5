package com.cryptoexchange.service;

import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;

import java.util.List;

/**
 * 市场数据服务接口
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface MarketService {

    /**
     * 获取交易对列表
     * 
     * @param request 查询请求
     * @return 交易对列表
     */
    List<SymbolResponse> getSymbols(SymbolQueryRequest request);

    /**
     * 获取交易对详情
     * 
     * @param symbol 交易对代码
     * @return 交易对详情
     */
    SymbolDetailResponse getSymbolDetail(String symbol);

    /**
     * 获取市场深度
     * 
     * @param symbol 交易对代码
     * @param limit 深度级别
     * @return 市场深度信息
     */
    MarketDepthResponse getMarketDepth(String symbol, Integer limit);

    /**
     * 获取最新成交记录
     * 
     * @param symbol 交易对代码
     * @param limit 记录数量
     * @return 最新成交记录
     */
    List<RecentTradeResponse> getRecentTrades(String symbol, Integer limit);

    /**
     * 获取24小时行情统计
     * 
     * @param symbol 交易对代码，为空时返回所有交易对
     * @return 24小时行情统计
     */
    List<Ticker24hrResponse> get24hrTicker(String symbol);

    /**
     * 获取当前价格
     * 
     * @param symbol 交易对代码，为空时返回所有交易对
     * @return 当前价格信息
     */
    List<TickerPriceResponse> getTickerPrice(String symbol);

    /**
     * 获取最优挂单价格
     * 
     * @param symbol 交易对代码，为空时返回所有交易对
     * @return 最优挂单价格
     */
    List<BookTickerResponse> getBookTicker(String symbol);

    /**
     * 获取K线数据
     * 
     * @param request K线查询请求
     * @return K线数据
     */
    List<KlineResponse> getKlines(KlineQueryRequest request);

    /**
     * 获取平均价格
     * 
     * @param symbol 交易对代码
     * @return 平均价格信息
     */
    AvgPriceResponse getAvgPrice(String symbol);

    /**
     * 获取交易统计
     * 
     * @param symbol 交易对代码
     * @param period 统计周期
     * @return 交易统计信息
     */
    TradingStatsResponse getTradingStats(String symbol, String period);

    /**
     * 获取历史交易记录
     * 
     * @param request 历史交易查询请求
     * @return 历史交易记录
     */
    List<HistoricalTradeResponse> getHistoricalTrades(HistoricalTradeQueryRequest request);

    /**
     * 获取聚合交易记录
     * 
     * @param request 聚合交易查询请求
     * @return 聚合交易记录
     */
    List<AggTradeResponse> getAggTrades(AggTradeQueryRequest request);

    /**
     * 获取市场概览
     * 
     * @return 市场概览信息
     */
    MarketOverviewResponse getMarketOverview();

    /**
     * 获取热门交易对
     * 
     * @param limit 记录数量
     * @return 热门交易对列表
     */
    List<HotSymbolResponse> getHotSymbols(Integer limit);

    /**
     * 获取涨跌幅排行
     * 
     * @param limit 记录数量
     * @return 涨跌幅排行信息
     */
    GainersLosersResponse getGainersLosers(Integer limit);

    /**
     * 获取成交量排行
     * 
     * @param limit 记录数量
     * @param period 统计周期
     * @return 成交量排行
     */
    List<VolumeRankingResponse> getVolumeRanking(Integer limit, String period);

    /**
     * 获取新币上线
     * 
     * @param limit 记录数量
     * @return 新上线交易对
     */
    List<NewListingResponse> getNewListings(Integer limit);

    /**
     * 获取市场分类
     * 
     * @return 市场分类列表
     */
    List<MarketCategoryResponse> getMarketCategories();

    /**
     * 根据分类获取交易对
     * 
     * @param categoryId 分类ID
     * @return 交易对列表
     */
    List<SymbolResponse> getSymbolsByCategory(String categoryId);

    /**
     * 搜索交易对
     * 
     * @param keyword 搜索关键词
     * @param limit 记录数量
     * @return 搜索结果
     */
    List<SymbolSearchResponse> searchSymbols(String keyword, Integer limit);

    /**
     * 获取交易对状态
     * 
     * @param symbol 交易对代码，为空时返回所有交易对
     * @return 交易对状态
     */
    List<SymbolStatusResponse> getSymbolStatus(String symbol);

    /**
     * 获取服务器时间
     * 
     * @return 服务器时间
     */
    ServerTimeResponse getServerTime();

    /**
     * 获取汇率信息
     * 
     * @param request 汇率查询请求
     * @return 汇率信息
     */
    List<ExchangeRateResponse> getExchangeRates(ExchangeRateQueryRequest request);

    /**
     * 获取市场公告
     * 
     * @param type 公告类型
     * @param limit 记录数量
     * @return 市场公告
     */
    List<MarketAnnouncementResponse> getMarketAnnouncements(String type, Integer limit);

    /**
     * 获取系统状态
     * 
     * @return 系统状态
     */
    SystemStatusResponse getSystemStatus();

    /**
     * 获取API限制信息
     * 
     * @return API限制信息
     */
    ApiLimitsResponse getApiLimits();

    /**
     * 获取历史K线数据
     * 
     * @param request 历史K线查询请求
     * @return 历史K线数据
     */
    List<KlineResponse> getHistoricalKlines(HistoricalKlineQueryRequest request);

    /**
     * 获取价格变化统计
     * 
     * @param symbol 交易对代码，为空时返回所有交易对
     * @param period 统计周期
     * @return 价格变化统计
     */
    List<PriceChangeStatsResponse> getPriceChangeStats(String symbol, String period);

    /**
     * 刷新市场数据缓存
     * 
     * @param symbol 交易对代码，为空时刷新所有
     */
    void refreshMarketDataCache(String symbol);

    /**
     * 获取实时价格推送数据
     * 
     * @param symbol 交易对代码
     * @return 实时价格数据
     */
    RealTimePriceResponse getRealTimePrice(String symbol);

    /**
     * 获取实时深度推送数据
     * 
     * @param symbol 交易对代码
     * @return 实时深度数据
     */
    RealTimeDepthResponse getRealTimeDepth(String symbol);

    /**
     * 获取实时成交推送数据
     * 
     * @param symbol 交易对代码
     * @return 实时成交数据
     */
    RealTimeTradeResponse getRealTimeTrade(String symbol);

    /**
     * 获取实时K线推送数据
     * 
     * @param symbol 交易对代码
     * @param interval 时间间隔
     * @return 实时K线数据
     */
    RealTimeKlineResponse getRealTimeKline(String symbol, String interval);

    /**
     * 获取市场数据统计
     * 
     * @param period 统计周期
     * @return 市场数据统计
     */
    MarketDataStatisticsResponse getMarketDataStatistics(String period);

    /**
     * 获取交易对配置
     * 
     * @param symbol 交易对代码
     * @return 交易对配置
     */
    SymbolConfigResponse getSymbolConfig(String symbol);

    /**
     * 更新交易对配置
     * 
     * @param symbol 交易对代码
     * @param config 配置信息
     */
    void updateSymbolConfig(String symbol, SymbolConfigRequest config);

    /**
     * 获取市场指数
     * 
     * @param indexName 指数名称
     * @return 市场指数信息
     */
    MarketIndexResponse getMarketIndex(String indexName);

    /**
     * 获取恐慌贪婪指数
     * 
     * @return 恐慌贪婪指数
     */
    FearGreedIndexResponse getFearGreedIndex();

    /**
     * 获取市场情绪分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 市场情绪分析
     */
    MarketSentimentResponse getMarketSentiment(String symbol, String period);

    /**
     * 获取技术指标
     * 
     * @param symbol 交易对代码
     * @param indicator 指标类型
     * @param period 计算周期
     * @return 技术指标数据
     */
    TechnicalIndicatorResponse getTechnicalIndicator(String symbol, String indicator, String period);

    /**
     * 获取支撑阻力位
     * 
     * @param symbol 交易对代码
     * @return 支撑阻力位信息
     */
    SupportResistanceResponse getSupportResistance(String symbol);

    /**
     * 获取价格预测
     * 
     * @param symbol 交易对代码
     * @param timeframe 预测时间范围
     * @return 价格预测信息
     */
    PricePredictionResponse getPricePrediction(String symbol, String timeframe);

    /**
     * 获取市场热力图
     * 
     * @return 市场热力图数据
     */
    MarketHeatmapResponse getMarketHeatmap();

    /**
     * 获取资金流向
     * 
     * @param symbol 交易对代码
     * @param period 统计周期
     * @return 资金流向信息
     */
    MoneyFlowResponse getMoneyFlow(String symbol, String period);

    /**
     * 获取大单监控
     * 
     * @param symbol 交易对代码
     * @param minAmount 最小金额
     * @param limit 记录数量
     * @return 大单交易记录
     */
    List<LargeOrderResponse> getLargeOrders(String symbol, String minAmount, Integer limit);

    /**
     * 获取异常交易监控
     * 
     * @param symbol 交易对代码
     * @param period 监控周期
     * @return 异常交易信息
     */
    AbnormalTradingResponse getAbnormalTrading(String symbol, String period);

    /**
     * 获取市场深度分析
     * 
     * @param symbol 交易对代码
     * @return 深度分析信息
     */
    DepthAnalysisResponse getDepthAnalysis(String symbol);

    /**
     * 获取价格分布
     * 
     * @param symbol 交易对代码
     * @param period 统计周期
     * @return 价格分布信息
     */
    PriceDistributionResponse getPriceDistribution(String symbol, String period);

    /**
     * 获取交易时段分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 交易时段分析
     */
    TradingSessionAnalysisResponse getTradingSessionAnalysis(String symbol, String period);

    /**
     * 获取波动率分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 波动率分析
     */
    VolatilityAnalysisResponse getVolatilityAnalysis(String symbol, String period);

    /**
     * 获取相关性分析
     * 
     * @param symbols 交易对列表
     * @param period 分析周期
     * @return 相关性分析
     */
    CorrelationAnalysisResponse getCorrelationAnalysis(List<String> symbols, String period);

    /**
     * 获取市场周期分析
     * 
     * @param symbol 交易对代码
     * @return 市场周期分析
     */
    MarketCycleAnalysisResponse getMarketCycleAnalysis(String symbol);

    /**
     * 获取趋势分析
     * 
     * @param symbol 交易对代码
     * @param timeframe 时间框架
     * @return 趋势分析
     */
    TrendAnalysisResponse getTrendAnalysis(String symbol, String timeframe);

    /**
     * 获取成交量分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 成交量分析
     */
    VolumeAnalysisResponse getVolumeAnalysis(String symbol, String period);

    /**
     * 获取价格行为分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 价格行为分析
     */
    PriceBehaviorAnalysisResponse getPriceBehaviorAnalysis(String symbol, String period);

    /**
     * 获取市场微观结构分析
     * 
     * @param symbol 交易对代码
     * @return 市场微观结构分析
     */
    MarketMicrostructureResponse getMarketMicrostructure(String symbol);

    /**
     * 获取流动性分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 流动性分析
     */
    LiquidityAnalysisResponse getLiquidityAnalysis(String symbol, String period);

    /**
     * 获取订单簿不平衡分析
     * 
     * @param symbol 交易对代码
     * @return 订单簿不平衡分析
     */
    OrderBookImbalanceResponse getOrderBookImbalance(String symbol);

    /**
     * 获取价格影响分析
     * 
     * @param symbol 交易对代码
     * @param orderSize 订单大小
     * @return 价格影响分析
     */
    PriceImpactAnalysisResponse getPriceImpactAnalysis(String symbol, String orderSize);

    /**
     * 获取套利机会分析
     * 
     * @param symbols 交易对列表
     * @return 套利机会分析
     */
    ArbitrageOpportunityResponse getArbitrageOpportunity(List<String> symbols);

    /**
     * 获取市场效率分析
     * 
     * @param symbol 交易对代码
     * @param period 分析周期
     * @return 市场效率分析
     */
    MarketEfficiencyResponse getMarketEfficiency(String symbol, String period);
}