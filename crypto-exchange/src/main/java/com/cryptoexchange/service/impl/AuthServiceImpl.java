package com.cryptoexchange.service.impl;

import com.cryptoexchange.dto.request.LoginRequest;
import com.cryptoexchange.dto.request.RegisterRequest;
import com.cryptoexchange.dto.request.ResetPasswordRequest;
import com.cryptoexchange.dto.response.LoginResponse;
import com.cryptoexchange.entity.User;
import com.cryptoexchange.exception.BusinessException;
import com.cryptoexchange.security.AuthenticationProvider;
import com.cryptoexchange.service.AuthService;
import com.cryptoexchange.service.UserService;
import com.cryptoexchange.util.JwtUtil;
import com.cryptoexchange.util.RedisUtil;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Random;
import java.util.UUID;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class AuthServiceImpl implements AuthService {

    private static final Logger log = LoggerFactory.getLogger(AuthServiceImpl.class);

    private final UserService userService;
    private final AuthenticationProvider authenticationProvider;
    private final JwtUtil jwtUtil;
    private final RedisUtil redisUtil;
    private final PasswordEncoder passwordEncoder;

    /**
     * 构造函数，使用@Lazy注解解决循环依赖
     */
    public AuthServiceImpl(@Lazy UserService userService,
                          @Lazy AuthenticationProvider authenticationProvider,
                          JwtUtil jwtUtil,
                          RedisUtil redisUtil,
                          PasswordEncoder passwordEncoder) {
        this.userService = userService;
        this.authenticationProvider = authenticationProvider;
        this.jwtUtil = jwtUtil;
        this.redisUtil = redisUtil;
        this.passwordEncoder = passwordEncoder;
    }

    // Redis键前缀
    private static final String EMAIL_CODE_PREFIX = "email_code:";
    private static final String SMS_CODE_PREFIX = "sms_code:";
    private static final String RESET_TOKEN_PREFIX = "reset_token:";
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";
    private static final String LOGIN_ATTEMPT_PREFIX = "login_attempt:";
    
    // 验证码有效期（分钟）
    private static final int CODE_EXPIRE_MINUTES = 5;
    // 重置令牌有效期（分钟）
    private static final int RESET_TOKEN_EXPIRE_MINUTES = 30;
    // 登录尝试限制
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int LOGIN_LOCK_MINUTES = 30;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void register(RegisterRequest request, String clientIp) {
        // 验证邮箱验证码
        if (!verifyCode(request.getEmail(), request.getEmailCode(), "register")) {
            throw new BusinessException("邮箱验证码无效或已过期");
        }
        
        // 验证推荐码（如果提供）
        User referrer = null;
        if (StringUtils.hasText(request.getReferralCode())) {
            referrer = userService.findByReferralCode(request.getReferralCode());
            if (referrer == null) {
                throw new BusinessException("推荐码无效");
            }
        }
        
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setNickname(request.getNickname());
        user.setCountry(request.getCountry());
        user.setLanguage(request.getLanguage());
        user.setReferrerId(referrer != null ? referrer.getId() : null);
        user.setRegisterIp(clientIp);
        
        userService.createUser(user);
        
        // 清除验证码
        clearCode(request.getEmail(), "register");
        
        log.info("用户注册成功: username={}, email={}", request.getUsername(), request.getEmail());
    }

    @Override
    public LoginResponse login(LoginRequest request, String clientIp, String userAgent) {
        // 检查登录尝试次数
        String attemptKey = LOGIN_ATTEMPT_PREFIX + clientIp;
        Object attempts = redisUtil.get(attemptKey);
        if (attempts != null && Integer.parseInt(attempts.toString()) >= MAX_LOGIN_ATTEMPTS) {
            throw new BusinessException("登录尝试次数过多，请稍后再试");
        }
        
        // 查找用户
        User user = findUserByLoginType(request);
        if (user == null) {
            incrementLoginAttempts(clientIp);
            throw new BusinessException("用户名或密码错误");
        }

        // 验证密码
        if (!authenticationProvider.verifyPassword(user, request.getPassword())) {
            incrementLoginAttempts(clientIp);
            throw new BusinessException("用户名或密码错误");
        }
        
        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new BusinessException("账户已被禁用");
        } else if (user.getStatus() == 2) {
            throw new BusinessException("账户已被锁定");
        }
        
        // 验证两步验证（如果启用）
        if (user.getTwoFactorEnabled() && !StringUtils.hasText(request.getTwoFactorCode())) {
            throw new BusinessException("请输入两步验证码");
        }
        
        // 生成令牌
        String accessToken = jwtUtil.generateAccessToken(user.getId());
        String refreshToken = jwtUtil.generateRefreshToken(user.getId());
        
        // 更新登录信息
        authenticationProvider.updateLastLoginTime(user.getId(), clientIp);
        
        // 清除登录尝试记录
        redisUtil.del(attemptKey);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtUtil.getAccessTokenExpiration());
        response.setRefreshExpiresIn(jwtUtil.getRefreshTokenExpiration());
        response.setLoginTime(LocalDateTime.now());
        response.setLastLoginTime(user.getLastLoginTime());
        response.setLoginIp(clientIp);
        response.setRequireTwoFactor(user.getTwoFactorEnabled());
        response.setFirstLogin(user.getLastLoginTime() == null);
        
        // 设置用户信息
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        response.setUserInfo(userInfo);
        
        // 设置权限和角色（这里简化处理）
        response.setPermissions(new ArrayList<>());
        response.setRoles(new ArrayList<>());
        
        log.info("用户登录成功: userId={}, username={}, ip={}", user.getId(), user.getUsername(), clientIp);
        return response;
    }

    @Override
    public void logout(String token) {
        if (StringUtils.hasText(token)) {
            // 将令牌加入黑名单
            Long remainingTime = jwtUtil.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisUtil.set(TOKEN_BLACKLIST_PREFIX + token, "1", remainingTime);
            }
            log.info("用户登出成功: token={}", token.substring(0, Math.min(token.length(), 20)) + "...");
        }
    }

    @Override
    public LoginResponse refreshToken(String refreshToken) {
        // 验证刷新令牌
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            throw new BusinessException("刷新令牌无效或已过期");
        }
        
        // 检查令牌是否在黑名单中
        if (redisUtil.hasKey(TOKEN_BLACKLIST_PREFIX + refreshToken)) {
            throw new BusinessException("刷新令牌已失效");
        }
        
        // 获取用户ID
        Long userId = jwtUtil.getUserIdFromToken(refreshToken);
        if (userId == null) {
            throw new BusinessException("无法从令牌中获取用户信息");
        }
        
        // 查找用户
        User user = userService.getById(userId);
        if (user == null || user.getStatus() != 1) {
            throw new BusinessException("用户不存在或已被禁用");
        }
        
        // 生成新的访问令牌
        String newAccessToken = jwtUtil.generateAccessToken(userId);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(newAccessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtUtil.getAccessTokenExpiration());
        response.setRefreshExpiresIn(jwtUtil.getRefreshTokenExpiration());
        
        log.info("令牌刷新成功: userId={}", userId);
        return response;
    }

    @Override
    public void sendEmailCode(String email, String type) {
        // 生成验证码
        String code = generateCode(email, type);
        
        // TODO: 发送邮件（这里需要集成邮件服务）
        log.info("发送邮箱验证码: email={}, code={}, type={}", email, code, type);
        
        // 实际项目中应该调用邮件服务发送验证码
        // emailService.sendVerificationCode(email, code, type);
    }

    @Override
    public void sendSmsCode(String phone, String type) {
        // 生成验证码
        String code = generateCode(phone, type);
        
        // TODO: 发送短信（这里需要集成短信服务）
        log.info("发送短信验证码: phone={}, code={}, type={}", phone, code, type);
        
        // 实际项目中应该调用短信服务发送验证码
        // smsService.sendVerificationCode(phone, code, type);
    }

    @Override
    public void verifyEmail(String email, String code) {
        if (!verifyCode(email, code, "verify")) {
            throw new BusinessException("验证码无效或已过期");
        }
        
        // 更新用户邮箱验证状态
        User user = userService.findByEmail(email);
        if (user != null) {
            // TODO: 更新邮箱验证状态
            log.info("邮箱验证成功: email={}", email);
        }
        
        clearCode(email, "verify");
    }

    @Override
    public void verifyPhone(String phone, String code) {
        if (!verifyCode(phone, code, "verify")) {
            throw new BusinessException("验证码无效或已过期");
        }
        
        // 更新用户手机验证状态
        User user = userService.findByPhone(phone);
        if (user != null) {
            // TODO: 更新手机验证状态
            log.info("手机验证成功: phone={}", phone);
        }
        
        clearCode(phone, "verify");
    }

    @Override
    public void forgotPassword(String email) {
        User user = userService.findByEmail(email);
        if (user == null) {
            throw new BusinessException("邮箱不存在");
        }
        
        // 生成重置令牌
        String resetToken = UUID.randomUUID().toString();
        redisUtil.set(RESET_TOKEN_PREFIX + email, resetToken, RESET_TOKEN_EXPIRE_MINUTES * 60);
        
        // TODO: 发送重置密码邮件
        log.info("发送重置密码邮件: email={}, resetToken={}", email, resetToken);
        
        // 实际项目中应该发送包含重置链接的邮件
        // emailService.sendPasswordResetEmail(email, resetToken);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(ResetPasswordRequest request) {
        // 验证重置令牌
        Object storedToken = redisUtil.get(RESET_TOKEN_PREFIX + request.getEmail());
        if (storedToken == null || !storedToken.toString().equals(request.getResetToken())) {
            throw new BusinessException("重置令牌无效或已过期");
        }
        
        // 验证验证码
        if (!verifyCode(request.getEmail(), request.getVerificationCode(), "reset_password")) {
            throw new BusinessException("验证码无效或已过期");
        }
        
        // 查找用户
        User user = userService.findByEmail(request.getEmail());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 更新密码
        userService.updatePassword(user.getId(), request.getNewPassword());
        
        // 清除重置令牌和验证码
        redisUtil.del(RESET_TOKEN_PREFIX + request.getEmail());
        clearCode(request.getEmail(), "reset_password");
        
        log.info("密码重置成功: email={}", request.getEmail());
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        return !userService.existsByUsername(username);
    }

    @Override
    public boolean isEmailAvailable(String email) {
        return !userService.existsByEmail(email);
    }

    @Override
    public boolean isPhoneAvailable(String phone) {
        return !userService.existsByPhone(phone);
    }

    @Override
    public Long validateToken(String token) {
        if (!jwtUtil.validateAccessToken(token)) {
            return null;
        }
        
        // 检查令牌是否在黑名单中
        if (redisUtil.hasKey(TOKEN_BLACKLIST_PREFIX + token)) {
            return null;
        }
        
        return jwtUtil.getUserIdFromToken(token);
    }

    @Override
    public Long validateRefreshToken(String refreshToken) {
        if (!jwtUtil.validateRefreshToken(refreshToken)) {
            return null;
        }
        
        // 检查令牌是否在黑名单中
        if (redisUtil.hasKey(TOKEN_BLACKLIST_PREFIX + refreshToken)) {
            return null;
        }
        
        return jwtUtil.getUserIdFromToken(refreshToken);
    }

    @Override
    public String generateAccessToken(Long userId) {
        return jwtUtil.generateAccessToken(userId);
    }

    @Override
    public String generateRefreshToken(Long userId) {
        return jwtUtil.generateRefreshToken(userId);
    }

    @Override
    public void invalidateToken(String token) {
        Long remainingTime = jwtUtil.getTokenRemainingTime(token);
        if (remainingTime > 0) {
            redisUtil.set(TOKEN_BLACKLIST_PREFIX + token, "1", remainingTime);
        }
    }

    @Override
    public boolean verifyCode(String key, String code, String type) {
        String redisKey = getCodeRedisKey(key, type);
        Object storedCode = redisUtil.get(redisKey);
        return storedCode != null && storedCode.toString().equals(code);
    }

    @Override
    public String generateCode(String key, String type) {
        // 生成6位数字验证码
        Random random = new Random();
        String code = String.format("%06d", random.nextInt(1000000));
        
        // 存储到Redis
        String redisKey = getCodeRedisKey(key, type);
        redisUtil.set(redisKey, code, CODE_EXPIRE_MINUTES * 60);
        
        return code;
    }

    @Override
    public void clearCode(String key, String type) {
        String redisKey = getCodeRedisKey(key, type);
        redisUtil.del(redisKey);
    }

    /**
     * 根据登录类型查找用户
     */
    private User findUserByLoginType(LoginRequest request) {
        String username = request.getUsername();

        // 尝试按用户名查找
        User user = authenticationProvider.findUserByUsername(username);
        if (user != null) {
            return user;
        }

        // 尝试按邮箱查找
        if (username.contains("@")) {
            user = authenticationProvider.findUserByEmail(username);
            if (user != null) {
                return user;
            }
        }

        // 尝试按手机号查找
        if (username.matches("^1[3-9]\\d{9}$")) {
            user = userService.findByPhone(username);
            if (user != null) {
                return user;
            }
        }

        return null;
    }

    /**
     * 增加登录尝试次数
     */
    private void incrementLoginAttempts(String clientIp) {
        String attemptKey = LOGIN_ATTEMPT_PREFIX + clientIp;
        Object attempts = redisUtil.get(attemptKey);
        int count = attempts != null ? Integer.parseInt(attempts.toString()) : 0;
        redisUtil.set(attemptKey, count + 1, LOGIN_LOCK_MINUTES * 60);
    }

    /**
     * 获取验证码Redis键
     */
    private String getCodeRedisKey(String key, String type) {
        if (key.contains("@")) {
            return EMAIL_CODE_PREFIX + type + ":" + key;
        } else {
            return SMS_CODE_PREFIX + type + ":" + key;
        }
    }
}