package com.cryptoexchange.service;

import com.cryptoexchange.common.PageResult;
import com.cryptoexchange.common.Result;
import com.cryptoexchange.dto.request.*;
import com.cryptoexchange.dto.response.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合约交易服务接口
 */
public interface FuturesService {
    
    /**
     * 获取合约产品列表
     */
    Result<List<Object>> getContracts();
    
    /**
     * 获取合约产品详情
     */
    Result<Object> getContract(String symbol);
    
    /**
     * 获取用户杠杆账户信息
     */
    Result<Object> getMarginAccount(Long userId);
    
    /**
     * 创建合约订单
     */
    Result<Object> createOrder(FuturesOrderRequest request);
    
    /**
     * 取消合约订单
     */
    Result<Void> cancelOrder(Long userId, String orderId);
    
    /**
     * 批量取消订单
     */
    Result<Void> cancelAllOrders(Long userId, String symbol);
    
    /**
     * 获取用户合约订单
     */
    Result<PageResult<Object>> getUserOrders(Long userId, String symbol, 
                                                           Integer status, Integer pageNum, Integer pageSize);
    
    /**
     * 获取订单详情
     */
    Result<Object> getOrderDetail(Long userId, String orderId);
    
    /**
     * 获取用户持仓
     */
    Result<List<FuturesPositionResponse>> getUserPositions(Long userId, String symbol);
    
    /**
     * 调整持仓保证金
     */
    Result<Void> adjustMargin(Long userId, String symbol, BigDecimal amount, String type);
    
    /**
     * 调整保证金（重载方法）
     * 
     * @param userId 用户ID
     * @param symbol 合约符号
     * @param amount 调整金额
     * @param type 调整类型（1-增加，2-减少）
     * @return 调整结果
     */
    Result<Object> adjustMargin(Long userId, String symbol, BigDecimal amount, Integer type);
    
    /**
     * 调整杠杆倍数
     */
    Result<Void> adjustLeverage(Long userId, String symbol, Integer leverage);
    
    /**
     * 获取用户合约交易记录
     */
    Result<PageResult<FuturesTradeResponse>> getUserTrades(Long userId, String symbol, 
                                                           Integer pageNum, Integer pageSize);
    
    /**
     * 获取强平记录
     */
    Result<PageResult<LiquidationResponse>> getLiquidationRecords(Long userId, String symbol, 
                                                                  Integer pageNum, Integer pageSize);
    
    /**
     * 获取资金费率
     */
    Result<FundingRateResponse> getFundingRate(String symbol);
    
    /**
     * 获取用户资金费用记录
     */
    Result<PageResult<UserFundingFeeResponse>> getUserFundingFees(Long userId, String symbol, 
                                                                  Integer pageNum, Integer pageSize);
    
    /**
     * 计算开仓所需保证金
     */
    Result<BigDecimal> calculateRequiredMargin(String symbol, BigDecimal quantity, 
                                               BigDecimal price, Integer leverage);
    
    /**
     * 计算强平价格
     * 
     * @param userId 用户ID
     * @param symbol 合约符号
     * @param quantity 数量
     * @param price 价格
     * @return 强平价格
     */
    Result<Object> calculateLiquidationPrice(Long userId, String symbol, BigDecimal quantity, BigDecimal price);
    
    /**
     * 获取风险等级
     * 
     * @param userId 用户ID
     * @param symbol 合约符号
     * @return 风险等级
     */
    Result<Object> getRiskLevel(Long userId, String symbol);
    
    /**
     * 执行强平
     */
    Result<Void> executeLiquidation(Long userId, String symbol);
    
    /**
     * 处理资金费用
     */
    Result<Void> processFundingFee(String symbol);
    
    /**
     * 获取合约市场深度
     */
    Result<Object> getMarketDepth(String symbol, Integer limit);
    
    /**
     * 获取合约最新成交
     */
    Result<List<Object>> getRecentTrades(String symbol, Integer limit);
    
    /**
     * 获取合约24小时行情
     */
    Result<Object> get24hrTicker(String symbol);
    
    /**
     * 获取合约K线数据
     */
    Result<List<Object>> getKlines(String symbol, String interval, Integer limit);
    
    /**
     * 获取合约K线数据（详细版本）
     */
    Result<List<Object>> getKlineData(String symbol, String interval, Long startTime, Long endTime, Integer limit);
    
    /**
     * 获取标记价格
     */
    Result<BigDecimal> getMarkPrice(String symbol);
    
    /**
     * 获取指数价格
     */
    Result<BigDecimal> getIndexPrice(String symbol);
    
    /**
     * 资产划转（现货与合约间）
     */
    Result<Void> transfer(Long userId, String asset, BigDecimal amount, Integer type);
    
    /**
     * 资产划转（现货与合约间）
     */
    Result<Void> transferAsset(AssetTransferRequest request);
    
    /**
     * 获取账户余额
     */
    Result<List<Object>> getBalance(Long userId);
    
    /**
     * 获取账户余额（详细版本）
     */
    Result<List<FuturesBalanceResponse>> getAccountBalance(Long userId);
    
    /**
     * 获取持仓风险
     */
    Result<List<Object>> getPositionRisk(Long userId, String symbol);
    

    
    /**
     * 设置止盈止损
     */
    Result<Void> setStopOrder(Long userId, String symbol, BigDecimal stopPrice, BigDecimal takeProfitPrice);
    
    /**
     * 设置止盈止损（详细版本）
     */
    Result<Void> setStopOrder(SetStopOrderRequest request);
    
    /**
     * 获取交易统计
     */
    Result<Object> getTradingStatistics(Long userId, String period);
    

    
    /**
     * 获取保险基金余额
     */
    Result<Object> getInsuranceFund(String symbol);
    
    /**
     * 获取保险基金余额（详细版本）
     */
    Result<BigDecimal> getInsuranceFundBalance();
    
    /**
     * 获取ADL排队位置
     * 
     * @param userId 用户ID
     * @param symbol 合约符号
     * @return ADL排队位置
     */
    Result<Object> getAdlQuantile(Long userId, String symbol);
    
    /**
     * 获取合约规格
     */
    Result<List<Object>> getContractSpecs(String symbol);
    
    /**
     * 获取杠杆档位
     */
    Result<List<Object>> getLeverageBrackets(String symbol);
    
    /**
     * 测试订单
     */
    Result<Object> testOrder(Long userId, FuturesOrderRequest request);
}