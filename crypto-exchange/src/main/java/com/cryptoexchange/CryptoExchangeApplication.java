package com.cryptoexchange;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 虚拟币交易所主启动类
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@MapperScan("com.cryptoexchange.mapper")
public class CryptoExchangeApplication {

    public static void main(String[] args) {
        SpringApplication.run(CryptoExchangeApplication.class, args);
        System.out.println("");
        System.out.println("  ____                  _          _____          _                            ");
        System.out.println(" / ___|_ __ _   _ _ __ | |_ ___   | ____|_  _____| |__   __ _ _ __   __ _  ___ ");
        System.out.println(" | |   | '__| | | | '_ \\| __/ _ \\  |  _| \\ \\/ / __| '_ \\ / _` | '_ \\ / _` |/ _ \\");
        System.out.println(" | |___| |  | |_| | |_) | || (_) | | |___ >  < (__| | | | (_| | | | | (_| |  __/");
        System.out.println("  \\____|_|   \\__, | .__/ \\__\\___/  |_____/_/\\_\\___|_| |_|\\__,_|_| |_|\\__, |\\___|");
        System.out.println("              |___/|_|                                                  |___/     ");
        System.out.println("");
        System.out.println("虚拟币交易所后端服务启动成功！");
        System.out.println("API文档地址: http://localhost:8080/doc.html");
        System.out.println("");
    }
}