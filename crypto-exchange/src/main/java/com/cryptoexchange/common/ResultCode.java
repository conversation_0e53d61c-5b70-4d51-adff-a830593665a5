package com.cryptoexchange.common;

/**
 * 响应码枚举
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum ResultCode {

    // 通用响应码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    PARAM_ERROR(400, "参数错误"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),

    // 认证授权相关
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "访问被禁止"),
    TOKEN_INVALID(4001, "Token无效"),
    TOKEN_EXPIRED(4002, "Token已过期"),
    TOKEN_MISSING(4003, "Token缺失"),
    LOGIN_REQUIRED(4004, "请先登录"),
    PERMISSION_DENIED(4005, "权限不足"),
    ACCOUNT_DISABLED(4006, "账户已被禁用"),
    ACCOUNT_LOCKED(4007, "账户已被锁定"),

    // 用户相关
    USER_NOT_FOUND(5001, "用户不存在"),
    USER_ALREADY_EXISTS(5002, "用户已存在"),
    USERNAME_ALREADY_EXISTS(5003, "用户名已存在"),
    EMAIL_ALREADY_EXISTS(5004, "邮箱已存在"),
    PHONE_ALREADY_EXISTS(5005, "手机号已存在"),
    PASSWORD_ERROR(5006, "密码错误"),
    OLD_PASSWORD_ERROR(5007, "原密码错误"),
    PASSWORD_TOO_WEAK(5008, "密码强度不够"),
    USER_INFO_INCOMPLETE(5009, "用户信息不完整"),
    KYC_NOT_VERIFIED(5010, "实名认证未通过"),
    TWO_FACTOR_REQUIRED(5011, "需要两步验证"),
    TWO_FACTOR_CODE_ERROR(5012, "两步验证码错误"),

    // 交易相关
    TRADING_PAIR_NOT_FOUND(6001, "交易对不存在"),
    TRADING_PAIR_DISABLED(6002, "交易对已禁用"),
    ORDER_NOT_FOUND(6003, "订单不存在"),
    ORDER_ALREADY_FILLED(6004, "订单已成交"),
    ORDER_ALREADY_CANCELLED(6005, "订单已撤销"),
    INSUFFICIENT_BALANCE(6006, "余额不足"),
    PRICE_TOO_HIGH(6007, "价格过高"),
    PRICE_TOO_LOW(6008, "价格过低"),
    QUANTITY_TOO_SMALL(6009, "数量过小"),
    QUANTITY_TOO_LARGE(6010, "数量过大"),
    AMOUNT_TOO_SMALL(6011, "金额过小"),
    AMOUNT_TOO_LARGE(6012, "金额过大"),
    PRICE_PRECISION_ERROR(6013, "价格精度错误"),
    QUANTITY_PRECISION_ERROR(6014, "数量精度错误"),
    TRADING_SUSPENDED(6015, "交易暂停"),
    MARKET_CLOSED(6016, "市场已关闭"),
    ORDER_RATE_LIMIT(6017, "下单频率限制"),
    SELF_TRADING_NOT_ALLOWED(6018, "不允许自成交"),

    // 钱包相关
    WALLET_NOT_FOUND(7001, "钱包不存在"),
    WALLET_DISABLED(7002, "钱包已禁用"),
    WALLET_FROZEN(7003, "钱包已冻结"),
    CURRENCY_NOT_SUPPORTED(7004, "不支持的币种"),
    DEPOSIT_DISABLED(7005, "充值已禁用"),
    WITHDRAW_DISABLED(7006, "提现已禁用"),
    WITHDRAW_AMOUNT_TOO_SMALL(7007, "提现金额过小"),
    WITHDRAW_AMOUNT_TOO_LARGE(7008, "提现金额过大"),
    WITHDRAW_ADDRESS_INVALID(7009, "提现地址无效"),
    WITHDRAW_PASSWORD_REQUIRED(7010, "需要交易密码"),
    WITHDRAW_PASSWORD_ERROR(7011, "交易密码错误"),
    DAILY_WITHDRAW_LIMIT_EXCEEDED(7012, "超过日提现限额"),
    WITHDRAW_FEE_INSUFFICIENT(7013, "提现手续费不足"),
    ADDRESS_ALREADY_EXISTS(7014, "地址已存在"),
    ADDRESS_NOT_WHITELISTED(7015, "地址不在白名单中"),

    // 市场数据相关
    MARKET_DATA_NOT_AVAILABLE(8001, "市场数据不可用"),
    KLINE_DATA_NOT_FOUND(8002, "K线数据不存在"),
    TICKER_DATA_NOT_FOUND(8003, "行情数据不存在"),
    DEPTH_DATA_NOT_FOUND(8004, "深度数据不存在"),
    TRADE_DATA_NOT_FOUND(8005, "成交数据不存在"),

    // 系统相关
    SYSTEM_MAINTENANCE(9001, "系统维护中"),
    SYSTEM_BUSY(9002, "系统繁忙，请稍后重试"),
    RATE_LIMIT_EXCEEDED(9003, "请求频率超限"),
    IP_BLOCKED(9004, "IP已被封禁"),
    CAPTCHA_REQUIRED(9005, "需要验证码"),
    CAPTCHA_ERROR(9006, "验证码错误"),
    CAPTCHA_EXPIRED(9007, "验证码已过期"),
    SMS_CODE_ERROR(9008, "短信验证码错误"),
    SMS_CODE_EXPIRED(9009, "短信验证码已过期"),
    EMAIL_CODE_ERROR(9010, "邮箱验证码错误"),
    EMAIL_CODE_EXPIRED(9011, "邮箱验证码已过期"),
    FILE_UPLOAD_ERROR(9012, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(9013, "不支持的文件类型"),
    FILE_SIZE_EXCEEDED(9014, "文件大小超限"),

    // 第三方服务相关
    THIRD_PARTY_SERVICE_ERROR(10001, "第三方服务错误"),
    PAYMENT_SERVICE_ERROR(10002, "支付服务错误"),
    SMS_SERVICE_ERROR(10003, "短信服务错误"),
    EMAIL_SERVICE_ERROR(10004, "邮件服务错误"),
    BLOCKCHAIN_SERVICE_ERROR(10005, "区块链服务错误"),
    PRICE_SERVICE_ERROR(10006, "价格服务错误");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 构造函数
     */
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * 获取响应码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 根据响应码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return ERROR;
    }
}