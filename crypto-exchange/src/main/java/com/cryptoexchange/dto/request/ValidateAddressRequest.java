package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Map;

/**
 * 验证地址请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "验证地址请求")
public class ValidateAddressRequest {

    @NotBlank(message = "地址不能为空")
    @Schema(description = "要验证的地址", example = "**********************************", required = true)
    private String address;

    @NotBlank(message = "币种不能为空")
    @Schema(description = "币种符号", example = "BTC", required = true)
    private String symbol;

    @Schema(description = "网络类型", example = "MAINNET", allowableValues = {"MAINNET", "TESTNET"})
    private String network = "MAINNET";

    @Schema(description = "验证类型", example = "FORMAT", allowableValues = {"FORMAT", "CHECKSUM", "NETWORK", "FULL"})
    private String validationType = "FULL";

    @Schema(description = "是否验证地址格式", example = "true")
    private Boolean validateFormat = true;

    @Schema(description = "是否验证校验和", example = "true")
    private Boolean validateChecksum = true;

    @Schema(description = "是否验证网络兼容性", example = "true")
    private Boolean validateNetwork = true;

    @Schema(description = "是否验证地址活跃性", example = "false")
    private Boolean validateActivity = false;

    @Schema(description = "是否验证余额", example = "false")
    private Boolean validateBalance = false;

    @Schema(description = "是否检查黑名单", example = "true")
    private Boolean checkBlacklist = true;

    @Schema(description = "是否检查白名单", example = "false")
    private Boolean checkWhitelist = false;

    @Schema(description = "是否检查制裁名单", example = "true")
    private Boolean checkSanctions = true;

    @Schema(description = "是否检查高风险地址", example = "true")
    private Boolean checkHighRisk = true;

    @Schema(description = "地址标签")
    private String[] addressTags;

    @Schema(description = "预期地址类型", example = "P2PKH", allowableValues = {"P2PKH", "P2SH", "P2WPKH", "P2WSH", "P2TR"})
    private String expectedAddressType;

    @Schema(description = "最小余额要求")
    private String minimumBalance;

    @Schema(description = "最大余额限制")
    private String maximumBalance;

    @Schema(description = "验证选项")
    private Map<String, Object> validationOptions;

    @Schema(description = "自定义验证规则")
    private Map<String, String> customRules;

    @Schema(description = "是否返回详细信息", example = "false")
    private Boolean returnDetails = false;

    @Schema(description = "是否缓存验证结果", example = "true")
    private Boolean cacheResult = true;

    @Schema(description = "缓存过期时间（秒）", example = "300")
    private Integer cacheExpiry = 300;

    @Schema(description = "验证超时时间（毫秒）", example = "5000")
    private Integer timeoutMs = 5000;

    @Schema(description = "是否异步验证", example = "false")
    private Boolean asyncValidation = false;

    @Schema(description = "回调URL")
    private String callbackUrl;

    @Schema(description = "验证上下文")
    private Map<String, Object> validationContext;

    @Schema(description = "备注信息")
    private String remarks;

    public ValidateAddressRequest() {}

    public ValidateAddressRequest(String address, String symbol) {
        this.address = address;
        this.symbol = symbol;
        this.network = "MAINNET";
        this.validationType = "FULL";
        this.validateFormat = true;
        this.validateChecksum = true;
        this.validateNetwork = true;
        this.validateActivity = false;
        this.validateBalance = false;
        this.checkBlacklist = true;
        this.checkWhitelist = false;
        this.checkSanctions = true;
        this.checkHighRisk = true;
        this.returnDetails = false;
        this.cacheResult = true;
        this.cacheExpiry = 300;
        this.timeoutMs = 5000;
        this.asyncValidation = false;
    }

    public ValidateAddressRequest(String address, String symbol, String network, String validationType) {
        this.address = address;
        this.symbol = symbol;
        this.network = network;
        this.validationType = validationType;
        this.validateFormat = true;
        this.validateChecksum = true;
        this.validateNetwork = true;
        this.validateActivity = false;
        this.validateBalance = false;
        this.checkBlacklist = true;
        this.checkWhitelist = false;
        this.checkSanctions = true;
        this.checkHighRisk = true;
        this.returnDetails = false;
        this.cacheResult = true;
        this.cacheExpiry = 300;
        this.timeoutMs = 5000;
        this.asyncValidation = false;
    }

    /**
     * 是否需要进行完整验证
     */
    public boolean isFullValidation() {
        return "FULL".equals(validationType);
    }

    /**
     * 获取币种符号
     */
    public String getCurrency() {
        return symbol;
    }

    /**
     * 是否需要进行安全检查
     */
    public boolean needSecurityCheck() {
        return checkBlacklist || checkSanctions || checkHighRisk;
    }

    /**
     * 是否需要进行余额验证
     */
    public boolean needBalanceValidation() {
        return validateBalance || minimumBalance != null || maximumBalance != null;
    }

    /**
     * 是否需要异步处理
     */
    public boolean needAsyncProcessing() {
        return asyncValidation || validateActivity || needBalanceValidation();
    }
}