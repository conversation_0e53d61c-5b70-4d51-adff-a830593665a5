package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Map;

/**
 * 生成地址请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "生成地址请求")
public class GenerateAddressRequest {

    @NotBlank(message = "币种不能为空")
    @Schema(description = "币种符号", example = "BTC", required = true)
    private String symbol;

    @Schema(description = "网络类型", example = "MAINNET", allowableValues = {"MAINNET", "TESTNET"})
    private String network = "MAINNET";

    @Schema(description = "地址类型", example = "DEPOSIT", allowableValues = {"DEPOSIT", "WITHDRAWAL", "INTERNAL"})
    private String addressType = "DEPOSIT";

    @Schema(description = "地址标签", example = "我的BTC地址")
    private String label;

    @Schema(description = "地址用途", example = "TRADING", allowableValues = {"TRADING", "SAVINGS", "STAKING", "LENDING"})
    private String purpose = "TRADING";

    @Schema(description = "是否为子地址", example = "false")
    private Boolean isSubAddress = false;

    @Schema(description = "父地址ID")
    private Long parentAddressId;

    @Schema(description = "派生路径")
    private String derivationPath;

    @Schema(description = "地址索引")
    private Integer addressIndex;

    @Schema(description = "是否启用多重签名", example = "false")
    private Boolean enableMultiSig = false;

    @Schema(description = "多重签名阈值", example = "2")
    private Integer multiSigThreshold;

    @Schema(description = "多重签名公钥列表")
    private String[] multiSigPublicKeys;

    @Schema(description = "地址格式", example = "LEGACY", allowableValues = {"LEGACY", "SEGWIT", "NATIVE_SEGWIT"})
    private String addressFormat = "NATIVE_SEGWIT";

    @Schema(description = "是否压缩公钥", example = "true")
    private Boolean compressedPublicKey = true;

    @Schema(description = "自定义参数")
    private Map<String, Object> customParams;

    @Schema(description = "安全级别", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "ULTRA"})
    private String securityLevel = "HIGH";

    @Schema(description = "是否启用白名单", example = "false")
    private Boolean enableWhitelist = false;

    @Schema(description = "白名单地址列表")
    private String[] whitelistAddresses;

    @Schema(description = "地址有效期（小时）", example = "24")
    private Integer validityHours;

    @Schema(description = "最大使用次数", example = "1")
    private Integer maxUsageCount;

    @Schema(description = "是否自动激活", example = "true")
    private Boolean autoActivate = true;

    @Schema(description = "通知设置")
    private Map<String, Boolean> notificationSettings;

    @Schema(description = "备注信息")
    private String remarks;

    public GenerateAddressRequest() {}

    public GenerateAddressRequest(String symbol) {
        this.symbol = symbol;
        this.network = "MAINNET";
        this.addressType = "DEPOSIT";
        this.purpose = "TRADING";
        this.isSubAddress = false;
        this.enableMultiSig = false;
        this.addressFormat = "NATIVE_SEGWIT";
        this.compressedPublicKey = true;
        this.securityLevel = "HIGH";
        this.enableWhitelist = false;
        this.autoActivate = true;
    }

    public GenerateAddressRequest(String symbol, String network, String addressType) {
        this.symbol = symbol;
        this.network = network;
        this.addressType = addressType;
        this.purpose = "TRADING";
        this.isSubAddress = false;
        this.enableMultiSig = false;
        this.addressFormat = "NATIVE_SEGWIT";
        this.compressedPublicKey = true;
        this.securityLevel = "HIGH";
        this.enableWhitelist = false;
        this.autoActivate = true;
    }

    /**
     * 验证多重签名配置
     */
    public boolean isMultiSigConfigValid() {
        if (!enableMultiSig) {
            return true;
        }
        
        if (multiSigThreshold == null || multiSigPublicKeys == null) {
            return false;
        }
        
        return multiSigThreshold > 0 && 
               multiSigThreshold <= multiSigPublicKeys.length &&
               multiSigPublicKeys.length >= 2;
    }

    /**
     * 验证子地址配置
     */
    public boolean isSubAddressConfigValid() {
        if (!isSubAddress) {
            return true;
        }
        
        return parentAddressId != null && parentAddressId > 0;
    }

    /**
     * 验证白名单配置
     */
    public boolean isWhitelistConfigValid() {
        if (!enableWhitelist) {
            return true;
        }

        return whitelistAddresses != null && whitelistAddresses.length > 0;
    }

    /**
     * 获取币种符号
     */
    public String getCurrency() {
        return symbol;
    }
}