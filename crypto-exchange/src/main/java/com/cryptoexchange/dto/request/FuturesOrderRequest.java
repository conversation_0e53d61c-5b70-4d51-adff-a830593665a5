package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 期货订单请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesOrderRequest {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * 订单方向 (BUY/SELL)
     */
    @NotBlank(message = "订单方向不能为空")
    private String side;
    
    /**
     * 持仓方向 (LONG/SHORT)
     */
    private String positionSide;
    
    /**
     * 订单类型 (MARKET/LIMIT/STOP/TAKE_PROFIT等)
     */
    @NotBlank(message = "订单类型不能为空")
    private String type;
    
    /**
     * 订单数量
     */
    @NotNull(message = "订单数量不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "订单数量必须大于0")
    private BigDecimal quantity;
    
    /**
     * 订单价格 (限价单必填)
     */
    private BigDecimal price;
    
    /**
     * 止损价格
     */
    private BigDecimal stopPrice;
    
    /**
     * 时效类型 (GTC/IOC/FOK等)
     */
    private String timeInForce;
    
    /**
     * 杠杆倍数
     */
    @Min(value = 1, message = "杠杆倍数不能小于1")
    private Integer leverage;
    
    /**
     * 保证金模式 (ISOLATED/CROSS)
     */
    private String marginType;
    
    /**
     * 是否为减仓单
     */
    private Boolean reduceOnly;
    
    /**
     * 是否为PostOnly订单
     */
    private Boolean postOnly;
    
    /**
     * 客户端订单ID
     */
    private String clientOrderId;
    
    /**
     * 触发价格
     */
    private BigDecimal triggerPrice;
    
    /**
     * 触发条件 (MARK_PRICE/CONTRACT_PRICE/INDEX_PRICE)
     */
    private String triggerType;
    
    /**
     * 工作类型 (MARK_PRICE/CONTRACT_PRICE)
     */
    private String workingType;
    
    /**
     * 激活价格
     */
    private BigDecimal activationPrice;
    
    /**
     * 回调比率
     */
    private BigDecimal callbackRate;
    
    /**
     * 订单来源
     */
    private String orderSource;
    
    /**
     * 备注
     */
    private String remark;
}