package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 操作日志查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "操作日志查询请求")
public class OperationLogQueryRequest {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "操作模块")
    private String module;

    @Schema(description = "操作结果")
    private String result;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;

    @Schema(description = "排序字段")
    private String sortField = "createTime";

    @Schema(description = "排序方向")
    private String sortOrder = "DESC";
}
