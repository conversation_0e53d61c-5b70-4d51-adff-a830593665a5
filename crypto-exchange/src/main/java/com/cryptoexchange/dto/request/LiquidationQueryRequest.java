package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 强平记录查询请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LiquidationQueryRequest {
    
    /**
     * 强平记录ID
     */
    private Long liquidationId;
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 强平方向
     */
    private String side;
    
    /**
     * 强平状态
     */
    private String status;
    
    /**
     * 强平类型
     */
    private String type;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 页码
     */
    private Integer page;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortDir;
    
    /**
     * 限制数量
     */
    private Integer limit;
}