package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 聚合交易记录查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AggTradeQueryRequest {

    /**
     * 交易对代码
     */
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    /**
     * 起始聚合交易ID
     */
    private Long fromId;

    /**
     * 结束聚合交易ID
     */
    private Long toId;

    /**
     * 起始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 起始时间戳（毫秒）
     */
    private Long startTimestamp;

    /**
     * 结束时间戳（毫秒）
     */
    private Long endTimestamp;

    /**
     * 返回数量限制
     */
    @Min(value = 1, message = "返回数量不能小于1")
    @Max(value = 1000, message = "返回数量不能大于1000")
    private Integer limit = 500;

    /**
     * 聚合时间间隔（秒）
     */
    private Integer aggregationInterval = 60;

    /**
     * 聚合类型（TIME/VOLUME/COUNT）
     */
    private String aggregationType = "TIME";

    /**
     * 最小聚合数量
     */
    private Integer minAggregationCount = 1;

    /**
     * 最大聚合数量
     */
    private Integer maxAggregationCount;

    /**
     * 交易类型（BUY/SELL）
     */
    private String tradeType;

    /**
     * 最小价格
     */
    private String minPrice;

    /**
     * 最大价格
     */
    private String maxPrice;

    /**
     * 最小数量
     */
    private String minQuantity;

    /**
     * 最大数量
     */
    private String maxQuantity;

    /**
     * 最小成交金额
     */
    private String minAmount;

    /**
     * 最大成交金额
     */
    private String maxAmount;

    /**
     * 是否为做市商交易
     */
    private Boolean isMaker;

    /**
     * 是否包含统计信息
     */
    private Boolean includeStatistics = false;

    /**
     * 是否包含OHLCV数据
     */
    private Boolean includeOHLCV = false;

    /**
     * 是否包含成交量加权平均价格
     */
    private Boolean includeVWAP = false;

    /**
     * 是否包含价格变化信息
     */
    private Boolean includePriceChange = false;

    /**
     * 是否包含波动率信息
     */
    private Boolean includeVolatility = false;

    /**
     * 是否包含流动性分析
     */
    private Boolean includeLiquidity = false;

    /**
     * 是否包含市场深度信息
     */
    private Boolean includeDepth = false;

    /**
     * 是否包含技术指标
     */
    private Boolean includeTechnicalIndicators = false;

    /**
     * 技术指标类型列表
     */
    private List<String> technicalIndicatorTypes;

    /**
     * 是否包含趋势分析
     */
    private Boolean includeTrendAnalysis = false;

    /**
     * 是否包含异常检测
     */
    private Boolean includeAnomalyDetection = false;

    /**
     * 异常检测阈值
     */
    private Double anomalyThreshold;

    /**
     * 数据格式（JSON/CSV/XML）
     */
    private String dataFormat = "JSON";

    /**
     * 排序字段
     */
    private String sortField = "timestamp";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "DESC";

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 1000, message = "每页大小不能大于1000")
    private Integer pageSize = 100;

    /**
     * 时区
     */
    private String timezone = "UTC";

    /**
     * 是否实时数据
     */
    private Boolean realTime = false;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 是否包含元数据
     */
    private Boolean includeMetadata = false;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 订阅ID（用于WebSocket推送）
     */
    private String subscriptionId;

    /**
     * 回调URL（用于异步通知）
     */
    private String callbackUrl;

    /**
     * 是否压缩数据
     */
    private Boolean compressed = false;

    /**
     * 缓存策略
     */
    private String cacheStrategy;

    /**
     * 质量过滤
     */
    private Boolean qualityFilter = false;

    /**
     * 最小质量评分
     */
    private Double minQualityScore;

    /**
     * 是否过滤异常交易
     */
    private Boolean filterAbnormal = false;

    /**
     * 交易对列表（批量查询）
     */
    private List<String> symbols;

    /**
     * 聚合方法（SUM/AVG/MAX/MIN/COUNT）
     */
    private String aggregationMethod = "SUM";

    /**
     * 是否包含子交易详情
     */
    private Boolean includeSubTrades = false;

    /**
     * 最大子交易数量
     */
    private Integer maxSubTradeCount;

    /**
     * 是否包含手续费信息
     */
    private Boolean includeFee = false;

    /**
     * 是否包含价格影响分析
     */
    private Boolean includePriceImpact = false;

    /**
     * 是否包含执行质量分析
     */
    private Boolean includeExecutionQuality = false;

    /**
     * 是否包含滑点分析
     */
    private Boolean includeSlippage = false;

    /**
     * 语言
     */
    private String language = "zh-CN";

    /**
     * 更新频率（秒）
     */
    private Integer updateFrequency;

    /**
     * 是否启用增量更新
     */
    private Boolean incrementalUpdate = false;

    /**
     * 快照间隔（秒）
     */
    private Integer snapshotInterval;

    /**
     * 聚合窗口大小
     */
    private Integer windowSize;

    /**
     * 聚合窗口类型（SLIDING/TUMBLING）
     */
    private String windowType = "TUMBLING";

    /**
     * 是否包含聚合统计
     */
    private Boolean includeAggregationStats = false;

    /**
     * 聚合统计类型列表
     */
    private List<String> aggregationStatsTypes;
}