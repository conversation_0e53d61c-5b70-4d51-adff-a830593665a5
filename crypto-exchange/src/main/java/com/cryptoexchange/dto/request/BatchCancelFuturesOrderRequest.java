package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量取消期货订单请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchCancelFuturesOrderRequest {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 订单ID列表
     */
    private List<Long> orderIds;
    
    /**
     * 客户端订单ID列表
     */
    private List<String> clientOrderIds;
    
    /**
     * 是否取消所有订单
     */
    private Boolean cancelAll;
    
    /**
     * 订单类型过滤
     */
    private String orderType;
    
    /**
     * 订单方向过滤
     */
    private String side;
    
    /**
     * 持仓方向过滤
     */
    private String positionSide;
}