package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户通知查询请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserNotificationQueryRequest {
    
    /**
     * 页码
     */
    @Positive(message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 页面大小
     */
    @Positive(message = "页面大小必须大于0")
    @Max(value = 100, message = "页面大小不能超过100")
    private Integer pageSize = 20;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 通知类型字符串
     */
    private String type;
    
    /**
     * 通知类型
     */
    private List<NotificationType> notificationTypes;
    
    /**
     * 通知状态
     */
    private NotificationStatus status;
    
    /**
     * 优先级
     */
    private NotificationPriority priority;
    
    /**
     * 是否已读
     */
    private Boolean isRead;
    
    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 关键词搜索
     */
    private String keyword;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 发送者类型
     */
    private SenderType senderType;
    
    /**
     * 通知分类
     */
    private List<String> categories;
    
    /**
     * 标签
     */
    private List<String> tags;
    
    /**
     * 排序字段
     */
    private String sortBy = "createTime";
    
    /**
     * 排序方向
     */
    private SortDirection sortDirection = SortDirection.DESC;
    
    /**
     * 是否包含已删除
     */
    private Boolean includeDeleted = false;
    
    /**
     * 通知渠道
     */
    private List<NotificationChannel> channels;
    
    /**
     * 最小重要性等级
     */
    @Min(value = 1, message = "重要性等级最小为1")
    @Max(value = 10, message = "重要性等级最大为10")
    private Integer minImportanceLevel;
    
    /**
     * 最大重要性等级
     */
    @Min(value = 1, message = "重要性等级最小为1")
    @Max(value = 10, message = "重要性等级最大为10")
    private Integer maxImportanceLevel;
    
    /**
     * 是否需要确认
     */
    private Boolean requiresConfirmation;
    
    /**
     * 过期状态
     */
    private ExpirationStatus expirationStatus;
    
    /**
     * 通知来源
     */
    private String source;
    
    /**
     * 关联业务ID
     */
    private String businessId;
    
    /**
     * 关联业务类型
     */
    private String businessType;
    
    /**
     * 通知类型
     */
    public enum NotificationType {
        SYSTEM("系统通知"),
        TRADE("交易通知"),
        SECURITY("安全通知"),
        PROMOTION("推广通知"),
        ANNOUNCEMENT("公告通知"),
        ALERT("警报通知"),
        REMINDER("提醒通知"),
        NEWS("新闻通知"),
        UPDATE("更新通知"),
        MAINTENANCE("维护通知");
        
        private final String description;
        
        NotificationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 通知状态
     */
    public enum NotificationStatus {
        PENDING("待发送"),
        SENT("已发送"),
        DELIVERED("已送达"),
        READ("已读"),
        FAILED("发送失败"),
        EXPIRED("已过期"),
        CANCELLED("已取消");
        
        private final String description;
        
        NotificationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 通知优先级
     */
    public enum NotificationPriority {
        LOW("低优先级"),
        NORMAL("普通优先级"),
        HIGH("高优先级"),
        URGENT("紧急优先级"),
        CRITICAL("严重优先级");
        
        private final String description;
        
        NotificationPriority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 发送者类型
     */
    public enum SenderType {
        SYSTEM("系统"),
        ADMIN("管理员"),
        USER("用户"),
        BOT("机器人"),
        SERVICE("服务"),
        EXTERNAL("外部系统");
        
        private final String description;
        
        SenderType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 排序方向
     */
    public enum SortDirection {
        ASC("升序"),
        DESC("降序");
        
        private final String description;
        
        SortDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 通知渠道
     */
    public enum NotificationChannel {
        IN_APP("应用内通知"),
        EMAIL("邮件通知"),
        SMS("短信通知"),
        PUSH("推送通知"),
        WEBHOOK("Webhook通知"),
        TELEGRAM("Telegram通知"),
        DISCORD("Discord通知"),
        SLACK("Slack通知");
        
        private final String description;
        
        NotificationChannel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 过期状态
     */
    public enum ExpirationStatus {
        NOT_EXPIRED("未过期"),
        EXPIRED("已过期"),
        EXPIRING_SOON("即将过期"),
        NO_EXPIRATION("永不过期");
        
        private final String description;
        
        ExpirationStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}