package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 资产转移请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "资产转移请求")
public class AssetTransferRequest {

    @Schema(description = "用户ID", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "资产类型", required = true)
    @NotBlank(message = "资产类型不能为空")
    private String asset;

    @Schema(description = "转移金额", required = true)
    @NotNull(message = "转移金额不能为空")
    @DecimalMin(value = "0.********", message = "转移金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "转移类型", required = true)
    @NotBlank(message = "转移类型不能为空")
    private String transferType;

    @Schema(description = "源账户类型", required = true)
    @NotBlank(message = "源账户类型不能为空")
    private String fromAccountType;

    @Schema(description = "目标账户类型", required = true)
    @NotBlank(message = "目标账户类型不能为空")
    private String toAccountType;

    @Schema(description = "备注")
    private String remark;
}