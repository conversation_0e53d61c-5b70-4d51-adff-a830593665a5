package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 期货K线查询请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesKlineQueryRequest {
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * K线间隔 (1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M)
     */
    @NotBlank(message = "K线间隔不能为空")
    private String interval;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 限制数量
     */
    private Integer limit;
    
    /**
     * K线类型 (MARK_PRICE/INDEX_PRICE/PREMIUM_INDEX)
     */
    private String klineType;
}