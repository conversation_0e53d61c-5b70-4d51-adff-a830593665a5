package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 创建订单请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "创建订单请求")
public class CreateOrderRequest {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "交易对", example = "BTCUSDT")
    @NotBlank(message = "交易对不能为空")
    private String symbol;

    @Schema(description = "订单类型：MARKET-市价，LIMIT-限价", example = "LIMIT")
    @NotBlank(message = "订单类型不能为空")
    private String orderType;

    @Schema(description = "交易方向：BUY-买入，SELL-卖出", example = "BUY")
    @NotBlank(message = "交易方向不能为空")
    private String side;

    @Schema(description = "数量", example = "0.001")
    @NotNull(message = "数量不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "数量必须大于0")
    private BigDecimal quantity;

    @Schema(description = "价格（限价单必填）", example = "50000.00")
    private BigDecimal price;

    @Schema(description = "客户端订单ID", example = "my_order_123")
    private String clientOrderId;

    @Schema(description = "时效类型：GTC-撤销前有效，IOC-立即成交或撤销，FOK-全部成交或撤销", example = "GTC")
    private String timeInForce = "GTC";

    @Schema(description = "止损价格", example = "49000.00")
    private BigDecimal stopPrice;

    @Schema(description = "冰山订单显示数量", example = "0.0001")
    private BigDecimal icebergQty;
}