package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 注册请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "注册请求")
public class RegisterRequest {

    @Schema(description = "用户名", example = "testuser")
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    private String email;

    @Schema(description = "手机号", example = "13800138000")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    @Schema(description = "密码", example = "123456")
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,}$", 
             message = "密码必须包含至少一个大写字母、一个小写字母和一个数字")
    private String password;

    @Schema(description = "确认密码", example = "123456")
    @NotBlank(message = "确认密码不能为空")
    private String confirmPassword;

    @Schema(description = "昵称", example = "测试用户")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @Schema(description = "邮箱验证码", example = "123456")
    @NotBlank(message = "邮箱验证码不能为空")
    @Size(min = 4, max = 6, message = "验证码长度必须在4-6个字符之间")
    private String emailCode;

    @Schema(description = "短信验证码", example = "123456")
    @Size(min = 4, max = 6, message = "验证码长度必须在4-6个字符之间")
    private String smsCode;

    @Schema(description = "图形验证码", example = "abcd")
    private String captcha;

    @Schema(description = "图形验证码Key", example = "captcha_key_123")
    private String captchaKey;

    @Schema(description = "推荐码", example = "ABC123")
    @Size(max = 20, message = "推荐码长度不能超过20个字符")
    private String referralCode;

    @Schema(description = "国家/地区", example = "CN")
    @Size(max = 10, message = "国家/地区代码长度不能超过10个字符")
    private String country = "CN";

    @Schema(description = "语言偏好", example = "zh-CN")
    @Size(max = 10, message = "语言代码长度不能超过10个字符")
    private String language = "zh-CN";

    @Schema(description = "是否同意用户协议", example = "true")
    @NotNull(message = "必须同意用户协议")
    @AssertTrue(message = "必须同意用户协议")
    private Boolean agreeTerms;

    @Schema(description = "是否同意隐私政策", example = "true")
    @NotNull(message = "必须同意隐私政策")
    @AssertTrue(message = "必须同意隐私政策")
    private Boolean agreePrivacy;

    @Schema(description = "是否订阅营销邮件", example = "false")
    private Boolean subscribeMarketing = false;

    /**
     * 验证密码是否一致
     */
    @AssertTrue(message = "两次输入的密码不一致")
    public boolean isPasswordMatching() {
        if (password == null || confirmPassword == null) {
            return false;
        }
        return password.equals(confirmPassword);
    }
}