package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 价格查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "价格查询请求")
public class PriceQueryRequest {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对代码列表")
    @Size(max = 100, message = "交易对数量不能超过100个")
    private List<String> symbols;

    @Schema(description = "价格类型")
    @Pattern(regexp = "^(CURRENT|BEST_BID|BEST_ASK|LAST_TRADE|WEIGHTED_AVERAGE|VWAP|TWAP)$", 
             message = "价格类型必须是: CURRENT, BEST_BID, BEST_ASK, LAST_TRADE, WEIGHTED_AVERAGE, VWAP, TWAP")
    private String priceType = "CURRENT";

    @Schema(description = "计价货币")
    private String quoteCurrency;

    @Schema(description = "价格精度")
    @Min(value = 0, message = "价格精度不能小于0")
    @Max(value = 8, message = "价格精度不能大于8")
    private Integer precision;

    @Schema(description = "是否包含24小时统计")
    private Boolean include24hStats = false;

    @Schema(description = "是否包含价格变化")
    private Boolean includePriceChange = false;

    @Schema(description = "是否包含成交量")
    private Boolean includeVolume = false;

    @Schema(description = "是否包含市值")
    private Boolean includeMarketCap = false;

    @Schema(description = "是否包含流通量")
    private Boolean includeCirculatingSupply = false;

    @Schema(description = "是否包含技术指标")
    private Boolean includeTechnicalIndicators = false;

    @Schema(description = "技术指标类型")
    private List<String> technicalIndicators;

    @Schema(description = "是否包含价格预测")
    private Boolean includePricePrediction = false;

    @Schema(description = "预测时间范围（小时）")
    @Min(value = 1, message = "预测时间范围不能小于1小时")
    @Max(value = 168, message = "预测时间范围不能大于168小时")
    private Integer predictionHours = 24;

    @Schema(description = "是否包含价格警报")
    private Boolean includePriceAlerts = false;

    @Schema(description = "是否包含支撑阻力位")
    private Boolean includeSupportResistance = false;

    @Schema(description = "是否包含价格分析")
    private Boolean includePriceAnalysis = false;

    @Schema(description = "是否包含趋势分析")
    private Boolean includeTrendAnalysis = false;

    @Schema(description = "是否包含波动率分析")
    private Boolean includeVolatilityAnalysis = false;

    @Schema(description = "是否包含相关性分析")
    private Boolean includeCorrelationAnalysis = false;

    @Schema(description = "相关性基准货币")
    private String correlationBaseCurrency = "BTC";

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|AGGREGATED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, AGGREGATED")
    private String format = "STANDARD";

    @Schema(description = "更新频率")
    @Pattern(regexp = "^(REAL_TIME|1S|5S|10S|30S|1M|5M)$", 
             message = "更新频率必须是: REAL_TIME, 1S, 5S, 10S, 30S, 1M, 5M")
    private String updateFrequency = "REAL_TIME";

    @Schema(description = "是否实时数据")
    private Boolean realTime = true;

    @Schema(description = "是否压缩数据")
    private Boolean compressed = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|MEDIUM_CACHE|LONG_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, MEDIUM_CACHE, LONG_CACHE")
    private String cacheStrategy = "SHORT_CACHE";

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|AGGREGATED|THIRD_PARTY|ORACLE)$", 
             message = "数据来源必须是: EXCHANGE, AGGREGATED, THIRD_PARTY, ORACLE")
    private String dataSource = "EXCHANGE";

    @Schema(description = "质量过滤")
    private Boolean qualityFilter = true;

    @Schema(description = "最小质量评分")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "1.0", message = "质量评分不能大于1")
    private Double minQualityScore = 0.9;

    @Schema(description = "是否过滤异常价格")
    private Boolean filterAnomalies = true;

    @Schema(description = "异常检测阈值")
    @DecimalMin(value = "0.0", message = "异常检测阈值不能小于0")
    @DecimalMax(value = "1.0", message = "异常检测阈值不能大于1")
    private Double anomalyThreshold = 0.1;

    @Schema(description = "排序字段")
    @Pattern(regexp = "^(symbol|price|priceChange|volume|marketCap)$", 
             message = "排序字段必须是: symbol, price, priceChange, volume, marketCap")
    private String sortBy = "symbol";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "ASC";

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 100;

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "语言")
    @Pattern(regexp = "^(zh|en|ja|ko)$", message = "语言必须是: zh, en, ja, ko")
    private String language = "zh";

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "订阅ID（用于WebSocket）")
    private String subscriptionId;

    @Schema(description = "回调URL（用于Webhook）")
    private String callbackUrl;
}