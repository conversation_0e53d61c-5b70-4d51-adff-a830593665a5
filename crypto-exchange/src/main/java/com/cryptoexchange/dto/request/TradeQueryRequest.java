package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易记录查询请求")
public class TradeQueryRequest {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易ID")
    private String tradeId;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "交易类型")
    @Pattern(regexp = "^(BUY|SELL)$", message = "交易类型必须是: BUY, SELL")
    private String side;

    @Schema(description = "交易方式")
    @Pattern(regexp = "^(MARKET|LIMIT|STOP|STOP_LIMIT|OCO|ICEBERG)$", 
             message = "交易方式必须是: MARKET, LIMIT, STOP, STOP_LIMIT, OCO, ICEBERG")
    private String orderType;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "开始时间戳（毫秒）")
    private Long startTimestamp;

    @Schema(description = "结束时间戳（毫秒）")
    private Long endTimestamp;

    @Schema(description = "最小价格")
    @DecimalMin(value = "0", message = "最小价格不能小于0")
    private BigDecimal minPrice;

    @Schema(description = "最大价格")
    @DecimalMin(value = "0", message = "最大价格不能小于0")
    private BigDecimal maxPrice;

    @Schema(description = "最小数量")
    @DecimalMin(value = "0", message = "最小数量不能小于0")
    private BigDecimal minQuantity;

    @Schema(description = "最大数量")
    @DecimalMin(value = "0", message = "最大数量不能小于0")
    private BigDecimal maxQuantity;

    @Schema(description = "最小成交金额")
    @DecimalMin(value = "0", message = "最小成交金额不能小于0")
    private BigDecimal minAmount;

    @Schema(description = "最大成交金额")
    @DecimalMin(value = "0", message = "最大成交金额不能小于0")
    private BigDecimal maxAmount;

    @Schema(description = "是否为做市商")
    private Boolean isMaker;

    @Schema(description = "是否为最优价格")
    private Boolean isBestMatch;

    @Schema(description = "交易来源")
    @Pattern(regexp = "^(WEB|MOBILE|API|ALGORITHM|MARKET_MAKER)$", 
             message = "交易来源必须是: WEB, MOBILE, API, ALGORITHM, MARKET_MAKER")
    private String tradeSource;

    @Schema(description = "交易状态")
    @Pattern(regexp = "^(FILLED|PARTIALLY_FILLED|CANCELED|REJECTED|EXPIRED)$", 
             message = "交易状态必须是: FILLED, PARTIALLY_FILLED, CANCELED, REJECTED, EXPIRED")
    private String tradeStatus;

    @Schema(description = "排序字段")
    @Pattern(regexp = "^(tradeTime|price|quantity|amount|tradeId)$", 
             message = "排序字段必须是: tradeTime, price, quantity, amount, tradeId")
    private String sortBy = "tradeTime";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "DESC";

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 100;

    @Schema(description = "返回数量限制")
    @Min(value = 1, message = "返回数量必须大于0")
    @Max(value = 1000, message = "返回数量不能超过1000")
    private Integer limit = 500;

    @Schema(description = "是否包含手续费信息")
    private Boolean includeFeeInfo = false;

    @Schema(description = "是否包含订单信息")
    private Boolean includeOrderInfo = false;

    @Schema(description = "是否包含用户信息")
    private Boolean includeUserInfo = false;

    @Schema(description = "是否包含市场影响分析")
    private Boolean includeMarketImpact = false;

    @Schema(description = "是否包含流动性分析")
    private Boolean includeLiquidityAnalysis = false;

    @Schema(description = "是否包含价格影响分析")
    private Boolean includePriceImpact = false;

    @Schema(description = "是否包含执行质量分析")
    private Boolean includeExecutionQuality = false;

    @Schema(description = "是否包含滑点分析")
    private Boolean includeSlippageAnalysis = false;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|AGGREGATED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, AGGREGATED")
    private String format = "STANDARD";

    @Schema(description = "聚合级别")
    @Pattern(regexp = "^(NONE|MINUTE|HOUR|DAY)$", 
             message = "聚合级别必须是: NONE, MINUTE, HOUR, DAY")
    private String aggregationLevel = "NONE";

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "是否实时数据")
    private Boolean realTime = false;

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|CACHE|ARCHIVE)$", 
             message = "数据来源必须是: EXCHANGE, CACHE, ARCHIVE")
    private String dataSource = "EXCHANGE";

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;
}