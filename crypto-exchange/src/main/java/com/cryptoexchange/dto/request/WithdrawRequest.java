package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 提现请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "提现请求")
public class WithdrawRequest {

    @Schema(description = "币种", example = "BTC")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Schema(description = "提现金额", example = "0.001")
    @NotNull(message = "提现金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "提现金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "提现地址", example = "**********************************")
    @NotBlank(message = "提现地址不能为空")
    private String address;

    @Schema(description = "网络类型", example = "BTC")
    @NotBlank(message = "网络类型不能为空")
    private String network;

    @Schema(description = "地址标签", example = "我的钱包")
    private String addressTag;

    @Schema(description = "备注", example = "提现备注")
    private String memo;

    @Schema(description = "交易密码", example = "123456")
    @NotBlank(message = "交易密码不能为空")
    private String tradingPassword;

    @Schema(description = "两步验证码", example = "123456")
    private String twoFactorCode;

    @Schema(description = "邮箱验证码", example = "123456")
    private String emailCode;

    @Schema(description = "短信验证码", example = "123456")
    private String smsCode;
}