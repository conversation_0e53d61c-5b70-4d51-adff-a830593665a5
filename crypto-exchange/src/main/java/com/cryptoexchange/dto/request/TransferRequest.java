package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 转账请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "转账请求")
public class TransferRequest {

    @Schema(description = "币种", example = "USDT")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Schema(description = "转账金额", example = "100.00")
    @NotNull(message = "转账金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "转账金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "转出账户类型：SPOT-现货，FUTURES-合约，MARGIN-杠杆", example = "SPOT")
    @NotBlank(message = "转出账户类型不能为空")
    private String fromAccountType;

    @Schema(description = "转入账户类型：SPOT-现货，FUTURES-合约，MARGIN-杠杆", example = "FUTURES")
    @NotBlank(message = "转入账户类型不能为空")
    private String toAccountType;

    @Schema(description = "收款用户ID（内部转账）", example = "123456")
    private Long toUserId;

    @Schema(description = "收款用户名（内部转账）", example = "user123")
    private String toUsername;

    @Schema(description = "收款邮箱（内部转账）", example = "<EMAIL>")
    private String toEmail;

    @Schema(description = "备注", example = "转账备注")
    private String memo;

    @Schema(description = "交易密码", example = "123456")
    private String tradingPassword;

    @Schema(description = "两步验证码", example = "123456")
    private String twoFactorCode;
}