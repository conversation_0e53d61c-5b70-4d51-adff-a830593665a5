package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;

/**
 * 资产快照查询请求
 */
public class AssetSnapshotQueryRequest {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 资产类型：SPOT-现货，FUTURES-期货，ALL-全部
     */
    private String assetType;
    
    /**
     * 资产名称
     */
    private String asset;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 快照类型：DAILY-日快照，WEEKLY-周快照，MONTHLY-月快照
     */
    private String snapshotType;
    
    /**
     * 页码
     */
    @Min(1)
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    @Min(1)
    @Max(1000)
    private Integer pageSize = 100;
    
    // Getters and Setters
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getAssetType() {
        return assetType;
    }
    
    public void setAssetType(String assetType) {
        this.assetType = assetType;
    }
    
    public String getAsset() {
        return asset;
    }
    
    public void setAsset(String asset) {
        this.asset = asset;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
    
    public String getSnapshotType() {
        return snapshotType;
    }
    
    public void setSnapshotType(String snapshotType) {
        this.snapshotType = snapshotType;
    }
    
    public Integer getPageNum() {
        return pageNum;
    }
    
    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
    
    public Integer getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}