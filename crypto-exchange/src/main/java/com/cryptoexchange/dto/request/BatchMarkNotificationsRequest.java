package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量标记通知请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "批量标记通知请求")
public class BatchMarkNotificationsRequest {

    @NotEmpty(message = "通知ID列表不能为空")
    @Schema(description = "通知ID列表", example = "[123456, 123457, 123458]", required = true)
    private List<Long> notificationIds;

    @NotNull(message = "操作类型不能为空")
    @Schema(description = "操作类型", example = "READ", allowableValues = {"read", "unread", "delete", "important", "unimportant"}, required = true)
    private String action;

    @Schema(description = "是否全选", example = "false")
    private Boolean selectAll = false;

    @Schema(description = "过滤条件 - 通知类型", example = "TRADE")
    private String filterType;

    @Schema(description = "过滤条件 - 是否已读")
    private Boolean filterRead;

    @Schema(description = "过滤条件 - 是否重要")
    private Boolean filterImportant;

    @Schema(description = "过滤条件 - 开始时间", example = "2024-01-01 00:00:00")
    private String startTime;

    @Schema(description = "过滤条件 - 结束时间", example = "2024-01-31 23:59:59")
    private String endTime;

    public BatchMarkNotificationsRequest() {}

    public BatchMarkNotificationsRequest(List<Long> notificationIds, String action) {
        this.notificationIds = notificationIds;
        this.action = action;
        this.selectAll = false;
    }

    public BatchMarkNotificationsRequest(String action, Boolean selectAll) {
        this.action = action;
        this.selectAll = selectAll;
    }
}