package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 期货订单查询请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesOrderQueryRequest {
    
    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 客户端订单ID
     */
    private String clientOrderId;
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 订单状态
     */
    private String status;
    
    /**
     * 订单类型
     */
    private String type;
    
    /**
     * 订单方向
     */
    private String side;
    
    /**
     * 持仓方向
     */
    private String positionSide;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 页码
     */
    private Integer page;
    
    /**
     * 每页大小
     */
    private Integer size;
    
    /**
     * 排序字段
     */
    private String sortBy;
    
    /**
     * 排序方向
     */
    private String sortDir;
    
    /**
     * 限制数量
     */
    private Integer limit;
}