package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 设置交易密码请求
 */
public class SetTradingPasswordRequest {
    
    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    @Size(min = 6, max = 20, message = "交易密码长度必须在6-20个字符之间")
    @Pattern(regexp = "^\\d{6,20}$", message = "交易密码只能包含数字")
    private String tradingPassword;
    
    /**
     * 确认交易密码
     */
    @NotBlank(message = "确认交易密码不能为空")
    private String confirmTradingPassword;
    
    /**
     * 验证码
     */
    private String verificationCode;
    
    /**
     * 验证码类型：EMAIL-邮箱验证码，SMS-短信验证码，TOTP-谷歌验证码
     */
    private String verificationType;
    
    // Getters and Setters
    public String getTradingPassword() {
        return tradingPassword;
    }
    
    public void setTradingPassword(String tradingPassword) {
        this.tradingPassword = tradingPassword;
    }
    
    public String getConfirmTradingPassword() {
        return confirmTradingPassword;
    }
    
    public void setConfirmTradingPassword(String confirmTradingPassword) {
        this.confirmTradingPassword = confirmTradingPassword;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getVerificationType() {
        return verificationType;
    }
    
    public void setVerificationType(String verificationType) {
        this.verificationType = verificationType;
    }
}