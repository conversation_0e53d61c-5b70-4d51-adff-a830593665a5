package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 余额变动记录查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "余额变动记录查询请求")
public class BalanceChangeQueryRequest {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "变动类型：1-充值，2-提现，3-交易，4-手续费，5-转账，6-奖励，7-其他")
    private Integer type;

    @Schema(description = "变动方向：1-增加，2-减少")
    private Integer direction;

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 20;

    @Schema(description = "排序字段")
    private String sortBy = "createTime";

    @Schema(description = "排序方向：ASC-升序，DESC-降序")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是ASC或DESC")
    private String sortOrder = "DESC";
}
