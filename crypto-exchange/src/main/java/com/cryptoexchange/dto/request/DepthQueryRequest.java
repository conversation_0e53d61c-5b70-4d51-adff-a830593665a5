package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 市场深度查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场深度查询请求")
public class DepthQueryRequest {

    @Schema(description = "交易对代码", required = true)
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    @Schema(description = "深度级别")
    @Min(value = 5, message = "深度级别不能小于5")
    @Max(value = 5000, message = "深度级别不能大于5000")
    private Integer limit = 100;

    @Schema(description = "价格精度")
    @Min(value = 0, message = "价格精度不能小于0")
    @Max(value = 8, message = "价格精度不能大于8")
    private Integer pricePrecision;

    @Schema(description = "数量精度")
    @Min(value = 0, message = "数量精度不能小于0")
    @Max(value = 8, message = "数量精度不能大于8")
    private Integer quantityPrecision;

    @Schema(description = "深度类型")
    @Pattern(regexp = "^(FULL|AGGREGATED|TOP_LEVEL)$", 
             message = "深度类型必须是: FULL, AGGREGATED, TOP_LEVEL")
    private String depthType = "FULL";

    @Schema(description = "聚合级别")
    @DecimalMin(value = "0", message = "聚合级别不能小于0")
    private BigDecimal aggregationLevel;

    @Schema(description = "是否包含统计信息")
    private Boolean includeStatistics = false;

    @Schema(description = "是否包含流动性分析")
    private Boolean includeLiquidityAnalysis = false;

    @Schema(description = "是否包含深度分析")
    private Boolean includeDepthAnalysis = false;

    @Schema(description = "是否包含价差分析")
    private Boolean includeSpreadAnalysis = false;

    @Schema(description = "是否包含订单簿不平衡分析")
    private Boolean includeImbalanceAnalysis = false;

    @Schema(description = "是否包含市场冲击成本")
    private Boolean includeMarketImpactCost = false;

    @Schema(description = "是否包含价格影响分析")
    private Boolean includePriceImpactAnalysis = false;

    @Schema(description = "是否包含流动性指标")
    private Boolean includeLiquidityMetrics = false;

    @Schema(description = "是否包含深度质量评估")
    private Boolean includeDepthQuality = false;

    @Schema(description = "是否包含历史对比")
    private Boolean includeHistoricalComparison = false;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|COMPRESSED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, COMPRESSED")
    private String format = "STANDARD";

    @Schema(description = "更新频率")
    @Pattern(regexp = "^(REAL_TIME|1S|5S|10S|30S|1M)$", 
             message = "更新频率必须是: REAL_TIME, 1S, 5S, 10S, 30S, 1M")
    private String updateFrequency = "REAL_TIME";

    @Schema(description = "是否实时数据")
    private Boolean realTime = true;

    @Schema(description = "是否压缩数据")
    private Boolean compressed = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|MEDIUM_CACHE|LONG_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, MEDIUM_CACHE, LONG_CACHE")
    private String cacheStrategy = "SHORT_CACHE";

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|AGGREGATED|THIRD_PARTY)$", 
             message = "数据来源必须是: EXCHANGE, AGGREGATED, THIRD_PARTY")
    private String dataSource = "EXCHANGE";

    @Schema(description = "质量过滤")
    private Boolean qualityFilter = true;

    @Schema(description = "最小质量评分")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "1.0", message = "质量评分不能大于1")
    private Double minQualityScore = 0.9;

    @Schema(description = "是否过滤异常数据")
    private Boolean filterAnomalies = true;

    @Schema(description = "异常检测阈值")
    @DecimalMin(value = "0.0", message = "异常检测阈值不能小于0")
    @DecimalMax(value = "1.0", message = "异常检测阈值不能大于1")
    private Double anomalyThreshold = 0.05;

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "订阅ID（用于WebSocket）")
    private String subscriptionId;

    @Schema(description = "回调URL（用于Webhook）")
    private String callbackUrl;

    @Schema(description = "是否启用增量更新")
    private Boolean incrementalUpdate = false;

    @Schema(description = "快照间隔（秒）")
    @Min(value = 1, message = "快照间隔不能小于1秒")
    @Max(value = 3600, message = "快照间隔不能大于3600秒")
    private Integer snapshotInterval = 60;
}