package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 历史交易记录查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistoricalTradeQueryRequest {

    /**
     * 交易对代码
     */
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    /**
     * 起始交易ID
     */
    private Long fromId;

    /**
     * 结束交易ID
     */
    private Long toId;

    /**
     * 起始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 起始时间戳（毫秒）
     */
    private Long startTimestamp;

    /**
     * 结束时间戳（毫秒）
     */
    private Long endTimestamp;

    /**
     * 返回数量限制
     */
    @Min(value = 1, message = "返回数量不能小于1")
    @Max(value = 1000, message = "返回数量不能大于1000")
    private Integer limit = 500;

    /**
     * 交易类型（BUY/SELL）
     */
    private String tradeType;

    /**
     * 交易方式（MARKET/LIMIT/STOP_LOSS等）
     */
    private String tradeMethod;

    /**
     * 最小价格
     */
    private String minPrice;

    /**
     * 最大价格
     */
    private String maxPrice;

    /**
     * 最小数量
     */
    private String minQuantity;

    /**
     * 最大数量
     */
    private String maxQuantity;

    /**
     * 最小成交金额
     */
    private String minAmount;

    /**
     * 最大成交金额
     */
    private String maxAmount;

    /**
     * 是否为做市商交易
     */
    private Boolean isMaker;

    /**
     * 是否为最优价格交易
     */
    private Boolean isBestPrice;

    /**
     * 交易来源
     */
    private String tradeSource;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 是否包含手续费信息
     */
    private Boolean includeFee = false;

    /**
     * 是否包含订单信息
     */
    private Boolean includeOrder = false;

    /**
     * 是否包含用户信息
     */
    private Boolean includeUser = false;

    /**
     * 是否包含市场影响分析
     */
    private Boolean includeMarketImpact = false;

    /**
     * 是否包含流动性分析
     */
    private Boolean includeLiquidity = false;

    /**
     * 是否包含价格影响分析
     */
    private Boolean includePriceImpact = false;

    /**
     * 是否包含执行质量分析
     */
    private Boolean includeExecutionQuality = false;

    /**
     * 是否包含滑点分析
     */
    private Boolean includeSlippage = false;

    /**
     * 数据格式（JSON/CSV/XML）
     */
    private String dataFormat = "JSON";

    /**
     * 聚合级别（TRADE/MINUTE/HOUR/DAY）
     */
    private String aggregationLevel = "TRADE";

    /**
     * 排序字段
     */
    private String sortField = "timestamp";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "DESC";

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 1000, message = "每页大小不能大于1000")
    private Integer pageSize = 100;

    /**
     * 时区
     */
    private String timezone = "UTC";

    /**
     * 是否实时数据
     */
    private Boolean realTime = false;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 是否包含元数据
     */
    private Boolean includeMetadata = false;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 订阅ID（用于WebSocket推送）
     */
    private String subscriptionId;

    /**
     * 回调URL（用于异步通知）
     */
    private String callbackUrl;

    /**
     * 是否压缩数据
     */
    private Boolean compressed = false;

    /**
     * 缓存策略
     */
    private String cacheStrategy;

    /**
     * 质量过滤
     */
    private Boolean qualityFilter = false;

    /**
     * 最小质量评分
     */
    private Double minQualityScore;

    /**
     * 是否过滤异常交易
     */
    private Boolean filterAbnormal = false;

    /**
     * 异常检测阈值
     */
    private Double abnormalThreshold;

    /**
     * 交易对列表（批量查询）
     */
    private List<String> symbols;

    /**
     * 用户ID列表（管理员查询）
     */
    private List<String> userIds;

    /**
     * 交易ID列表（精确查询）
     */
    private List<Long> tradeIds;

    /**
     * 是否包含技术指标
     */
    private Boolean includeTechnicalIndicators = false;

    /**
     * 技术指标类型列表
     */
    private List<String> technicalIndicatorTypes;

    /**
     * 是否包含统计信息
     */
    private Boolean includeStatistics = false;

    /**
     * 统计周期
     */
    private String statisticsPeriod;

    /**
     * 是否包含趋势分析
     */
    private Boolean includeTrendAnalysis = false;

    /**
     * 趋势分析周期
     */
    private String trendAnalysisPeriod;

    /**
     * 是否包含波动率分析
     */
    private Boolean includeVolatilityAnalysis = false;

    /**
     * 波动率分析周期
     */
    private String volatilityAnalysisPeriod;

    /**
     * 是否包含相关性分析
     */
    private Boolean includeCorrelationAnalysis = false;

    /**
     * 相关性分析基准货币
     */
    private String correlationBaseCurrency;

    /**
     * 语言
     */
    private String language = "zh-CN";

    /**
     * 更新频率（秒）
     */
    private Integer updateFrequency;

    /**
     * 是否启用增量更新
     */
    private Boolean incrementalUpdate = false;

    /**
     * 快照间隔（秒）
     */
    private Integer snapshotInterval;
}