package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * KYC审核请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "KYC审核请求")
public class KycReviewRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "123456", required = true)
    private Long userId;

    @NotBlank(message = "审核结果不能为空")
    @Schema(description = "审核结果", example = "APPROVED", allowableValues = {"APPROVED", "REJECTED", "PENDING"}, required = true)
    private String reviewResult;

    @Schema(description = "审核意见", example = "身份信息验证通过")
    private String reviewComment;

    @Schema(description = "拒绝原因", example = "身份证照片不清晰")
    private String rejectReason;

    @Schema(description = "拒绝原因代码", example = "PHOTO_UNCLEAR")
    private String rejectReasonCode;

    @Schema(description = "审核等级", example = "LEVEL_2", allowableValues = {"LEVEL_1", "LEVEL_2", "LEVEL_3"})
    private String kycLevel;

    @Schema(description = "审核人员", example = "admin")
    private String reviewer;

    @Schema(description = "审核部门", example = "COMPLIANCE")
    private String reviewDepartment;

    @Schema(description = "风险评分", example = "85")
    private Integer riskScore;

    @Schema(description = "风险等级", example = "LOW", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
    private String riskLevel;

    @Schema(description = "需要补充的材料")
    private List<String> requiredDocuments;

    @Schema(description = "审核标签")
    private List<String> reviewTags;

    @Schema(description = "是否需要人工复审", example = "false")
    private Boolean needManualReview = false;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    @Schema(description = "审核优先级", example = "NORMAL", allowableValues = {"LOW", "NORMAL", "HIGH", "URGENT"})
    private String priority = "NORMAL";

    @Schema(description = "有效期(天)", example = "365")
    private Integer validityDays;

    @Schema(description = "限制说明")
    private String restrictions;

    @Schema(description = "内部备注")
    private String internalNotes;

    @Schema(description = "合规检查结果")
    private String complianceResult;

    @Schema(description = "反洗钱检查结果")
    private String amlResult;

    @Schema(description = "制裁名单检查结果")
    private String sanctionResult;

    public KycReviewRequest() {}

    public KycReviewRequest(Long userId, String reviewResult, String reviewComment) {
        this.userId = userId;
        this.reviewResult = reviewResult;
        this.reviewComment = reviewComment;
        this.needManualReview = false;
        this.sendNotification = true;
        this.priority = "NORMAL";
    }

    public KycReviewRequest(Long userId, String reviewResult, String rejectReason, String rejectReasonCode) {
        this.userId = userId;
        this.reviewResult = reviewResult;
        this.rejectReason = rejectReason;
        this.rejectReasonCode = rejectReasonCode;
        this.needManualReview = false;
        this.sendNotification = true;
        this.priority = "NORMAL";
    }
}