package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 行情数据查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "行情数据查询请求")
public class TickerQueryRequest {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对代码列表")
    @Size(max = 100, message = "交易对数量不能超过100个")
    private List<String> symbols;

    @Schema(description = "行情类型")
    @Pattern(regexp = "^(24HR|MINI|FULL)$", 
             message = "行情类型必须是: 24HR, MINI, FULL")
    private String tickerType = "24HR";

    @Schema(description = "基础货币")
    private String baseAsset;

    @Schema(description = "计价货币")
    private String quoteAsset;

    @Schema(description = "市场分类")
    private List<String> categories;

    @Schema(description = "交易状态")
    @Pattern(regexp = "^(TRADING|HALT|BREAK|AUCTION_MATCH|PRE_TRADING|POST_TRADING)$", 
             message = "交易状态必须是: TRADING, HALT, BREAK, AUCTION_MATCH, PRE_TRADING, POST_TRADING")
    private String status;

    @Schema(description = "是否支持现货交易")
    private Boolean spotTradingEnabled;

    @Schema(description = "是否支持杠杆交易")
    private Boolean marginTradingEnabled;

    @Schema(description = "是否支持期货交易")
    private Boolean futuresTradingEnabled;

    @Schema(description = "最小24小时成交量")
    @DecimalMin(value = "0", message = "最小成交量不能小于0")
    private String minVolume24h;

    @Schema(description = "最大24小时成交量")
    @DecimalMin(value = "0", message = "最大成交量不能小于0")
    private String maxVolume24h;

    @Schema(description = "最小24小时价格变化百分比")
    private String minPriceChangePercent24h;

    @Schema(description = "最大24小时价格变化百分比")
    private String maxPriceChangePercent24h;

    @Schema(description = "最小市值")
    @DecimalMin(value = "0", message = "最小市值不能小于0")
    private String minMarketCap;

    @Schema(description = "最大市值")
    @DecimalMin(value = "0", message = "最大市值不能小于0")
    private String maxMarketCap;

    @Schema(description = "是否包含技术指标")
    private Boolean includeTechnicalIndicators = false;

    @Schema(description = "技术指标类型")
    private List<String> technicalIndicators;

    @Schema(description = "是否包含市场深度信息")
    private Boolean includeDepthInfo = false;

    @Schema(description = "是否包含最近交易信息")
    private Boolean includeRecentTrades = false;

    @Schema(description = "是否包含K线数据")
    private Boolean includeKlineData = false;

    @Schema(description = "K线时间间隔")
    @Pattern(regexp = "^(1m|5m|15m|30m|1h|4h|1d)$", 
             message = "K线时间间隔必须是: 1m, 5m, 15m, 30m, 1h, 4h, 1d")
    private String klineInterval = "1h";

    @Schema(description = "是否包含流动性分析")
    private Boolean includeLiquidityAnalysis = false;

    @Schema(description = "是否包含波动率分析")
    private Boolean includeVolatilityAnalysis = false;

    @Schema(description = "是否包含趋势分析")
    private Boolean includeTrendAnalysis = false;

    @Schema(description = "是否包含相关性分析")
    private Boolean includeCorrelationAnalysis = false;

    @Schema(description = "相关性基准货币")
    private String correlationBaseCurrency = "BTC";

    @Schema(description = "是否包含项目信息")
    private Boolean includeProjectInfo = false;

    @Schema(description = "是否包含社交媒体信息")
    private Boolean includeSocialMediaInfo = false;

    @Schema(description = "排序字段")
    @Pattern(regexp = "^(symbol|price|priceChangePercent|volume|marketCap|count)$", 
             message = "排序字段必须是: symbol, price, priceChangePercent, volume, marketCap, count")
    private String sortBy = "volume";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "DESC";

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 100;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|AGGREGATED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, AGGREGATED")
    private String format = "STANDARD";

    @Schema(description = "更新频率")
    @Pattern(regexp = "^(REAL_TIME|1S|5S|10S|30S|1M|5M)$", 
             message = "更新频率必须是: REAL_TIME, 1S, 5S, 10S, 30S, 1M, 5M")
    private String updateFrequency = "REAL_TIME";

    @Schema(description = "是否实时数据")
    private Boolean realTime = true;

    @Schema(description = "是否压缩数据")
    private Boolean compressed = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|MEDIUM_CACHE|LONG_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, MEDIUM_CACHE, LONG_CACHE")
    private String cacheStrategy = "SHORT_CACHE";

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|AGGREGATED|THIRD_PARTY)$", 
             message = "数据来源必须是: EXCHANGE, AGGREGATED, THIRD_PARTY")
    private String dataSource = "EXCHANGE";

    @Schema(description = "质量过滤")
    private Boolean qualityFilter = true;

    @Schema(description = "最小质量评分")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "1.0", message = "质量评分不能大于1")
    private Double minQualityScore = 0.9;

    @Schema(description = "是否过滤异常数据")
    private Boolean filterAnomalies = true;

    @Schema(description = "异常检测阈值")
    @DecimalMin(value = "0.0", message = "异常检测阈值不能小于0")
    @DecimalMax(value = "1.0", message = "异常检测阈值不能大于1")
    private Double anomalyThreshold = 0.1;

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "语言")
    @Pattern(regexp = "^(zh|en|ja|ko)$", message = "语言必须是: zh, en, ja, ko")
    private String language = "zh";

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "订阅ID（用于WebSocket）")
    private String subscriptionId;

    @Schema(description = "回调URL（用于Webhook）")
    private String callbackUrl;
}