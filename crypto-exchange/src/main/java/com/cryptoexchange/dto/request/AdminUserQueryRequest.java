package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 管理员用户查询请求
 */
@Data
public class AdminUserQueryRequest {
    
    /**
     * 页码
     */
    @Positive(message = "页码必须为正数")
    private Integer pageNum = 1;
    
    /**
     * 页面大小
     */
    @Min(value = 1, message = "页面大小不能小于1")
    @Max(value = 100, message = "页面大小不能超过100")
    private Integer pageSize = 10;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 用户状态
     */
    private UserStatus status;
    
    /**
     * 用户类型
     */
    private UserType userType;
    
    /**
     * KYC状态
     */
    private KycStatus kycStatus;
    
    /**
     * 是否启用2FA
     */
    private Boolean twoFactorEnabled;
    
    /**
     * 注册时间开始
     */
    private LocalDateTime registrationTimeStart;
    
    /**
     * 注册时间结束
     */
    private LocalDateTime registrationTimeEnd;
    
    /**
     * 最后登录时间开始
     */
    private LocalDateTime lastLoginTimeStart;
    
    /**
     * 最后登录时间结束
     */
    private LocalDateTime lastLoginTimeEnd;
    
    /**
     * 用户等级
     */
    private List<Integer> userLevels;
    
    /**
     * 国家/地区
     */
    private String country;
    
    /**
     * 是否VIP用户
     */
    private Boolean isVip;
    
    /**
     * 风险等级
     */
    private RiskLevel riskLevel;
    
    /**
     * 排序字段
     */
    private String sortField = "registrationTime";
    
    /**
     * 排序方向
     */
    private SortDirection sortDirection = SortDirection.DESC;
    
    /**
     * 是否包含已删除用户
     */
    private Boolean includeDeleted = false;
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        SUSPENDED("暂停"),
        FROZEN("冻结"),
        DELETED("已删除");
        
        private final String description;
        
        UserStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 用户类型枚举
     */
    public enum UserType {
        INDIVIDUAL("个人用户"),
        ENTERPRISE("企业用户"),
        INSTITUTIONAL("机构用户"),
        VIP("VIP用户");
        
        private final String description;
        
        UserType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * KYC状态枚举
     */
    public enum KycStatus {
        NOT_SUBMITTED("未提交"),
        PENDING("待审核"),
        APPROVED("已通过"),
        REJECTED("已拒绝"),
        EXPIRED("已过期");
        
        private final String description;
        
        KycStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 风险等级枚举
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中风险"),
        HIGH("高风险"),
        CRITICAL("极高风险");
        
        private final String description;
        
        RiskLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        ASC("升序"),
        DESC("降序");
        
        private final String description;
        
        SortDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}