package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;

/**
 * 更新安全设置请求
 */
public class UpdateSecuritySettingsRequest {
    
    /**
     * 是否启用邮箱通知
     */
    private Boolean emailNotificationEnabled;
    
    /**
     * 是否启用短信通知
     */
    private Boolean smsNotificationEnabled;
    
    /**
     * 是否启用登录保护
     */
    private Boolean loginProtectionEnabled;
    
    /**
     * 是否启用交易保护
     */
    private Boolean tradingProtectionEnabled;
    
    /**
     * 是否启用提现保护
     */
    private Boolean withdrawalProtectionEnabled;
    
    /**
     * 登录IP白名单
     */
    private String[] allowedIps;
    
    /**
     * 会话超时时间（分钟）
     */
    private Integer sessionTimeout;
    
    // Getters and Setters
    public Boolean getEmailNotificationEnabled() {
        return emailNotificationEnabled;
    }
    
    public void setEmailNotificationEnabled(Boolean emailNotificationEnabled) {
        this.emailNotificationEnabled = emailNotificationEnabled;
    }
    
    public Boolean getSmsNotificationEnabled() {
        return smsNotificationEnabled;
    }
    
    public void setSmsNotificationEnabled(Boolean smsNotificationEnabled) {
        this.smsNotificationEnabled = smsNotificationEnabled;
    }
    
    public Boolean getLoginProtectionEnabled() {
        return loginProtectionEnabled;
    }
    
    public void setLoginProtectionEnabled(Boolean loginProtectionEnabled) {
        this.loginProtectionEnabled = loginProtectionEnabled;
    }
    
    public Boolean getTradingProtectionEnabled() {
        return tradingProtectionEnabled;
    }
    
    public void setTradingProtectionEnabled(Boolean tradingProtectionEnabled) {
        this.tradingProtectionEnabled = tradingProtectionEnabled;
    }
    
    public Boolean getWithdrawalProtectionEnabled() {
        return withdrawalProtectionEnabled;
    }
    
    public void setWithdrawalProtectionEnabled(Boolean withdrawalProtectionEnabled) {
        this.withdrawalProtectionEnabled = withdrawalProtectionEnabled;
    }
    
    public String[] getAllowedIps() {
        return allowedIps;
    }
    
    public void setAllowedIps(String[] allowedIps) {
        this.allowedIps = allowedIps;
    }
    
    public Integer getSessionTimeout() {
        return sessionTimeout;
    }
    
    public void setSessionTimeout(Integer sessionTimeout) {
        this.sessionTimeout = sessionTimeout;
    }
}