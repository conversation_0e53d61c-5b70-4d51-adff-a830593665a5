package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * 交易对查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易对查询请求")
public class SymbolQueryRequest {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "基础货币")
    private String baseAsset;

    @Schema(description = "计价货币")
    private String quoteAsset;

    @Schema(description = "交易状态")
    @Pattern(regexp = "^(TRADING|HALT|BREAK|AUCTION_MATCH|PRE_TRADING|POST_TRADING)$", 
             message = "交易状态必须是: TRADING, HALT, BREAK, AUCTION_MATCH, PRE_TRADING, POST_TRADING")
    private String status;

    @Schema(description = "交易类型")
    @Pattern(regexp = "^(SPOT|MARGIN|FUTURES|OPTIONS)$", 
             message = "交易类型必须是: SPOT, MARGIN, FUTURES, OPTIONS")
    private String tradeType;

    @Schema(description = "市场分类")
    private String category;

    @Schema(description = "是否支持现货交易")
    private Boolean spotTradingEnabled;

    @Schema(description = "是否支持杠杆交易")
    private Boolean marginTradingEnabled;

    @Schema(description = "是否支持期货交易")
    private Boolean futuresTradingEnabled;

    @Schema(description = "是否支持期权交易")
    private Boolean optionsTradingEnabled;

    @Schema(description = "是否为新币")
    private Boolean isNewListing;

    @Schema(description = "风险等级")
    @Pattern(regexp = "^(LOW|MEDIUM|HIGH|EXTREME)$", 
             message = "风险等级必须是: LOW, MEDIUM, HIGH, EXTREME")
    private String riskLevel;

    @Schema(description = "最小价格")
    private String minPrice;

    @Schema(description = "最大价格")
    private String maxPrice;

    @Schema(description = "最小24小时成交量")
    private String minVolume24h;

    @Schema(description = "最大24小时成交量")
    private String maxVolume24h;

    @Schema(description = "最小24小时价格变化百分比")
    private String minPriceChangePercent24h;

    @Schema(description = "最大24小时价格变化百分比")
    private String maxPriceChangePercent24h;

    @Schema(description = "排序字段")
    @Pattern(regexp = "^(symbol|price|volume|priceChangePercent|marketCap|listingTime)$", 
             message = "排序字段必须是: symbol, price, volume, priceChangePercent, marketCap, listingTime")
    private String sortBy = "symbol";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "ASC";

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 100;

    @Schema(description = "包含的字段")
    private List<String> includeFields;

    @Schema(description = "排除的字段")
    private List<String> excludeFields;

    @Schema(description = "是否包含统计信息")
    private Boolean includeStats = false;

    @Schema(description = "是否包含技术指标")
    private Boolean includeTechnicalIndicators = false;

    @Schema(description = "是否包含项目信息")
    private Boolean includeProjectInfo = false;

    @Schema(description = "语言")
    @Pattern(regexp = "^(zh|en|ja|ko)$", message = "语言必须是: zh, en, ja, ko")
    private String language = "zh";

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";
}