package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 实名认证请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "实名认证请求")
public class RealNameAuthRequest {

    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 2, max = 50, message = "真实姓名长度必须在2-50个字符之间")
    @Schema(description = "真实姓名", example = "张三", required = true)
    private String realName;

    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    @Schema(description = "身份证号", example = "110101199001011234", required = true)
    private String idCardNumber;

    @NotBlank(message = "身份证正面照片不能为空")
    @Schema(description = "身份证正面照片URL", example = "https://example.com/id-front.jpg", required = true)
    private String idCardFrontUrl;

    @NotBlank(message = "身份证反面照片不能为空")
    @Schema(description = "身份证反面照片URL", example = "https://example.com/id-back.jpg", required = true)
    private String idCardBackUrl;

    @NotBlank(message = "手持身份证照片不能为空")
    @Schema(description = "手持身份证照片URL", example = "https://example.com/id-hand.jpg", required = true)
    private String idCardHandUrl;

    @Schema(description = "国籍", example = "中国")
    private String nationality = "中国";

    @Schema(description = "职业", example = "软件工程师")
    private String occupation;

    @Schema(description = "详细地址", example = "北京市朝阳区xxx街道xxx号")
    private String address;

    @Schema(description = "认证类型", example = "PERSONAL", allowableValues = {"PERSONAL", "ENTERPRISE"})
    private String authType = "PERSONAL";

    @Schema(description = "备注信息")
    private String remarks;

    public RealNameAuthRequest() {}

    public RealNameAuthRequest(String realName, String idCardNumber, String idCardFrontUrl, 
                             String idCardBackUrl, String idCardHandUrl) {
        this.realName = realName;
        this.idCardNumber = idCardNumber;
        this.idCardFrontUrl = idCardFrontUrl;
        this.idCardBackUrl = idCardBackUrl;
        this.idCardHandUrl = idCardHandUrl;
    }
}