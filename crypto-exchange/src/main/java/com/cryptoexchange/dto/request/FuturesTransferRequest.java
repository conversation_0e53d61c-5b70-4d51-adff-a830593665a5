package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 期货转账请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesTransferRequest {
    
    /**
     * 币种
     */
    @NotBlank(message = "币种不能为空")
    private String asset;
    
    /**
     * 转账金额
     */
    @NotNull(message = "转账金额不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "转账金额必须大于0")
    private BigDecimal amount;
    
    /**
     * 转账类型 (1: 现货账户转入期货账户, 2: 期货账户转入现货账户)
     */
    @NotNull(message = "转账类型不能为空")
    private Integer type;
    
    /**
     * 客户端转账ID
     */
    private String clientTranId;
    
    /**
     * 备注
     */
    private String remark;
}