package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 历史K线数据查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HistoricalKlineQueryRequest {

    /**
     * 交易对代码
     */
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    /**
     * 时间间隔（1s/1m/3m/5m/15m/30m/1h/2h/4h/6h/8h/12h/1d/3d/1w/1M）
     */
    @NotBlank(message = "时间间隔不能为空")
    private String interval;

    /**
     * 起始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 起始时间戳（毫秒）
     */
    private Long startTimestamp;

    /**
     * 结束时间戳（毫秒）
     */
    private Long endTimestamp;

    /**
     * 返回数量限制
     */
    @Min(value = 1, message = "返回数量不能小于1")
    @Max(value = 1000, message = "返回数量不能大于1000")
    private Integer limit = 500;

    /**
     * K线类型（STANDARD/HEIKIN_ASHI/RENKO/KAGI/POINT_FIGURE）
     */
    private String klineType = "STANDARD";

    /**
     * 价格类型（CLOSE/OHLC/TYPICAL/WEIGHTED）
     */
    private String priceType = "OHLC";

    /**
     * 价格精度
     */
    private Integer pricePrecision;

    /**
     * 数量精度
     */
    private Integer quantityPrecision;

    /**
     * 是否包含成交量
     */
    private Boolean includeVolume = true;

    /**
     * 是否包含成交额
     */
    private Boolean includeAmount = true;

    /**
     * 是否包含成交笔数
     */
    private Boolean includeCount = false;

    /**
     * 是否包含主动买入成交量
     */
    private Boolean includeTakerBuyVolume = false;

    /**
     * 是否包含主动买入成交额
     */
    private Boolean includeTakerBuyAmount = false;

    /**
     * 是否包含技术指标
     */
    private Boolean includeTechnicalIndicators = false;

    /**
     * 技术指标类型列表
     */
    private List<String> technicalIndicatorTypes;

    /**
     * 移动平均线周期列表
     */
    private List<Integer> maPeriods;

    /**
     * 是否包含布林带
     */
    private Boolean includeBollingerBands = false;

    /**
     * 布林带周期
     */
    private Integer bollingerPeriod = 20;

    /**
     * 布林带标准差倍数
     */
    private Double bollingerStdDev = 2.0;

    /**
     * 是否包含MACD
     */
    private Boolean includeMacd = false;

    /**
     * MACD快线周期
     */
    private Integer macdFastPeriod = 12;

    /**
     * MACD慢线周期
     */
    private Integer macdSlowPeriod = 26;

    /**
     * MACD信号线周期
     */
    private Integer macdSignalPeriod = 9;

    /**
     * 是否包含RSI
     */
    private Boolean includeRsi = false;

    /**
     * RSI周期
     */
    private Integer rsiPeriod = 14;

    /**
     * 是否包含KDJ
     */
    private Boolean includeKdj = false;

    /**
     * KDJ K值周期
     */
    private Integer kdjKPeriod = 9;

    /**
     * KDJ D值周期
     */
    private Integer kdjDPeriod = 3;

    /**
     * KDJ J值周期
     */
    private Integer kdjJPeriod = 3;

    /**
     * 是否包含威廉指标
     */
    private Boolean includeWilliamsR = false;

    /**
     * 威廉指标周期
     */
    private Integer williamsRPeriod = 14;

    /**
     * 是否包含CCI
     */
    private Boolean includeCci = false;

    /**
     * CCI周期
     */
    private Integer cciPeriod = 20;

    /**
     * 是否包含ATR
     */
    private Boolean includeAtr = false;

    /**
     * ATR周期
     */
    private Integer atrPeriod = 14;

    /**
     * 是否包含OBV
     */
    private Boolean includeObv = false;

    /**
     * 是否包含SAR
     */
    private Boolean includeSar = false;

    /**
     * SAR加速因子
     */
    private Double sarAcceleration = 0.02;

    /**
     * SAR最大加速因子
     */
    private Double sarMaximum = 0.2;

    /**
     * 是否包含斐波那契回调
     */
    private Boolean includeFibonacci = false;

    /**
     * 斐波那契计算周期
     */
    private Integer fibonacciPeriod;

    /**
     * 是否包含支撑阻力位
     */
    private Boolean includeSupportResistance = false;

    /**
     * 支撑阻力位计算周期
     */
    private Integer supportResistancePeriod;

    /**
     * 是否包含趋势线
     */
    private Boolean includeTrendLines = false;

    /**
     * 趋势线计算周期
     */
    private Integer trendLinePeriod;

    /**
     * 是否包含形态识别
     */
    private Boolean includePatternRecognition = false;

    /**
     * 形态类型列表
     */
    private List<String> patternTypes;

    /**
     * 是否包含波动率分析
     */
    private Boolean includeVolatilityAnalysis = false;

    /**
     * 波动率计算周期
     */
    private Integer volatilityPeriod;

    /**
     * 是否包含相关性分析
     */
    private Boolean includeCorrelationAnalysis = false;

    /**
     * 相关性基准交易对
     */
    private String correlationBaseSymbol;

    /**
     * 相关性计算周期
     */
    private Integer correlationPeriod;

    /**
     * 是否包含季节性分析
     */
    private Boolean includeSeasonalityAnalysis = false;

    /**
     * 季节性分析周期
     */
    private String seasonalityPeriod;

    /**
     * 数据格式（JSON/CSV/XML）
     */
    private String dataFormat = "JSON";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "ASC";

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 1000, message = "每页大小不能大于1000")
    private Integer pageSize = 100;

    /**
     * 时区
     */
    private String timezone = "UTC";

    /**
     * 是否实时数据
     */
    private Boolean realTime = false;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 是否包含元数据
     */
    private Boolean includeMetadata = false;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 订阅ID（用于WebSocket推送）
     */
    private String subscriptionId;

    /**
     * 回调URL（用于异步通知）
     */
    private String callbackUrl;

    /**
     * 是否压缩数据
     */
    private Boolean compressed = false;

    /**
     * 缓存策略
     */
    private String cacheStrategy;

    /**
     * 质量过滤
     */
    private Boolean qualityFilter = false;

    /**
     * 最小质量评分
     */
    private Double minQualityScore;

    /**
     * 是否过滤异常数据
     */
    private Boolean filterAbnormal = false;

    /**
     * 异常检测阈值
     */
    private Double abnormalThreshold;

    /**
     * 交易对列表（批量查询）
     */
    private List<String> symbols;

    /**
     * 时间间隔列表（批量查询）
     */
    private List<String> intervals;

    /**
     * 语言
     */
    private String language = "zh-CN";

    /**
     * 更新频率（秒）
     */
    private Integer updateFrequency;

    /**
     * 是否启用增量更新
     */
    private Boolean incrementalUpdate = false;

    /**
     * 快照间隔（秒）
     */
    private Integer snapshotInterval;

    /**
     * 数据压缩级别
     */
    private Integer compressionLevel;

    /**
     * 是否包含预测数据
     */
    private Boolean includeForecast = false;

    /**
     * 预测周期数
     */
    private Integer forecastPeriods;

    /**
     * 预测模型类型
     */
    private String forecastModel;

    /**
     * 是否包含异常检测
     */
    private Boolean includeAnomalyDetection = false;

    /**
     * 异常检测模型类型
     */
    private String anomalyDetectionModel;

    /**
     * 是否包含数据质量评估
     */
    private Boolean includeQualityAssessment = false;

    /**
     * 质量评估指标列表
     */
    private List<String> qualityMetrics;
}