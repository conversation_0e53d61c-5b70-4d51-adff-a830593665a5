package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场统计查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场统计查询请求")
public class MarketStatsQueryRequest {

    @Schema(description = "统计类型")
    @Pattern(regexp = "^(OVERVIEW|DETAILED|CATEGORY|REGIONAL|TRENDING|PERFORMANCE)$", 
             message = "统计类型必须是: OVERVIEW, DETAILED, CATEGORY, REGIONAL, TRENDING, PERFORMANCE")
    private String statsType = "OVERVIEW";

    @Schema(description = "统计周期")
    @Pattern(regexp = "^(1h|4h|24h|7d|30d|90d|1y|ALL)$", 
             message = "统计周期必须是: 1h, 4h, 24h, 7d, 30d, 90d, 1y, ALL")
    private String period = "24h";

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "市场分类")
    private List<String> categories;

    @Schema(description = "地区")
    private List<String> regions;

    @Schema(description = "交易对列表")
    @Size(max = 100, message = "交易对数量不能超过100个")
    private List<String> symbols;

    @Schema(description = "基础货币列表")
    private List<String> baseAssets;

    @Schema(description = "计价货币列表")
    private List<String> quoteAssets;

    @Schema(description = "最小市值")
    @DecimalMin(value = "0", message = "最小市值不能小于0")
    private String minMarketCap;

    @Schema(description = "最大市值")
    @DecimalMin(value = "0", message = "最大市值不能小于0")
    private String maxMarketCap;

    @Schema(description = "最小24小时成交量")
    @DecimalMin(value = "0", message = "最小成交量不能小于0")
    private String minVolume24h;

    @Schema(description = "最大24小时成交量")
    @DecimalMin(value = "0", message = "最大成交量不能小于0")
    private String maxVolume24h;

    @Schema(description = "最小价格变化百分比")
    private String minPriceChangePercent;

    @Schema(description = "最大价格变化百分比")
    private String maxPriceChangePercent;

    @Schema(description = "是否包含总体统计")
    private Boolean includeOverallStats = true;

    @Schema(description = "是否包含分类统计")
    private Boolean includeCategoryStats = false;

    @Schema(description = "是否包含地区统计")
    private Boolean includeRegionalStats = false;

    @Schema(description = "是否包含用户统计")
    private Boolean includeUserStats = false;

    @Schema(description = "是否包含交易统计")
    private Boolean includeTradingStats = false;

    @Schema(description = "是否包含流动性统计")
    private Boolean includeLiquidityStats = false;

    @Schema(description = "是否包含风险统计")
    private Boolean includeRiskStats = false;

    @Schema(description = "是否包含技术指标")
    private Boolean includeTechnicalIndicators = false;

    @Schema(description = "是否包含历史对比")
    private Boolean includeHistoricalComparison = false;

    @Schema(description = "是否包含预测指标")
    private Boolean includePredictionMetrics = false;

    @Schema(description = "是否包含排行榜")
    private Boolean includeRankings = false;

    @Schema(description = "排行榜类型")
    private List<String> rankingTypes;

    @Schema(description = "排行榜数量")
    @Min(value = 1, message = "排行榜数量必须大于0")
    @Max(value = 100, message = "排行榜数量不能超过100")
    private Integer rankingLimit = 10;

    @Schema(description = "是否包含热门交易对")
    private Boolean includeHotTradingPairs = false;

    @Schema(description = "热门交易对数量")
    @Min(value = 1, message = "热门交易对数量必须大于0")
    @Max(value = 50, message = "热门交易对数量不能超过50")
    private Integer hotPairsLimit = 10;

    @Schema(description = "是否包含新币统计")
    private Boolean includeNewListings = false;

    @Schema(description = "新币时间范围（天）")
    @Min(value = 1, message = "新币时间范围不能小于1天")
    @Max(value = 365, message = "新币时间范围不能超过365天")
    private Integer newListingDays = 30;

    @Schema(description = "是否包含市场情绪")
    private Boolean includeMarketSentiment = false;

    @Schema(description = "是否包含恐慌贪婪指数")
    private Boolean includeFearGreedIndex = false;

    @Schema(description = "是否包含资金流向")
    private Boolean includeMoneyFlow = false;

    @Schema(description = "是否包含大单监控")
    private Boolean includeLargeOrderMonitoring = false;

    @Schema(description = "大单阈值")
    @DecimalMin(value = "0", message = "大单阈值不能小于0")
    private String largeOrderThreshold;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|AGGREGATED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, AGGREGATED")
    private String format = "STANDARD";

    @Schema(description = "聚合级别")
    @Pattern(regexp = "^(NONE|HOUR|DAY|WEEK|MONTH)$", 
             message = "聚合级别必须是: NONE, HOUR, DAY, WEEK, MONTH")
    private String aggregationLevel = "NONE";

    @Schema(description = "排序字段")
    @Pattern(regexp = "^(marketCap|volume|priceChange|tradingPairs|users)$", 
             message = "排序字段必须是: marketCap, volume, priceChange, tradingPairs, users")
    private String sortBy = "marketCap";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "DESC";

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer page = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer size = 100;

    @Schema(description = "是否实时数据")
    private Boolean realTime = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|MEDIUM_CACHE|LONG_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, MEDIUM_CACHE, LONG_CACHE")
    private String cacheStrategy = "MEDIUM_CACHE";

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|AGGREGATED|THIRD_PARTY)$", 
             message = "数据来源必须是: EXCHANGE, AGGREGATED, THIRD_PARTY")
    private String dataSource = "EXCHANGE";

    @Schema(description = "是否包含数据质量信息")
    private Boolean includeDataQuality = false;

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "语言")
    @Pattern(regexp = "^(zh|en|ja|ko)$", message = "语言必须是: zh, en, ja, ko")
    private String language = "zh";

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;
}