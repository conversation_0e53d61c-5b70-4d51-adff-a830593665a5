package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 交易对配置请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SymbolConfigRequest {

    /**
     * 交易对代码
     */
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    /**
     * 基础货币
     */
    @NotBlank(message = "基础货币不能为空")
    private String baseAsset;

    /**
     * 计价货币
     */
    @NotBlank(message = "计价货币不能为空")
    private String quoteAsset;

    /**
     * 交易对状态（TRADING/HALT/BREAK）
     */
    @NotBlank(message = "交易对状态不能为空")
    private String status;

    /**
     * 交易对类型（SPOT/MARGIN/FUTURES/OPTIONS）
     */
    private String symbolType = "SPOT";

    /**
     * 是否支持现货交易
     */
    private Boolean spotTradingAllowed = true;

    /**
     * 是否支持杠杆交易
     */
    private Boolean marginTradingAllowed = false;

    /**
     * 是否支持期货交易
     */
    private Boolean futuresTradingAllowed = false;

    /**
     * 是否支持期权交易
     */
    private Boolean optionsTradingAllowed = false;

    /**
     * 最小订单数量
     */
    @NotNull(message = "最小订单数量不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "最小订单数量必须大于0")
    private BigDecimal minQty;

    /**
     * 最大订单数量
     */
    @NotNull(message = "最大订单数量不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "最大订单数量必须大于0")
    private BigDecimal maxQty;

    /**
     * 数量步长
     */
    @NotNull(message = "数量步长不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "数量步长必须大于0")
    private BigDecimal stepSize;

    /**
     * 最小订单金额
     */
    @NotNull(message = "最小订单金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "最小订单金额必须大于0")
    private BigDecimal minNotional;

    /**
     * 最大订单金额
     */
    private BigDecimal maxNotional;

    /**
     * 价格精度
     */
    @NotNull(message = "价格精度不能为空")
    @Min(value = 0, message = "价格精度不能小于0")
    @Max(value = 18, message = "价格精度不能大于18")
    private Integer pricePrecision;

    /**
     * 数量精度
     */
    @NotNull(message = "数量精度不能为空")
    @Min(value = 0, message = "数量精度不能小于0")
    @Max(value = 18, message = "数量精度不能大于18")
    private Integer quantityPrecision;

    /**
     * 基础资产精度
     */
    @Min(value = 0, message = "基础资产精度不能小于0")
    @Max(value = 18, message = "基础资产精度不能大于18")
    private Integer baseAssetPrecision;

    /**
     * 计价资产精度
     */
    @Min(value = 0, message = "计价资产精度不能小于0")
    @Max(value = 18, message = "计价资产精度不能大于18")
    private Integer quoteAssetPrecision;

    /**
     * 最小价格变动
     */
    @NotNull(message = "最小价格变动不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "最小价格变动必须大于0")
    private BigDecimal tickSize;

    /**
     * 交易手续费率
     */
    @DecimalMin(value = "0", message = "交易手续费率不能小于0")
    private BigDecimal tradingFeeRate;

    /**
     * Maker手续费率
     */
    @DecimalMin(value = "0", message = "Maker手续费率不能小于0")
    private BigDecimal makerFeeRate;

    /**
     * Taker手续费率
     */
    @DecimalMin(value = "0", message = "Taker手续费率不能小于0")
    private BigDecimal takerFeeRate;

    /**
     * 杠杆倍数
     */
    @Min(value = 1, message = "杠杆倍数不能小于1")
    @Max(value = 1000, message = "杠杆倍数不能大于1000")
    private Integer leverage;

    /**
     * 最大杠杆倍数
     */
    @Min(value = 1, message = "最大杠杆倍数不能小于1")
    @Max(value = 1000, message = "最大杠杆倍数不能大于1000")
    private Integer maxLeverage;

    /**
     * 维持保证金率
     */
    @DecimalMin(value = "0", message = "维持保证金率不能小于0")
    private BigDecimal maintenanceMarginRate;

    /**
     * 初始保证金率
     */
    @DecimalMin(value = "0", message = "初始保证金率不能小于0")
    private BigDecimal initialMarginRate;

    /**
     * 市场分类
     */
    private String marketCategory;

    /**
     * 交易区域
     */
    private String tradingRegion;

    /**
     * 上市时间
     */
    private LocalDateTime listingTime;

    /**
     * 下市时间
     */
    private LocalDateTime delistingTime;

    /**
     * 交易时间段
     */
    private List<Map<String, String>> tradingHours;

    /**
     * 是否支持API交易
     */
    private Boolean apiTradingAllowed = true;

    /**
     * 是否支持止损单
     */
    private Boolean stopOrderAllowed = true;

    /**
     * 是否支持止盈单
     */
    private Boolean takeProfitOrderAllowed = true;

    /**
     * 是否支持冰山订单
     */
    private Boolean icebergOrderAllowed = false;

    /**
     * 是否支持隐藏订单
     */
    private Boolean hiddenOrderAllowed = false;

    /**
     * 是否支持时间加权订单
     */
    private Boolean timeWeightedOrderAllowed = false;

    /**
     * 最大订单数量限制
     */
    @Min(value = 1, message = "最大订单数量限制不能小于1")
    private Integer maxOrderCount = 200;

    /**
     * 最大持仓数量
     */
    private BigDecimal maxPositionSize;

    /**
     * 价格保护阈值
     */
    @DecimalMin(value = "0", message = "价格保护阈值不能小于0")
    private BigDecimal priceProtectionThreshold;

    /**
     * 熔断机制配置
     */
    private Map<String, Object> circuitBreakerConfig;

    /**
     * 风险控制参数
     */
    private Map<String, Object> riskControlParams;

    /**
     * 流动性提供商配置
     */
    private Map<String, Object> liquidityProviderConfig;

    /**
     * 做市商配置
     */
    private Map<String, Object> marketMakerConfig;

    /**
     * 是否启用价格发现
     */
    private Boolean priceDiscoveryEnabled = true;

    /**
     * 价格发现算法
     */
    private String priceDiscoveryAlgorithm;

    /**
     * 是否启用自动做市
     */
    private Boolean autoMarketMakingEnabled = false;

    /**
     * 自动做市参数
     */
    private Map<String, Object> autoMarketMakingParams;

    /**
     * 是否启用套利检测
     */
    private Boolean arbitrageDetectionEnabled = false;

    /**
     * 套利检测参数
     */
    private Map<String, Object> arbitrageDetectionParams;

    /**
     * 是否启用异常交易检测
     */
    private Boolean abnormalTradingDetectionEnabled = true;

    /**
     * 异常交易检测参数
     */
    private Map<String, Object> abnormalTradingDetectionParams;

    /**
     * 数据源配置
     */
    private Map<String, Object> dataSourceConfig;

    /**
     * 缓存配置
     */
    private Map<String, Object> cacheConfig;

    /**
     * 监控配置
     */
    private Map<String, Object> monitoringConfig;

    /**
     * 报警配置
     */
    private Map<String, Object> alertConfig;

    /**
     * 审计配置
     */
    private Map<String, Object> auditConfig;

    /**
     * 合规配置
     */
    private Map<String, Object> complianceConfig;

    /**
     * 税务配置
     */
    private Map<String, Object> taxConfig;

    /**
     * 国际化配置
     */
    private Map<String, Object> i18nConfig;

    /**
     * 自定义属性
     */
    private Map<String, Object> customProperties;

    /**
     * 配置版本
     */
    private String configVersion;

    /**
     * 配置生效时间
     */
    private LocalDateTime effectiveTime;

    /**
     * 配置过期时间
     */
    private LocalDateTime expiryTime;

    /**
     * 配置创建者
     */
    private String createdBy;

    /**
     * 配置更新者
     */
    private String updatedBy;

    /**
     * 配置备注
     */
    private String remarks;

    /**
     * 是否启用配置
     */
    private Boolean enabled = true;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求ID
     */
    private String requestId;
}