package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 调整保证金请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdjustMarginRequest {
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * 持仓方向 (LONG/SHORT)
     */
    @NotBlank(message = "持仓方向不能为空")
    private String positionSide;
    
    /**
     * 调整类型 (ADD/REDUCE)
     */
    @NotBlank(message = "调整类型不能为空")
    private String type;
    
    /**
     * 调整金额
     */
    @NotNull(message = "调整金额不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "调整金额必须大于0")
    private BigDecimal amount;
    
    /**
     * 币种
     */
    private String asset;
    
    /**
     * 备注
     */
    private String remark;
}