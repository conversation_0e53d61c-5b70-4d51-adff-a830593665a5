package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 汇率信息查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeRateQueryRequest {

    /**
     * 基础货币代码
     */
    private String baseCurrency;

    /**
     * 计价货币代码
     */
    private String quoteCurrency;

    /**
     * 货币对代码（如：BTC/USDT）
     */
    private String currencyPair;

    /**
     * 基础货币列表（批量查询）
     */
    private List<String> baseCurrencies;

    /**
     * 计价货币列表（批量查询）
     */
    private List<String> quoteCurrencies;

    /**
     * 货币对列表（批量查询）
     */
    private List<String> currencyPairs;

    /**
     * 汇率类型（SPOT/FORWARD/SWAP）
     */
    private String rateType = "SPOT";

    /**
     * 汇率来源（INTERNAL/EXTERNAL/AGGREGATED）
     */
    private String rateSource = "AGGREGATED";

    /**
     * 外部数据源列表
     */
    private List<String> externalSources;

    /**
     * 查询时间
     */
    private LocalDateTime queryTime;

    /**
     * 查询时间戳（毫秒）
     */
    private Long queryTimestamp;

    /**
     * 起始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 起始时间戳（毫秒）
     */
    private Long startTimestamp;

    /**
     * 结束时间戳（毫秒）
     */
    private Long endTimestamp;

    /**
     * 历史数据周期（1m/5m/15m/30m/1h/4h/1d/1w/1M）
     */
    private String period;

    /**
     * 返回数量限制
     */
    @Min(value = 1, message = "返回数量不能小于1")
    @Max(value = 1000, message = "返回数量不能大于1000")
    private Integer limit = 100;

    /**
     * 价格精度
     */
    private Integer pricePrecision;

    /**
     * 是否包含历史汇率
     */
    private Boolean includeHistory = false;

    /**
     * 是否包含汇率变化信息
     */
    private Boolean includeChange = false;

    /**
     * 是否包含24小时统计
     */
    private Boolean include24hrStats = false;

    /**
     * 是否包含波动率信息
     */
    private Boolean includeVolatility = false;

    /**
     * 是否包含趋势分析
     */
    private Boolean includeTrend = false;

    /**
     * 是否包含技术指标
     */
    private Boolean includeTechnicalIndicators = false;

    /**
     * 技术指标类型列表
     */
    private List<String> technicalIndicatorTypes;

    /**
     * 是否包含相关性分析
     */
    private Boolean includeCorrelation = false;

    /**
     * 相关性基准货币
     */
    private String correlationBaseCurrency;

    /**
     * 是否包含预测信息
     */
    private Boolean includeForecast = false;

    /**
     * 预测时间范围
     */
    private String forecastTimeframe;

    /**
     * 是否包含套利机会
     */
    private Boolean includeArbitrage = false;

    /**
     * 套利类型（TRIANGULAR/SPATIAL/TEMPORAL）
     */
    private String arbitrageType;

    /**
     * 最小套利利润率
     */
    private Double minArbitrageProfit;

    /**
     * 是否包含流动性信息
     */
    private Boolean includeLiquidity = false;

    /**
     * 是否包含市场深度
     */
    private Boolean includeDepth = false;

    /**
     * 深度级别
     */
    private Integer depthLevel;

    /**
     * 是否包含成交量信息
     */
    private Boolean includeVolume = false;

    /**
     * 是否包含价差信息
     */
    private Boolean includeSpread = false;

    /**
     * 是否包含手续费信息
     */
    private Boolean includeFee = false;

    /**
     * 数据格式（JSON/CSV/XML）
     */
    private String dataFormat = "JSON";

    /**
     * 排序字段
     */
    private String sortField = "timestamp";

    /**
     * 排序方向（ASC/DESC）
     */
    private String sortDirection = "DESC";

    /**
     * 页码
     */
    @Min(value = 1, message = "页码不能小于1")
    private Integer page = 1;

    /**
     * 每页大小
     */
    @Min(value = 1, message = "每页大小不能小于1")
    @Max(value = 1000, message = "每页大小不能大于1000")
    private Integer pageSize = 100;

    /**
     * 时区
     */
    private String timezone = "UTC";

    /**
     * 是否实时数据
     */
    private Boolean realTime = true;

    /**
     * 更新频率（秒）
     */
    private Integer updateFrequency;

    /**
     * 是否压缩数据
     */
    private Boolean compressed = false;

    /**
     * 缓存策略
     */
    private String cacheStrategy;

    /**
     * 缓存过期时间（秒）
     */
    private Integer cacheExpiry;

    /**
     * 数据来源优先级
     */
    private List<String> sourcePriority;

    /**
     * 质量过滤
     */
    private Boolean qualityFilter = false;

    /**
     * 最小质量评分
     */
    private Double minQualityScore;

    /**
     * 是否过滤异常汇率
     */
    private Boolean filterAbnormal = false;

    /**
     * 异常检测阈值
     */
    private Double abnormalThreshold;

    /**
     * 是否包含元数据
     */
    private Boolean includeMetadata = false;

    /**
     * 客户端ID
     */
    private String clientId;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 订阅ID（用于WebSocket推送）
     */
    private String subscriptionId;

    /**
     * 回调URL（用于异步通知）
     */
    private String callbackUrl;

    /**
     * 语言
     */
    private String language = "zh-CN";

    /**
     * 货币类型过滤（FIAT/CRYPTO/STABLE）
     */
    private List<String> currencyTypes;

    /**
     * 市场分类过滤
     */
    private List<String> marketCategories;

    /**
     * 地区过滤
     */
    private List<String> regions;

    /**
     * 是否包含跨链汇率
     */
    private Boolean includeCrossChain = false;

    /**
     * 跨链网络列表
     */
    private List<String> crossChainNetworks;

    /**
     * 是否包含DeFi协议汇率
     */
    private Boolean includeDeFi = false;

    /**
     * DeFi协议列表
     */
    private List<String> defiProtocols;

    /**
     * 是否包含CEX汇率
     */
    private Boolean includeCEX = true;

    /**
     * CEX交易所列表
     */
    private List<String> cexExchanges;

    /**
     * 是否包含DEX汇率
     */
    private Boolean includeDEX = false;

    /**
     * DEX交易所列表
     */
    private List<String> dexExchanges;

    /**
     * 汇率聚合方法（WEIGHTED_AVERAGE/MEDIAN/MODE）
     */
    private String aggregationMethod = "WEIGHTED_AVERAGE";

    /**
     * 权重计算方法（VOLUME/LIQUIDITY/REPUTATION）
     */
    private String weightMethod = "VOLUME";

    /**
     * 是否启用增量更新
     */
    private Boolean incrementalUpdate = false;

    /**
     * 快照间隔（秒）
     */
    private Integer snapshotInterval;
}