package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * 提现记录查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "提现记录查询请求")
public class WithdrawQueryRequest {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "提现状态：0-待审核，1-处理中，2-成功，3-失败，4-已取消")
    private Integer status;

    @Schema(description = "网络类型")
    private String network;

    @Schema(description = "交易哈希")
    private String txHash;

    @Schema(description = "提现地址")
    private String address;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 20;

    @Schema(description = "排序字段")
    private String sortBy = "createTime";

    @Schema(description = "排序方向：ASC-升序，DESC-降序")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是ASC或DESC")
    private String sortOrder = "DESC";
}
