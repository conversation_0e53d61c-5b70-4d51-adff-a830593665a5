package com.cryptoexchange.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.time.LocalDateTime;

/**
 * K线查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "K线查询请求")
public class KlineQueryRequest {

    @Schema(description = "交易对代码", required = true)
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    @Schema(description = "时间间隔", required = true)
    @NotBlank(message = "时间间隔不能为空")
    @Pattern(regexp = "^(1m|3m|5m|15m|30m|1h|2h|4h|6h|8h|12h|1d|3d|1w|1M)$", 
             message = "时间间隔必须是: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M")
    private String interval;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "开始时间戳（毫秒）")
    private Long startTimestamp;

    @Schema(description = "结束时间戳（毫秒）")
    private Long endTimestamp;

    @Schema(description = "返回数量限制")
    @Min(value = 1, message = "返回数量必须大于0")
    @Max(value = 1000, message = "返回数量不能超过1000")
    private Integer limit = 500;

    @Schema(description = "是否包含技术指标")
    private Boolean includeTechnicalIndicators = false;

    @Schema(description = "技术指标类型")
    private String[] technicalIndicators;

    @Schema(description = "是否包含成交量分析")
    private Boolean includeVolumeAnalysis = false;

    @Schema(description = "是否包含价格分析")
    private Boolean includePriceAnalysis = false;

    @Schema(description = "是否包含趋势分析")
    private Boolean includeTrendAnalysis = false;

    @Schema(description = "是否包含波动率分析")
    private Boolean includeVolatilityAnalysis = false;

    @Schema(description = "是否包含支撑阻力分析")
    private Boolean includeSupportResistanceAnalysis = false;

    @Schema(description = "是否包含形态识别")
    private Boolean includePatternRecognition = false;

    @Schema(description = "是否包含市场结构分析")
    private Boolean includeMarketStructure = false;

    @Schema(description = "是否包含风险指标")
    private Boolean includeRiskMetrics = false;

    @Schema(description = "是否包含预测分析")
    private Boolean includePredictiveAnalysis = false;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(OHLCV|DETAILED|MINIMAL)$", 
             message = "数据格式必须是: OHLCV, DETAILED, MINIMAL")
    private String format = "OHLCV";

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "是否填充缺失数据")
    private Boolean fillMissingData = true;

    @Schema(description = "缺失数据填充方法")
    @Pattern(regexp = "^(FORWARD_FILL|BACKWARD_FILL|LINEAR_INTERPOLATION|ZERO_FILL)$", 
             message = "填充方法必须是: FORWARD_FILL, BACKWARD_FILL, LINEAR_INTERPOLATION, ZERO_FILL")
    private String fillMethod = "FORWARD_FILL";

    @Schema(description = "是否压缩数据")
    private Boolean compressed = false;

    @Schema(description = "数据精度")
    @Min(value = 0, message = "数据精度不能小于0")
    @Max(value = 8, message = "数据精度不能大于8")
    private Integer precision;

    @Schema(description = "是否实时数据")
    private Boolean realTime = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|LONG_CACHE|PERSISTENT_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, LONG_CACHE, PERSISTENT_CACHE")
    private String cacheStrategy = "SHORT_CACHE";

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|AGGREGATED|THIRD_PARTY)$", 
             message = "数据来源必须是: EXCHANGE, AGGREGATED, THIRD_PARTY")
    private String dataSource = "EXCHANGE";

    @Schema(description = "质量过滤")
    private Boolean qualityFilter = true;

    @Schema(description = "最小质量评分")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "1.0", message = "质量评分不能大于1")
    private Double minQualityScore = 0.8;

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;
}