package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.Map;

/**
 * 创建钱包请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "创建钱包请求")
public class CreateWalletRequest {

    @NotBlank(message = "钱包名称不能为空")
    @Schema(description = "钱包名称", example = "我的钱包", required = true)
    private String walletName;

    @NotBlank(message = "钱包类型不能为空")
    @Schema(description = "钱包类型", example = "SPOT", allowableValues = {"SPOT", "FUTURES", "MARGIN", "SAVINGS"}, required = true)
    private String walletType;

    @Schema(description = "钱包描述", example = "现货交易钱包")
    private String description;

    @Schema(description = "是否设为默认钱包", example = "false")
    private Boolean isDefault = false;

    @Schema(description = "钱包密码")
    private String password;

    @Schema(description = "确认密码")
    private String confirmPassword;

    @Schema(description = "助记词")
    private String mnemonic;

    @Schema(description = "私钥")
    private String privateKey;

    @Schema(description = "钱包地址")
    private String address;

    @Schema(description = "网络类型", example = "MAINNET", allowableValues = {"MAINNET", "TESTNET"})
    private String network = "MAINNET";

    @Schema(description = "支持的币种列表")
    private String[] supportedSymbols;

    @Schema(description = "钱包标签")
    private String[] tags;

    @Schema(description = "是否启用多重签名", example = "false")
    private Boolean enableMultiSig = false;

    @Schema(description = "多重签名阈值", example = "2")
    private Integer multiSigThreshold;

    @Schema(description = "多重签名公钥列表")
    private String[] multiSigPublicKeys;

    @Schema(description = "是否启用硬件钱包", example = "false")
    private Boolean enableHardwareWallet = false;

    @Schema(description = "硬件钱包类型", example = "LEDGER")
    private String hardwareWalletType;

    @Schema(description = "硬件钱包设备ID")
    private String hardwareDeviceId;

    @Schema(description = "钱包配置")
    private Map<String, Object> walletConfig;

    @Schema(description = "安全设置")
    private Map<String, Object> securitySettings;

    @Schema(description = "通知设置")
    private Map<String, Boolean> notificationSettings;

    @Schema(description = "备注信息")
    private String remarks;

    public CreateWalletRequest() {}

    public CreateWalletRequest(String walletName, String walletType) {
        this.walletName = walletName;
        this.walletType = walletType;
        this.isDefault = false;
        this.network = "MAINNET";
        this.enableMultiSig = false;
        this.enableHardwareWallet = false;
    }

    public CreateWalletRequest(String walletName, String walletType, String description, Boolean isDefault) {
        this.walletName = walletName;
        this.walletType = walletType;
        this.description = description;
        this.isDefault = isDefault;
        this.network = "MAINNET";
        this.enableMultiSig = false;
        this.enableHardwareWallet = false;
    }

    /**
     * 验证密码一致性
     */
    public boolean isPasswordMatch() {
        if (password == null || confirmPassword == null) {
            return false;
        }
        return password.equals(confirmPassword);
    }

    /**
     * 验证多重签名配置
     */
    public boolean isMultiSigConfigValid() {
        if (!enableMultiSig) {
            return true;
        }
        
        if (multiSigThreshold == null || multiSigPublicKeys == null) {
            return false;
        }
        
        return multiSigThreshold > 0 && 
               multiSigThreshold <= multiSigPublicKeys.length &&
               multiSigPublicKeys.length >= 2;
    }
}