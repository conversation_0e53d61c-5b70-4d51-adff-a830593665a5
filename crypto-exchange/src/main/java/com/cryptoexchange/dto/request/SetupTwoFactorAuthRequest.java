package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 设置双因子认证请求
 */
public class SetupTwoFactorAuthRequest {
    
    /**
     * 双因子认证类型：TOTP-谷歌验证器
     */
    @NotBlank(message = "双因子认证类型不能为空")
    private String authType;
    
    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;
    
    /**
     * 密钥（用于TOTP）
     */
    private String secret;
    
    // Getters and Setters
    public String getAuthType() {
        return authType;
    }
    
    public void setAuthType(String authType) {
        this.authType = authType;
    }
    
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getSecret() {
        return secret;
    }
    
    public void setSecret(String secret) {
        this.secret = secret;
    }
}