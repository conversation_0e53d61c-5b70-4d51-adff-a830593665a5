package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * KYC申请请求
 */
public class KycApplicationRequest {
    
    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 50, message = "姓名长度不能超过50个字符")
    private String realName;
    
    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idNumber;
    
    /**
     * 身份证正面照片URL
     */
    @NotBlank(message = "身份证正面照片不能为空")
    private String idCardFrontUrl;
    
    /**
     * 身份证反面照片URL
     */
    @NotBlank(message = "身份证反面照片不能为空")
    private String idCardBackUrl;
    
    /**
     * 手持身份证照片URL
     */
    @NotBlank(message = "手持身份证照片不能为空")
    private String idCardHandUrl;
    
    /**
     * 国籍
     */
    private String nationality;
    
    /**
     * 职业
     */
    private String occupation;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 申请类型：INDIVIDUAL-个人认证，ENTERPRISE-企业认证
     */
    private String applicationType;
    
    // Getters and Setters
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getIdNumber() {
        return idNumber;
    }
    
    public void setIdNumber(String idNumber) {
        this.idNumber = idNumber;
    }
    
    public String getIdCardFrontUrl() {
        return idCardFrontUrl;
    }
    
    public void setIdCardFrontUrl(String idCardFrontUrl) {
        this.idCardFrontUrl = idCardFrontUrl;
    }
    
    public String getIdCardBackUrl() {
        return idCardBackUrl;
    }
    
    public void setIdCardBackUrl(String idCardBackUrl) {
        this.idCardBackUrl = idCardBackUrl;
    }
    
    public String getIdCardHandUrl() {
        return idCardHandUrl;
    }
    
    public void setIdCardHandUrl(String idCardHandUrl) {
        this.idCardHandUrl = idCardHandUrl;
    }
    
    public String getNationality() {
        return nationality;
    }
    
    public void setNationality(String nationality) {
        this.nationality = nationality;
    }
    
    public String getOccupation() {
        return occupation;
    }
    
    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getApplicationType() {
        return applicationType;
    }
    
    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }
}