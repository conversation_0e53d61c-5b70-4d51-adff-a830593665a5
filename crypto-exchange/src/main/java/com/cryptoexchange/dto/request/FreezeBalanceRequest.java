package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 冻结余额请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "冻结余额请求")
public class FreezeBalanceRequest {

    @NotNull(message = "用户ID不能为空")
    @Min(value = 1, message = "用户ID必须大于0")
    @Schema(description = "用户ID", example = "123456", required = true)
    private Long userId;

    @NotBlank(message = "币种不能为空")
    @Schema(description = "币种符号", example = "BTC", required = true)
    private String symbol;

    @NotNull(message = "冻结金额不能为空")
    @DecimalMin(value = "0.00000001", message = "冻结金额必须大于0")
    @Schema(description = "冻结金额", example = "1.5", required = true)
    private BigDecimal amount;

    @NotBlank(message = "冻结类型不能为空")
    @Schema(description = "冻结类型", example = "TRADING", allowableValues = {"TRADING", "WITHDRAWAL", "DEPOSIT", "TRANSFER", "ALL"}, required = true)
    private String freezeType;

    @NotBlank(message = "冻结原因不能为空")
    @Schema(description = "冻结原因", example = "风险控制", required = true)
    private String reason;

    @Schema(description = "冻结原因代码", example = "RISK_CONTROL")
    private String reasonCode;

    @Schema(description = "冻结时长（小时）", example = "24")
    private Integer durationHours;

    @Schema(description = "冻结到期时间")
    private LocalDateTime expireTime;

    @Schema(description = "是否永久冻结", example = "false")
    private Boolean isPermanent = false;

    @Schema(description = "冻结优先级", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "URGENT"})
    private String priority = "MEDIUM";

    @Schema(description = "冻结范围", example = "ACCOUNT", allowableValues = {"ACCOUNT", "SYMBOL", "GLOBAL"})
    private String scope = "ACCOUNT";

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "关联交易ID")
    private String tradeId;

    @Schema(description = "风险等级", example = "MEDIUM", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
    private String riskLevel = "MEDIUM";

    @Schema(description = "操作人员ID")
    private Long operatorId;

    @Schema(description = "操作人员姓名")
    private String operatorName;

    @Schema(description = "操作部门")
    private String department;

    @Schema(description = "审批状态", example = "PENDING", allowableValues = {"PENDING", "APPROVED", "REJECTED"})
    private String approvalStatus = "PENDING";

    @Schema(description = "审批人员ID")
    private Long approverId;

    @Schema(description = "审批意见")
    private String approvalComment;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    @Schema(description = "通知方式", allowableValues = {"EMAIL", "SMS", "PUSH", "ALL"})
    private String[] notificationMethods;

    @Schema(description = "证据文件URL列表")
    private String[] evidenceUrls;

    @Schema(description = "详细说明")
    private String description;

    @Schema(description = "内部备注")
    private String internalNotes;

    @Schema(description = "客户可见备注")
    private String customerNotes;

    @Schema(description = "冻结标签")
    private String[] tags;

    @Schema(description = "自定义属性")
    private Map<String, Object> customAttributes;

    @Schema(description = "合规要求")
    private Map<String, String> complianceRequirements;

    @Schema(description = "解冻条件")
    private Map<String, Object> unfreezeConditions;

    @Schema(description = "是否自动解冻", example = "false")
    private Boolean autoUnfreeze = false;

    @Schema(description = "自动解冻条件")
    private String autoUnfreezeCondition;

    @Schema(description = "备注信息")
    private String remarks;

    public FreezeBalanceRequest() {}

    public FreezeBalanceRequest(Long userId, String symbol, BigDecimal amount, String freezeType, String reason) {
        this.userId = userId;
        this.symbol = symbol;
        this.amount = amount;
        this.freezeType = freezeType;
        this.reason = reason;
        this.isPermanent = false;
        this.priority = "MEDIUM";
        this.scope = "ACCOUNT";
        this.riskLevel = "MEDIUM";
        this.approvalStatus = "PENDING";
        this.sendNotification = true;
        this.autoUnfreeze = false;
    }

    public FreezeBalanceRequest(Long userId, String symbol, BigDecimal amount, String freezeType, String reason, Integer durationHours) {
        this.userId = userId;
        this.symbol = symbol;
        this.amount = amount;
        this.freezeType = freezeType;
        this.reason = reason;
        this.durationHours = durationHours;
        this.isPermanent = false;
        this.priority = "MEDIUM";
        this.scope = "ACCOUNT";
        this.riskLevel = "MEDIUM";
        this.approvalStatus = "PENDING";
        this.sendNotification = true;
        this.autoUnfreeze = false;
    }

    /**
     * 计算冻结到期时间
     */
    public LocalDateTime calculateExpireTime() {
        if (isPermanent || durationHours == null) {
            return null;
        }
        return LocalDateTime.now().plusHours(durationHours);
    }

    /**
     * 验证冻结配置
     */
    public boolean isValidFreezeConfig() {
        if (isPermanent && (durationHours != null || expireTime != null)) {
            return false;
        }
        
        if (!isPermanent && durationHours == null && expireTime == null) {
            return false;
        }
        
        return true;
    }

    /**
     * 是否需要审批
     */
    public boolean needApproval() {
        return "HIGH".equals(priority) || "URGENT".equals(priority) || 
               "HIGH".equals(riskLevel) || "CRITICAL".equals(riskLevel) ||
               isPermanent || amount.compareTo(new BigDecimal("10000")) > 0;
    }

    /**
     * 是否为高风险操作
     */
    public boolean isHighRiskOperation() {
        return "CRITICAL".equals(riskLevel) || isPermanent || 
               "ALL".equals(freezeType) || "GLOBAL".equals(scope);
    }
}