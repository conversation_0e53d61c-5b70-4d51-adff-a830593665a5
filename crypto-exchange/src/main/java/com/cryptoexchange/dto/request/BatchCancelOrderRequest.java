package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 批量取消订单请求
 */
public class BatchCancelOrderRequest {
    
    /**
     * 交易对符号
     */
    @NotBlank(message = "交易对符号不能为空")
    private String symbol;
    
    /**
     * 订单ID列表
     */
    @NotNull(message = "订单ID列表不能为空")
    @Size(min = 1, max = 100, message = "订单ID列表大小必须在1-100之间")
    private List<Long> orderIds;
    
    /**
     * 客户端订单ID列表
     */
    @Size(max = 100, message = "客户端订单ID列表大小不能超过100")
    private List<String> clientOrderIds;
    
    /**
     * 取消类型：ALL-全部取消，BUY-取消买单，SELL-取消卖单
     */
    private String cancelType;
    
    /**
     * 是否强制取消
     */
    private Boolean force = false;
    
    // Getters and Setters
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public List<Long> getOrderIds() {
        return orderIds;
    }
    
    public void setOrderIds(List<Long> orderIds) {
        this.orderIds = orderIds;
    }
    
    public List<String> getClientOrderIds() {
        return clientOrderIds;
    }
    
    public void setClientOrderIds(List<String> clientOrderIds) {
        this.clientOrderIds = clientOrderIds;
    }
    
    public String getCancelType() {
        return cancelType;
    }
    
    public void setCancelType(String cancelType) {
        this.cancelType = cancelType;
    }
    
    public Boolean getForce() {
        return force;
    }
    
    public void setForce(Boolean force) {
        this.force = force;
    }
}