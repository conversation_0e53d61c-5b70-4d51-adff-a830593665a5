package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 验证交易密码请求
 */
public class VerifyTradingPasswordRequest {
    
    /**
     * 交易密码
     */
    @NotBlank(message = "交易密码不能为空")
    private String tradingPassword;
    
    // Getters and Setters
    public String getTradingPassword() {
        return tradingPassword;
    }
    
    public void setTradingPassword(String tradingPassword) {
        this.tradingPassword = tradingPassword;
    }
}