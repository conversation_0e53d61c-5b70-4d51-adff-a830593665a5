package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;

/**
 * 最近交易查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "最近交易查询请求")
public class RecentTradeQueryRequest {

    @Schema(description = "交易对代码", required = true)
    @NotBlank(message = "交易对代码不能为空")
    private String symbol;

    @Schema(description = "返回数量限制")
    @Min(value = 1, message = "返回数量必须大于0")
    @Max(value = 1000, message = "返回数量不能超过1000")
    private Integer limit = 500;

    @Schema(description = "起始交易ID")
    private String fromId;

    @Schema(description = "结束交易ID")
    private String toId;

    @Schema(description = "交易类型")
    @Pattern(regexp = "^(BUY|SELL|ALL)$", message = "交易类型必须是: BUY, SELL, ALL")
    private String side = "ALL";

    @Schema(description = "是否包含手续费信息")
    private Boolean includeFeeInfo = false;

    @Schema(description = "是否包含订单信息")
    private Boolean includeOrderInfo = false;

    @Schema(description = "是否包含用户信息")
    private Boolean includeUserInfo = false;

    @Schema(description = "是否包含市场影响分析")
    private Boolean includeMarketImpact = false;

    @Schema(description = "是否包含流动性分析")
    private Boolean includeLiquidityAnalysis = false;

    @Schema(description = "是否包含价格影响分析")
    private Boolean includePriceImpact = false;

    @Schema(description = "是否包含执行质量分析")
    private Boolean includeExecutionQuality = false;

    @Schema(description = "是否包含滑点分析")
    private Boolean includeSlippageAnalysis = false;

    @Schema(description = "数据格式")
    @Pattern(regexp = "^(STANDARD|DETAILED|MINIMAL|AGGREGATED)$", 
             message = "数据格式必须是: STANDARD, DETAILED, MINIMAL, AGGREGATED")
    private String format = "STANDARD";

    @Schema(description = "聚合级别")
    @Pattern(regexp = "^(NONE|SECOND|MINUTE|HOUR)$", 
             message = "聚合级别必须是: NONE, SECOND, MINUTE, HOUR")
    private String aggregationLevel = "NONE";

    @Schema(description = "排序方向")
    @Pattern(regexp = "^(ASC|DESC)$", message = "排序方向必须是: ASC, DESC")
    private String sortOrder = "DESC";

    @Schema(description = "时区")
    private String timezone = "Asia/Shanghai";

    @Schema(description = "是否实时数据")
    private Boolean realTime = true;

    @Schema(description = "数据来源")
    @Pattern(regexp = "^(EXCHANGE|CACHE|ARCHIVE)$", 
             message = "数据来源必须是: EXCHANGE, CACHE, ARCHIVE")
    private String dataSource = "EXCHANGE";

    @Schema(description = "是否包含元数据")
    private Boolean includeMetadata = false;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "订阅ID（用于WebSocket）")
    private String subscriptionId;

    @Schema(description = "回调URL（用于Webhook）")
    private String callbackUrl;

    @Schema(description = "是否压缩数据")
    private Boolean compressed = false;

    @Schema(description = "缓存策略")
    @Pattern(regexp = "^(NO_CACHE|SHORT_CACHE|MEDIUM_CACHE|LONG_CACHE)$", 
             message = "缓存策略必须是: NO_CACHE, SHORT_CACHE, MEDIUM_CACHE, LONG_CACHE")
    private String cacheStrategy = "SHORT_CACHE";

    @Schema(description = "质量过滤")
    private Boolean qualityFilter = true;

    @Schema(description = "最小质量评分")
    @DecimalMin(value = "0.0", message = "质量评分不能小于0")
    @DecimalMax(value = "1.0", message = "质量评分不能大于1")
    private Double minQualityScore = 0.9;

    @Schema(description = "是否过滤异常交易")
    private Boolean filterAnomalies = true;

    @Schema(description = "异常检测阈值")
    @DecimalMin(value = "0.0", message = "异常检测阈值不能小于0")
    @DecimalMax(value = "1.0", message = "异常检测阈值不能大于1")
    private Double anomalyThreshold = 0.1;
}