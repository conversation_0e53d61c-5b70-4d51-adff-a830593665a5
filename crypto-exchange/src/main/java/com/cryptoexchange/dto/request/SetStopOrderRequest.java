package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 设置止损订单请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SetStopOrderRequest {
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * 持仓方向 (LONG/SHORT)
     */
    @NotBlank(message = "持仓方向不能为空")
    private String positionSide;
    
    /**
     * 止损价格
     */
    @NotNull(message = "止损价格不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "止损价格必须大于0")
    private BigDecimal stopPrice;
    
    /**
     * 止盈价格
     */
    private BigDecimal takeProfitPrice;
    
    /**
     * 止损类型 (STOP_MARKET/STOP_LIMIT)
     */
    private String stopType;
    
    /**
     * 止盈类型 (TAKE_PROFIT_MARKET/TAKE_PROFIT_LIMIT)
     */
    private String takeProfitType;
    
    /**
     * 止损限价 (STOP_LIMIT时使用)
     */
    private BigDecimal stopLimitPrice;
    
    /**
     * 止盈限价 (TAKE_PROFIT_LIMIT时使用)
     */
    private BigDecimal takeProfitLimitPrice;
    
    /**
     * 工作类型 (MARK_PRICE/CONTRACT_PRICE)
     */
    private String workingType;
    
    /**
     * 时效类型 (GTC/IOC/FOK)
     */
    private String timeInForce;
    
    /**
     * 客户端订单ID
     */
    private String clientOrderId;
    
    /**
     * 是否关闭持仓
     */
    private Boolean closePosition;
}