package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import java.util.List;

/**
 * 冻结用户请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "冻结用户请求")
public class FreezeUserRequest {

    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", example = "123456", required = true)
    private Long userId;

    @NotBlank(message = "冻结原因不能为空")
    @Schema(description = "冻结原因", example = "违规交易", required = true)
    private String reason;

    @Schema(description = "冻结类型", example = "TRADE", allowableValues = {"ALL", "TRADE", "WITHDRAW", "LOGIN"})
    private String freezeType = "ALL";

    @Schema(description = "冻结时长(小时)", example = "24")
    @Min(value = 1, message = "冻结时长必须大于0")
    private Integer freezeDurationHours;

    @Schema(description = "是否永久冻结", example = "false")
    private Boolean isPermanent = false;

    @Schema(description = "冻结范围", example = "[\"SPOT_TRADE\", \"FUTURES_TRADE\"]")
    private List<String> freezeScopes;

    @Schema(description = "详细说明")
    private String description;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    @Schema(description = "操作人员", example = "admin")
    private String operator;

    @Schema(description = "风险等级", example = "HIGH", allowableValues = {"LOW", "MEDIUM", "HIGH", "CRITICAL"})
    private String riskLevel;

    @Schema(description = "关联订单ID")
    private String relatedOrderId;

    @Schema(description = "关联交易ID")
    private String relatedTradeId;

    @Schema(description = "证据文件URL列表")
    private List<String> evidenceUrls;

    @Schema(description = "备注信息")
    private String remarks;

    public FreezeUserRequest() {}

    public FreezeUserRequest(Long userId, String reason, String freezeType) {
        this.userId = userId;
        this.reason = reason;
        this.freezeType = freezeType;
        this.isPermanent = false;
        this.sendNotification = true;
    }

    public FreezeUserRequest(Long userId, String reason, Integer freezeDurationHours) {
        this.userId = userId;
        this.reason = reason;
        this.freezeType = "ALL";
        this.freezeDurationHours = freezeDurationHours;
        this.isPermanent = false;
        this.sendNotification = true;
    }
}