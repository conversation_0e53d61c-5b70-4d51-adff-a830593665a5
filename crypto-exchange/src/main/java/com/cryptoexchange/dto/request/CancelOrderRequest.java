package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 取消订单请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "取消订单请求")
public class CancelOrderRequest {

    @Schema(description = "交易对", example = "BTCUSDT")
    @NotBlank(message = "交易对不能为空")
    private String symbol;

    @Schema(description = "订单ID", example = "123456")
    private Long orderId;

    @Schema(description = "客户端订单ID", example = "my_order_123")
    private String clientOrderId;
}