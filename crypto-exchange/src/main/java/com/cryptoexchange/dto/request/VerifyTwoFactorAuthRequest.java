package com.cryptoexchange.dto.request;

import jakarta.validation.constraints.NotBlank;

/**
 * 验证双因子认证请求
 */
public class VerifyTwoFactorAuthRequest {
    
    /**
     * 验证码
     */
    @NotBlank(message = "验证码不能为空")
    private String verificationCode;
    
    /**
     * 认证类型：TOTP-谷歌验证器
     */
    private String authType;
    
    // Getters and Setters
    public String getVerificationCode() {
        return verificationCode;
    }
    
    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }
    
    public String getAuthType() {
        return authType;
    }
    
    public void setAuthType(String authType) {
        this.authType = authType;
    }
    
    /**
     * 获取验证码（别名方法）
     */
    public String getCode() {
        return verificationCode;
    }
}