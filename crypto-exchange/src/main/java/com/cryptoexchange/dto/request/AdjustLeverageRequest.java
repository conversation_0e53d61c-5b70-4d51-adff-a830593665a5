package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 调整杠杆请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdjustLeverageRequest {
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * 杠杆倍数
     */
    @NotNull(message = "杠杆倍数不能为空")
    @Min(value = 1, message = "杠杆倍数不能小于1")
    @Max(value = 125, message = "杠杆倍数不能大于125")
    private Integer leverage;
    
    /**
     * 保证金模式 (ISOLATED/CROSS)
     */
    private String marginType;
    
    /**
     * 持仓方向 (LONG/SHORT/BOTH)
     */
    private String positionSide;
}