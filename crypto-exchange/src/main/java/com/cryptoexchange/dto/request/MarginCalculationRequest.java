package com.cryptoexchange.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 保证金计算请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarginCalculationRequest {
    
    /**
     * 合约符号
     */
    @NotBlank(message = "合约符号不能为空")
    private String symbol;
    
    /**
     * 持仓方向 (LONG/SHORT)
     */
    @NotBlank(message = "持仓方向不能为空")
    private String positionSide;
    
    /**
     * 持仓数量
     */
    @NotNull(message = "持仓数量不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "持仓数量必须大于0")
    private BigDecimal quantity;
    
    /**
     * 开仓价格
     */
    @NotNull(message = "开仓价格不能为空")
    @DecimalMin(value = "0.0", inclusive = false, message = "开仓价格必须大于0")
    private BigDecimal entryPrice;
    
    /**
     * 杠杆倍数
     */
    @NotNull(message = "杠杆倍数不能为空")
    private Integer leverage;
    
    /**
     * 保证金模式 (ISOLATED/CROSS)
     */
    private String marginType;
    
    /**
     * 标记价格 (用于计算未实现盈亏)
     */
    private BigDecimal markPrice;
}