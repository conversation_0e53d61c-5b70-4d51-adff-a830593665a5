package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场深度分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DepthAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 市场深度等级
     */
    private DepthLevel depthLevel;

    /**
     * 深度统计信息
     */
    private DepthStatistics depthStatistics;

    /**
     * 买单深度分析
     */
    private OrderSideDepthAnalysis bidDepthAnalysis;

    /**
     * 卖单深度分析
     */
    private OrderSideDepthAnalysis askDepthAnalysis;

    /**
     * 深度不平衡分析
     */
    private DepthImbalanceAnalysis depthImbalanceAnalysis;

    /**
     * 价格层级分析
     */
    private List<PriceLevelAnalysis> priceLevelAnalyses;

    /**
     * 深度质量评估
     */
    private DepthQualityAssessment depthQualityAssessment;

    /**
     * 深度稳定性分析
     */
    private DepthStabilityAnalysis depthStabilityAnalysis;

    /**
     * 深度变化分析
     */
    private DepthChangeAnalysis depthChangeAnalysis;

    /**
     * 深度预测
     */
    private DepthForecast depthForecast;

    /**
     * 深度交易建议
     */
    private DepthTradingAdvice depthTradingAdvice;

    /**
     * 深度风险评估
     */
    private DepthRiskAssessment depthRiskAssessment;

    /**
     * 市场深度等级
     */
    public enum DepthLevel {
        VERY_DEEP("极深", "市场深度极佳，流动性充足"),
        DEEP("深", "市场深度良好，流动性较好"),
        MODERATE("中等", "市场深度中等，流动性一般"),
        SHALLOW("浅", "市场深度较浅，流动性不足"),
        VERY_SHALLOW("极浅", "市场深度极浅，流动性严重不足");

        private final String description;
        private final String detail;

        DepthLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 深度统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthStatistics {
        /**
         * 总深度
         */
        private BigDecimal totalDepth;

        /**
         * 买单总深度
         */
        private BigDecimal totalBidDepth;

        /**
         * 卖单总深度
         */
        private BigDecimal totalAskDepth;

        /**
         * 买单总量
         */
        private BigDecimal totalBidVolume;

        /**
         * 卖单总量
         */
        private BigDecimal totalAskVolume;

        /**
         * 买单总价值
         */
        private BigDecimal totalBidValue;

        /**
         * 卖单总价值
         */
        private BigDecimal totalAskValue;

        /**
         * 价格层级数量
         */
        private Integer priceLevelCount;

        /**
         * 买单层级数量
         */
        private Integer bidLevelCount;

        /**
         * 卖单层级数量
         */
        private Integer askLevelCount;

        /**
         * 平均订单大小
         */
        private BigDecimal averageOrderSize;

        /**
         * 最大单笔订单
         */
        private BigDecimal maxOrderSize;

        /**
         * 最小单笔订单
         */
        private BigDecimal minOrderSize;
    }

    /**
     * 订单方向深度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderSideDepthAnalysis {
        /**
         * 订单方向 (BID/ASK)
         */
        private String orderSide;

        /**
         * 深度分布
         */
        private DepthDistribution depthDistribution;

        /**
         * 价格层级分析
         */
        private List<PriceLevelDepth> priceLevelDepths;

        /**
         * 深度集中度
         */
        private DepthConcentration depthConcentration;

        /**
         * 大单分析
         */
        private LargeOrderAnalysis largeOrderAnalysis;

        /**
         * 深度质量指标
         */
        private DepthQualityMetrics depthQualityMetrics;
    }

    /**
     * 深度分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthDistribution {
        /**
         * 近价深度 (1%价格范围内)
         */
        private BigDecimal nearPriceDepth;

        /**
         * 中距深度 (1-5%价格范围内)
         */
        private BigDecimal midRangeDepth;

        /**
         * 远价深度 (5%以上价格范围内)
         */
        private BigDecimal farPriceDepth;

        /**
         * 深度分布比例
         */
        private Map<String, BigDecimal> depthDistributionRatio;

        /**
         * 累积深度
         */
        private List<CumulativeDepth> cumulativeDepths;
    }

    /**
     * 累积深度
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CumulativeDepth {
        /**
         * 价格偏离百分比
         */
        private BigDecimal priceDeviationPercentage;

        /**
         * 累积数量
         */
        private BigDecimal cumulativeQuantity;

        /**
         * 累积价值
         */
        private BigDecimal cumulativeValue;

        /**
         * 订单数量
         */
        private Integer orderCount;
    }

    /**
     * 价格层级深度
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceLevelDepth {
        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private BigDecimal quantity;

        /**
         * 价值
         */
        private BigDecimal value;

        /**
         * 订单数量
         */
        private Integer orderCount;

        /**
         * 价格偏离度
         */
        private BigDecimal priceDeviation;

        /**
         * 深度权重
         */
        private BigDecimal depthWeight;

        /**
         * 层级重要性
         */
        private BigDecimal levelImportance;
    }

    /**
     * 深度集中度
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthConcentration {
        /**
         * 赫芬达尔指数
         */
        private BigDecimal herfindahlIndex;

        /**
         * 基尼系数
         */
        private BigDecimal giniCoefficient;

        /**
         * 前5层级集中度
         */
        private BigDecimal top5LevelConcentration;

        /**
         * 前10层级集中度
         */
        private BigDecimal top10LevelConcentration;

        /**
         * 集中度等级
         */
        private String concentrationLevel;
    }

    /**
     * 大单分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderAnalysis {
        /**
         * 大单定义阈值
         */
        private BigDecimal largeOrderThreshold;

        /**
         * 大单数量
         */
        private Integer largeOrderCount;

        /**
         * 大单总量
         */
        private BigDecimal largeOrderTotalQuantity;

        /**
         * 大单总价值
         */
        private BigDecimal largeOrderTotalValue;

        /**
         * 大单占比
         */
        private BigDecimal largeOrderRatio;

        /**
         * 大单分布
         */
        private List<LargeOrderDistribution> largeOrderDistributions;

        /**
         * 大单影响评估
         */
        private LargeOrderImpact largeOrderImpact;
    }

    /**
     * 大单分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderDistribution {
        /**
         * 价格区间
         */
        private PriceRange priceRange;

        /**
         * 大单数量
         */
        private Integer largeOrderCount;

        /**
         * 大单总量
         */
        private BigDecimal largeOrderQuantity;

        /**
         * 区间重要性
         */
        private BigDecimal rangeImportance;
    }

    /**
     * 价格区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 中点
         */
        private BigDecimal midPoint;
    }

    /**
     * 大单影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderImpact {
        /**
         * 价格影响
         */
        private BigDecimal priceImpact;

        /**
         * 流动性影响
         */
        private BigDecimal liquidityImpact;

        /**
         * 市场稳定性影响
         */
        private BigDecimal marketStabilityImpact;

        /**
         * 影响持续时间
         */
        private BigDecimal impactDuration;
    }

    /**
     * 深度质量指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthQualityMetrics {
        /**
         * 深度厚度
         */
        private BigDecimal depthThickness;

        /**
         * 深度连续性
         */
        private BigDecimal depthContinuity;

        /**
         * 深度均匀性
         */
        private BigDecimal depthUniformity;

        /**
         * 深度稳定性
         */
        private BigDecimal depthStability;

        /**
         * 深度响应性
         */
        private BigDecimal depthResponsiveness;
    }

    /**
     * 深度不平衡分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthImbalanceAnalysis {
        /**
         * 总体不平衡指数
         */
        private BigDecimal overallImbalanceIndex;

        /**
         * 数量不平衡
         */
        private QuantityImbalance quantityImbalance;

        /**
         * 价值不平衡
         */
        private ValueImbalance valueImbalance;

        /**
         * 层级不平衡
         */
        private LevelImbalance levelImbalance;

        /**
         * 不平衡趋势
         */
        private ImbalanceTrend imbalanceTrend;

        /**
         * 不平衡影响评估
         */
        private ImbalanceImpactAssessment imbalanceImpactAssessment;
    }

    /**
     * 数量不平衡
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuantityImbalance {
        /**
         * 买卖数量比
         */
        private BigDecimal bidAskQuantityRatio;

        /**
         * 数量不平衡度
         */
        private BigDecimal quantityImbalanceDegree;

        /**
         * 数量偏向
         */
        private String quantityBias;

        /**
         * 数量差异
         */
        private BigDecimal quantityDifference;
    }

    /**
     * 价值不平衡
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValueImbalance {
        /**
         * 买卖价值比
         */
        private BigDecimal bidAskValueRatio;

        /**
         * 价值不平衡度
         */
        private BigDecimal valueImbalanceDegree;

        /**
         * 价值偏向
         */
        private String valueBias;

        /**
         * 价值差异
         */
        private BigDecimal valueDifference;
    }

    /**
     * 层级不平衡
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelImbalance {
        /**
         * 买卖层级比
         */
        private BigDecimal bidAskLevelRatio;

        /**
         * 层级不平衡度
         */
        private BigDecimal levelImbalanceDegree;

        /**
         * 层级偏向
         */
        private String levelBias;

        /**
         * 层级差异
         */
        private Integer levelDifference;
    }

    /**
     * 不平衡趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImbalanceTrend {
        /**
         * 趋势方向
         */
        private String trendDirection;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续时间
         */
        private Integer trendDuration;

        /**
         * 趋势稳定性
         */
        private BigDecimal trendStability;
    }

    /**
     * 不平衡影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImbalanceImpactAssessment {
        /**
         * 价格压力
         */
        private BigDecimal pricePressure;

        /**
         * 流动性影响
         */
        private BigDecimal liquidityImpact;

        /**
         * 交易成本影响
         */
        private BigDecimal tradingCostImpact;

        /**
         * 市场效率影响
         */
        private BigDecimal marketEfficiencyImpact;
    }

    /**
     * 价格层级分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceLevelAnalysis {
        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 订单方向
         */
        private String orderSide;

        /**
         * 数量
         */
        private BigDecimal quantity;

        /**
         * 价值
         */
        private BigDecimal value;

        /**
         * 订单数量
         */
        private Integer orderCount;

        /**
         * 层级重要性
         */
        private BigDecimal levelImportance;

        /**
         * 支撑/阻力强度
         */
        private BigDecimal supportResistanceStrength;

        /**
         * 层级稳定性
         */
        private BigDecimal levelStability;

        /**
         * 历史表现
         */
        private LevelHistoricalPerformance levelHistoricalPerformance;

        /**
         * 层级预测
         */
        private LevelForecast levelForecast;
    }

    /**
     * 层级历史表现
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelHistoricalPerformance {
        /**
         * 历史出现次数
         */
        private Integer historicalOccurrences;

        /**
         * 平均持续时间
         */
        private BigDecimal averageDuration;

        /**
         * 突破概率
         */
        private BigDecimal breakoutProbability;

        /**
         * 反弹概率
         */
        private BigDecimal bounceBackProbability;

        /**
         * 历史最大数量
         */
        private BigDecimal historicalMaxQuantity;

        /**
         * 历史平均数量
         */
        private BigDecimal historicalAverageQuantity;
    }

    /**
     * 层级预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelForecast {
        /**
         * 预测持续时间
         */
        private BigDecimal predictedDuration;

        /**
         * 预测数量变化
         */
        private BigDecimal predictedQuantityChange;

        /**
         * 预测突破概率
         */
        private BigDecimal predictedBreakoutProbability;

        /**
         * 预测价格影响
         */
        private BigDecimal predictedPriceImpact;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;
    }

    /**
     * 深度质量评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthQualityAssessment {
        /**
         * 整体质量评分
         */
        private BigDecimal overallQualityScore;

        /**
         * 质量等级
         */
        private String qualityLevel;

        /**
         * 质量维度评估
         */
        private QualityDimensionAssessment qualityDimensionAssessment;

        /**
         * 质量改进建议
         */
        private List<String> qualityImprovementRecommendations;

        /**
         * 质量风险警告
         */
        private List<String> qualityRiskWarnings;
    }

    /**
     * 质量维度评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QualityDimensionAssessment {
        /**
         * 深度充足性
         */
        private BigDecimal depthAdequacy;

        /**
         * 深度分布均匀性
         */
        private BigDecimal depthDistributionUniformity;

        /**
         * 深度稳定性
         */
        private BigDecimal depthStability;

        /**
         * 深度响应性
         */
        private BigDecimal depthResponsiveness;

        /**
         * 深度透明性
         */
        private BigDecimal depthTransparency;
    }

    /**
     * 深度稳定性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthStabilityAnalysis {
        /**
         * 稳定性指数
         */
        private BigDecimal stabilityIndex;

        /**
         * 稳定性等级
         */
        private String stabilityLevel;

        /**
         * 波动性分析
         */
        private DepthVolatilityAnalysis depthVolatilityAnalysis;

        /**
         * 持续性分析
         */
        private DepthPersistenceAnalysis depthPersistenceAnalysis;

        /**
         * 稳定性影响因素
         */
        private List<StabilityFactor> stabilityFactors;
    }

    /**
     * 深度波动性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthVolatilityAnalysis {
        /**
         * 深度波动率
         */
        private BigDecimal depthVolatility;

        /**
         * 波动性类型
         */
        private String volatilityType;

        /**
         * 波动性趋势
         */
        private String volatilityTrend;

        /**
         * 波动性周期
         */
        private VolatilityCycle volatilityCycle;
    }

    /**
     * 波动性周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityCycle {
        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 当前周期位置
         */
        private BigDecimal currentCyclePosition;
    }

    /**
     * 深度持续性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthPersistenceAnalysis {
        /**
         * 持续性指数
         */
        private BigDecimal persistenceIndex;

        /**
         * 平均持续时间
         */
        private BigDecimal averagePersistenceDuration;

        /**
         * 持续性模式
         */
        private String persistencePattern;

        /**
         * 持续性预测
         */
        private BigDecimal persistenceForecast;
    }

    /**
     * 稳定性因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StabilityFactor {
        /**
         * 因素名称
         */
        private String factorName;

        /**
         * 因素类型
         */
        private String factorType;

        /**
         * 影响程度
         */
        private BigDecimal impactDegree;

        /**
         * 影响方向
         */
        private String impactDirection;

        /**
         * 因素描述
         */
        private String factorDescription;
    }

    /**
     * 深度变化分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthChangeAnalysis {
        /**
         * 变化趋势
         */
        private String changeTrend;

        /**
         * 变化速度
         */
        private BigDecimal changeVelocity;

        /**
         * 变化幅度
         */
        private BigDecimal changeMagnitude;

        /**
         * 变化模式
         */
        private ChangePattern changePattern;

        /**
         * 变化驱动因素
         */
        private List<ChangeDrivingFactor> changeDrivingFactors;

        /**
         * 变化影响评估
         */
        private ChangeImpactAssessment changeImpactAssessment;
    }

    /**
     * 变化模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangePattern {
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 模式持续时间
         */
        private Integer patternDuration;

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;
    }

    /**
     * 变化驱动因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeDrivingFactor {
        /**
         * 因素名称
         */
        private String factorName;

        /**
         * 因素类型
         */
        private String factorType;

        /**
         * 贡献度
         */
        private BigDecimal contributionDegree;

        /**
         * 因素强度
         */
        private BigDecimal factorStrength;

        /**
         * 因素描述
         */
        private String factorDescription;
    }

    /**
     * 变化影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChangeImpactAssessment {
        /**
         * 价格影响
         */
        private BigDecimal priceImpact;

        /**
         * 流动性影响
         */
        private BigDecimal liquidityImpact;

        /**
         * 交易成本影响
         */
        private BigDecimal tradingCostImpact;

        /**
         * 市场效率影响
         */
        private BigDecimal marketEfficiencyImpact;

        /**
         * 整体影响评分
         */
        private BigDecimal overallImpactScore;
    }

    /**
     * 深度预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthForecast {
        /**
         * 短期预测
         */
        private DepthPrediction shortTermPrediction;

        /**
         * 中期预测
         */
        private DepthPrediction mediumTermPrediction;

        /**
         * 长期预测
         */
        private DepthPrediction longTermPrediction;

        /**
         * 预测模型评估
         */
        private ForecastModelEvaluation forecastModelEvaluation;
    }

    /**
     * 深度预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthPrediction {
        /**
         * 预测时间范围
         */
        private String predictionTimeRange;

        /**
         * 预测深度等级
         */
        private DepthLevel predictedDepthLevel;

        /**
         * 预测总深度
         */
        private BigDecimal predictedTotalDepth;

        /**
         * 预测买单深度
         */
        private BigDecimal predictedBidDepth;

        /**
         * 预测卖单深度
         */
        private BigDecimal predictedAskDepth;

        /**
         * 预测不平衡指数
         */
        private BigDecimal predictedImbalanceIndex;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 预测置信区间
         */
        private ConfidenceInterval predictionConfidenceInterval;
    }

    /**
     * 置信区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfidenceInterval {
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;

        /**
         * 下界
         */
        private BigDecimal lowerBound;

        /**
         * 上界
         */
        private BigDecimal upperBound;
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastModelEvaluation {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;

        /**
         * 模型可靠性
         */
        private BigDecimal modelReliability;

        /**
         * 模型性能指标
         */
        private Map<String, BigDecimal> modelPerformanceMetrics;
    }

    /**
     * 深度交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthTradingAdvice {
        /**
         * 基于深度的交易策略
         */
        private List<DepthBasedStrategy> depthBasedStrategies;

        /**
         * 层级交易建议
         */
        private List<LevelTradingAdvice> levelTradingAdvices;

        /**
         * 深度套利机会
         */
        private List<DepthArbitrageOpportunity> depthArbitrageOpportunities;

        /**
         * 执行建议
         */
        private ExecutionAdvice executionAdvice;
    }

    /**
     * 基于深度的策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthBasedStrategy {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略类型
         */
        private String strategyType;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 适用深度条件
         */
        private List<String> applicableDepthConditions;

        /**
         * 策略参数
         */
        private Map<String, String> strategyParameters;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险水平
         */
        private BigDecimal riskLevel;

        /**
         * 成功概率
         */
        private BigDecimal successProbability;
    }

    /**
     * 层级交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LevelTradingAdvice {
        /**
         * 目标价格层级
         */
        private BigDecimal targetPriceLevel;

        /**
         * 交易方向
         */
        private String tradingDirection;

        /**
         * 建议数量
         */
        private BigDecimal recommendedQuantity;

        /**
         * 执行时机
         */
        private String executionTiming;

        /**
         * 风险评估
         */
        private BigDecimal riskAssessment;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;
    }

    /**
     * 深度套利机会
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthArbitrageOpportunity {
        /**
         * 套利类型
         */
        private String arbitrageType;

        /**
         * 套利描述
         */
        private String arbitrageDescription;

        /**
         * 套利价格差
         */
        private BigDecimal arbitragePriceDifference;

        /**
         * 套利收益率
         */
        private BigDecimal arbitrageReturnRate;

        /**
         * 套利风险
         */
        private BigDecimal arbitrageRisk;

        /**
         * 时间窗口
         */
        private String timeWindow;
    }

    /**
     * 执行建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionAdvice {
        /**
         * 最佳执行时机
         */
        private String optimalExecutionTiming;

        /**
         * 订单分割建议
         */
        private OrderSplittingAdvice orderSplittingAdvice;

        /**
         * 价格改进机会
         */
        private List<String> priceImprovementOpportunities;

        /**
         * 执行风险警告
         */
        private List<String> executionRiskWarnings;
    }

    /**
     * 订单分割建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderSplittingAdvice {
        /**
         * 是否需要分割
         */
        private boolean needsSplitting;

        /**
         * 建议分割数量
         */
        private Integer recommendedSplitCount;

        /**
         * 分割策略
         */
        private String splittingStrategy;

        /**
         * 时间间隔建议
         */
        private BigDecimal recommendedTimeInterval;
    }

    /**
     * 深度风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 深度风险指标
         */
        private DepthRiskMetrics depthRiskMetrics;

        /**
         * 流动性风险
         */
        private LiquidityRisk liquidityRisk;

        /**
         * 执行风险
         */
        private ExecutionRisk executionRisk;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 深度风险指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthRiskMetrics {
        /**
         * 深度不足风险
         */
        private BigDecimal depthInadequacyRisk;

        /**
         * 深度集中风险
         */
        private BigDecimal depthConcentrationRisk;

        /**
         * 深度波动风险
         */
        private BigDecimal depthVolatilityRisk;

        /**
         * 深度不平衡风险
         */
        private BigDecimal depthImbalanceRisk;
    }

    /**
     * 流动性风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityRisk {
        /**
         * 流动性枯竭风险
         */
        private BigDecimal liquidityDryUpRisk;

        /**
         * 流动性冲击风险
         */
        private BigDecimal liquidityShockRisk;

        /**
         * 流动性成本风险
         */
        private BigDecimal liquidityCostRisk;

        /**
         * 流动性时间风险
         */
        private BigDecimal liquidityTimingRisk;
    }

    /**
     * 执行风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionRisk {
        /**
         * 价格冲击风险
         */
        private BigDecimal priceImpactRisk;

        /**
         * 执行延迟风险
         */
        private BigDecimal executionDelayRisk;

        /**
         * 部分成交风险
         */
        private BigDecimal partialFillRisk;

        /**
         * 滑点风险
         */
        private BigDecimal slippageRisk;
    }

    /**
     * 获取深度分析摘要
     */
    public String getDepthAnalysisSummary() {
        return String.format("深度等级: %s, 总深度: %s, 买单深度: %s, 卖单深度: %s",
            depthLevel != null ? depthLevel.getDescription() : "未知",
            depthStatistics != null && depthStatistics.getTotalDepth() != null ? 
                depthStatistics.getTotalDepth().toString() : "N/A",
            depthStatistics != null && depthStatistics.getTotalBidDepth() != null ? 
                depthStatistics.getTotalBidDepth().toString() : "N/A",
            depthStatistics != null && depthStatistics.getTotalAskDepth() != null ? 
                depthStatistics.getTotalAskDepth().toString() : "N/A");
    }

    /**
     * 检查是否为深度市场
     */
    public boolean isDeepMarket() {
        return depthLevel == DepthLevel.DEEP || depthLevel == DepthLevel.VERY_DEEP;
    }

    /**
     * 检查是否为浅度市场
     */
    public boolean isShallowMarket() {
        return depthLevel == DepthLevel.SHALLOW || depthLevel == DepthLevel.VERY_SHALLOW;
    }

    /**
     * 获取不平衡状况
     */
    public String getImbalanceStatus() {
        if (depthImbalanceAnalysis == null) {
            return "不平衡分析不可用";
        }
        
        BigDecimal imbalanceIndex = depthImbalanceAnalysis.getOverallImbalanceIndex();
        if (imbalanceIndex == null) {
            return "不平衡指数不可用";
        }
        
        if (imbalanceIndex.compareTo(BigDecimal.valueOf(0.8)) >= 0) {
            return "严重不平衡";
        } else if (imbalanceIndex.compareTo(BigDecimal.valueOf(0.6)) >= 0) {
            return "中度不平衡";
        } else if (imbalanceIndex.compareTo(BigDecimal.valueOf(0.3)) >= 0) {
            return "轻度不平衡";
        } else {
            return "基本平衡";
        }
    }

    /**
     * 获取质量评估
     */
    public String getQualityAssessment() {
        if (depthQualityAssessment == null) {
            return "质量评估不可用";
        }
        
        String qualityLevel = depthQualityAssessment.getQualityLevel();
        BigDecimal qualityScore = depthQualityAssessment.getOverallQualityScore();
        
        return String.format("质量等级: %s (评分: %s)",
            qualityLevel != null ? qualityLevel : "未知",
            qualityScore != null ? qualityScore.toString() : "N/A");
    }

    /**
     * 获取稳定性状况
     */
    public String getStabilityStatus() {
        if (depthStabilityAnalysis == null) {
            return "稳定性分析不可用";
        }
        
        String stabilityLevel = depthStabilityAnalysis.getStabilityLevel();
        BigDecimal stabilityIndex = depthStabilityAnalysis.getStabilityIndex();
        
        return String.format("稳定性等级: %s (指数: %s)",
            stabilityLevel != null ? stabilityLevel : "未知",
            stabilityIndex != null ? stabilityIndex.toString() : "N/A");
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (depthTradingAdvice == null || 
            depthTradingAdvice.getDepthBasedStrategies() == null ||
            depthTradingAdvice.getDepthBasedStrategies().isEmpty()) {
            return "暂无交易建议";
        }
        
        DepthBasedStrategy topStrategy = depthTradingAdvice.getDepthBasedStrategies().get(0);
        return String.format("%s - %s (成功概率: %s%%)",
            topStrategy.getStrategyName(),
            topStrategy.getStrategyDescription(),
            topStrategy.getSuccessProbability() != null ? 
                topStrategy.getSuccessProbability().toString() : "N/A");
    }

    /**
     * 检查是否有套利机会
     */
    public boolean hasArbitrageOpportunities() {
        return depthTradingAdvice != null && 
               depthTradingAdvice.getDepthArbitrageOpportunities() != null &&
               !depthTradingAdvice.getDepthArbitrageOpportunities().isEmpty();
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (depthRiskAssessment == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = depthRiskAssessment.getOverallRiskLevel();
        if (riskLevel == null) {
            return "风险等级未知";
        }
        
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：市场深度存在较高风险，建议谨慎交易";
            case "MEDIUM":
                return "中等风险：注意深度变化，适度控制仓位";
            case "LOW":
                return "低风险：市场深度相对稳定，可正常交易";
            default:
                return "风险等级: " + riskLevel;
        }
    }

    /**
     * 获取执行建议摘要
     */
    public String getExecutionAdviceSummary() {
        if (depthTradingAdvice == null || 
            depthTradingAdvice.getExecutionAdvice() == null) {
            return "执行建议不可用";
        }
        
        ExecutionAdvice advice = depthTradingAdvice.getExecutionAdvice();
        String timing = advice.getOptimalExecutionTiming();
        
        OrderSplittingAdvice splittingAdvice = advice.getOrderSplittingAdvice();
        boolean needsSplitting = splittingAdvice != null && splittingAdvice.isNeedsSplitting();
        
        return String.format("最佳执行时机: %s, %s",
            timing != null ? timing : "未指定",
            needsSplitting ? "建议分割订单" : "可整单执行");
    }

    /**
     * 获取深度预测摘要
     */
    public String getDepthForecastSummary() {
        if (depthForecast == null || depthForecast.getShortTermPrediction() == null) {
            return "深度预测不可用";
        }
        
        DepthPrediction shortTerm = depthForecast.getShortTermPrediction();
        return String.format("短期预测: %s深度 (置信度: %s%%)",
            shortTerm.getPredictedDepthLevel() != null ? 
                shortTerm.getPredictedDepthLevel().getDescription() : "未知",
            shortTerm.getPredictionConfidence() != null ? 
                shortTerm.getPredictionConfidence().toString() : "N/A");
    }

    /**
     * 检查是否需要订单分割
     */
    public boolean needsOrderSplitting() {
        if (depthTradingAdvice == null || 
            depthTradingAdvice.getExecutionAdvice() == null ||
            depthTradingAdvice.getExecutionAdvice().getOrderSplittingAdvice() == null) {
            return false;
        }
        
        return depthTradingAdvice.getExecutionAdvice().getOrderSplittingAdvice().isNeedsSplitting();
    }

    /**
     * 获取大单影响评估
     */
    public String getLargeOrderImpactAssessment() {
        if (bidDepthAnalysis == null || 
            bidDepthAnalysis.getLargeOrderAnalysis() == null ||
            bidDepthAnalysis.getLargeOrderAnalysis().getLargeOrderImpact() == null) {
            return "大单影响评估不可用";
        }
        
        LargeOrderImpact impact = bidDepthAnalysis.getLargeOrderAnalysis().getLargeOrderImpact();
        return String.format("价格影响: %s%%, 流动性影响: %s%%",
            impact.getPriceImpact() != null ? impact.getPriceImpact().toString() : "N/A",
            impact.getLiquidityImpact() != null ? impact.getLiquidityImpact().toString() : "N/A");
    }
}