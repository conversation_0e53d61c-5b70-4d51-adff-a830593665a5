package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货市场深度响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesMarketDepthResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 最后更新ID
     */
    private Long lastUpdateId;
    
    /**
     * 事务时间
     */
    private Long transactionTime;
    
    /**
     * 事件时间
     */
    private Long eventTime;
    
    /**
     * 买单深度
     */
    private List<DepthLevel> bids;
    
    /**
     * 卖单深度
     */
    private List<DepthLevel> asks;
    
    /**
     * 最佳买价
     */
    private BigDecimal bestBidPrice;
    
    /**
     * 最佳买量
     */
    private BigDecimal bestBidQty;
    
    /**
     * 最佳卖价
     */
    private BigDecimal bestAskPrice;
    
    /**
     * 最佳卖量
     */
    private BigDecimal bestAskQty;
    
    /**
     * 买卖价差
     */
    private BigDecimal spread;
    
    /**
     * 买卖价差百分比
     */
    private BigDecimal spreadPercentage;
    
    /**
     * 深度等级数量
     */
    private Integer depthLevels;
    
    /**
     * 数据时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 深度等级
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DepthLevel {
        /**
         * 价格
         */
        private BigDecimal price;
        
        /**
         * 数量
         */
        private BigDecimal quantity;
        
        /**
         * 订单数量
         */
        private Integer orderCount;
    }
}