package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 实时市场深度响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RealTimeDepthResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 事件时间
     */
    private Long eventTime;
    
    /**
     * 交易对
     */
    private String s;
    
    /**
     * 首次更新ID
     */
    private Long firstUpdateId;
    
    /**
     * 最终更新ID
     */
    private Long finalUpdateId;
    
    /**
     * 买单深度更新
     */
    private List<DepthUpdate> bids;
    
    /**
     * 卖单深度更新
     */
    private List<DepthUpdate> asks;
    
    /**
     * 序列号
     */
    private Long sequence;
    
    /**
     * 是否为快照
     */
    private Boolean isSnapshot;
    
    /**
     * 数据来源
     */
    private String source;
    
    /**
     * 延迟时间（毫秒）
     */
    private Long latency;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 深度更新条目
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DepthUpdate {
        /**
         * 价格
         */
        private BigDecimal price;
        
        /**
         * 数量（为0表示删除该价格档位）
         */
        private BigDecimal quantity;
        
        /**
         * 更新类型 (ADD, UPDATE, DELETE)
         */
        private String updateType;
        
        /**
         * 订单数量
         */
        private Integer orderCount;
        
        /**
         * 更新时间
         */
        private Long updateTime;
    }
}