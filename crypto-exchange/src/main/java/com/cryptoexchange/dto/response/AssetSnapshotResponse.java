package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产快照响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetSnapshotResponse {
    
    /**
     * 快照ID
     */
    private Long snapshotId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 快照时间
     */
    private LocalDateTime snapshotTime;
    
    /**
     * 快照类型 (DAILY, WEEKLY, MONTHLY, MANUAL)
     */
    private String snapshotType;
    
    /**
     * 总资产价值（USDT计价）
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 计价货币
     */
    private String quoteCurrency;
    
    /**
     * 现货钱包价值
     */
    private BigDecimal spotWalletValue;
    
    /**
     * 期货钱包价值
     */
    private BigDecimal futuresWalletValue;
    
    /**
     * 理财钱包价值
     */
    private BigDecimal savingsWalletValue;
    
    /**
     * 质押钱包价值
     */
    private BigDecimal stakingWalletValue;
    
    /**
     * 与上次快照的变化
     */
    private BigDecimal changeFromLast;
    
    /**
     * 与上次快照的变化百分比
     */
    private BigDecimal changePercentFromLast;
    
    /**
     * 资产详情列表
     */
    private List<AssetDetail> assetDetails;
    
    /**
     * 钱包分布
     */
    private List<WalletSnapshot> walletSnapshots;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 资产详情内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssetDetail {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 货币名称
         */
        private String currencyName;
        
        /**
         * 总余额
         */
        private BigDecimal totalBalance;
        
        /**
         * 可用余额
         */
        private BigDecimal availableBalance;
        
        /**
         * 冻结余额
         */
        private BigDecimal frozenBalance;
        
        /**
         * 快照时价格
         */
        private BigDecimal priceAtSnapshot;
        
        /**
         * 资产价值
         */
        private BigDecimal assetValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 与上次快照余额变化
         */
        private BigDecimal balanceChange;
        
        /**
         * 与上次快照价值变化
         */
        private BigDecimal valueChange;
        
        /**
         * 钱包类型
         */
        private String walletType;
    }
    
    /**
     * 钱包快照内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WalletSnapshot {
        /**
         * 钱包类型
         */
        private String walletType;
        
        /**
         * 钱包名称
         */
        private String walletName;
        
        /**
         * 钱包价值
         */
        private BigDecimal walletValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 资产种类数量
         */
        private Integer assetCount;
        
        /**
         * 与上次快照变化
         */
        private BigDecimal changeFromLast;
        
        /**
         * 与上次快照变化百分比
         */
        private BigDecimal changePercentFromLast;
        
        /**
         * 钱包状态
         */
        private String status;
    }
}