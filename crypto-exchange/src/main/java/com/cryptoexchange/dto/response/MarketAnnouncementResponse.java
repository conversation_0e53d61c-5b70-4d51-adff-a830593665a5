package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场公告响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场公告响应")
public class MarketAnnouncementResponse {

    @Schema(description = "公告ID")
    private String announcementId;

    @Schema(description = "公告标题")
    private String title;

    @Schema(description = "公告副标题")
    private String subtitle;

    @Schema(description = "公告内容")
    private String content;

    @Schema(description = "公告摘要")
    private String summary;

    @Schema(description = "公告类型")
    private String type;

    @Schema(description = "公告分类")
    private String category;

    @Schema(description = "重要程度")
    private String importance;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "公告状态")
    private String status;

    @Schema(description = "是否置顶")
    private Boolean isPinned;

    @Schema(description = "是否紧急")
    private Boolean isUrgent;

    @Schema(description = "是否推送")
    private Boolean isPushEnabled;

    @Schema(description = "是否邮件通知")
    private Boolean isEmailNotification;

    @Schema(description = "是否短信通知")
    private Boolean isSmsNotification;

    @Schema(description = "是否弹窗提醒")
    private Boolean isPopupAlert;

    @Schema(description = "目标用户群体")
    private List<String> targetAudience;

    @Schema(description = "适用地区")
    private List<String> applicableRegions;

    @Schema(description = "相关交易对")
    private List<String> relatedSymbols;

    @Schema(description = "相关功能")
    private List<String> relatedFeatures;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "关键词")
    private List<String> keywords;

    @Schema(description = "发布者")
    private String publisher;

    @Schema(description = "发布者角色")
    private String publisherRole;

    @Schema(description = "审核者")
    private String reviewer;

    @Schema(description = "审核状态")
    private String reviewStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;

    @Schema(description = "生效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectiveTime;

    @Schema(description = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiryTime;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "阅读次数")
    private Long readCount;

    @Schema(description = "点赞次数")
    private Long likeCount;

    @Schema(description = "分享次数")
    private Long shareCount;

    @Schema(description = "评论次数")
    private Long commentCount;

    @Schema(description = "收藏次数")
    private Long favoriteCount;

    @Schema(description = "语言")
    private String language;

    @Schema(description = "多语言版本")
    private List<LanguageVersion> languageVersions;

    @Schema(description = "附件列表")
    private List<Attachment> attachments;

    @Schema(description = "相关链接")
    private List<RelatedLink> relatedLinks;

    @Schema(description = "图片列表")
    private List<String> imageUrls;

    @Schema(description = "视频列表")
    private List<String> videoUrls;

    @Schema(description = "封面图片")
    private String coverImageUrl;

    @Schema(description = "缩略图")
    private String thumbnailUrl;

    @Schema(description = "公告链接")
    private String announcementUrl;

    @Schema(description = "外部链接")
    private String externalUrl;

    @Schema(description = "QR码")
    private String qrCodeUrl;

    @Schema(description = "显示位置")
    private List<String> displayLocations;

    @Schema(description = "显示条件")
    private String displayCondition;

    @Schema(description = "显示次数限制")
    private Integer displayLimit;

    @Schema(description = "已显示次数")
    private Integer displayedCount;

    @Schema(description = "点击次数")
    private Long clickCount;

    @Schema(description = "点击率(%)")
    private Double clickRate;

    @Schema(description = "转化次数")
    private Long conversionCount;

    @Schema(description = "转化率(%)")
    private Double conversionRate;

    @Schema(description = "用户反馈")
    private List<UserFeedback> userFeedbacks;

    @Schema(description = "SEO关键词")
    private List<String> seoKeywords;

    @Schema(description = "SEO描述")
    private String seoDescription;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "修订历史")
    private List<RevisionHistory> revisionHistory;

    @Schema(description = "是否需要确认")
    private Boolean requiresConfirmation;

    @Schema(description = "确认截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime confirmationDeadline;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "合规状态")
    private String complianceStatus;

    @Schema(description = "法律声明")
    private String legalDisclaimer;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 语言版本
     */
    @Data
    @Schema(description = "语言版本")
    public static class LanguageVersion {
        
        @Schema(description = "语言代码")
        private String languageCode;
        
        @Schema(description = "语言名称")
        private String languageName;
        
        @Schema(description = "标题")
        private String title;
        
        @Schema(description = "内容")
        private String content;
        
        @Schema(description = "摘要")
        private String summary;
        
        @Schema(description = "公告链接")
        private String announcementUrl;
    }

    /**
     * 附件
     */
    @Data
    @Schema(description = "附件")
    public static class Attachment {
        
        @Schema(description = "附件ID")
        private String attachmentId;
        
        @Schema(description = "文件名")
        private String fileName;
        
        @Schema(description = "文件类型")
        private String fileType;
        
        @Schema(description = "文件大小(字节)")
        private Long fileSize;
        
        @Schema(description = "文件URL")
        private String fileUrl;
        
        @Schema(description = "下载次数")
        private Long downloadCount;
        
        @Schema(description = "上传时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime uploadTime;
    }

    /**
     * 相关链接
     */
    @Data
    @Schema(description = "相关链接")
    public static class RelatedLink {
        
        @Schema(description = "链接标题")
        private String title;
        
        @Schema(description = "链接URL")
        private String url;
        
        @Schema(description = "链接描述")
        private String description;
        
        @Schema(description = "链接类型")
        private String linkType;
        
        @Schema(description = "是否外部链接")
        private Boolean isExternal;
    }

    /**
     * 用户反馈
     */
    @Data
    @Schema(description = "用户反馈")
    public static class UserFeedback {
        
        @Schema(description = "反馈ID")
        private String feedbackId;
        
        @Schema(description = "用户ID")
        private String userId;
        
        @Schema(description = "反馈类型")
        private String feedbackType;
        
        @Schema(description = "反馈内容")
        private String content;
        
        @Schema(description = "评分")
        private Integer rating;
        
        @Schema(description = "反馈时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime feedbackTime;
    }

    /**
     * 修订历史
     */
    @Data
    @Schema(description = "修订历史")
    public static class RevisionHistory {
        
        @Schema(description = "版本号")
        private String version;
        
        @Schema(description = "修订描述")
        private String description;
        
        @Schema(description = "修订者")
        private String revisor;
        
        @Schema(description = "修订时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime revisionTime;
        
        @Schema(description = "变更内容")
        private List<String> changes;
    }
}