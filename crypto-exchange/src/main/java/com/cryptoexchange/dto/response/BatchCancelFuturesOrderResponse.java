package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量取消期货订单响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchCancelFuturesOrderResponse {
    
    /**
     * 成功取消的订单列表
     */
    private List<CancelledOrder> successOrders;
    
    /**
     * 取消失败的订单列表
     */
    private List<FailedOrder> failedOrders;
    
    /**
     * 总取消数量
     */
    private Integer totalCancelled;
    
    /**
     * 总失败数量
     */
    private Integer totalFailed;
    
    /**
     * 操作时间
     */
    private LocalDateTime operationTime;
    
    /**
     * 成功取消的订单信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CancelledOrder {
        /**
         * 订单ID
         */
        private Long orderId;
        
        /**
         * 客户端订单ID
         */
        private String clientOrderId;
        
        /**
         * 合约符号
         */
        private String symbol;
        
        /**
         * 取消时间
         */
        private LocalDateTime cancelTime;
    }
    
    /**
     * 取消失败的订单信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FailedOrder {
        /**
         * 订单ID
         */
        private Long orderId;
        
        /**
         * 客户端订单ID
         */
        private String clientOrderId;
        
        /**
         * 失败原因
         */
        private String reason;
        
        /**
         * 错误代码
         */
        private String errorCode;
    }
}