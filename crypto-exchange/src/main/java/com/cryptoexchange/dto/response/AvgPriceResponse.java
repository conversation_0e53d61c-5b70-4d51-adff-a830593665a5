package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 平均价格响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "平均价格响应")
public class AvgPriceResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "平均价格")
    private BigDecimal avgPrice;

    @Schema(description = "计算周期（分钟）")
    private Integer period;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "成交笔数")
    private Long tradeCount;

    @Schema(description = "总成交量")
    private BigDecimal totalVolume;

    @Schema(description = "总成交额")
    private BigDecimal totalAmount;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "价格变化")
    private BigDecimal priceChange;

    @Schema(description = "价格变化百分比")
    private BigDecimal priceChangePercent;

    @Schema(description = "加权平均价格")
    private BigDecimal weightedAvgPrice;

    @Schema(description = "成交量加权平均价格")
    private BigDecimal vwap;

    @Schema(description = "标准差")
    private BigDecimal standardDeviation;

    @Schema(description = "方差")
    private BigDecimal variance;

    @Schema(description = "数据时间戳")
    private Long timestamp;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "计算方法")
    private String calculationMethod;

    @Schema(description = "是否为实时数据")
    private Boolean isRealTime;

    @Schema(description = "数据质量评分")
    private BigDecimal qualityScore;
}