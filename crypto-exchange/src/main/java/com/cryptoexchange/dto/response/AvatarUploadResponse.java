package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 头像上传响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "头像上传响应")
public class AvatarUploadResponse {

    @Schema(description = "头像URL", example = "https://example.com/avatars/user123.jpg")
    private String avatarUrl;

    @Schema(description = "文件名", example = "user123.jpg")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "102400")
    private Long fileSize;

    @Schema(description = "文件类型", example = "image/jpeg")
    private String contentType;

    @Schema(description = "上传时间戳", example = "1640995200000")
    private Long uploadTime;

    public AvatarUploadResponse() {}

    public AvatarUploadResponse(String avatarUrl, String fileName, Long fileSize, String contentType) {
        this.avatarUrl = avatarUrl;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.contentType = contentType;
        this.uploadTime = System.currentTimeMillis();
    }
}