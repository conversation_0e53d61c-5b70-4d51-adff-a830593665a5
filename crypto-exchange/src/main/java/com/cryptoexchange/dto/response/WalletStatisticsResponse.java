package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 钱包统计响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletStatisticsResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 统计时间范围
     */
    private String timeRange;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 总资产价值
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 计价货币
     */
    private String quoteCurrency;
    
    /**
     * 资产变化统计
     */
    private AssetChangeStats assetChangeStats;
    
    /**
     * 交易统计
     */
    private TransactionStats transactionStats;
    
    /**
     * 钱包类型统计
     */
    private List<WalletTypeStats> walletTypeStats;
    
    /**
     * 货币统计
     */
    private List<CurrencyStats> currencyStats;
    
    /**
     * 收益统计
     */
    private ProfitStats profitStats;
    
    /**
     * 风险统计
     */
    private RiskStats riskStats;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 资产变化统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssetChangeStats {
        /**
         * 期初资产价值
         */
        private BigDecimal initialValue;
        
        /**
         * 期末资产价值
         */
        private BigDecimal finalValue;
        
        /**
         * 绝对变化
         */
        private BigDecimal absoluteChange;
        
        /**
         * 百分比变化
         */
        private BigDecimal percentageChange;
        
        /**
         * 最高资产价值
         */
        private BigDecimal maxValue;
        
        /**
         * 最低资产价值
         */
        private BigDecimal minValue;
        
        /**
         * 平均资产价值
         */
        private BigDecimal avgValue;
        
        /**
         * 波动率
         */
        private BigDecimal volatility;
    }
    
    /**
     * 交易统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransactionStats {
        /**
         * 充值次数
         */
        private Integer depositCount;
        
        /**
         * 充值总额
         */
        private BigDecimal totalDepositAmount;
        
        /**
         * 提现次数
         */
        private Integer withdrawCount;
        
        /**
         * 提现总额
         */
        private BigDecimal totalWithdrawAmount;
        
        /**
         * 转账次数
         */
        private Integer transferCount;
        
        /**
         * 转账总额
         */
        private BigDecimal totalTransferAmount;
        
        /**
         * 交易次数
         */
        private Integer tradeCount;
        
        /**
         * 交易总额
         */
        private BigDecimal totalTradeAmount;
        
        /**
         * 手续费总额
         */
        private BigDecimal totalFeeAmount;
    }
    
    /**
     * 钱包类型统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WalletTypeStats {
        /**
         * 钱包类型
         */
        private String walletType;
        
        /**
         * 钱包名称
         */
        private String walletName;
        
        /**
         * 当前价值
         */
        private BigDecimal currentValue;
        
        /**
         * 期初价值
         */
        private BigDecimal initialValue;
        
        /**
         * 价值变化
         */
        private BigDecimal valueChange;
        
        /**
         * 价值变化百分比
         */
        private BigDecimal valueChangePercent;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 资产种类数量
         */
        private Integer assetCount;
        
        /**
         * 活跃度
         */
        private BigDecimal activityLevel;
    }
    
    /**
     * 货币统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CurrencyStats {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 货币名称
         */
        private String currencyName;
        
        /**
         * 当前余额
         */
        private BigDecimal currentBalance;
        
        /**
         * 当前价值
         */
        private BigDecimal currentValue;
        
        /**
         * 期初余额
         */
        private BigDecimal initialBalance;
        
        /**
         * 期初价值
         */
        private BigDecimal initialValue;
        
        /**
         * 余额变化
         */
        private BigDecimal balanceChange;
        
        /**
         * 价值变化
         */
        private BigDecimal valueChange;
        
        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 交易次数
         */
        private Integer transactionCount;
    }
    
    /**
     * 收益统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ProfitStats {
        /**
         * 总收益
         */
        private BigDecimal totalProfit;
        
        /**
         * 已实现收益
         */
        private BigDecimal realizedProfit;
        
        /**
         * 未实现收益
         */
        private BigDecimal unrealizedProfit;
        
        /**
         * 收益率
         */
        private BigDecimal profitRate;
        
        /**
         * 年化收益率
         */
        private BigDecimal annualizedReturn;
        
        /**
         * 最大回撤
         */
        private BigDecimal maxDrawdown;
        
        /**
         * 夏普比率
         */
        private BigDecimal sharpeRatio;
    }
    
    /**
     * 风险统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RiskStats {
        /**
         * 风险等级
         */
        private String riskLevel;
        
        /**
         * 风险评分
         */
        private BigDecimal riskScore;
        
        /**
         * 集中度风险
         */
        private BigDecimal concentrationRisk;
        
        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;
        
        /**
         * 市场风险
         */
        private BigDecimal marketRisk;
        
        /**
         * VaR（风险价值）
         */
        private BigDecimal valueAtRisk;
        
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;
    }
}