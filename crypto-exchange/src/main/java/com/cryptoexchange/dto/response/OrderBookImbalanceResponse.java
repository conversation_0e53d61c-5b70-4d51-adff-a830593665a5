package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单簿不平衡响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderBookImbalanceResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 总体不平衡指数 (-1到1之间，负值表示卖压，正值表示买压)
     */
    private BigDecimal overallImbalanceIndex;

    /**
     * 不平衡等级
     */
    private ImbalanceLevel imbalanceLevel;

    /**
     * 买单总量
     */
    private BigDecimal totalBidVolume;

    /**
     * 卖单总量
     */
    private BigDecimal totalAskVolume;

    /**
     * 买单总价值
     */
    private BigDecimal totalBidValue;

    /**
     * 卖单总价值
     */
    private BigDecimal totalAskValue;

    /**
     * 深度不平衡分析
     */
    private DepthImbalanceAnalysis depthImbalanceAnalysis;

    /**
     * 价格层级不平衡
     */
    private List<PriceLevelImbalance> priceLevelImbalances;

    /**
     * 时间序列不平衡
     */
    private TimeSeriesImbalance timeSeriesImbalance;

    /**
     * 流动性分布
     */
    private LiquidityDistribution liquidityDistribution;

    /**
     * 市场压力指标
     */
    private MarketPressureIndicators marketPressureIndicators;

    /**
     * 预测分析
     */
    private PredictiveAnalysis predictiveAnalysis;

    /**
     * 交易建议
     */
    private List<TradingRecommendation> tradingRecommendations;

    /**
     * 风险警告
     */
    private List<RiskWarning> riskWarnings;

    /**
     * 不平衡等级
     */
    public enum ImbalanceLevel {
        EXTREME_SELL_PRESSURE("极度卖压", -4),
        HIGH_SELL_PRESSURE("高度卖压", -3),
        MODERATE_SELL_PRESSURE("中度卖压", -2),
        SLIGHT_SELL_PRESSURE("轻度卖压", -1),
        BALANCED("平衡", 0),
        SLIGHT_BUY_PRESSURE("轻度买压", 1),
        MODERATE_BUY_PRESSURE("中度买压", 2),
        HIGH_BUY_PRESSURE("高度买压", 3),
        EXTREME_BUY_PRESSURE("极度买压", 4);

        private final String description;
        private final Integer level;

        ImbalanceLevel(String description, Integer level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }

        public static ImbalanceLevel fromIndex(BigDecimal index) {
            if (index == null) return BALANCED;
            
            double value = index.doubleValue();
            if (value <= -0.8) return EXTREME_SELL_PRESSURE;
            else if (value <= -0.6) return HIGH_SELL_PRESSURE;
            else if (value <= -0.3) return MODERATE_SELL_PRESSURE;
            else if (value <= -0.1) return SLIGHT_SELL_PRESSURE;
            else if (value <= 0.1) return BALANCED;
            else if (value <= 0.3) return SLIGHT_BUY_PRESSURE;
            else if (value <= 0.6) return MODERATE_BUY_PRESSURE;
            else if (value <= 0.8) return HIGH_BUY_PRESSURE;
            else return EXTREME_BUY_PRESSURE;
        }
    }

    /**
     * 深度不平衡分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DepthImbalanceAnalysis {
        /**
         * 顶层不平衡（最优买卖价）
         */
        private BigDecimal topLevelImbalance;

        /**
         * 5档不平衡
         */
        private BigDecimal fiveLevelImbalance;

        /**
         * 10档不平衡
         */
        private BigDecimal tenLevelImbalance;

        /**
         * 20档不平衡
         */
        private BigDecimal twentyLevelImbalance;

        /**
         * 加权不平衡（按价格距离加权）
         */
        private BigDecimal weightedImbalance;

        /**
         * 有效深度不平衡
         */
        private BigDecimal effectiveDepthImbalance;
    }

    /**
     * 价格层级不平衡
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceLevelImbalance {
        /**
         * 价格层级
         */
        private Integer priceLevel;

        /**
         * 买单价格
         */
        private BigDecimal bidPrice;

        /**
         * 卖单价格
         */
        private BigDecimal askPrice;

        /**
         * 买单数量
         */
        private BigDecimal bidQuantity;

        /**
         * 卖单数量
         */
        private BigDecimal askQuantity;

        /**
         * 该层级不平衡指数
         */
        private BigDecimal levelImbalanceIndex;

        /**
         * 累积不平衡指数
         */
        private BigDecimal cumulativeImbalanceIndex;

        /**
         * 价格影响权重
         */
        private BigDecimal priceImpactWeight;
    }

    /**
     * 时间序列不平衡
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeSeriesImbalance {
        /**
         * 1分钟不平衡趋势
         */
        private BigDecimal oneMinuteTrend;

        /**
         * 5分钟不平衡趋势
         */
        private BigDecimal fiveMinuteTrend;

        /**
         * 15分钟不平衡趋势
         */
        private BigDecimal fifteenMinuteTrend;

        /**
         * 不平衡持续时间（秒）
         */
        private Integer imbalanceDuration;

        /**
         * 不平衡强度变化率
         */
        private BigDecimal intensityChangeRate;

        /**
         * 历史不平衡数据
         */
        private List<HistoricalImbalancePoint> historicalData;
    }

    /**
     * 历史不平衡点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalImbalancePoint {
        private LocalDateTime timestamp;
        private BigDecimal imbalanceIndex;
        private BigDecimal volume;
        private BigDecimal price;
    }

    /**
     * 流动性分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityDistribution {
        /**
         * 买方流动性分布
         */
        private Map<String, BigDecimal> bidLiquidityDistribution;

        /**
         * 卖方流动性分布
         */
        private Map<String, BigDecimal> askLiquidityDistribution;

        /**
         * 流动性集中度
         */
        private BigDecimal liquidityConcentration;

        /**
         * 流动性分散度
         */
        private BigDecimal liquidityDispersion;

        /**
         * 大单占比
         */
        private BigDecimal largeOrderRatio;

        /**
         * 小单占比
         */
        private BigDecimal smallOrderRatio;
    }

    /**
     * 市场压力指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketPressureIndicators {
        /**
         * 买压强度
         */
        private BigDecimal buyPressureIntensity;

        /**
         * 卖压强度
         */
        private BigDecimal sellPressureIntensity;

        /**
         * 压力不对称性
         */
        private BigDecimal pressureAsymmetry;

        /**
         * 突发压力指标
         */
        private BigDecimal suddenPressureIndicator;

        /**
         * 持续压力指标
         */
        private BigDecimal sustainedPressureIndicator;

        /**
         * 压力释放预期
         */
        private BigDecimal pressureReleaseExpectation;
    }

    /**
     * 预测分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictiveAnalysis {
        /**
         * 短期价格方向预测
         */
        private String shortTermPriceDirection;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 预期价格变动幅度
         */
        private BigDecimal expectedPriceMovement;

        /**
         * 不平衡消解时间预估
         */
        private Integer imbalanceResolutionTime;

        /**
         * 流动性恢复预期
         */
        private String liquidityRecoveryExpectation;

        /**
         * 风险概率
         */
        private Map<String, BigDecimal> riskProbabilities;
    }

    /**
     * 交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingRecommendation {
        /**
         * 建议类型
         */
        private String recommendationType;

        /**
         * 建议操作
         */
        private String recommendedAction;

        /**
         * 建议时机
         */
        private String timing;

        /**
         * 建议规模
         */
        private BigDecimal recommendedSize;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 优先级
         */
        private Integer priority;
    }

    /**
     * 风险警告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskWarning {
        /**
         * 警告类型
         */
        private String warningType;

        /**
         * 警告级别
         */
        private String warningLevel;

        /**
         * 警告内容
         */
        private String warningMessage;

        /**
         * 影响程度
         */
        private BigDecimal impactSeverity;

        /**
         * 建议措施
         */
        private String recommendedAction;

        /**
         * 警告时效
         */
        private LocalDateTime warningExpiry;
    }

    /**
     * 获取市场状态描述
     */
    public String getMarketStateDescription() {
        if (imbalanceLevel == null) {
            return "市场状态未知";
        }
        
        return String.format("市场状态: %s (不平衡指数: %s)",
            imbalanceLevel.getDescription(),
            overallImbalanceIndex != null ? overallImbalanceIndex.toString() : "N/A");
    }

    /**
     * 检查是否存在极端不平衡
     */
    public boolean hasExtremeImbalance() {
        return imbalanceLevel == ImbalanceLevel.EXTREME_BUY_PRESSURE || 
               imbalanceLevel == ImbalanceLevel.EXTREME_SELL_PRESSURE;
    }

    /**
     * 获取主导方向
     */
    public String getDominantDirection() {
        if (overallImbalanceIndex == null) {
            return "未知";
        }
        
        if (overallImbalanceIndex.compareTo(BigDecimal.ZERO) > 0) {
            return "买方主导";
        } else if (overallImbalanceIndex.compareTo(BigDecimal.ZERO) < 0) {
            return "卖方主导";
        } else {
            return "平衡";
        }
    }

    /**
     * 获取不平衡强度
     */
    public String getImbalanceIntensity() {
        if (overallImbalanceIndex == null) {
            return "未知";
        }
        
        BigDecimal abs = overallImbalanceIndex.abs();
        if (abs.compareTo(BigDecimal.valueOf(0.2)) <= 0) {
            return "轻微";
        } else if (abs.compareTo(BigDecimal.valueOf(0.5)) <= 0) {
            return "中等";
        } else if (abs.compareTo(BigDecimal.valueOf(0.8)) <= 0) {
            return "强烈";
        } else {
            return "极端";
        }
    }
}