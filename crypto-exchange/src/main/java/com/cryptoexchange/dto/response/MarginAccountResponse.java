package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保证金账户响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class MarginAccountResponse {

    /**
     * 账户ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 账户余额
     */
    private BigDecimal balance;

    /**
     * 可用余额
     */
    private BigDecimal availableBalance;

    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;

    /**
     * 保证金余额
     */
    private BigDecimal marginBalance;

    /**
     * 已用保证金
     */
    private BigDecimal usedMargin;

    /**
     * 可用保证金
     */
    private BigDecimal availableMargin;

    /**
     * 持仓保证金
     */
    private BigDecimal positionMargin;

    /**
     * 委托保证金
     */
    private BigDecimal orderMargin;

    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedPnl;

    /**
     * 已实现盈亏
     */
    private BigDecimal realizedPnl;

    /**
     * 钱包余额
     */
    private BigDecimal walletBalance;

    /**
     * 总权益
     */
    private BigDecimal totalEquity;

    /**
     * 风险率
     */
    private BigDecimal riskRatio;

    /**
     * 维持保证金率
     */
    private BigDecimal maintenanceMarginRate;

    /**
     * 初始保证金率
     */
    private BigDecimal initialMarginRate;

    /**
     * 最大可开仓价值
     */
    private BigDecimal maxOpenValue;

    /**
     * 最大可提取金额
     */
    private BigDecimal maxWithdrawAmount;

    /**
     * 账户状态：1-正常，2-风险，3-强平中，4-已爆仓
     */
    private Integer status;

    /**
     * 是否启用自动追加保证金
     */
    private Boolean autoAddMarginEnabled;

    /**
     * 自动追加保证金阈值
     */
    private BigDecimal autoAddMarginThreshold;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}