package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "钱包响应")
public class WalletResponse {

    @Schema(description = "钱包ID", example = "123456")
    private Long walletId;

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "币种", example = "BTC")
    private String currency;

    @Schema(description = "账户类型：SPOT-现货，FUTURES-合约，MARGIN-杠杆", example = "SPOT")
    private String accountType;

    @Schema(description = "可用余额", example = "1.********")
    private BigDecimal availableBalance;

    @Schema(description = "冻结余额", example = "0.********")
    private BigDecimal frozenBalance;

    @Schema(description = "总余额", example = "1.********")
    private BigDecimal totalBalance;

    @Schema(description = "币种全名", example = "Bitcoin")
    private String currencyName;

    @Schema(description = "币种图标URL", example = "https://example.com/btc.png")
    private String currencyIcon;

    @Schema(description = "币种精度", example = "8")
    private Integer precision;

    @Schema(description = "最小提现金额", example = "0.001")
    private BigDecimal minWithdrawAmount;

    @Schema(description = "最大提现金额", example = "10.0")
    private BigDecimal maxWithdrawAmount;

    @Schema(description = "提现手续费", example = "0.0005")
    private BigDecimal withdrawFee;

    @Schema(description = "是否支持充值", example = "true")
    private Boolean depositEnabled;

    @Schema(description = "是否支持提现", example = "true")
    private Boolean withdrawEnabled;

    @Schema(description = "是否支持交易", example = "true")
    private Boolean tradingEnabled;

    @Schema(description = "钱包状态：NORMAL-正常，FROZEN-冻结，DISABLED-禁用", example = "NORMAL")
    private String status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "估值（USDT）", example = "67890.12")
    private BigDecimal usdtValue;

    @Schema(description = "24小时变化率", example = "0.0523")
    private BigDecimal change24h;
}