package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易对状态响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易对状态响应")
public class SymbolStatusResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "基础货币名称")
    private String baseAssetName;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "计价货币名称")
    private String quoteAssetName;

    @Schema(description = "交易状态")
    private String status;

    @Schema(description = "状态描述")
    private String statusDescription;

    @Schema(description = "是否支持交易")
    private Boolean tradingEnabled;

    @Schema(description = "是否支持现货交易")
    private Boolean spotTradingEnabled;

    @Schema(description = "是否支持杠杆交易")
    private Boolean marginTradingEnabled;

    @Schema(description = "是否支持合约交易")
    private Boolean futuresTradingEnabled;

    @Schema(description = "是否支持期权交易")
    private Boolean optionsTradingEnabled;

    @Schema(description = "是否支持充值")
    private Boolean depositEnabled;

    @Schema(description = "是否支持提现")
    private Boolean withdrawEnabled;

    @Schema(description = "是否支持买入")
    private Boolean buyEnabled;

    @Schema(description = "是否支持卖出")
    private Boolean sellEnabled;

    @Schema(description = "是否暂停交易")
    private Boolean tradingSuspended;

    @Schema(description = "暂停原因")
    private String suspensionReason;

    @Schema(description = "预计恢复时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expectedResumeTime;

    @Schema(description = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingTime;

    @Schema(description = "下线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime delistingTime;

    @Schema(description = "是否为新币")
    private Boolean isNewListing;

    @Schema(description = "是否即将下线")
    private Boolean isDelisting;

    @Schema(description = "价格精度")
    private Integer pricePrecision;

    @Schema(description = "数量精度")
    private Integer quantityPrecision;

    @Schema(description = "最小交易量")
    private BigDecimal minTradeQuantity;

    @Schema(description = "最大交易量")
    private BigDecimal maxTradeQuantity;

    @Schema(description = "最小交易额")
    private BigDecimal minTradeAmount;

    @Schema(description = "最大交易额")
    private BigDecimal maxTradeAmount;

    @Schema(description = "最小价格变动")
    private BigDecimal tickSize;

    @Schema(description = "最小数量变动")
    private BigDecimal stepSize;

    @Schema(description = "手续费率")
    private BigDecimal feeRate;

    @Schema(description = "买入手续费率")
    private BigDecimal buyFeeRate;

    @Schema(description = "卖出手续费率")
    private BigDecimal sellFeeRate;

    @Schema(description = "做市商手续费率")
    private BigDecimal makerFeeRate;

    @Schema(description = "吃单手续费率")
    private BigDecimal takerFeeRate;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "24小时涨跌幅")
    private BigDecimal changePercent24h;

    @Schema(description = "24小时成交量")
    private BigDecimal volume24h;

    @Schema(description = "24小时成交额")
    private BigDecimal amount24h;

    @Schema(description = "市值")
    private BigDecimal marketCap;

    @Schema(description = "市值排名")
    private Integer marketCapRank;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "风险评分")
    private BigDecimal riskScore;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "交易限制")
    private List<TradingRestriction> tradingRestrictions;

    @Schema(description = "维护计划")
    private List<MaintenancePlan> maintenancePlans;

    @Schema(description = "公告信息")
    private List<AnnouncementInfo> announcements;

    @Schema(description = "特殊标记")
    private List<String> specialMarks;

    @Schema(description = "合规状态")
    private String complianceStatus;

    @Schema(description = "监管信息")
    private String regulatoryInfo;

    @Schema(description = "地区限制")
    private List<String> regionRestrictions;

    @Schema(description = "KYC要求等级")
    private String kycRequiredLevel;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 交易限制
     */
    @Data
    @Schema(description = "交易限制")
    public static class TradingRestriction {
        
        @Schema(description = "限制类型")
        private String restrictionType;
        
        @Schema(description = "限制描述")
        private String description;
        
        @Schema(description = "限制值")
        private BigDecimal restrictionValue;
        
        @Schema(description = "限制单位")
        private String unit;
        
        @Schema(description = "生效时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime effectiveTime;
        
        @Schema(description = "失效时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expiryTime;
        
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 维护计划
     */
    @Data
    @Schema(description = "维护计划")
    public static class MaintenancePlan {
        
        @Schema(description = "维护ID")
        private String maintenanceId;
        
        @Schema(description = "维护类型")
        private String maintenanceType;
        
        @Schema(description = "维护描述")
        private String description;
        
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
        
        @Schema(description = "预计持续时间(分钟)")
        private Integer estimatedDuration;
        
        @Schema(description = "影响功能")
        private List<String> affectedFunctions;
        
        @Schema(description = "维护状态")
        private String status;
    }

    /**
     * 公告信息
     */
    @Data
    @Schema(description = "公告信息")
    public static class AnnouncementInfo {
        
        @Schema(description = "公告ID")
        private String announcementId;
        
        @Schema(description = "公告标题")
        private String title;
        
        @Schema(description = "公告内容")
        private String content;
        
        @Schema(description = "公告类型")
        private String type;
        
        @Schema(description = "重要程度")
        private String importance;
        
        @Schema(description = "发布时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime publishTime;
        
        @Schema(description = "生效时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime effectiveTime;
        
        @Schema(description = "公告链接")
        private String announcementUrl;
    }
}