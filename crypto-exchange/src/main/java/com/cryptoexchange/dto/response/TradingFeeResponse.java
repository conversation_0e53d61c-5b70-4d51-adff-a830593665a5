package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易手续费响应
 */
public class TradingFeeResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * Maker手续费率
     */
    private BigDecimal makerCommission;
    
    /**
     * Taker手续费率
     */
    private BigDecimal takerCommission;
    
    /**
     * 用户等级
     */
    private Integer userLevel;
    
    /**
     * 30天交易量
     */
    private BigDecimal volume30d;
    
    /**
     * BNB持有量
     */
    private BigDecimal bnbBalance;
    
    /**
     * 是否使用BNB抵扣手续费
     */
    private Boolean usingBnbDiscount;
    
    /**
     * BNB抵扣后的Maker手续费率
     */
    private BigDecimal discountedMakerRate;
    
    /**
     * BNB抵扣后的Taker手续费率
     */
    private BigDecimal discountedTakerRate;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    public TradingFeeResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public BigDecimal getMakerCommission() {
        return makerCommission;
    }
    
    public void setMakerCommission(BigDecimal makerCommission) {
        this.makerCommission = makerCommission;
    }
    
    public BigDecimal getTakerCommission() {
        return takerCommission;
    }
    
    public void setTakerCommission(BigDecimal takerCommission) {
        this.takerCommission = takerCommission;
    }
    
    public Integer getUserLevel() {
        return userLevel;
    }
    
    public void setUserLevel(Integer userLevel) {
        this.userLevel = userLevel;
    }
    
    public BigDecimal getVolume30d() {
        return volume30d;
    }
    
    public void setVolume30d(BigDecimal volume30d) {
        this.volume30d = volume30d;
    }
    
    public BigDecimal getBnbBalance() {
        return bnbBalance;
    }
    
    public void setBnbBalance(BigDecimal bnbBalance) {
        this.bnbBalance = bnbBalance;
    }
    
    public Boolean getUsingBnbDiscount() {
        return usingBnbDiscount;
    }
    
    public void setUsingBnbDiscount(Boolean usingBnbDiscount) {
        this.usingBnbDiscount = usingBnbDiscount;
    }
    
    public BigDecimal getDiscountedMakerRate() {
        return discountedMakerRate;
    }
    
    public void setDiscountedMakerRate(BigDecimal discountedMakerRate) {
        this.discountedMakerRate = discountedMakerRate;
    }
    
    public BigDecimal getDiscountedTakerRate() {
        return discountedTakerRate;
    }
    
    public void setDiscountedTakerRate(BigDecimal discountedTakerRate) {
        this.discountedTakerRate = discountedTakerRate;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}