package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单响应")
public class OrderResponse {

    @Schema(description = "订单ID", example = "123456")
    private Long orderId;

    @Schema(description = "客户端订单ID", example = "my_order_123")
    private String clientOrderId;

    @Schema(description = "交易对", example = "BTCUSDT")
    private String symbol;

    @Schema(description = "订单类型：MARKET-市价，LIMIT-限价", example = "LIMIT")
    private String type;

    @Schema(description = "交易方向：BUY-买入，SELL-卖出", example = "BUY")
    private String side;

    @Schema(description = "订单状态：NEW-新建，PARTIALLY_FILLED-部分成交，FILLED-完全成交，CANCELED-已取消，REJECTED-已拒绝", example = "NEW")
    private String status;

    @Schema(description = "原始数量", example = "0.001")
    private BigDecimal origQty;

    @Schema(description = "已成交数量", example = "0.0005")
    private BigDecimal executedQty;

    @Schema(description = "累计成交金额", example = "25.00")
    private BigDecimal cummulativeQuoteQty;

    @Schema(description = "订单价格", example = "50000.00")
    private BigDecimal price;

    @Schema(description = "平均成交价格", example = "50000.00")
    private BigDecimal avgPrice;

    @Schema(description = "止损价格", example = "49000.00")
    private BigDecimal stopPrice;

    @Schema(description = "时效类型：GTC-撤销前有效，IOC-立即成交或撤销，FOK-全部成交或撤销", example = "GTC")
    private String timeInForce;

    @Schema(description = "手续费", example = "0.00001")
    private BigDecimal commission;

    @Schema(description = "手续费币种", example = "BTC")
    private String commissionAsset;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "冰山订单显示数量", example = "0.0001")
    private BigDecimal icebergQty;

    @Schema(description = "是否只做Maker", example = "false")
    private Boolean isWorking;
}