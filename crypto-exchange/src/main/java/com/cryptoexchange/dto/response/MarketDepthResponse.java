package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场深度响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场深度响应")
public class MarketDepthResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "最后更新ID")
    private Long lastUpdateId;

    @Schema(description = "买单深度列表")
    private List<PriceLevel> bids;

    @Schema(description = "卖单深度列表")
    private List<PriceLevel> asks;

    @Schema(description = "深度级别")
    private Integer level;

    @Schema(description = "数据时间戳")
    private Long timestamp;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "买一价")
    private BigDecimal bestBidPrice;

    @Schema(description = "买一量")
    private BigDecimal bestBidQty;

    @Schema(description = "卖一价")
    private BigDecimal bestAskPrice;

    @Schema(description = "卖一量")
    private BigDecimal bestAskQty;

    @Schema(description = "买卖价差")
    private BigDecimal spread;

    @Schema(description = "买卖价差百分比")
    private BigDecimal spreadPercent;

    @Schema(description = "总买单量")
    private BigDecimal totalBidQty;

    @Schema(description = "总卖单量")
    private BigDecimal totalAskQty;

    @Schema(description = "买卖比例")
    private BigDecimal bidAskRatio;

    @Schema(description = "深度质量评分")
    private BigDecimal depthScore;

    @Schema(description = "是否为实时数据")
    private Boolean isRealTime;

    @Schema(description = "数据延迟（毫秒）")
    private Long latency;

    /**
     * 价格级别
     */
    @Data
    @Schema(description = "价格级别")
    public static class PriceLevel {
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量")
        private BigDecimal quantity;
        
        @Schema(description = "订单数量")
        private Integer orderCount;
        
        @Schema(description = "累计数量")
        private BigDecimal cumulativeQty;
        
        @Schema(description = "累计金额")
        private BigDecimal cumulativeAmount;
        
        @Schema(description = "价格级别权重")
        private BigDecimal weight;
    }
}