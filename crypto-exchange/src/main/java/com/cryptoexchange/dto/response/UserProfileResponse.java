package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户档案响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户档案响应")
public class UserProfileResponse {

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户名", example = "john_doe")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "脱敏邮箱", example = "j***@example.com")
    private String maskedEmail;

    @Schema(description = "手机号", example = "+1234567890")
    private String phone;

    @Schema(description = "脱敏手机号", example = "+123****7890")
    private String maskedPhone;

    @Schema(description = "昵称", example = "John")
    private String nickname;

    @Schema(description = "真实姓名", example = "John Doe")
    private String realName;

    @Schema(description = "脱敏真实姓名", example = "J***e")
    private String maskedRealName;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatarUrl;

    @Schema(description = "性别", example = "MALE", allowableValues = {"MALE", "FEMALE", "OTHER"})
    private String gender;

    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @Schema(description = "国家/地区", example = "CN")
    private String country;

    @Schema(description = "省份/州", example = "Beijing")
    private String province;

    @Schema(description = "城市", example = "Beijing")
    private String city;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "邮政编码", example = "100000")
    private String postalCode;

    @Schema(description = "时区", example = "Asia/Shanghai")
    private String timezone;

    @Schema(description = "语言偏好", example = "zh-CN")
    private String language;

    @Schema(description = "货币偏好", example = "CNY")
    private String currency;

    @Schema(description = "用户状态", example = "ACTIVE")
    private String status;

    @Schema(description = "用户等级", example = "VIP1")
    private String userLevel;

    @Schema(description = "KYC状态", example = "VERIFIED")
    private String kycStatus;

    @Schema(description = "KYC等级", example = "LEVEL_2")
    private String kycLevel;

    @Schema(description = "是否启用2FA", example = "true")
    private Boolean twoFactorEnabled;

    @Schema(description = "是否启用邮箱验证", example = "true")
    private Boolean emailVerified;

    @Schema(description = "是否启用手机验证", example = "true")
    private Boolean phoneVerified;

    @Schema(description = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActiveTime;

    @Schema(description = "推荐码", example = "ABC123")
    private String referralCode;

    @Schema(description = "推荐人用户名", example = "referrer_user")
    private String referrerUsername;

    @Schema(description = "用户标签")
    private List<String> tags;

    @Schema(description = "用户偏好设置")
    private Map<String, Object> preferences;

    @Schema(description = "隐私设置")
    private Map<String, Boolean> privacySettings;

    @Schema(description = "通知设置")
    private Map<String, Boolean> notificationSettings;

    @Schema(description = "安全设置")
    private Map<String, Object> securitySettings;

    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "职业")
    private String occupation;

    @Schema(description = "公司")
    private String company;

    @Schema(description = "网站")
    private String website;

    @Schema(description = "社交媒体链接")
    private Map<String, String> socialLinks;

    @Schema(description = "是否公开档案", example = "false")
    private Boolean isPublic;

    @Schema(description = "档案完整度", example = "85")
    private Integer profileCompleteness;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    public UserProfileResponse() {}

    public UserProfileResponse(Long userId, String username, String email, String nickname) {
        this.userId = userId;
        this.username = username;
        this.email = email;
        this.nickname = nickname;
        this.twoFactorEnabled = false;
        this.emailVerified = false;
        this.phoneVerified = false;
        this.isPublic = false;
        this.profileCompleteness = 0;
    }

    /**
     * 计算档案完整度
     */
    public void calculateProfileCompleteness() {
        int totalFields = 20; // 总字段数
        int completedFields = 0;
        
        if (username != null && !username.isEmpty()) completedFields++;
        if (email != null && !email.isEmpty()) completedFields++;
        if (phone != null && !phone.isEmpty()) completedFields++;
        if (nickname != null && !nickname.isEmpty()) completedFields++;
        if (realName != null && !realName.isEmpty()) completedFields++;
        if (avatarUrl != null && !avatarUrl.isEmpty()) completedFields++;
        if (gender != null && !gender.isEmpty()) completedFields++;
        if (birthday != null) completedFields++;
        if (country != null && !country.isEmpty()) completedFields++;
        if (province != null && !province.isEmpty()) completedFields++;
        if (city != null && !city.isEmpty()) completedFields++;
        if (address != null && !address.isEmpty()) completedFields++;
        if (timezone != null && !timezone.isEmpty()) completedFields++;
        if (language != null && !language.isEmpty()) completedFields++;
        if (currency != null && !currency.isEmpty()) completedFields++;
        if (emailVerified != null && emailVerified) completedFields++;
        if (phoneVerified != null && phoneVerified) completedFields++;
        if (twoFactorEnabled != null && twoFactorEnabled) completedFields++;
        if (bio != null && !bio.isEmpty()) completedFields++;
        if (occupation != null && !occupation.isEmpty()) completedFields++;
        
        this.profileCompleteness = (int) ((completedFields * 100.0) / totalFields);
    }
}