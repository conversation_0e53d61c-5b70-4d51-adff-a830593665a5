package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 最优挂单价格响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BookTickerResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 最佳买价
     */
    private BigDecimal bidPrice;
    
    /**
     * 最佳买量
     */
    private BigDecimal bidQty;
    
    /**
     * 最佳卖价
     */
    private BigDecimal askPrice;
    
    /**
     * 最佳卖量
     */
    private BigDecimal askQty;
    
    /**
     * 买卖价差
     */
    private BigDecimal spread;
    
    /**
     * 买卖价差百分比
     */
    private BigDecimal spreadPercent;
    
    /**
     * 中间价
     */
    private BigDecimal midPrice;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 序列号
     */
    private Long sequence;
    
    /**
     * 交易对类型
     */
    private String type;
    
    /**
     * 数据来源
     */
    private String source;
}