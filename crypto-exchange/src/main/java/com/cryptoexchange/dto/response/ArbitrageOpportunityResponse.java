package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 套利机会响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArbitrageOpportunityResponse {

    /**
     * 套利机会ID
     */
    private String opportunityId;

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 套利类型
     */
    private ArbitrageType arbitrageType;

    /**
     * 预期收益率
     */
    private BigDecimal expectedReturn;

    /**
     * 风险评级
     */
    private RiskLevel riskLevel;

    /**
     * 套利路径
     */
    private List<ArbitragePath> arbitragePaths;

    /**
     * 最小投资金额
     */
    private BigDecimal minimumInvestment;

    /**
     * 最大投资金额
     */
    private BigDecimal maximumInvestment;

    /**
     * 预计执行时间（秒）
     */
    private Integer estimatedExecutionTime;

    /**
     * 机会有效期
     */
    private LocalDateTime expiryTime;

    /**
     * 发现时间
     */
    private LocalDateTime discoveryTime;

    /**
     * 置信度
     */
    private BigDecimal confidence;

    /**
     * 交易费用
     */
    private TradingFees tradingFees;

    /**
     * 市场条件
     */
    private MarketConditions marketConditions;

    /**
     * 执行建议
     */
    private List<ExecutionRecommendation> executionRecommendations;

    /**
     * 套利类型
     */
    public enum ArbitrageType {
        /**
         * 空间套利（跨交易所）
         */
        SPATIAL("空间套利", "利用不同交易所间的价格差异"),

        /**
         * 时间套利
         */
        TEMPORAL("时间套利", "利用同一资产在不同时间的价格差异"),

        /**
         * 三角套利
         */
        TRIANGULAR("三角套利", "利用三种货币间的汇率差异"),

        /**
         * 统计套利
         */
        STATISTICAL("统计套利", "基于统计模型的套利机会"),

        /**
         * 期现套利
         */
        CASH_FUTURES("期现套利", "现货与期货价格差异套利"),

        /**
         * 跨期套利
         */
        CALENDAR_SPREAD("跨期套利", "不同到期日合约间的价格差异");

        private final String description;
        private final String detail;

        ArbitrageType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 风险等级
     */
    public enum RiskLevel {
        LOW("低风险", 1),
        MEDIUM("中等风险", 2),
        HIGH("高风险", 3),
        VERY_HIGH("极高风险", 4);

        private final String description;
        private final Integer level;

        RiskLevel(String description, Integer level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }
    }

    /**
     * 套利路径
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ArbitragePath {
        /**
         * 步骤序号
         */
        private Integer stepNumber;

        /**
         * 交易所
         */
        private String exchange;

        /**
         * 交易对
         */
        private String tradingPair;

        /**
         * 操作类型（买入/卖出）
         */
        private String action;

        /**
         * 价格
         */
        private BigDecimal price;

        /**
         * 数量
         */
        private BigDecimal quantity;

        /**
         * 预期收益
         */
        private BigDecimal expectedProfit;

        /**
         * 执行优先级
         */
        private Integer priority;
    }

    /**
     * 交易费用
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingFees {
        /**
         * 交易手续费
         */
        private BigDecimal tradingFee;

        /**
         * 提现费用
         */
        private BigDecimal withdrawalFee;

        /**
         * 网络费用
         */
        private BigDecimal networkFee;

        /**
         * 总费用
         */
        private BigDecimal totalFees;

        /**
         * 费用占比
         */
        private BigDecimal feePercentage;
    }

    /**
     * 市场条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketConditions {
        /**
         * 市场波动率
         */
        private BigDecimal volatility;

        /**
         * 流动性指标
         */
        private BigDecimal liquidity;

        /**
         * 订单簿深度
         */
        private BigDecimal orderBookDepth;

        /**
         * 买卖价差
         */
        private BigDecimal bidAskSpread;

        /**
         * 交易量
         */
        private BigDecimal volume;

        /**
         * 市场趋势
         */
        private String marketTrend;
    }

    /**
     * 执行建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionRecommendation {
        /**
         * 建议类型
         */
        private String recommendationType;

        /**
         * 建议内容
         */
        private String recommendation;

        /**
         * 重要性等级
         */
        private Integer importance;

        /**
         * 预期影响
         */
        private String expectedImpact;

        /**
         * 执行时机
         */
        private String timing;
    }

    /**
     * 计算净收益率（扣除费用后）
     */
    public BigDecimal getNetReturn() {
        if (expectedReturn == null || tradingFees == null || tradingFees.getFeePercentage() == null) {
            return expectedReturn;
        }
        return expectedReturn.subtract(tradingFees.getFeePercentage());
    }

    /**
     * 检查套利机会是否仍然有效
     */
    public boolean isValid() {
        return expiryTime == null || LocalDateTime.now().isBefore(expiryTime);
    }

    /**
     * 获取风险收益比
     */
    public BigDecimal getRiskReturnRatio() {
        if (expectedReturn == null || riskLevel == null) {
            return BigDecimal.ZERO;
        }
        return expectedReturn.divide(BigDecimal.valueOf(riskLevel.getLevel()), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 获取套利机会摘要
     */
    public String getOpportunitySummary() {
        return String.format("%s套利机会 - 预期收益: %s%%, 风险等级: %s, 有效期至: %s",
            arbitrageType != null ? arbitrageType.getDescription() : "未知",
            expectedReturn != null ? expectedReturn.multiply(BigDecimal.valueOf(100)).toString() : "N/A",
            riskLevel != null ? riskLevel.getDescription() : "未知",
            expiryTime != null ? expiryTime.toString() : "无限期");
    }
}