package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 资产分布响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AssetDistributionResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总资产价值
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 计价货币
     */
    private String quoteCurrency;
    
    /**
     * 资产分布列表
     */
    private List<AssetItem> assets;
    
    /**
     * 钱包类型分布
     */
    private List<WalletDistribution> walletDistributions;
    
    /**
     * 货币类型分布
     */
    private List<CurrencyDistribution> currencyDistributions;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 资产项内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssetItem {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 货币名称
         */
        private String currencyName;
        
        /**
         * 钱包类型
         */
        private String walletType;
        
        /**
         * 总余额
         */
        private BigDecimal totalBalance;
        
        /**
         * 可用余额
         */
        private BigDecimal availableBalance;
        
        /**
         * 冻结余额
         */
        private BigDecimal frozenBalance;
        
        /**
         * 当前价格
         */
        private BigDecimal currentPrice;
        
        /**
         * 资产价值
         */
        private BigDecimal assetValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 24小时价格变化
         */
        private BigDecimal priceChange24h;
        
        /**
         * 24小时价格变化百分比
         */
        private BigDecimal priceChangePercent24h;
        
        /**
         * 是否隐藏小额资产
         */
        private Boolean isSmallAsset;
    }
    
    /**
     * 钱包分布内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WalletDistribution {
        /**
         * 钱包类型
         */
        private String walletType;
        
        /**
         * 钱包名称
         */
        private String walletName;
        
        /**
         * 钱包价值
         */
        private BigDecimal walletValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 资产种类数量
         */
        private Integer assetCount;
        
        /**
         * 钱包状态
         */
        private String status;
    }
    
    /**
     * 货币分布内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CurrencyDistribution {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 货币名称
         */
        private String currencyName;
        
        /**
         * 总余额
         */
        private BigDecimal totalBalance;
        
        /**
         * 总价值
         */
        private BigDecimal totalValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 分布在的钱包数量
         */
        private Integer walletCount;
        
        /**
         * 货币类型 (CRYPTO, FIAT)
         */
        private String currencyType;
    }
}