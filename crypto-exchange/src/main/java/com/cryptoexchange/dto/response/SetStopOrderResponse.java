package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设置止损订单响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SetStopOrderResponse {
    
    /**
     * 止损订单ID
     */
    private String stopOrderId;
    
    /**
     * 客户端订单ID
     */
    private String clientOrderId;
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 持仓方向 (LONG/SHORT)
     */
    private String positionSide;
    
    /**
     * 止损价格
     */
    private BigDecimal stopLossPrice;
    
    /**
     * 止盈价格
     */
    private BigDecimal takeProfitPrice;
    
    /**
     * 止损类型 (MARKET/LIMIT)
     */
    private String stopLossType;
    
    /**
     * 止盈类型 (MARKET/LIMIT)
     */
    private String takeProfitType;
    
    /**
     * 止损限价
     */
    private BigDecimal stopLossLimitPrice;
    
    /**
     * 止盈限价
     */
    private BigDecimal takeProfitLimitPrice;
    
    /**
     * 工作类型 (MARK_PRICE/CONTRACT_PRICE)
     */
    private String workingType;
    
    /**
     * 时效类型 (GTC/IOC/FOK)
     */
    private String timeInForce;
    
    /**
     * 是否关闭持仓
     */
    private Boolean closePosition;
    
    /**
     * 订单状态 (NEW, PENDING, ACTIVE, TRIGGERED, CANCELLED, EXPIRED)
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 激活价格
     */
    private BigDecimal activatePrice;
    
    /**
     * 回调比率
     */
    private BigDecimal callbackRate;
    
    /**
     * 关联的持仓数量
     */
    private BigDecimal positionAmt;
    
    /**
     * 预计触发价格
     */
    private BigDecimal estimatedTriggerPrice;
    
    /**
     * 风险等级
     */
    private String riskLevel;
}