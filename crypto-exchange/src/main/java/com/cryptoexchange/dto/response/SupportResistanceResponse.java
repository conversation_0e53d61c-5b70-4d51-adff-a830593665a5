package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 支撑阻力分析响应
 */
public class SupportResistanceResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;
    
    /**
     * 分析时间范围
     */
    private String timeRange;
    
    /**
     * 当前价格
     */
    private BigDecimal currentPrice;
    
    /**
     * 支撑阻力强度等级
     */
    private SupportResistanceStrength overallStrength;
    
    /**
     * 支撑位分析
     */
    private List<SupportLevel> supportLevels;
    
    /**
     * 阻力位分析
     */
    private List<ResistanceLevel> resistanceLevels;
    
    /**
     * 关键价格区间
     */
    private List<KeyPriceZone> keyPriceZones;
    
    /**
     * 突破分析
     */
    private BreakoutAnalysis breakoutAnalysis;
    
    /**
     * 历史测试分析
     */
    private HistoricalTestAnalysis historicalTestAnalysis;
    
    /**
     * 成交量确认分析
     */
    private VolumeConfirmationAnalysis volumeConfirmationAnalysis;
    
    /**
     * 动态支撑阻力分析
     */
    private DynamicSupportResistanceAnalysis dynamicAnalysis;
    
    /**
     * 支撑阻力预测
     */
    private SupportResistanceForecast forecast;
    
    /**
     * 交易建议
     */
    private SupportResistanceTradingAdvice tradingAdvice;
    
    /**
     * 风险评估
     */
    private SupportResistanceRiskAssessment riskAssessment;
    
    /**
     * 支撑阻力强度等级
     */
    public enum SupportResistanceStrength {
        VERY_WEAK,
        WEAK,
        MODERATE,
        STRONG,
        VERY_STRONG
    }
    
    /**
     * 支撑位
     */
    public static class SupportLevel {
        private BigDecimal price;
        private SupportResistanceStrength strength;
        private Integer testCount;
        private LocalDateTime lastTestTime;
        private BigDecimal distanceFromCurrent;
        private BigDecimal distancePercentage;
        private SupportType type;
        private List<HistoricalTest> historicalTests;
        private VolumeProfile volumeProfile;
        private ConfidenceLevel confidence;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
        public Integer getTestCount() { return testCount; }
        public void setTestCount(Integer testCount) { this.testCount = testCount; }
        public LocalDateTime getLastTestTime() { return lastTestTime; }
        public void setLastTestTime(LocalDateTime lastTestTime) { this.lastTestTime = lastTestTime; }
        public BigDecimal getDistanceFromCurrent() { return distanceFromCurrent; }
        public void setDistanceFromCurrent(BigDecimal distanceFromCurrent) { this.distanceFromCurrent = distanceFromCurrent; }
        public BigDecimal getDistancePercentage() { return distancePercentage; }
        public void setDistancePercentage(BigDecimal distancePercentage) { this.distancePercentage = distancePercentage; }
        public SupportType getType() { return type; }
        public void setType(SupportType type) { this.type = type; }
        public List<HistoricalTest> getHistoricalTests() { return historicalTests; }
        public void setHistoricalTests(List<HistoricalTest> historicalTests) { this.historicalTests = historicalTests; }
        public VolumeProfile getVolumeProfile() { return volumeProfile; }
        public void setVolumeProfile(VolumeProfile volumeProfile) { this.volumeProfile = volumeProfile; }
        public ConfidenceLevel getConfidence() { return confidence; }
        public void setConfidence(ConfidenceLevel confidence) { this.confidence = confidence; }
    }
    
    /**
     * 阻力位
     */
    public static class ResistanceLevel {
        private BigDecimal price;
        private SupportResistanceStrength strength;
        private Integer testCount;
        private LocalDateTime lastTestTime;
        private BigDecimal distanceFromCurrent;
        private BigDecimal distancePercentage;
        private ResistanceType type;
        private List<HistoricalTest> historicalTests;
        private VolumeProfile volumeProfile;
        private ConfidenceLevel confidence;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
        public Integer getTestCount() { return testCount; }
        public void setTestCount(Integer testCount) { this.testCount = testCount; }
        public LocalDateTime getLastTestTime() { return lastTestTime; }
        public void setLastTestTime(LocalDateTime lastTestTime) { this.lastTestTime = lastTestTime; }
        public BigDecimal getDistanceFromCurrent() { return distanceFromCurrent; }
        public void setDistanceFromCurrent(BigDecimal distanceFromCurrent) { this.distanceFromCurrent = distanceFromCurrent; }
        public BigDecimal getDistancePercentage() { return distancePercentage; }
        public void setDistancePercentage(BigDecimal distancePercentage) { this.distancePercentage = distancePercentage; }
        public ResistanceType getType() { return type; }
        public void setType(ResistanceType type) { this.type = type; }
        public List<HistoricalTest> getHistoricalTests() { return historicalTests; }
        public void setHistoricalTests(List<HistoricalTest> historicalTests) { this.historicalTests = historicalTests; }
        public VolumeProfile getVolumeProfile() { return volumeProfile; }
        public void setVolumeProfile(VolumeProfile volumeProfile) { this.volumeProfile = volumeProfile; }
        public ConfidenceLevel getConfidence() { return confidence; }
        public void setConfidence(ConfidenceLevel confidence) { this.confidence = confidence; }
    }
    
    /**
     * 支撑类型
     */
    public enum SupportType {
        HORIZONTAL,
        TRENDLINE,
        MOVING_AVERAGE,
        FIBONACCI,
        PSYCHOLOGICAL,
        VOLUME_PROFILE
    }
    
    /**
     * 阻力类型
     */
    public enum ResistanceType {
        HORIZONTAL,
        TRENDLINE,
        MOVING_AVERAGE,
        FIBONACCI,
        PSYCHOLOGICAL,
        VOLUME_PROFILE
    }
    
    /**
     * 历史测试
     */
    public static class HistoricalTest {
        private LocalDateTime testTime;
        private BigDecimal testPrice;
        private BigDecimal volume;
        private TestResult result;
        private BigDecimal bounceStrength;
        private String duration;
        
        // getters and setters
        public LocalDateTime getTestTime() { return testTime; }
        public void setTestTime(LocalDateTime testTime) { this.testTime = testTime; }
        public BigDecimal getTestPrice() { return testPrice; }
        public void setTestPrice(BigDecimal testPrice) { this.testPrice = testPrice; }
        public BigDecimal getVolume() { return volume; }
        public void setVolume(BigDecimal volume) { this.volume = volume; }
        public TestResult getResult() { return result; }
        public void setResult(TestResult result) { this.result = result; }
        public BigDecimal getBounceStrength() { return bounceStrength; }
        public void setBounceStrength(BigDecimal bounceStrength) { this.bounceStrength = bounceStrength; }
        public String getDuration() { return duration; }
        public void setDuration(String duration) { this.duration = duration; }
    }
    
    /**
     * 测试结果
     */
    public enum TestResult {
        STRONG_BOUNCE,
        WEAK_BOUNCE,
        BREAKTHROUGH,
        FALSE_BREAKTHROUGH
    }
    
    /**
     * 成交量分布
     */
    public static class VolumeProfile {
        private BigDecimal averageVolume;
        private BigDecimal peakVolume;
        private BigDecimal volumeConcentration;
        private List<VolumeNode> volumeNodes;
        
        // getters and setters
        public BigDecimal getAverageVolume() { return averageVolume; }
        public void setAverageVolume(BigDecimal averageVolume) { this.averageVolume = averageVolume; }
        public BigDecimal getPeakVolume() { return peakVolume; }
        public void setPeakVolume(BigDecimal peakVolume) { this.peakVolume = peakVolume; }
        public BigDecimal getVolumeConcentration() { return volumeConcentration; }
        public void setVolumeConcentration(BigDecimal volumeConcentration) { this.volumeConcentration = volumeConcentration; }
        public List<VolumeNode> getVolumeNodes() { return volumeNodes; }
        public void setVolumeNodes(List<VolumeNode> volumeNodes) { this.volumeNodes = volumeNodes; }
    }
    
    /**
     * 成交量节点
     */
    public static class VolumeNode {
        private BigDecimal price;
        private BigDecimal volume;
        private BigDecimal percentage;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public BigDecimal getVolume() { return volume; }
        public void setVolume(BigDecimal volume) { this.volume = volume; }
        public BigDecimal getPercentage() { return percentage; }
        public void setPercentage(BigDecimal percentage) { this.percentage = percentage; }
    }
    
    /**
     * 置信度等级
     */
    public enum ConfidenceLevel {
        VERY_LOW,
        LOW,
        MEDIUM,
        HIGH,
        VERY_HIGH
    }
    
    /**
     * 关键价格区间
     */
    public static class KeyPriceZone {
        private BigDecimal lowerBound;
        private BigDecimal upperBound;
        private ZoneType type;
        private SupportResistanceStrength strength;
        private String description;
        private List<PriceAction> priceActions;
        
        // getters and setters
        public BigDecimal getLowerBound() { return lowerBound; }
        public void setLowerBound(BigDecimal lowerBound) { this.lowerBound = lowerBound; }
        public BigDecimal getUpperBound() { return upperBound; }
        public void setUpperBound(BigDecimal upperBound) { this.upperBound = upperBound; }
        public ZoneType getType() { return type; }
        public void setType(ZoneType type) { this.type = type; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<PriceAction> getPriceActions() { return priceActions; }
        public void setPriceActions(List<PriceAction> priceActions) { this.priceActions = priceActions; }
    }
    
    /**
     * 区间类型
     */
    public enum ZoneType {
        SUPPORT_ZONE,
        RESISTANCE_ZONE,
        CONSOLIDATION_ZONE,
        BREAKOUT_ZONE
    }
    
    /**
     * 价格行为
     */
    public static class PriceAction {
        private LocalDateTime time;
        private BigDecimal price;
        private ActionType actionType;
        private String description;
        
        // getters and setters
        public LocalDateTime getTime() { return time; }
        public void setTime(LocalDateTime time) { this.time = time; }
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public ActionType getActionType() { return actionType; }
        public void setActionType(ActionType actionType) { this.actionType = actionType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    /**
     * 行为类型
     */
    public enum ActionType {
        BOUNCE,
        REJECTION,
        BREAKTHROUGH,
        CONSOLIDATION
    }
    
    /**
     * 突破分析
     */
    public static class BreakoutAnalysis {
        private List<BreakoutSignal> breakoutSignals;
        private List<FalseBreakoutRisk> falseBreakoutRisks;
        private BreakoutProbability breakoutProbability;
        private List<BreakoutTarget> breakoutTargets;
        
        // getters and setters
        public List<BreakoutSignal> getBreakoutSignals() { return breakoutSignals; }
        public void setBreakoutSignals(List<BreakoutSignal> breakoutSignals) { this.breakoutSignals = breakoutSignals; }
        public List<FalseBreakoutRisk> getFalseBreakoutRisks() { return falseBreakoutRisks; }
        public void setFalseBreakoutRisks(List<FalseBreakoutRisk> falseBreakoutRisks) { this.falseBreakoutRisks = falseBreakoutRisks; }
        public BreakoutProbability getBreakoutProbability() { return breakoutProbability; }
        public void setBreakoutProbability(BreakoutProbability breakoutProbability) { this.breakoutProbability = breakoutProbability; }
        public List<BreakoutTarget> getBreakoutTargets() { return breakoutTargets; }
        public void setBreakoutTargets(List<BreakoutTarget> breakoutTargets) { this.breakoutTargets = breakoutTargets; }
    }
    
    /**
     * 突破信号
     */
    public static class BreakoutSignal {
        private BigDecimal level;
        private BreakoutDirection direction;
        private SignalStrength strength;
        private List<String> confirmationFactors;
        private BigDecimal requiredVolume;
        
        // getters and setters
        public BigDecimal getLevel() { return level; }
        public void setLevel(BigDecimal level) { this.level = level; }
        public BreakoutDirection getDirection() { return direction; }
        public void setDirection(BreakoutDirection direction) { this.direction = direction; }
        public SignalStrength getStrength() { return strength; }
        public void setStrength(SignalStrength strength) { this.strength = strength; }
        public List<String> getConfirmationFactors() { return confirmationFactors; }
        public void setConfirmationFactors(List<String> confirmationFactors) { this.confirmationFactors = confirmationFactors; }
        public BigDecimal getRequiredVolume() { return requiredVolume; }
        public void setRequiredVolume(BigDecimal requiredVolume) { this.requiredVolume = requiredVolume; }
    }
    
    /**
     * 突破方向
     */
    public enum BreakoutDirection {
        UPWARD,
        DOWNWARD
    }
    
    /**
     * 信号强度
     */
    public enum SignalStrength {
        WEAK,
        MODERATE,
        STRONG,
        VERY_STRONG
    }
    
    /**
     * 假突破风险
     */
    public static class FalseBreakoutRisk {
        private BigDecimal level;
        private BigDecimal riskProbability;
        private List<String> riskFactors;
        private String mitigation;
        
        // getters and setters
        public BigDecimal getLevel() { return level; }
        public void setLevel(BigDecimal level) { this.level = level; }
        public BigDecimal getRiskProbability() { return riskProbability; }
        public void setRiskProbability(BigDecimal riskProbability) { this.riskProbability = riskProbability; }
        public List<String> getRiskFactors() { return riskFactors; }
        public void setRiskFactors(List<String> riskFactors) { this.riskFactors = riskFactors; }
        public String getMitigation() { return mitigation; }
        public void setMitigation(String mitigation) { this.mitigation = mitigation; }
    }
    
    /**
     * 突破概率
     */
    public static class BreakoutProbability {
        private BigDecimal upwardProbability;
        private BigDecimal downwardProbability;
        private BigDecimal consolidationProbability;
        private List<String> probabilityFactors;
        
        // getters and setters
        public BigDecimal getUpwardProbability() { return upwardProbability; }
        public void setUpwardProbability(BigDecimal upwardProbability) { this.upwardProbability = upwardProbability; }
        public BigDecimal getDownwardProbability() { return downwardProbability; }
        public void setDownwardProbability(BigDecimal downwardProbability) { this.downwardProbability = downwardProbability; }
        public BigDecimal getConsolidationProbability() { return consolidationProbability; }
        public void setConsolidationProbability(BigDecimal consolidationProbability) { this.consolidationProbability = consolidationProbability; }
        public List<String> getProbabilityFactors() { return probabilityFactors; }
        public void setProbabilityFactors(List<String> probabilityFactors) { this.probabilityFactors = probabilityFactors; }
    }
    
    /**
     * 突破目标
     */
    public static class BreakoutTarget {
        private BigDecimal targetPrice;
        private BreakoutDirection direction;
        private BigDecimal probability;
        private String timeframe;
        private String calculationMethod;
        
        // getters and setters
        public BigDecimal getTargetPrice() { return targetPrice; }
        public void setTargetPrice(BigDecimal targetPrice) { this.targetPrice = targetPrice; }
        public BreakoutDirection getDirection() { return direction; }
        public void setDirection(BreakoutDirection direction) { this.direction = direction; }
        public BigDecimal getProbability() { return probability; }
        public void setProbability(BigDecimal probability) { this.probability = probability; }
        public String getTimeframe() { return timeframe; }
        public void setTimeframe(String timeframe) { this.timeframe = timeframe; }
        public String getCalculationMethod() { return calculationMethod; }
        public void setCalculationMethod(String calculationMethod) { this.calculationMethod = calculationMethod; }
    }
    
    /**
     * 历史测试分析
     */
    public static class HistoricalTestAnalysis {
        private Integer totalTests;
        private BigDecimal successRate;
        private BigDecimal averageBounceStrength;
        private String averageHoldTime;
        private List<TestPattern> testPatterns;
        
        // getters and setters
        public Integer getTotalTests() { return totalTests; }
        public void setTotalTests(Integer totalTests) { this.totalTests = totalTests; }
        public BigDecimal getSuccessRate() { return successRate; }
        public void setSuccessRate(BigDecimal successRate) { this.successRate = successRate; }
        public BigDecimal getAverageBounceStrength() { return averageBounceStrength; }
        public void setAverageBounceStrength(BigDecimal averageBounceStrength) { this.averageBounceStrength = averageBounceStrength; }
        public String getAverageHoldTime() { return averageHoldTime; }
        public void setAverageHoldTime(String averageHoldTime) { this.averageHoldTime = averageHoldTime; }
        public List<TestPattern> getTestPatterns() { return testPatterns; }
        public void setTestPatterns(List<TestPattern> testPatterns) { this.testPatterns = testPatterns; }
    }
    
    /**
     * 测试模式
     */
    public static class TestPattern {
        private String patternName;
        private Integer occurrences;
        private BigDecimal successRate;
        private String description;
        
        // getters and setters
        public String getPatternName() { return patternName; }
        public void setPatternName(String patternName) { this.patternName = patternName; }
        public Integer getOccurrences() { return occurrences; }
        public void setOccurrences(Integer occurrences) { this.occurrences = occurrences; }
        public BigDecimal getSuccessRate() { return successRate; }
        public void setSuccessRate(BigDecimal successRate) { this.successRate = successRate; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    /**
     * 成交量确认分析
     */
    public static class VolumeConfirmationAnalysis {
        private BigDecimal currentVolume;
        private BigDecimal averageVolume;
        private BigDecimal volumeRatio;
        private VolumeConfirmationLevel confirmationLevel;
        private List<VolumeSignal> volumeSignals;
        
        // getters and setters
        public BigDecimal getCurrentVolume() { return currentVolume; }
        public void setCurrentVolume(BigDecimal currentVolume) { this.currentVolume = currentVolume; }
        public BigDecimal getAverageVolume() { return averageVolume; }
        public void setAverageVolume(BigDecimal averageVolume) { this.averageVolume = averageVolume; }
        public BigDecimal getVolumeRatio() { return volumeRatio; }
        public void setVolumeRatio(BigDecimal volumeRatio) { this.volumeRatio = volumeRatio; }
        public VolumeConfirmationLevel getConfirmationLevel() { return confirmationLevel; }
        public void setConfirmationLevel(VolumeConfirmationLevel confirmationLevel) { this.confirmationLevel = confirmationLevel; }
        public List<VolumeSignal> getVolumeSignals() { return volumeSignals; }
        public void setVolumeSignals(List<VolumeSignal> volumeSignals) { this.volumeSignals = volumeSignals; }
    }
    
    /**
     * 成交量确认等级
     */
    public enum VolumeConfirmationLevel {
        NO_CONFIRMATION,
        WEAK_CONFIRMATION,
        MODERATE_CONFIRMATION,
        STRONG_CONFIRMATION
    }
    
    /**
     * 成交量信号
     */
    public static class VolumeSignal {
        private String signalType;
        private SignalStrength strength;
        private String description;
        private BigDecimal threshold;
        
        // getters and setters
        public String getSignalType() { return signalType; }
        public void setSignalType(String signalType) { this.signalType = signalType; }
        public SignalStrength getStrength() { return strength; }
        public void setStrength(SignalStrength strength) { this.strength = strength; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getThreshold() { return threshold; }
        public void setThreshold(BigDecimal threshold) { this.threshold = threshold; }
    }
    
    /**
     * 动态支撑阻力分析
     */
    public static class DynamicSupportResistanceAnalysis {
        private List<DynamicLevel> dynamicSupportLevels;
        private List<DynamicLevel> dynamicResistanceLevels;
        private TrendlineAnalysis trendlineAnalysis;
        private MovingAverageAnalysis movingAverageAnalysis;
        
        // getters and setters
        public List<DynamicLevel> getDynamicSupportLevels() { return dynamicSupportLevels; }
        public void setDynamicSupportLevels(List<DynamicLevel> dynamicSupportLevels) { this.dynamicSupportLevels = dynamicSupportLevels; }
        public List<DynamicLevel> getDynamicResistanceLevels() { return dynamicResistanceLevels; }
        public void setDynamicResistanceLevels(List<DynamicLevel> dynamicResistanceLevels) { this.dynamicResistanceLevels = dynamicResistanceLevels; }
        public TrendlineAnalysis getTrendlineAnalysis() { return trendlineAnalysis; }
        public void setTrendlineAnalysis(TrendlineAnalysis trendlineAnalysis) { this.trendlineAnalysis = trendlineAnalysis; }
        public MovingAverageAnalysis getMovingAverageAnalysis() { return movingAverageAnalysis; }
        public void setMovingAverageAnalysis(MovingAverageAnalysis movingAverageAnalysis) { this.movingAverageAnalysis = movingAverageAnalysis; }
    }
    
    /**
     * 动态水平
     */
    public static class DynamicLevel {
        private BigDecimal currentPrice;
        private BigDecimal slope;
        private SupportResistanceStrength strength;
        private String equation;
        private BigDecimal projectedPrice;
        
        // getters and setters
        public BigDecimal getCurrentPrice() { return currentPrice; }
        public void setCurrentPrice(BigDecimal currentPrice) { this.currentPrice = currentPrice; }
        public BigDecimal getSlope() { return slope; }
        public void setSlope(BigDecimal slope) { this.slope = slope; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
        public String getEquation() { return equation; }
        public void setEquation(String equation) { this.equation = equation; }
        public BigDecimal getProjectedPrice() { return projectedPrice; }
        public void setProjectedPrice(BigDecimal projectedPrice) { this.projectedPrice = projectedPrice; }
    }
    
    /**
     * 趋势线分析
     */
    public static class TrendlineAnalysis {
        private List<Trendline> supportTrendlines;
        private List<Trendline> resistanceTrendlines;
        private List<TrendlineIntersection> intersections;
        
        // getters and setters
        public List<Trendline> getSupportTrendlines() { return supportTrendlines; }
        public void setSupportTrendlines(List<Trendline> supportTrendlines) { this.supportTrendlines = supportTrendlines; }
        public List<Trendline> getResistanceTrendlines() { return resistanceTrendlines; }
        public void setResistanceTrendlines(List<Trendline> resistanceTrendlines) { this.resistanceTrendlines = resistanceTrendlines; }
        public List<TrendlineIntersection> getIntersections() { return intersections; }
        public void setIntersections(List<TrendlineIntersection> intersections) { this.intersections = intersections; }
    }
    
    /**
     * 趋势线
     */
    public static class Trendline {
        private BigDecimal startPrice;
        private BigDecimal endPrice;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private BigDecimal slope;
        private Integer touchPoints;
        private SupportResistanceStrength strength;
        
        // getters and setters
        public BigDecimal getStartPrice() { return startPrice; }
        public void setStartPrice(BigDecimal startPrice) { this.startPrice = startPrice; }
        public BigDecimal getEndPrice() { return endPrice; }
        public void setEndPrice(BigDecimal endPrice) { this.endPrice = endPrice; }
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        public BigDecimal getSlope() { return slope; }
        public void setSlope(BigDecimal slope) { this.slope = slope; }
        public Integer getTouchPoints() { return touchPoints; }
        public void setTouchPoints(Integer touchPoints) { this.touchPoints = touchPoints; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
    }
    
    /**
     * 趋势线交叉
     */
    public static class TrendlineIntersection {
        private BigDecimal intersectionPrice;
        private LocalDateTime intersectionTime;
        private String intersectionType;
        private SignalStrength significance;
        
        // getters and setters
        public BigDecimal getIntersectionPrice() { return intersectionPrice; }
        public void setIntersectionPrice(BigDecimal intersectionPrice) { this.intersectionPrice = intersectionPrice; }
        public LocalDateTime getIntersectionTime() { return intersectionTime; }
        public void setIntersectionTime(LocalDateTime intersectionTime) { this.intersectionTime = intersectionTime; }
        public String getIntersectionType() { return intersectionType; }
        public void setIntersectionType(String intersectionType) { this.intersectionType = intersectionType; }
        public SignalStrength getSignificance() { return significance; }
        public void setSignificance(SignalStrength significance) { this.significance = significance; }
    }
    
    /**
     * 移动平均线分析
     */
    public static class MovingAverageAnalysis {
        private List<MovingAverageLevel> supportingMAs;
        private List<MovingAverageLevel> resistingMAs;
        private MAClusterAnalysis clusterAnalysis;
        
        // getters and setters
        public List<MovingAverageLevel> getSupportingMAs() { return supportingMAs; }
        public void setSupportingMAs(List<MovingAverageLevel> supportingMAs) { this.supportingMAs = supportingMAs; }
        public List<MovingAverageLevel> getResistingMAs() { return resistingMAs; }
        public void setResistingMAs(List<MovingAverageLevel> resistingMAs) { this.resistingMAs = resistingMAs; }
        public MAClusterAnalysis getClusterAnalysis() { return clusterAnalysis; }
        public void setClusterAnalysis(MAClusterAnalysis clusterAnalysis) { this.clusterAnalysis = clusterAnalysis; }
    }
    
    /**
     * 移动平均线水平
     */
    public static class MovingAverageLevel {
        private Integer period;
        private String type;
        private BigDecimal currentValue;
        private BigDecimal slope;
        private SupportResistanceStrength strength;
        
        // getters and setters
        public Integer getPeriod() { return period; }
        public void setPeriod(Integer period) { this.period = period; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
        public BigDecimal getCurrentValue() { return currentValue; }
        public void setCurrentValue(BigDecimal currentValue) { this.currentValue = currentValue; }
        public BigDecimal getSlope() { return slope; }
        public void setSlope(BigDecimal slope) { this.slope = slope; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
    }
    
    /**
     * 移动平均线集群分析
     */
    public static class MAClusterAnalysis {
        private List<MACluster> supportClusters;
        private List<MACluster> resistanceClusters;
        private BigDecimal clusterStrength;
        
        // getters and setters
        public List<MACluster> getSupportClusters() { return supportClusters; }
        public void setSupportClusters(List<MACluster> supportClusters) { this.supportClusters = supportClusters; }
        public List<MACluster> getResistanceClusters() { return resistanceClusters; }
        public void setResistanceClusters(List<MACluster> resistanceClusters) { this.resistanceClusters = resistanceClusters; }
        public BigDecimal getClusterStrength() { return clusterStrength; }
        public void setClusterStrength(BigDecimal clusterStrength) { this.clusterStrength = clusterStrength; }
    }
    
    /**
     * 移动平均线集群
     */
    public static class MACluster {
        private BigDecimal centerPrice;
        private BigDecimal priceRange;
        private List<Integer> maPeriods;
        private SupportResistanceStrength strength;
        
        // getters and setters
        public BigDecimal getCenterPrice() { return centerPrice; }
        public void setCenterPrice(BigDecimal centerPrice) { this.centerPrice = centerPrice; }
        public BigDecimal getPriceRange() { return priceRange; }
        public void setPriceRange(BigDecimal priceRange) { this.priceRange = priceRange; }
        public List<Integer> getMaPeriods() { return maPeriods; }
        public void setMaPeriods(List<Integer> maPeriods) { this.maPeriods = maPeriods; }
        public SupportResistanceStrength getStrength() { return strength; }
        public void setStrength(SupportResistanceStrength strength) { this.strength = strength; }
    }
    
    /**
     * 支撑阻力预测
     */
    public static class SupportResistanceForecast {
        private List<FutureSupportLevel> futureSupportLevels;
        private List<FutureResistanceLevel> futureResistanceLevels;
        private List<KeyEvent> keyEvents;
        private ForecastConfidence confidence;
        
        // getters and setters
        public List<FutureSupportLevel> getFutureSupportLevels() { return futureSupportLevels; }
        public void setFutureSupportLevels(List<FutureSupportLevel> futureSupportLevels) { this.futureSupportLevels = futureSupportLevels; }
        public List<FutureResistanceLevel> getFutureResistanceLevels() { return futureResistanceLevels; }
        public void setFutureResistanceLevels(List<FutureResistanceLevel> futureResistanceLevels) { this.futureResistanceLevels = futureResistanceLevels; }
        public List<KeyEvent> getKeyEvents() { return keyEvents; }
        public void setKeyEvents(List<KeyEvent> keyEvents) { this.keyEvents = keyEvents; }
        public ForecastConfidence getConfidence() { return confidence; }
        public void setConfidence(ForecastConfidence confidence) { this.confidence = confidence; }
    }
    
    /**
     * 未来支撑位
     */
    public static class FutureSupportLevel {
        private BigDecimal price;
        private LocalDateTime expectedTime;
        private BigDecimal probability;
        private SupportResistanceStrength expectedStrength;
        private String basis;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public LocalDateTime getExpectedTime() { return expectedTime; }
        public void setExpectedTime(LocalDateTime expectedTime) { this.expectedTime = expectedTime; }
        public BigDecimal getProbability() { return probability; }
        public void setProbability(BigDecimal probability) { this.probability = probability; }
        public SupportResistanceStrength getExpectedStrength() { return expectedStrength; }
        public void setExpectedStrength(SupportResistanceStrength expectedStrength) { this.expectedStrength = expectedStrength; }
        public String getBasis() { return basis; }
        public void setBasis(String basis) { this.basis = basis; }
    }
    
    /**
     * 未来阻力位
     */
    public static class FutureResistanceLevel {
        private BigDecimal price;
        private LocalDateTime expectedTime;
        private BigDecimal probability;
        private SupportResistanceStrength expectedStrength;
        private String basis;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public LocalDateTime getExpectedTime() { return expectedTime; }
        public void setExpectedTime(LocalDateTime expectedTime) { this.expectedTime = expectedTime; }
        public BigDecimal getProbability() { return probability; }
        public void setProbability(BigDecimal probability) { this.probability = probability; }
        public SupportResistanceStrength getExpectedStrength() { return expectedStrength; }
        public void setExpectedStrength(SupportResistanceStrength expectedStrength) { this.expectedStrength = expectedStrength; }
        public String getBasis() { return basis; }
        public void setBasis(String basis) { this.basis = basis; }
    }
    
    /**
     * 关键事件
     */
    public static class KeyEvent {
        private LocalDateTime eventTime;
        private String eventType;
        private BigDecimal impactLevel;
        private String description;
        private List<BigDecimal> affectedLevels;
        
        // getters and setters
        public LocalDateTime getEventTime() { return eventTime; }
        public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
        public String getEventType() { return eventType; }
        public void setEventType(String eventType) { this.eventType = eventType; }
        public BigDecimal getImpactLevel() { return impactLevel; }
        public void setImpactLevel(BigDecimal impactLevel) { this.impactLevel = impactLevel; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public List<BigDecimal> getAffectedLevels() { return affectedLevels; }
        public void setAffectedLevels(List<BigDecimal> affectedLevels) { this.affectedLevels = affectedLevels; }
    }
    
    /**
     * 预测置信度
     */
    public static class ForecastConfidence {
        private BigDecimal overallConfidence;
        private BigDecimal shortTermConfidence;
        private BigDecimal mediumTermConfidence;
        private BigDecimal longTermConfidence;
        private List<String> confidenceFactors;
        
        // getters and setters
        public BigDecimal getOverallConfidence() { return overallConfidence; }
        public void setOverallConfidence(BigDecimal overallConfidence) { this.overallConfidence = overallConfidence; }
        public BigDecimal getShortTermConfidence() { return shortTermConfidence; }
        public void setShortTermConfidence(BigDecimal shortTermConfidence) { this.shortTermConfidence = shortTermConfidence; }
        public BigDecimal getMediumTermConfidence() { return mediumTermConfidence; }
        public void setMediumTermConfidence(BigDecimal mediumTermConfidence) { this.mediumTermConfidence = mediumTermConfidence; }
        public BigDecimal getLongTermConfidence() { return longTermConfidence; }
        public void setLongTermConfidence(BigDecimal longTermConfidence) { this.longTermConfidence = longTermConfidence; }
        public List<String> getConfidenceFactors() { return confidenceFactors; }
        public void setConfidenceFactors(List<String> confidenceFactors) { this.confidenceFactors = confidenceFactors; }
    }
    
    /**
     * 支撑阻力交易建议
     */
    public static class SupportResistanceTradingAdvice {
        private List<TradingStrategy> buyStrategies;
        private List<TradingStrategy> sellStrategies;
        private List<RiskManagementAdvice> riskManagement;
        private List<EntryPoint> entryPoints;
        private List<ExitPoint> exitPoints;
        
        // getters and setters
        public List<TradingStrategy> getBuyStrategies() { return buyStrategies; }
        public void setBuyStrategies(List<TradingStrategy> buyStrategies) { this.buyStrategies = buyStrategies; }
        public List<TradingStrategy> getSellStrategies() { return sellStrategies; }
        public void setSellStrategies(List<TradingStrategy> sellStrategies) { this.sellStrategies = sellStrategies; }
        public List<RiskManagementAdvice> getRiskManagement() { return riskManagement; }
        public void setRiskManagement(List<RiskManagementAdvice> riskManagement) { this.riskManagement = riskManagement; }
        public List<EntryPoint> getEntryPoints() { return entryPoints; }
        public void setEntryPoints(List<EntryPoint> entryPoints) { this.entryPoints = entryPoints; }
        public List<ExitPoint> getExitPoints() { return exitPoints; }
        public void setExitPoints(List<ExitPoint> exitPoints) { this.exitPoints = exitPoints; }
    }
    
    /**
     * 交易策略
     */
    public static class TradingStrategy {
        private String strategyName;
        private String description;
        private BigDecimal targetLevel;
        private BigDecimal stopLoss;
        private BigDecimal takeProfit;
        private BigDecimal riskRewardRatio;
        private BigDecimal successProbability;
        
        // getters and setters
        public String getStrategyName() { return strategyName; }
        public void setStrategyName(String strategyName) { this.strategyName = strategyName; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getTargetLevel() { return targetLevel; }
        public void setTargetLevel(BigDecimal targetLevel) { this.targetLevel = targetLevel; }
        public BigDecimal getStopLoss() { return stopLoss; }
        public void setStopLoss(BigDecimal stopLoss) { this.stopLoss = stopLoss; }
        public BigDecimal getTakeProfit() { return takeProfit; }
        public void setTakeProfit(BigDecimal takeProfit) { this.takeProfit = takeProfit; }
        public BigDecimal getRiskRewardRatio() { return riskRewardRatio; }
        public void setRiskRewardRatio(BigDecimal riskRewardRatio) { this.riskRewardRatio = riskRewardRatio; }
        public BigDecimal getSuccessProbability() { return successProbability; }
        public void setSuccessProbability(BigDecimal successProbability) { this.successProbability = successProbability; }
    }
    
    /**
     * 风险管理建议
     */
    public static class RiskManagementAdvice {
        private String adviceType;
        private String description;
        private BigDecimal recommendedPositionSize;
        private BigDecimal maxRiskPerTrade;
        private List<String> riskFactors;
        
        // getters and setters
        public String getAdviceType() { return adviceType; }
        public void setAdviceType(String adviceType) { this.adviceType = adviceType; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getRecommendedPositionSize() { return recommendedPositionSize; }
        public void setRecommendedPositionSize(BigDecimal recommendedPositionSize) { this.recommendedPositionSize = recommendedPositionSize; }
        public BigDecimal getMaxRiskPerTrade() { return maxRiskPerTrade; }
        public void setMaxRiskPerTrade(BigDecimal maxRiskPerTrade) { this.maxRiskPerTrade = maxRiskPerTrade; }
        public List<String> getRiskFactors() { return riskFactors; }
        public void setRiskFactors(List<String> riskFactors) { this.riskFactors = riskFactors; }
    }
    
    /**
     * 入场点
     */
    public static class EntryPoint {
        private BigDecimal price;
        private String entryType;
        private BigDecimal confidence;
        private String timing;
        private List<String> confirmationSignals;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public String getEntryType() { return entryType; }
        public void setEntryType(String entryType) { this.entryType = entryType; }
        public BigDecimal getConfidence() { return confidence; }
        public void setConfidence(BigDecimal confidence) { this.confidence = confidence; }
        public String getTiming() { return timing; }
        public void setTiming(String timing) { this.timing = timing; }
        public List<String> getConfirmationSignals() { return confirmationSignals; }
        public void setConfirmationSignals(List<String> confirmationSignals) { this.confirmationSignals = confirmationSignals; }
    }
    
    /**
     * 出场点
     */
    public static class ExitPoint {
        private BigDecimal price;
        private String exitType;
        private BigDecimal confidence;
        private String reasoning;
        private List<String> exitSignals;
        
        // getters and setters
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public String getExitType() { return exitType; }
        public void setExitType(String exitType) { this.exitType = exitType; }
        public BigDecimal getConfidence() { return confidence; }
        public void setConfidence(BigDecimal confidence) { this.confidence = confidence; }
        public String getReasoning() { return reasoning; }
        public void setReasoning(String reasoning) { this.reasoning = reasoning; }
        public List<String> getExitSignals() { return exitSignals; }
        public void setExitSignals(List<String> exitSignals) { this.exitSignals = exitSignals; }
    }
    
    /**
     * 支撑阻力风险评估
     */
    public static class SupportResistanceRiskAssessment {
        private BigDecimal overallRiskLevel;
        private List<RiskFactor> riskFactors;
        private List<RiskMitigation> mitigationStrategies;
        private MarketConditionRisk marketConditionRisk;
        
        // getters and setters
        public BigDecimal getOverallRiskLevel() { return overallRiskLevel; }
        public void setOverallRiskLevel(BigDecimal overallRiskLevel) { this.overallRiskLevel = overallRiskLevel; }
        public List<RiskFactor> getRiskFactors() { return riskFactors; }
        public void setRiskFactors(List<RiskFactor> riskFactors) { this.riskFactors = riskFactors; }
        public List<RiskMitigation> getMitigationStrategies() { return mitigationStrategies; }
        public void setMitigationStrategies(List<RiskMitigation> mitigationStrategies) { this.mitigationStrategies = mitigationStrategies; }
        public MarketConditionRisk getMarketConditionRisk() { return marketConditionRisk; }
        public void setMarketConditionRisk(MarketConditionRisk marketConditionRisk) { this.marketConditionRisk = marketConditionRisk; }
    }
    
    /**
     * 风险因子
     */
    public static class RiskFactor {
        private String factorName;
        private BigDecimal riskLevel;
        private String description;
        private BigDecimal impact;
        private BigDecimal probability;
        
        // getters and setters
        public String getFactorName() { return factorName; }
        public void setFactorName(String factorName) { this.factorName = factorName; }
        public BigDecimal getRiskLevel() { return riskLevel; }
        public void setRiskLevel(BigDecimal riskLevel) { this.riskLevel = riskLevel; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getImpact() { return impact; }
        public void setImpact(BigDecimal impact) { this.impact = impact; }
        public BigDecimal getProbability() { return probability; }
        public void setProbability(BigDecimal probability) { this.probability = probability; }
    }
    
    /**
     * 风险缓解
     */
    public static class RiskMitigation {
        private String strategy;
        private String description;
        private BigDecimal effectiveness;
        private BigDecimal implementationCost;
        
        // getters and setters
        public String getStrategy() { return strategy; }
        public void setStrategy(String strategy) { this.strategy = strategy; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        public BigDecimal getEffectiveness() { return effectiveness; }
        public void setEffectiveness(BigDecimal effectiveness) { this.effectiveness = effectiveness; }
        public BigDecimal getImplementationCost() { return implementationCost; }
        public void setImplementationCost(BigDecimal implementationCost) { this.implementationCost = implementationCost; }
    }
    
    /**
     * 市场条件风险
     */
    public static class MarketConditionRisk {
        private BigDecimal volatilityRisk;
        private BigDecimal liquidityRisk;
        private BigDecimal trendRisk;
        private BigDecimal newsRisk;
        private String marketRegime;
        
        // getters and setters
        public BigDecimal getVolatilityRisk() { return volatilityRisk; }
        public void setVolatilityRisk(BigDecimal volatilityRisk) { this.volatilityRisk = volatilityRisk; }
        public BigDecimal getLiquidityRisk() { return liquidityRisk; }
        public void setLiquidityRisk(BigDecimal liquidityRisk) { this.liquidityRisk = liquidityRisk; }
        public BigDecimal getTrendRisk() { return trendRisk; }
        public void setTrendRisk(BigDecimal trendRisk) { this.trendRisk = trendRisk; }
        public BigDecimal getNewsRisk() { return newsRisk; }
        public void setNewsRisk(BigDecimal newsRisk) { this.newsRisk = newsRisk; }
        public String getMarketRegime() { return marketRegime; }
        public void setMarketRegime(String marketRegime) { this.marketRegime = marketRegime; }
    }
    
    // Main class getters and setters
    public String getSymbol() { return symbol; }
    public void setSymbol(String symbol) { this.symbol = symbol; }
    
    public LocalDateTime getAnalysisTime() { return analysisTime; }
    public void setAnalysisTime(LocalDateTime analysisTime) { this.analysisTime = analysisTime; }
    
    public String getTimeRange() { return timeRange; }
    public void setTimeRange(String timeRange) { this.timeRange = timeRange; }
    
    public BigDecimal getCurrentPrice() { return currentPrice; }
    public void setCurrentPrice(BigDecimal currentPrice) { this.currentPrice = currentPrice; }
    
    public SupportResistanceStrength getOverallStrength() { return overallStrength; }
    public void setOverallStrength(SupportResistanceStrength overallStrength) { this.overallStrength = overallStrength; }
    
    public List<SupportLevel> getSupportLevels() { return supportLevels; }
    public void setSupportLevels(List<SupportLevel> supportLevels) { this.supportLevels = supportLevels; }
    
    public List<ResistanceLevel> getResistanceLevels() { return resistanceLevels; }
    public void setResistanceLevels(List<ResistanceLevel> resistanceLevels) { this.resistanceLevels = resistanceLevels; }
    
    public List<KeyPriceZone> getKeyPriceZones() { return keyPriceZones; }
    public void setKeyPriceZones(List<KeyPriceZone> keyPriceZones) { this.keyPriceZones = keyPriceZones; }
    
    public BreakoutAnalysis getBreakoutAnalysis() { return breakoutAnalysis; }
    public void setBreakoutAnalysis(BreakoutAnalysis breakoutAnalysis) { this.breakoutAnalysis = breakoutAnalysis; }
    
    public HistoricalTestAnalysis getHistoricalTestAnalysis() { return historicalTestAnalysis; }
    public void setHistoricalTestAnalysis(HistoricalTestAnalysis historicalTestAnalysis) { this.historicalTestAnalysis = historicalTestAnalysis; }
    
    public VolumeConfirmationAnalysis getVolumeConfirmationAnalysis() { return volumeConfirmationAnalysis; }
    public void setVolumeConfirmationAnalysis(VolumeConfirmationAnalysis volumeConfirmationAnalysis) { this.volumeConfirmationAnalysis = volumeConfirmationAnalysis; }
    
    public DynamicSupportResistanceAnalysis getDynamicAnalysis() { return dynamicAnalysis; }
    public void setDynamicAnalysis(DynamicSupportResistanceAnalysis dynamicAnalysis) { this.dynamicAnalysis = dynamicAnalysis; }
    
    public SupportResistanceForecast getForecast() { return forecast; }
    public void setForecast(SupportResistanceForecast forecast) { this.forecast = forecast; }
    
    public SupportResistanceTradingAdvice getTradingAdvice() { return tradingAdvice; }
    public void setTradingAdvice(SupportResistanceTradingAdvice tradingAdvice) { this.tradingAdvice = tradingAdvice; }
    
    public SupportResistanceRiskAssessment getRiskAssessment() { return riskAssessment; }
    public void setRiskAssessment(SupportResistanceRiskAssessment riskAssessment) { this.riskAssessment = riskAssessment; }
}