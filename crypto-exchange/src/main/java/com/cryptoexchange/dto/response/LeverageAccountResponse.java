package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 杠杆账户响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LeverageAccountResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账户总资产
     */
    private BigDecimal totalAsset;
    
    /**
     * 账户总负债
     */
    private BigDecimal totalLiability;
    
    /**
     * 净资产
     */
    private BigDecimal netAsset;
    
    /**
     * 风险等级
     */
    private String riskLevel;
    
    /**
     * 保证金率
     */
    private BigDecimal marginRatio;
    
    /**
     * 可用余额
     */
    private BigDecimal availableBalance;
    
    /**
     * 已用保证金
     */
    private BigDecimal usedMargin;
    
    /**
     * 最大可借
     */
    private BigDecimal maxBorrowable;
    
    /**
     * 当前杠杆倍数
     */
    private BigDecimal currentLeverage;
    
    /**
     * 最大杠杆倍数
     */
    private Integer maxLeverage;
    
    /**
     * 账户状态
     */
    private String status;
    
    /**
     * 资产详情列表
     */
    private List<LeverageAssetDetail> assets;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 杠杆资产详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LeverageAssetDetail {
        
        /**
         * 资产符号
         */
        private String asset;
        
        /**
         * 可用余额
         */
        private BigDecimal free;
        
        /**
         * 冻结余额
         */
        private BigDecimal locked;
        
        /**
         * 借贷余额
         */
        private BigDecimal borrowed;
        
        /**
         * 利息
         */
        private BigDecimal interest;
        
        /**
         * 净资产
         */
        private BigDecimal netAsset;
    }
}