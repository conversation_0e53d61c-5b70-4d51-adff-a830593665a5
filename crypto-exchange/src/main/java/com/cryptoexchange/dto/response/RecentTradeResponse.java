package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 最近交易响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RecentTradeResponse {
    
    /**
     * 交易ID
     */
    private Long id;
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 成交价格
     */
    private BigDecimal price;
    
    /**
     * 成交数量
     */
    private BigDecimal qty;
    
    /**
     * 成交金额
     */
    private BigDecimal quoteQty;
    
    /**
     * 成交时间
     */
    private Long time;
    
    /**
     * 是否为买方成交
     */
    private Boolean isBuyerMaker;
    
    /**
     * 是否为最优价格成交
     */
    private Boolean isBestMatch;
    
    /**
     * 交易方向 (BUY, SELL)
     */
    private String side;
    
    /**
     * 交易类型 (MARKET, LIMIT)
     */
    private String type;
    
    /**
     * 成交时间（格式化）
     */
    private LocalDateTime tradeTime;
    
    /**
     * 买方订单ID
     */
    private Long buyerOrderId;
    
    /**
     * 卖方订单ID
     */
    private Long sellerOrderId;
    
    /**
     * 交易手续费
     */
    private BigDecimal commission;
    
    /**
     * 手续费资产
     */
    private String commissionAsset;
    
    /**
     * 交易序号
     */
    private Long tradeSequence;
    
    /**
     * 是否为主动成交
     */
    private Boolean isActive;
    
    /**
     * 流动性方向 (MAKER, TAKER)
     */
    private String liquidityType;
}