package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 强平记录响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class LiquidationResponse {

    /**
     * 强平记录ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 强平方向：1-多头强平，2-空头强平
     */
    private Integer side;

    /**
     * 强平数量
     */
    private BigDecimal quantity;

    /**
     * 强平价格
     */
    private BigDecimal price;

    /**
     * 强平金额
     */
    private BigDecimal amount;

    /**
     * 保证金
     */
    private BigDecimal margin;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 损失金额
     */
    private BigDecimal loss;

    /**
     * 保险基金补偿
     */
    private BigDecimal insuranceFundCompensation;

    /**
     * 强平原因：1-保证金不足，2-风险率过高，3-系统强平
     */
    private Integer liquidationReason;

    /**
     * 强平状态：1-进行中，2-已完成，3-部分完成，4-失败
     */
    private Integer status;

    /**
     * 风险率
     */
    private BigDecimal riskRatio;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 破产价格
     */
    private BigDecimal bankruptcyPrice;

    /**
     * 强平开始时间
     */
    private LocalDateTime liquidationStartTime;

    /**
     * 强平完成时间
     */
    private LocalDateTime liquidationEndTime;

    /**
     * 原始持仓数量
     */
    private BigDecimal originalQuantity;

    /**
     * 原始持仓价格
     */
    private BigDecimal originalPrice;

    /**
     * 原始保证金
     */
    private BigDecimal originalMargin;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 保证金模式
     */
    private Integer marginMode;

    /**
     * 强平类型：1-自动强平，2-手动强平
     */
    private Integer liquidationType;

    /**
     * 强平执行者
     */
    private String liquidator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}