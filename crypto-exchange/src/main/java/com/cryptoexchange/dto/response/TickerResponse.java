package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 行情数据响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TickerResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 加权平均价格
     */
    private BigDecimal weightedAvgPrice;
    
    /**
     * 前一个收盘价
     */
    private BigDecimal prevClosePrice;
    
    /**
     * 最新成交价
     */
    private BigDecimal lastQty;
    
    /**
     * 最佳买价
     */
    private BigDecimal bidPrice;
    
    /**
     * 最佳买量
     */
    private BigDecimal bidQty;
    
    /**
     * 最佳卖价
     */
    private BigDecimal askPrice;
    
    /**
     * 最佳卖量
     */
    private BigDecimal askQty;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume;
    
    /**
     * 24小时交易额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime openTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime closeTime;
    
    /**
     * 首次交易ID
     */
    private Long firstId;
    
    /**
     * 最后交易ID
     */
    private Long lastId;
    
    /**
     * 24小时交易次数
     */
    private Long count;
    
    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 交易对类型 (SPOT, FUTURES)
     */
    private String type;
    
    /**
     * 交易对状态
     */
    private String status;
    
    /**
     * 市值排名
     */
    private Integer rank;
    
    /**
     * 流通市值
     */
    private BigDecimal marketCap;
    
    /**
     * 流通供应量
     */
    private BigDecimal circulatingSupply;
    
    /**
     * 总供应量
     */
    private BigDecimal totalSupply;
    
    /**
     * 最大供应量
     */
    private BigDecimal maxSupply;
}