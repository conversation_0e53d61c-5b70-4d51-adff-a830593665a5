package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 提现限制响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawLimitResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 用户等级
     */
    private String userLevel;
    
    /**
     * KYC状态
     */
    private String kycStatus;
    
    /**
     * 单次最小提现金额
     */
    private BigDecimal minWithdrawAmount;
    
    /**
     * 单次最大提现金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 日提现限额
     */
    private BigDecimal dailyLimit;
    
    /**
     * 周提现限额
     */
    private BigDecimal weeklyLimit;
    
    /**
     * 月提现限额
     */
    private BigDecimal monthlyLimit;
    
    /**
     * 年提现限额
     */
    private BigDecimal yearlyLimit;
    
    /**
     * 今日已提现金额
     */
    private BigDecimal todayWithdrawn;
    
    /**
     * 本周已提现金额
     */
    private BigDecimal weekWithdrawn;
    
    /**
     * 本月已提现金额
     */
    private BigDecimal monthWithdrawn;
    
    /**
     * 本年已提现金额
     */
    private BigDecimal yearWithdrawn;
    
    /**
     * 今日剩余提现额度
     */
    private BigDecimal todayRemaining;
    
    /**
     * 本周剩余提现额度
     */
    private BigDecimal weekRemaining;
    
    /**
     * 本月剩余提现额度
     */
    private BigDecimal monthRemaining;
    
    /**
     * 本年剩余提现额度
     */
    private BigDecimal yearRemaining;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 手续费类型 (FIXED, PERCENTAGE)
     */
    private String feeType;
    
    /**
     * 最小手续费
     */
    private BigDecimal minFee;
    
    /**
     * 最大手续费
     */
    private BigDecimal maxFee;
    
    /**
     * 是否需要二次验证
     */
    private Boolean requiresTwoFactor;
    
    /**
     * 是否需要邮箱验证
     */
    private Boolean requiresEmailVerification;
    
    /**
     * 是否需要短信验证
     */
    private Boolean requiresSmsVerification;
    
    /**
     * 提现冷却时间（小时）
     */
    private Integer cooldownHours;
    
    /**
     * 上次提现时间
     */
    private LocalDateTime lastWithdrawTime;
    
    /**
     * 下次可提现时间
     */
    private LocalDateTime nextAvailableTime;
    
    /**
     * 是否可以提现
     */
    private Boolean canWithdraw;
    
    /**
     * 限制原因列表
     */
    private List<LimitReason> limitReasons;
    
    /**
     * 特殊限制
     */
    private List<SpecialLimit> specialLimits;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 限制重置时间
     */
    private ResetTime resetTime;
    
    /**
     * 限制原因内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class LimitReason {
        /**
         * 限制类型
         */
        private String limitType;
        
        /**
         * 限制原因
         */
        private String reason;
        
        /**
         * 限制描述
         */
        private String description;
        
        /**
         * 是否为临时限制
         */
        private Boolean isTemporary;
        
        /**
         * 限制开始时间
         */
        private LocalDateTime startTime;
        
        /**
         * 限制结束时间
         */
        private LocalDateTime endTime;
    }
    
    /**
     * 特殊限制内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class SpecialLimit {
        /**
         * 限制名称
         */
        private String limitName;
        
        /**
         * 限制值
         */
        private BigDecimal limitValue;
        
        /**
         * 限制单位
         */
        private String limitUnit;
        
        /**
         * 限制条件
         */
        private String condition;
        
        /**
         * 是否激活
         */
        private Boolean isActive;
        
        /**
         * 优先级
         */
        private Integer priority;
    }
    
    /**
     * 重置时间内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ResetTime {
        /**
         * 日限额重置时间
         */
        private LocalDateTime dailyResetTime;
        
        /**
         * 周限额重置时间
         */
        private LocalDateTime weeklyResetTime;
        
        /**
         * 月限额重置时间
         */
        private LocalDateTime monthlyResetTime;
        
        /**
         * 年限额重置时间
         */
        private LocalDateTime yearlyResetTime;
    }
}