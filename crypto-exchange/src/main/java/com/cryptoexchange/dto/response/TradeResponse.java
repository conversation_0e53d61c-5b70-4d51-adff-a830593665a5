package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易响应")
public class TradeResponse {

    @Schema(description = "交易ID", example = "123456")
    private Long tradeId;

    @Schema(description = "订单ID", example = "789012")
    private Long orderId;

    @Schema(description = "交易对", example = "BTCUSDT")
    private String symbol;

    @Schema(description = "交易方向：BUY-买入，SELL-卖出", example = "BUY")
    private String side;

    @Schema(description = "成交价格", example = "50000.00")
    private BigDecimal price;

    @Schema(description = "成交数量", example = "0.001")
    private BigDecimal qty;

    @Schema(description = "成交金额", example = "50.00")
    private BigDecimal quoteQty;

    @Schema(description = "手续费", example = "0.00001")
    private BigDecimal commission;

    @Schema(description = "手续费币种", example = "BTC")
    private String commissionAsset;

    @Schema(description = "成交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime time;

    @Schema(description = "是否为买方", example = "true")
    private Boolean isBuyer;

    @Schema(description = "是否为挂单方", example = "false")
    private Boolean isMaker;

    @Schema(description = "是否为最优价格匹配", example = "true")
    private Boolean isBestMatch;

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "对手方用户ID", example = "456")
    private Long counterpartyUserId;

    @Schema(description = "交易类型：SPOT-现货，FUTURES-合约", example = "SPOT")
    private String tradeType;
}