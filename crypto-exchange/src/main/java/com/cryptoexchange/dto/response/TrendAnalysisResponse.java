package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 趋势分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrendAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 趋势强度评分 (0-100)
     */
    private BigDecimal trendStrengthScore;

    /**
     * 主要趋势方向
     */
    private TrendDirection primaryTrendDirection;

    /**
     * 多时间框架趋势
     */
    private MultiTimeframeTrend multiTimeframeTrend;

    /**
     * 趋势技术指标
     */
    private TrendTechnicalIndicators trendTechnicalIndicators;

    /**
     * 趋势线分析
     */
    private TrendLineAnalysis trendLineAnalysis;

    /**
     * 趋势确认信号
     */
    private TrendConfirmationSignals trendConfirmationSignals;

    /**
     * 趋势反转信号
     */
    private TrendReversalSignals trendReversalSignals;

    /**
     * 趋势持续性分析
     */
    private TrendPersistenceAnalysis trendPersistenceAnalysis;

    /**
     * 趋势目标位
     */
    private TrendTargets trendTargets;

    /**
     * 趋势风险评估
     */
    private TrendRiskAssessment trendRiskAssessment;

    /**
     * 趋势交易建议
     */
    private List<TrendTradingAdvice> trendTradingAdvice;

    /**
     * 历史趋势对比
     */
    private HistoricalTrendComparison historicalTrendComparison;

    /**
     * 趋势方向
     */
    public enum TrendDirection {
        STRONG_UPTREND("强上升趋势", 5, "价格呈现强劲上升趋势"),
        UPTREND("上升趋势", 4, "价格呈现上升趋势"),
        SIDEWAYS("横盘", 3, "价格在区间内震荡"),
        DOWNTREND("下降趋势", 2, "价格呈现下降趋势"),
        STRONG_DOWNTREND("强下降趋势", 1, "价格呈现强劲下降趋势");

        private final String description;
        private final Integer strength;
        private final String detail;

        TrendDirection(String description, Integer strength, String detail) {
            this.description = description;
            this.strength = strength;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public Integer getStrength() {
            return strength;
        }

        public String getDetail() {
            return detail;
        }

        public static TrendDirection fromScore(BigDecimal score) {
            if (score == null) return SIDEWAYS;
            
            double value = score.doubleValue();
            if (value >= 80) return STRONG_UPTREND;
            else if (value >= 60) return UPTREND;
            else if (value >= 40) return SIDEWAYS;
            else if (value >= 20) return DOWNTREND;
            else return STRONG_DOWNTREND;
        }
    }

    /**
     * 多时间框架趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MultiTimeframeTrend {
        /**
         * 1分钟趋势
         */
        private String oneMinuteTrend;

        /**
         * 5分钟趋势
         */
        private String fiveMinuteTrend;

        /**
         * 15分钟趋势
         */
        private String fifteenMinuteTrend;

        /**
         * 1小时趋势
         */
        private String oneHourTrend;

        /**
         * 4小时趋势
         */
        private String fourHourTrend;

        /**
         * 日线趋势
         */
        private String dailyTrend;

        /**
         * 周线趋势
         */
        private String weeklyTrend;

        /**
         * 趋势一致性
         */
        private BigDecimal trendConsistency;

        /**
         * 趋势分歧度
         */
        private BigDecimal trendDivergence;
    }

    /**
     * 趋势技术指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendTechnicalIndicators {
        /**
         * 移动平均线
         */
        private MovingAverages movingAverages;

        /**
         * MACD指标
         */
        private MacdIndicator macdIndicator;

        /**
         * ADX指标（趋势强度）
         */
        private AdxIndicator adxIndicator;

        /**
         * 抛物线SAR
         */
        private ParabolicSar parabolicSar;

        /**
         * 一目均衡表
         */
        private IchimokuCloud ichimokuCloud;

        /**
         * 趋势指标汇总
         */
        private Map<String, BigDecimal> trendIndicatorSummary;
    }

    /**
     * 移动平均线
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovingAverages {
        /**
         * MA5
         */
        private BigDecimal ma5;

        /**
         * MA10
         */
        private BigDecimal ma10;

        /**
         * MA20
         */
        private BigDecimal ma20;

        /**
         * MA50
         */
        private BigDecimal ma50;

        /**
         * MA200
         */
        private BigDecimal ma200;

        /**
         * EMA12
         */
        private BigDecimal ema12;

        /**
         * EMA26
         */
        private BigDecimal ema26;

        /**
         * 均线排列
         */
        private String maAlignment;

        /**
         * 均线支撑阻力
         */
        private List<String> maSupportResistance;
    }

    /**
     * MACD指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MacdIndicator {
        /**
         * MACD线
         */
        private BigDecimal macdLine;

        /**
         * 信号线
         */
        private BigDecimal signalLine;

        /**
         * 柱状图
         */
        private BigDecimal histogram;

        /**
         * MACD信号
         */
        private String macdSignal;

        /**
         * 背离情况
         */
        private String divergence;

        /**
         * 金叉死叉
         */
        private String crossoverSignal;
    }

    /**
     * ADX指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdxIndicator {
        /**
         * ADX值
         */
        private BigDecimal adxValue;

        /**
         * +DI值
         */
        private BigDecimal plusDi;

        /**
         * -DI值
         */
        private BigDecimal minusDi;

        /**
         * 趋势强度
         */
        private String trendStrength;

        /**
         * DI交叉信号
         */
        private String diCrossSignal;
    }

    /**
     * 抛物线SAR
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ParabolicSar {
        /**
         * SAR值
         */
        private BigDecimal sarValue;

        /**
         * SAR信号
         */
        private String sarSignal;

        /**
         * 加速因子
         */
        private BigDecimal accelerationFactor;

        /**
         * 趋势方向
         */
        private String trendDirection;
    }

    /**
     * 一目均衡表
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IchimokuCloud {
        /**
         * 转换线
         */
        private BigDecimal tenkanSen;

        /**
         * 基准线
         */
        private BigDecimal kijunSen;

        /**
         * 先行带A
         */
        private BigDecimal senkouSpanA;

        /**
         * 先行带B
         */
        private BigDecimal senkouSpanB;

        /**
         * 滞后线
         */
        private BigDecimal chikouSpan;

        /**
         * 云层信号
         */
        private String cloudSignal;

        /**
         * 价格与云层关系
         */
        private String priceCloudRelation;
    }

    /**
     * 趋势线分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendLineAnalysis {
        /**
         * 上升趋势线
         */
        private List<TrendLine> uptrendLines;

        /**
         * 下降趋势线
         */
        private List<TrendLine> downtrendLines;

        /**
         * 通道线
         */
        private List<TrendChannel> trendChannels;

        /**
         * 趋势线有效性
         */
        private Map<String, BigDecimal> trendLineValidity;

        /**
         * 突破概率
         */
        private Map<String, BigDecimal> breakoutProbability;
    }

    /**
     * 趋势线
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendLine {
        /**
         * 起始点价格
         */
        private BigDecimal startPrice;

        /**
         * 起始点时间
         */
        private LocalDateTime startTime;

        /**
         * 结束点价格
         */
        private BigDecimal endPrice;

        /**
         * 结束点时间
         */
        private LocalDateTime endTime;

        /**
         * 斜率
         */
        private BigDecimal slope;

        /**
         * 有效性
         */
        private BigDecimal validity;

        /**
         * 触及次数
         */
        private Integer touchCount;
    }

    /**
     * 趋势通道
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendChannel {
        /**
         * 上轨
         */
        private TrendLine upperLine;

        /**
         * 下轨
         */
        private TrendLine lowerLine;

        /**
         * 通道宽度
         */
        private BigDecimal channelWidth;

        /**
         * 通道有效性
         */
        private BigDecimal channelValidity;

        /**
         * 当前位置
         */
        private String currentPosition;
    }

    /**
     * 趋势确认信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendConfirmationSignals {
        /**
         * 成交量确认
         */
        private String volumeConfirmation;

        /**
         * 动量确认
         */
        private String momentumConfirmation;

        /**
         * 多重时间框架确认
         */
        private String multiTimeframeConfirmation;

        /**
         * 技术指标确认
         */
        private List<String> technicalIndicatorConfirmations;

        /**
         * 确认强度
         */
        private BigDecimal confirmationStrength;

        /**
         * 确认可靠性
         */
        private BigDecimal confirmationReliability;
    }

    /**
     * 趋势反转信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendReversalSignals {
        /**
         * 反转信号列表
         */
        private List<String> reversalSignals;

        /**
         * 反转概率
         */
        private BigDecimal reversalProbability;

        /**
         * 反转确认条件
         */
        private List<String> reversalConfirmationConditions;

        /**
         * 早期反转警告
         */
        private List<String> earlyReversalWarnings;

        /**
         * 反转目标位
         */
        private List<BigDecimal> reversalTargets;

        /**
         * 反转时间预估
         */
        private String reversalTimeEstimate;
    }

    /**
     * 趋势持续性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendPersistenceAnalysis {
        /**
         * 趋势持续概率
         */
        private BigDecimal trendPersistenceProbability;

        /**
         * 趋势疲劳度
         */
        private BigDecimal trendFatigue;

        /**
         * 趋势生命周期阶段
         */
        private String trendLifecycleStage;

        /**
         * 趋势加速度
         */
        private BigDecimal trendAcceleration;

        /**
         * 趋势成熟度
         */
        private BigDecimal trendMaturity;

        /**
         * 持续性影响因素
         */
        private List<String> persistenceInfluencingFactors;
    }

    /**
     * 趋势目标位
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendTargets {
        /**
         * 短期目标
         */
        private List<BigDecimal> shortTermTargets;

        /**
         * 中期目标
         */
        private List<BigDecimal> mediumTermTargets;

        /**
         * 长期目标
         */
        private List<BigDecimal> longTermTargets;

        /**
         * 斐波那契目标
         */
        private Map<String, BigDecimal> fibonacciTargets;

        /**
         * 测量目标
         */
        private List<BigDecimal> measuredTargets;

        /**
         * 目标达成概率
         */
        private Map<BigDecimal, BigDecimal> targetProbabilities;
    }

    /**
     * 趋势风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendRiskAssessment {
        /**
         * 趋势风险等级
         */
        private String trendRiskLevel;

        /**
         * 反转风险
         */
        private BigDecimal reversalRisk;

        /**
         * 假突破风险
         */
        private BigDecimal falseBreakoutRisk;

        /**
         * 趋势衰竭风险
         */
        private BigDecimal trendExhaustionRisk;

        /**
         * 最大回撤预估
         */
        private BigDecimal maxDrawdownEstimate;

        /**
         * 风险控制建议
         */
        private List<String> riskControlSuggestions;
    }

    /**
     * 趋势交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendTradingAdvice {
        /**
         * 建议类型
         */
        private String adviceType;

        /**
         * 交易方向
         */
        private String tradingDirection;

        /**
         * 入场点
         */
        private BigDecimal entryPoint;

        /**
         * 止损点
         */
        private BigDecimal stopLoss;

        /**
         * 止盈点
         */
        private BigDecimal takeProfit;

        /**
         * 风险回报比
         */
        private BigDecimal riskRewardRatio;

        /**
         * 建议强度
         */
        private BigDecimal adviceStrength;

        /**
         * 有效期
         */
        private String validity;

        /**
         * 建议描述
         */
        private String description;
    }

    /**
     * 历史趋势对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalTrendComparison {
        /**
         * 相似历史趋势
         */
        private List<String> similarHistoricalTrends;

        /**
         * 历史趋势匹配度
         */
        private BigDecimal historicalTrendMatch;

        /**
         * 历史表现统计
         */
        private Map<String, BigDecimal> historicalPerformanceStats;

        /**
         * 历史趋势持续时间
         */
        private Map<String, String> historicalTrendDurations;

        /**
         * 历史经验教训
         */
        private List<String> historicalLessons;

        /**
         * 成功率统计
         */
        private BigDecimal historicalSuccessRate;
    }

    /**
     * 获取趋势摘要
     */
    public String getTrendSummary() {
        return String.format("趋势方向: %s (强度: %s) - %s",
            primaryTrendDirection != null ? primaryTrendDirection.getDescription() : "未知",
            trendStrengthScore != null ? trendStrengthScore.toString() : "N/A",
            primaryTrendDirection != null ? primaryTrendDirection.getDetail() : "无详细信息");
    }

    /**
     * 检查趋势是否强劲
     */
    public boolean isStrongTrend() {
        return primaryTrendDirection != null && 
               (primaryTrendDirection == TrendDirection.STRONG_UPTREND || 
                primaryTrendDirection == TrendDirection.STRONG_DOWNTREND);
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (trendTradingAdvice == null || trendTradingAdvice.isEmpty()) {
            return "暂无交易建议";
        }
        
        TrendTradingAdvice strongestAdvice = trendTradingAdvice.stream()
            .max((a1, a2) -> a1.getAdviceStrength().compareTo(a2.getAdviceStrength()))
            .orElse(null);
        
        if (strongestAdvice != null) {
            return String.format("%s - %s (强度: %s)",
                strongestAdvice.getTradingDirection(),
                strongestAdvice.getDescription(),
                strongestAdvice.getAdviceStrength());
        }
        
        return "无明确交易建议";
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (trendRiskAssessment == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = trendRiskAssessment.getTrendRiskLevel();
        if (riskLevel == null) {
            return "风险等级未知";
        }
        
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：趋势可能反转，建议谨慎交易";
            case "MEDIUM":
                return "中等风险：注意趋势变化，适度交易";
            case "LOW":
                return "低风险：趋势相对稳定，可正常交易";
            default:
                return "风险等级: " + riskLevel;
        }
    }

    /**
     * 检查是否有反转信号
     */
    public boolean hasReversalSignals() {
        return trendReversalSignals != null && 
               trendReversalSignals.getReversalSignals() != null &&
               !trendReversalSignals.getReversalSignals().isEmpty();
    }

    /**
     * 获取趋势一致性描述
     */
    public String getTrendConsistencyDescription() {
        if (multiTimeframeTrend == null || multiTimeframeTrend.getTrendConsistency() == null) {
            return "趋势一致性未知";
        }
        
        double consistency = multiTimeframeTrend.getTrendConsistency().doubleValue();
        if (consistency >= 0.8) {
            return "多时间框架趋势高度一致";
        } else if (consistency >= 0.6) {
            return "多时间框架趋势基本一致";
        } else if (consistency >= 0.4) {
            return "多时间框架趋势存在分歧";
        } else {
            return "多时间框架趋势严重分歧";
        }
    }

    /**
     * 获取趋势生命周期阶段描述
     */
    public String getTrendLifecycleDescription() {
        if (trendPersistenceAnalysis == null || 
            trendPersistenceAnalysis.getTrendLifecycleStage() == null) {
            return "趋势生命周期阶段未知";
        }
        
        String stage = trendPersistenceAnalysis.getTrendLifecycleStage();
        switch (stage.toUpperCase()) {
            case "EARLY":
                return "趋势初期：刚刚形成，具有较大发展空间";
            case "MATURE":
                return "趋势成熟期：发展充分，需注意反转风险";
            case "LATE":
                return "趋势后期：可能即将结束，建议谨慎";
            case "EXHAUSTED":
                return "趋势衰竭期：动能不足，反转概率较高";
            default:
                return "趋势阶段: " + stage;
        }
    }
}