package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约规格响应
 */
public class ContractSpecResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 合约类型
     */
    private String contractType;
    
    /**
     * 合约大小
     */
    private BigDecimal contractSize;
    
    /**
     * 最小价格变动
     */
    private BigDecimal tickSize;
    
    /**
     * 最小数量变动
     */
    private BigDecimal stepSize;
    
    /**
     * 最大杠杆倍数
     */
    private Integer maxLeverage;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRate;
    
    /**
     * 初始保证金率
     */
    private BigDecimal requiredMarginRate;
    
    /**
     * 基础资产
     */
    private String baseAsset;
    
    /**
     * 报价资产
     */
    private String quoteAsset;
    
    /**
     * 结算资产
     */
    private String settlementAsset;
    
    /**
     * 合约状态
     */
    private String status;
    
    /**
     * 上市时间
     */
    private LocalDateTime listingTime;
    
    /**
     * 交割时间（永续合约为null）
     */
    private LocalDateTime deliveryTime;
    
    public ContractSpecResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public String getContractType() {
        return contractType;
    }
    
    public void setContractType(String contractType) {
        this.contractType = contractType;
    }
    
    public BigDecimal getContractSize() {
        return contractSize;
    }
    
    public void setContractSize(BigDecimal contractSize) {
        this.contractSize = contractSize;
    }
    
    public BigDecimal getTickSize() {
        return tickSize;
    }
    
    public void setTickSize(BigDecimal tickSize) {
        this.tickSize = tickSize;
    }
    
    public BigDecimal getStepSize() {
        return stepSize;
    }
    
    public void setStepSize(BigDecimal stepSize) {
        this.stepSize = stepSize;
    }
    
    public Integer getMaxLeverage() {
        return maxLeverage;
    }
    
    public void setMaxLeverage(Integer maxLeverage) {
        this.maxLeverage = maxLeverage;
    }
    
    public BigDecimal getMaintMarginRate() {
        return maintMarginRate;
    }
    
    public void setMaintMarginRate(BigDecimal maintMarginRate) {
        this.maintMarginRate = maintMarginRate;
    }
    
    public BigDecimal getRequiredMarginRate() {
        return requiredMarginRate;
    }
    
    public void setRequiredMarginRate(BigDecimal requiredMarginRate) {
        this.requiredMarginRate = requiredMarginRate;
    }
    
    public String getBaseAsset() {
        return baseAsset;
    }
    
    public void setBaseAsset(String baseAsset) {
        this.baseAsset = baseAsset;
    }
    
    public String getQuoteAsset() {
        return quoteAsset;
    }
    
    public void setQuoteAsset(String quoteAsset) {
        this.quoteAsset = quoteAsset;
    }
    
    public String getSettlementAsset() {
        return settlementAsset;
    }
    
    public void setSettlementAsset(String settlementAsset) {
        this.settlementAsset = settlementAsset;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getListingTime() {
        return listingTime;
    }
    
    public void setListingTime(LocalDateTime listingTime) {
        this.listingTime = listingTime;
    }
    
    public LocalDateTime getDeliveryTime() {
        return deliveryTime;
    }
    
    public void setDeliveryTime(LocalDateTime deliveryTime) {
        this.deliveryTime = deliveryTime;
    }
}