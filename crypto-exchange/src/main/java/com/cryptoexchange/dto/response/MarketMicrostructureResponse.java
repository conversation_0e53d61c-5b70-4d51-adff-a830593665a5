package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场微观结构分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketMicrostructureResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 市场微观结构评分 (0-100)
     */
    private BigDecimal microstructureScore;

    /**
     * 市场质量等级
     */
    private MarketQualityLevel qualityLevel;

    /**
     * 价格发现效率
     */
    private PriceDiscoveryEfficiency priceDiscoveryEfficiency;

    /**
     * 订单流分析
     */
    private OrderFlowAnalysis orderFlowAnalysis;

    /**
     * 交易成本分析
     */
    private TradingCostAnalysis tradingCostAnalysis;

    /**
     * 市场深度结构
     */
    private MarketDepthStructure marketDepthStructure;

    /**
     * 流动性提供机制
     */
    private LiquidityProvisionMechanism liquidityProvisionMechanism;

    /**
     * 信息传播分析
     */
    private InformationPropagationAnalysis informationPropagationAnalysis;

    /**
     * 市场参与者结构
     */
    private MarketParticipantStructure marketParticipantStructure;

    /**
     * 交易模式分析
     */
    private TradingPatternAnalysis tradingPatternAnalysis;

    /**
     * 市场稳定性指标
     */
    private MarketStabilityIndicators stabilityIndicators;

    /**
     * 监管合规性
     */
    private RegulatoryCompliance regulatoryCompliance;

    /**
     * 改进建议
     */
    private List<MicrostructureImprovement> improvementSuggestions;

    /**
     * 市场质量等级
     */
    public enum MarketQualityLevel {
        EXCELLENT("优秀", 5, "市场微观结构优秀，交易效率高"),
        GOOD("良好", 4, "市场结构良好，适合各类交易"),
        MODERATE("中等", 3, "市场结构中等，存在改进空间"),
        POOR("较差", 2, "市场结构较差，交易成本较高"),
        VERY_POOR("极差", 1, "市场结构极差，不利于交易");

        private final String description;
        private final Integer level;
        private final String detail;

        MarketQualityLevel(String description, Integer level, String detail) {
            this.description = description;
            this.level = level;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }

        public String getDetail() {
            return detail;
        }

        public static MarketQualityLevel fromScore(BigDecimal score) {
            if (score == null) return MODERATE;
            
            double value = score.doubleValue();
            if (value >= 80) return EXCELLENT;
            else if (value >= 60) return GOOD;
            else if (value >= 40) return MODERATE;
            else if (value >= 20) return POOR;
            else return VERY_POOR;
        }
    }

    /**
     * 价格发现效率
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceDiscoveryEfficiency {
        /**
         * 价格发现效率指数
         */
        private BigDecimal efficiencyIndex;

        /**
         * 信息融入速度
         */
        private BigDecimal informationIncorporationSpeed;

        /**
         * 价格偏差程度
         */
        private BigDecimal priceDeviationLevel;

        /**
         * 套利机会持续时间
         */
        private BigDecimal arbitrageDuration;

        /**
         * 价格连续性
         */
        private BigDecimal priceContinuity;

        /**
         * 有效价差
         */
        private BigDecimal effectiveSpread;

        /**
         * 价格冲击衰减速度
         */
        private BigDecimal impactDecaySpeed;
    }

    /**
     * 订单流分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderFlowAnalysis {
        /**
         * 订单流不平衡
         */
        private BigDecimal orderFlowImbalance;

        /**
         * 大单占比
         */
        private BigDecimal largeOrderRatio;

        /**
         * 订单到达频率
         */
        private BigDecimal orderArrivalRate;

        /**
         * 订单取消率
         */
        private BigDecimal orderCancellationRate;

        /**
         * 隐藏订单比例
         */
        private BigDecimal hiddenOrderRatio;

        /**
         * 订单分割程度
         */
        private BigDecimal orderFragmentation;

        /**
         * 算法交易占比
         */
        private BigDecimal algorithmicTradingRatio;

        /**
         * 订单流毒性
         */
        private BigDecimal orderFlowToxicity;
    }

    /**
     * 交易成本分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingCostAnalysis {
        /**
         * 总交易成本
         */
        private BigDecimal totalTradingCost;

        /**
         * 显性成本（手续费等）
         */
        private BigDecimal explicitCost;

        /**
         * 隐性成本（滑点等）
         */
        private BigDecimal implicitCost;

        /**
         * 市场冲击成本
         */
        private BigDecimal marketImpactCost;

        /**
         * 时机成本
         */
        private BigDecimal timingCost;

        /**
         * 机会成本
         */
        private BigDecimal opportunityCost;

        /**
         * 成本效率指数
         */
        private BigDecimal costEfficiencyIndex;
    }

    /**
     * 市场深度结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketDepthStructure {
        /**
         * 深度分布均匀性
         */
        private BigDecimal depthUniformity;

        /**
         * 深度弹性
         */
        private BigDecimal depthResilience;

        /**
         * 深度稳定性
         */
        private BigDecimal depthStability;

        /**
         * 价格层级数量
         */
        private Integer priceLevelCount;

        /**
         * 平均层级深度
         */
        private BigDecimal averageLevelDepth;

        /**
         * 深度集中度
         */
        private BigDecimal depthConcentration;

        /**
         * 深度不对称性
         */
        private BigDecimal depthAsymmetry;
    }

    /**
     * 流动性提供机制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityProvisionMechanism {
        /**
         * 做市商激励机制
         */
        private String marketMakerIncentives;

        /**
         * 流动性挖矿效果
         */
        private BigDecimal liquidityMiningEffect;

        /**
         * 自动做市商占比
         */
        private BigDecimal ammRatio;

        /**
         * 专业做市商占比
         */
        private BigDecimal professionalMmRatio;

        /**
         * 流动性提供稳定性
         */
        private BigDecimal provisionStability;

        /**
         * 流动性竞争强度
         */
        private BigDecimal competitionIntensity;
    }

    /**
     * 信息传播分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InformationPropagationAnalysis {
        /**
         * 信息传播速度
         */
        private BigDecimal propagationSpeed;

        /**
         * 信息不对称程度
         */
        private BigDecimal informationAsymmetry;

        /**
         * 内幕交易检测
         */
        private BigDecimal insiderTradingDetection;

        /**
         * 新闻影响延迟
         */
        private BigDecimal newsImpactDelay;

        /**
         * 市场透明度
         */
        private BigDecimal marketTransparency;

        /**
         * 信息效率指数
         */
        private BigDecimal informationEfficiencyIndex;
    }

    /**
     * 市场参与者结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketParticipantStructure {
        /**
         * 机构投资者占比
         */
        private BigDecimal institutionalRatio;

        /**
         * 零售投资者占比
         */
        private BigDecimal retailRatio;

        /**
         * 高频交易占比
         */
        private BigDecimal hftRatio;

        /**
         * 套利交易占比
         */
        private BigDecimal arbitrageRatio;

        /**
         * 参与者多样性
         */
        private BigDecimal participantDiversity;

        /**
         * 市场集中度
         */
        private BigDecimal marketConcentration;
    }

    /**
     * 交易模式分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingPatternAnalysis {
        /**
         * 交易时间分布
         */
        private Map<String, BigDecimal> tradingTimeDistribution;

        /**
         * 交易规模分布
         */
        private Map<String, BigDecimal> tradingSizeDistribution;

        /**
         * 交易频率模式
         */
        private String tradingFrequencyPattern;

        /**
         * 季节性特征
         */
        private String seasonalityCharacteristics;

        /**
         * 异常交易检测
         */
        private List<String> anomalousTrading;

        /**
         * 交易行为聚类
         */
        private Map<String, BigDecimal> behaviorClusters;
    }

    /**
     * 市场稳定性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketStabilityIndicators {
        /**
         * 价格稳定性
         */
        private BigDecimal priceStability;

        /**
         * 流动性稳定性
         */
        private BigDecimal liquidityStability;

        /**
         * 交易量稳定性
         */
        private BigDecimal volumeStability;

        /**
         * 系统性风险指标
         */
        private BigDecimal systemicRiskIndicator;

        /**
         * 市场韧性
         */
        private BigDecimal marketResilience;

        /**
         * 压力测试结果
         */
        private BigDecimal stressTestResult;
    }

    /**
     * 监管合规性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryCompliance {
        /**
         * 合规评分
         */
        private BigDecimal complianceScore;

        /**
         * 反洗钱合规
         */
        private String amlCompliance;

        /**
         * 市场操纵检测
         */
        private String marketManipulationDetection;

        /**
         * 交易报告完整性
         */
        private BigDecimal reportingCompleteness;

        /**
         * 风险管理合规
         */
        private String riskManagementCompliance;

        /**
         * 监管要求满足度
         */
        private BigDecimal regulatoryRequirementsMet;
    }

    /**
     * 微观结构改进建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MicrostructureImprovement {
        /**
         * 改进领域
         */
        private String improvementArea;

        /**
         * 具体建议
         */
        private String suggestion;

        /**
         * 预期效果
         */
        private String expectedEffect;

        /**
         * 实施复杂度
         */
        private String implementationComplexity;

        /**
         * 优先级
         */
        private Integer priority;

        /**
         * 预期改善幅度
         */
        private BigDecimal expectedImprovement;

        /**
         * 实施成本
         */
        private String implementationCost;
    }

    /**
     * 获取市场微观结构摘要
     */
    public String getMicrostructureSummary() {
        return String.format("市场质量: %s (评分: %s) - %s",
            qualityLevel != null ? qualityLevel.getDescription() : "未知",
            microstructureScore != null ? microstructureScore.toString() : "N/A",
            qualityLevel != null ? qualityLevel.getDetail() : "无详细信息");
    }

    /**
     * 检查是否适合机构交易
     */
    public boolean isSuitableForInstitutionalTrading() {
        return qualityLevel != null && 
               (qualityLevel == MarketQualityLevel.EXCELLENT || qualityLevel == MarketQualityLevel.GOOD);
    }

    /**
     * 获取主要风险提示
     */
    public String getMainRiskAlert() {
        if (stabilityIndicators == null) {
            return "稳定性指标不可用";
        }
        
        BigDecimal systemicRisk = stabilityIndicators.getSystemicRiskIndicator();
        if (systemicRisk == null) {
            return "系统性风险评估不可用";
        }
        
        double risk = systemicRisk.doubleValue();
        if (risk >= 0.8) {
            return "高系统性风险：建议暂停大额交易";
        } else if (risk >= 0.6) {
            return "中等系统性风险：注意市场波动";
        } else if (risk >= 0.4) {
            return "低系统性风险：可正常交易";
        } else {
            return "系统性风险较低：市场环境良好";
        }
    }

    /**
     * 获取交易建议
     */
    public String getTradingRecommendation() {
        if (microstructureScore == null) {
            return "无法提供交易建议";
        }
        
        double score = microstructureScore.doubleValue();
        if (score >= 80) {
            return "优秀的市场结构，适合各类交易策略";
        } else if (score >= 60) {
            return "良好的市场结构，建议采用适中的交易策略";
        } else if (score >= 40) {
            return "一般的市场结构，建议谨慎交易";
        } else {
            return "较差的市场结构，建议避免大额交易";
        }
    }
}