package com.cryptoexchange.dto.response;

import java.util.List;

/**
 * 批量取消订单响应
 */
public class BatchCancelOrderResponse {
    
    /**
     * 成功取消的订单ID列表
     */
    private List<Long> successOrderIds;
    
    /**
     * 取消失败的订单信息列表
     */
    private List<FailedOrder> failedOrders;
    
    /**
     * 总处理订单数
     */
    private Integer totalProcessed;
    
    /**
     * 成功取消订单数
     */
    private Integer successCount;
    
    /**
     * 失败订单数
     */
    private Integer failedCount;
    
    public BatchCancelOrderResponse() {}
    
    public List<Long> getSuccessOrderIds() {
        return successOrderIds;
    }
    
    public void setSuccessOrderIds(List<Long> successOrderIds) {
        this.successOrderIds = successOrderIds;
    }
    
    public List<FailedOrder> getFailedOrders() {
        return failedOrders;
    }
    
    public void setFailedOrders(List<FailedOrder> failedOrders) {
        this.failedOrders = failedOrders;
    }
    
    public Integer getTotalProcessed() {
        return totalProcessed;
    }
    
    public void setTotalProcessed(Integer totalProcessed) {
        this.totalProcessed = totalProcessed;
    }
    
    public Integer getSuccessCount() {
        return successCount;
    }
    
    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }
    
    public Integer getFailedCount() {
        return failedCount;
    }
    
    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }
    
    /**
     * 失败订单信息
     */
    public static class FailedOrder {
        
        /**
         * 订单ID
         */
        private Long orderId;
        
        /**
         * 失败原因
         */
        private String reason;
        
        /**
         * 错误代码
         */
        private String errorCode;
        
        public FailedOrder() {}
        
        public FailedOrder(Long orderId, String reason, String errorCode) {
            this.orderId = orderId;
            this.reason = reason;
            this.errorCode = errorCode;
        }
        
        public Long getOrderId() {
            return orderId;
        }
        
        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }
        
        public String getReason() {
            return reason;
        }
        
        public void setReason(String reason) {
            this.reason = reason;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
    }
}