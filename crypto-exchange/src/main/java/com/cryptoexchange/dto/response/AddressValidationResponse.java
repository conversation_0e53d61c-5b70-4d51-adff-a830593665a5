package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 地址验证响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddressValidationResponse {
    
    /**
     * 验证的地址
     */
    private String address;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 是否有效
     */
    private Boolean isValid;
    
    /**
     * 验证状态 (VALID, INVALID, UNKNOWN)
     */
    private String validationStatus;
    
    /**
     * 地址类型 (LEGACY, SEGWIT, NATIVE_SEGWIT, CONTRACT, EOA)
     */
    private String addressType;
    
    /**
     * 地址格式 (BASE58, BECH32, HEX)
     */
    private String addressFormat;
    
    /**
     * 是否为合约地址
     */
    private Boolean isContract;
    
    /**
     * 是否为多签地址
     */
    private Boolean isMultiSig;
    
    /**
     * 是否需要标签/备忘录
     */
    private Boolean requiresTag;
    
    /**
     * 标签/备忘录
     */
    private String tag;
    
    /**
     * 是否为内部地址
     */
    private Boolean isInternal;
    
    /**
     * 验证错误信息
     */
    private String errorMessage;
    
    /**
     * 验证错误代码
     */
    private String errorCode;
    
    /**
     * 风险等级 (LOW, MEDIUM, HIGH)
     */
    private String riskLevel;
    
    /**
     * 风险原因
     */
    private List<String> riskReasons;
    
    /**
     * 地址标签信息
     */
    private List<AddressLabel> addressLabels;
    
    /**
     * 验证时间
     */
    private LocalDateTime validationTime;
    
    /**
     * 验证来源
     */
    private String validationSource;
    
    /**
     * 额外验证信息
     */
    private ValidationDetails validationDetails;
    
    /**
     * 地址标签内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AddressLabel {
        /**
         * 标签类型
         */
        private String labelType;
        
        /**
         * 标签值
         */
        private String labelValue;
        
        /**
         * 标签来源
         */
        private String source;
        
        /**
         * 置信度
         */
        private Double confidence;
    }
    
    /**
     * 验证详情内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationDetails {
        /**
         * 校验和验证
         */
        private Boolean checksumValid;
        
        /**
         * 长度验证
         */
        private Boolean lengthValid;
        
        /**
         * 格式验证
         */
        private Boolean formatValid;
        
        /**
         * 网络兼容性
         */
        private Boolean networkCompatible;
        
        /**
         * 地址活跃状态
         */
        private Boolean isActive;
        
        /**
         * 最后活跃时间
         */
        private LocalDateTime lastActiveTime;
        
        /**
         * 交易数量
         */
        private Long transactionCount;
        
        /**
         * 余额信息
         */
        private String balance;
    }
}