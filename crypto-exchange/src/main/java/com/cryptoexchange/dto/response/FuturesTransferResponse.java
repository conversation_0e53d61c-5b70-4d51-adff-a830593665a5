package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货转账响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesTransferResponse {
    
    /**
     * 转账ID
     */
    private String transferId;
    
    /**
     * 客户端转账ID
     */
    private String clientTransferId;
    
    /**
     * 币种
     */
    private String asset;
    
    /**
     * 转账金额
     */
    private BigDecimal amount;
    
    /**
     * 转账类型 (1: 现货账户转入期货账户, 2: 期货账户转入现货账户)
     */
    private Integer type;
    
    /**
     * 转账状态 (PENDING, SUCCESS, FAILED)
     */
    private String status;
    
    /**
     * 转账时间
     */
    private LocalDateTime transferTime;
    
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 手续费币种
     */
    private String feeAsset;
    
    /**
     * 转出账户余额
     */
    private BigDecimal fromBalance;
    
    /**
     * 转入账户余额
     */
    private BigDecimal toBalance;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 交易哈希 (如果涉及链上转账)
     */
    private String txHash;
}