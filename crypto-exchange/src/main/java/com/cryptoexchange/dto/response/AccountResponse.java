package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 账户响应
 */
public class AccountResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 账户类型
     */
    private String accountType;
    
    /**
     * 总资产
     */
    private BigDecimal totalAsset;
    
    /**
     * 可用余额
     */
    private BigDecimal availableBalance;
    
    /**
     * 冻结余额
     */
    private BigDecimal frozenBalance;
    
    /**
     * 总负债
     */
    private BigDecimal totalLiability;
    
    /**
     * 净资产
     */
    private BigDecimal netAsset;
    
    /**
     * 账户状态
     */
    private String status;
    
    /**
     * 资产详情列表
     */
    private List<AssetDetail> assets;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    public AccountResponse() {}
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getAccountType() {
        return accountType;
    }
    
    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }
    
    public BigDecimal getTotalAsset() {
        return totalAsset;
    }
    
    public void setTotalAsset(BigDecimal totalAsset) {
        this.totalAsset = totalAsset;
    }
    
    public BigDecimal getAvailableBalance() {
        return availableBalance;
    }
    
    public void setAvailableBalance(BigDecimal availableBalance) {
        this.availableBalance = availableBalance;
    }
    
    public BigDecimal getFrozenBalance() {
        return frozenBalance;
    }
    
    public void setFrozenBalance(BigDecimal frozenBalance) {
        this.frozenBalance = frozenBalance;
    }
    
    public BigDecimal getTotalLiability() {
        return totalLiability;
    }
    
    public void setTotalLiability(BigDecimal totalLiability) {
        this.totalLiability = totalLiability;
    }
    
    public BigDecimal getNetAsset() {
        return netAsset;
    }
    
    public void setNetAsset(BigDecimal netAsset) {
        this.netAsset = netAsset;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public List<AssetDetail> getAssets() {
        return assets;
    }
    
    public void setAssets(List<AssetDetail> assets) {
        this.assets = assets;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    
    /**
     * 资产详情
     */
    public static class AssetDetail {
        
        /**
         * 资产名称
         */
        private String asset;
        
        /**
         * 可用余额
         */
        private BigDecimal free;
        
        /**
         * 冻结余额
         */
        private BigDecimal locked;
        
        /**
         * 总余额
         */
        private BigDecimal total;
        
        /**
         * 借贷金额
         */
        private BigDecimal borrowed;
        
        /**
         * 利息
         */
        private BigDecimal interest;
        
        public AssetDetail() {}
        
        public String getAsset() {
            return asset;
        }
        
        public void setAsset(String asset) {
            this.asset = asset;
        }
        
        public BigDecimal getFree() {
            return free;
        }
        
        public void setFree(BigDecimal free) {
            this.free = free;
        }
        
        public BigDecimal getLocked() {
            return locked;
        }
        
        public void setLocked(BigDecimal locked) {
            this.locked = locked;
        }
        
        public BigDecimal getTotal() {
            return total;
        }
        
        public void setTotal(BigDecimal total) {
            this.total = total;
        }
        
        public BigDecimal getBorrowed() {
            return borrowed;
        }
        
        public void setBorrowed(BigDecimal borrowed) {
            this.borrowed = borrowed;
        }
        
        public BigDecimal getInterest() {
            return interest;
        }
        
        public void setInterest(BigDecimal interest) {
            this.interest = interest;
        }
    }
}