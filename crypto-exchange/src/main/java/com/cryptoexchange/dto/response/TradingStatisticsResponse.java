package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易统计响应
 */
public class TradingStatisticsResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总交易量
     */
    private BigDecimal totalVolume;
    
    /**
     * 总交易次数
     */
    private Long totalTrades;
    
    /**
     * 总手续费
     */
    private BigDecimal totalFees;
    
    /**
     * 买入交易次数
     */
    private Long buyTrades;
    
    /**
     * 卖出交易次数
     */
    private Long sellTrades;
    
    /**
     * 买入交易量
     */
    private BigDecimal buyVolume;
    
    /**
     * 卖出交易量
     */
    private BigDecimal sellVolume;
    
    /**
     * 平均交易金额
     */
    private BigDecimal avgTradeAmount;
    
    /**
     * 最大单笔交易金额
     */
    private BigDecimal maxTradeAmount;
    
    /**
     * 最小单笔交易金额
     */
    private BigDecimal minTradeAmount;
    
    /**
     * 首次交易时间
     */
    private LocalDateTime firstTradeTime;
    
    /**
     * 最后交易时间
     */
    private LocalDateTime lastTradeTime;
    
    /**
     * 统计周期开始时间
     */
    private LocalDateTime periodStart;
    
    /**
     * 统计周期结束时间
     */
    private LocalDateTime periodEnd;
    
    public TradingStatisticsResponse() {}
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public BigDecimal getTotalVolume() {
        return totalVolume;
    }
    
    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }
    
    public Long getTotalTrades() {
        return totalTrades;
    }
    
    public void setTotalTrades(Long totalTrades) {
        this.totalTrades = totalTrades;
    }
    
    public BigDecimal getTotalFees() {
        return totalFees;
    }
    
    public void setTotalFees(BigDecimal totalFees) {
        this.totalFees = totalFees;
    }
    
    public Long getBuyTrades() {
        return buyTrades;
    }
    
    public void setBuyTrades(Long buyTrades) {
        this.buyTrades = buyTrades;
    }
    
    public Long getSellTrades() {
        return sellTrades;
    }
    
    public void setSellTrades(Long sellTrades) {
        this.sellTrades = sellTrades;
    }
    
    public BigDecimal getBuyVolume() {
        return buyVolume;
    }
    
    public void setBuyVolume(BigDecimal buyVolume) {
        this.buyVolume = buyVolume;
    }
    
    public BigDecimal getSellVolume() {
        return sellVolume;
    }
    
    public void setSellVolume(BigDecimal sellVolume) {
        this.sellVolume = sellVolume;
    }
    
    public BigDecimal getAvgTradeAmount() {
        return avgTradeAmount;
    }
    
    public void setAvgTradeAmount(BigDecimal avgTradeAmount) {
        this.avgTradeAmount = avgTradeAmount;
    }
    
    public BigDecimal getMaxTradeAmount() {
        return maxTradeAmount;
    }
    
    public void setMaxTradeAmount(BigDecimal maxTradeAmount) {
        this.maxTradeAmount = maxTradeAmount;
    }
    
    public BigDecimal getMinTradeAmount() {
        return minTradeAmount;
    }
    
    public void setMinTradeAmount(BigDecimal minTradeAmount) {
        this.minTradeAmount = minTradeAmount;
    }
    
    public LocalDateTime getFirstTradeTime() {
        return firstTradeTime;
    }
    
    public void setFirstTradeTime(LocalDateTime firstTradeTime) {
        this.firstTradeTime = firstTradeTime;
    }
    
    public LocalDateTime getLastTradeTime() {
        return lastTradeTime;
    }
    
    public void setLastTradeTime(LocalDateTime lastTradeTime) {
        this.lastTradeTime = lastTradeTime;
    }
    
    public LocalDateTime getPeriodStart() {
        return periodStart;
    }
    
    public void setPeriodStart(LocalDateTime periodStart) {
        this.periodStart = periodStart;
    }
    
    public LocalDateTime getPeriodEnd() {
        return periodEnd;
    }
    
    public void setPeriodEnd(LocalDateTime periodEnd) {
        this.periodEnd = periodEnd;
    }
}