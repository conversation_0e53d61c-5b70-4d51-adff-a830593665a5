package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 技术指标分析响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TechnicalIndicatorResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime analysisTime;
    
    /**
     * 时间范围
     */
    private String timeframe;
    
    /**
     * 指标类型
     */
    private List<IndicatorType> indicatorTypes;
    
    /**
     * 指标值集合
     */
    private Map<String, IndicatorValue> indicatorValues;
    
    /**
     * 移动平均线分析
     */
    private MovingAverage movingAverage;
    
    /**
     * MACD分析
     */
    private MACD macd;
    
    /**
     * RSI分析
     */
    private RSI rsi;
    
    /**
     * 布林带分析
     */
    private BollingerBands bollingerBands;
    
    /**
     * 随机振荡器分析
     */
    private StochasticOscillator stochasticOscillator;
    
    /**
     * ATR分析
     */
    private ATR atr;
    
    /**
     * ADX分析
     */
    private ADX adx;
    
    /**
     * 一目均衡表分析
     */
    private IchimokuCloud ichimokuCloud;
    
    /**
     * 成交量指标分析
     */
    private VolumeIndicator volumeIndicator;
    
    /**
     * 波动率指标分析
     */
    private VolatilityIndicator volatilityIndicator;
    
    /**
     * 趋势指标分析
     */
    private TrendIndicator trendIndicator;
    
    /**
     * 动量指标分析
     */
    private MomentumIndicator momentumIndicator;
    
    /**
     * 振荡器指标分析
     */
    private OscillatorIndicator oscillatorIndicator;
    
    /**
     * 支撑阻力指标分析
     */
    private SupportResistanceIndicator supportResistanceIndicator;
    
    /**
     * 自定义指标分析
     */
    private List<CustomIndicator> customIndicators;
    
    /**
     * 指标信号集合
     */
    private List<IndicatorSignal> signals;
    
    /**
     * 指标解读
     */
    private IndicatorInterpretation interpretation;
    
    /**
     * 交易策略建议
     */
    private TradingStrategyRecommendation tradingStrategy;
    
    /**
     * 技术指标预测
     */
    private TechnicalIndicatorForecast forecast;
    
    /**
     * 技术指标风险评估
     */
    private TechnicalIndicatorRiskAssessment riskAssessment;
    
    /**
     * 指标类型枚举
     */
    public enum IndicatorType {
        MOVING_AVERAGE,
        MACD,
        RSI,
        BOLLINGER_BANDS,
        STOCHASTIC,
        ATR,
        ADX,
        ICHIMOKU,
        VOLUME,
        VOLATILITY,
        TREND,
        MOMENTUM,
        OSCILLATOR,
        SUPPORT_RESISTANCE,
        CUSTOM
    }
    
    /**
     * 指标值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndicatorValue {
        private BigDecimal value;
        private String unit;
        private String description;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        private String status;
        private BigDecimal changePercent;
        private String trend;
    }
    
    /**
     * 移动平均线分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MovingAverage {
        private BigDecimal sma5;
        private BigDecimal sma10;
        private BigDecimal sma20;
        private BigDecimal sma50;
        private BigDecimal sma100;
        private BigDecimal sma200;
        private BigDecimal ema5;
        private BigDecimal ema10;
        private BigDecimal ema20;
        private BigDecimal ema50;
        private BigDecimal ema100;
        private BigDecimal ema200;
        private String trend;
        private String signal;
        private BigDecimal convergence;
        private String crossoverSignal;
        private List<String> supportLevels;
        private List<String> resistanceLevels;
    }
    
    /**
     * MACD分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MACD {
        private BigDecimal macdLine;
        private BigDecimal signalLine;
        private BigDecimal histogram;
        private String signal;
        private String trend;
        private BigDecimal divergence;
        private String crossoverSignal;
        private BigDecimal momentum;
        private String histogramTrend;
        private BigDecimal zeroCrossing;
    }
    
    /**
     * RSI分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RSI {
        private BigDecimal rsi14;
        private BigDecimal rsi21;
        private String signal;
        private String overboughtLevel;
        private String oversoldLevel;
        private BigDecimal divergence;
        private String trend;
        private BigDecimal momentum;
        private String failureSwing;
        private List<String> supportLevels;
        private List<String> resistanceLevels;
    }
    
    /**
     * 布林带分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BollingerBands {
        private BigDecimal upperBand;
        private BigDecimal middleBand;
        private BigDecimal lowerBand;
        private BigDecimal bandwidth;
        private BigDecimal percentB;
        private String signal;
        private String squeeze;
        private String expansion;
        private String walkTheBands;
        private BigDecimal volatility;
    }
    
    /**
     * 随机振荡器分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StochasticOscillator {
        private BigDecimal percentK;
        private BigDecimal percentD;
        private String signal;
        private String overboughtLevel;
        private String oversoldLevel;
        private BigDecimal divergence;
        private String crossoverSignal;
        private String trend;
        private BigDecimal momentum;
    }
    
    /**
     * ATR分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ATR {
        private BigDecimal atr14;
        private BigDecimal atr21;
        private BigDecimal volatility;
        private String volatilityLevel;
        private String trend;
        private BigDecimal percentile;
        private String signal;
        private BigDecimal stopLossLevel;
        private BigDecimal takeProfitLevel;
    }
    
    /**
     * ADX分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ADX {
        private BigDecimal adx;
        private BigDecimal plusDI;
        private BigDecimal minusDI;
        private String trendStrength;
        private String trendDirection;
        private String signal;
        private BigDecimal momentum;
        private String crossoverSignal;
    }
    
    /**
     * 一目均衡表分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IchimokuCloud {
        private BigDecimal tenkanSen;
        private BigDecimal kijunSen;
        private BigDecimal senkouSpanA;
        private BigDecimal senkouSpanB;
        private BigDecimal chikouSpan;
        private String cloudColor;
        private String signal;
        private String trend;
        private String momentum;
        private List<String> supportLevels;
        private List<String> resistanceLevels;
    }
    
    /**
     * 成交量指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeIndicator {
        private OnBalanceVolume obv;
        private ChaikinMoneyFlow cmf;
        private AccumulationDistributionLine adl;
        private BigDecimal volumeMA;
        private String volumeTrend;
        private String signal;
        private BigDecimal volumeRatio;
    }
    
    /**
     * 能量潮指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OnBalanceVolume {
        private BigDecimal obv;
        private String trend;
        private BigDecimal divergence;
        private String signal;
        private BigDecimal momentum;
    }
    
    /**
     * 蔡金资金流量指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChaikinMoneyFlow {
        private BigDecimal cmf;
        private String signal;
        private String trend;
        private BigDecimal strength;
        private String buyingPressure;
        private String sellingPressure;
    }
    
    /**
     * 累积/派发线
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccumulationDistributionLine {
        private BigDecimal adl;
        private String trend;
        private BigDecimal divergence;
        private String signal;
        private String accumulation;
        private String distribution;
    }
    
    /**
     * 波动率指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityIndicator {
        private HistoricalVolatility historicalVolatility;
        private ImpliedVolatility impliedVolatility;
        private BigDecimal volatilityRatio;
        private String volatilityLevel;
        private String signal;
        private String trend;
    }
    
    /**
     * 历史波动率
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalVolatility {
        private BigDecimal hv20;
        private BigDecimal hv30;
        private BigDecimal hv60;
        private String level;
        private String trend;
        private BigDecimal percentile;
    }
    
    /**
     * 隐含波动率
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpliedVolatility {
        private BigDecimal iv;
        private String level;
        private String trend;
        private BigDecimal skew;
        private String term;
    }
    
    /**
     * 趋势指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendIndicator {
        private String primaryTrend;
        private String secondaryTrend;
        private String shortTermTrend;
        private BigDecimal trendStrength;
        private String signal;
        private BigDecimal momentum;
        private List<String> trendLines;
    }
    
    /**
     * 动量指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MomentumIndicator {
        private BigDecimal momentum;
        private BigDecimal rateOfChange;
        private String signal;
        private String trend;
        private BigDecimal acceleration;
        private BigDecimal divergence;
    }
    
    /**
     * 振荡器指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OscillatorIndicator {
        private BigDecimal williamsR;
        private BigDecimal commodityChannelIndex;
        private BigDecimal ultimateOscillator;
        private String signal;
        private String overboughtLevel;
        private String oversoldLevel;
        private BigDecimal divergence;
    }
    
    /**
     * 支撑阻力指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupportResistanceIndicator {
        private List<String> supportLevels;
        private List<String> resistanceLevels;
        private BigDecimal pivotPoint;
        private String signal;
        private String strength;
        private BigDecimal probability;
    }
    
    /**
     * 自定义指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomIndicator {
        private String name;
        private String description;
        private BigDecimal value;
        private String signal;
        private String interpretation;
        private Map<String, Object> parameters;
    }
    
    /**
     * 指标信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndicatorSignal {
        private String indicatorName;
        private SignalType signalType;
        private SignalStrength strength;
        private SignalDirection direction;
        private SignalConfidence confidence;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        private String description;
        private BigDecimal price;
        private String timeframe;
    }
    
    /**
     * 信号类型
     */
    public enum SignalType {
        BUY,
        SELL,
        HOLD,
        NEUTRAL,
        WARNING,
        CONFIRMATION
    }
    
    /**
     * 信号强度
     */
    public enum SignalStrength {
        VERY_WEAK,
        WEAK,
        MODERATE,
        STRONG,
        VERY_STRONG
    }
    
    /**
     * 信号方向
     */
    public enum SignalDirection {
        BULLISH,
        BEARISH,
        NEUTRAL,
        SIDEWAYS
    }
    
    /**
     * 信号置信度
     */
    public enum SignalConfidence {
        VERY_LOW,
        LOW,
        MEDIUM,
        HIGH,
        VERY_HIGH
    }
    
    /**
     * 指标解读
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndicatorInterpretation {
        private InterpretationLevel overallLevel;
        private String summary;
        private List<InterpretationDetail> details;
        private String marketCondition;
        private String recommendation;
        private List<String> keyPoints;
        private String riskWarning;
    }
    
    /**
     * 解读等级
     */
    public enum InterpretationLevel {
        VERY_BEARISH,
        BEARISH,
        NEUTRAL,
        BULLISH,
        VERY_BULLISH
    }
    
    /**
     * 解读详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterpretationDetail {
        private String indicatorName;
        private String interpretation;
        private String significance;
        private String implication;
        private BigDecimal weight;
    }
    
    /**
     * 交易策略建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingStrategyRecommendation {
        private StrategyType strategyType;
        private List<EntryCondition> entryConditions;
        private List<ExitCondition> exitConditions;
        private RiskManagement riskManagement;
        private String timeframe;
        private BigDecimal expectedReturn;
        private BigDecimal riskLevel;
        private String description;
    }
    
    /**
     * 策略类型
     */
    public enum StrategyType {
        TREND_FOLLOWING,
        MEAN_REVERSION,
        MOMENTUM,
        BREAKOUT,
        SCALPING,
        SWING_TRADING,
        POSITION_TRADING
    }
    
    /**
     * 入场条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EntryCondition {
        private String condition;
        private String indicator;
        private BigDecimal threshold;
        private String operator;
        private String description;
    }
    
    /**
     * 出场条件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExitCondition {
        private String condition;
        private String indicator;
        private BigDecimal threshold;
        private String operator;
        private String description;
    }
    
    /**
     * 风险管理
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskManagement {
        private BigDecimal stopLoss;
        private BigDecimal takeProfit;
        private BigDecimal positionSize;
        private BigDecimal riskRewardRatio;
        private String riskLevel;
        private List<String> riskMitigationMeasures;
    }
    
    /**
     * 技术指标预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalIndicatorForecast {
        private List<ForecastPeriod> forecastPeriods;
        private String overallTrend;
        private BigDecimal confidence;
        private List<String> keyFactors;
        private String methodology;
        private String disclaimer;
    }
    
    /**
     * 预测周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastPeriod {
        private String period;
        private List<ForecastValue> forecastValues;
        private ForecastConfidence confidence;
        private String scenario;
        private List<String> assumptions;
    }
    
    /**
     * 预测值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastValue {
        private String indicatorName;
        private BigDecimal value;
        private String trend;
        private BigDecimal probability;
        private String description;
    }
    
    /**
     * 预测置信度
     */
    public enum ForecastConfidence {
        VERY_LOW,
        LOW,
        MEDIUM,
        HIGH,
        VERY_HIGH
    }
    
    /**
     * 技术指标风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalIndicatorRiskAssessment {
        private String overallRiskLevel;
        private List<RiskFactor> riskFactors;
        private BigDecimal riskScore;
        private List<RiskMitigation> riskMitigations;
        private String riskWarning;
        private String recommendation;
    }
    
    /**
     * 风险因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        private String factor;
        private String description;
        private String severity;
        private BigDecimal probability;
        private String impact;
        private String category;
    }
    
    /**
     * 风险缓解措施
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskMitigation {
        private String measure;
        private String description;
        private String effectiveness;
        private String implementation;
        private BigDecimal cost;
    }
}