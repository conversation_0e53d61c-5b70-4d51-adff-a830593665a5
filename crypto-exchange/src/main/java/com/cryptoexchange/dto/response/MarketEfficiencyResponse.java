package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场效率响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketEfficiencyResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析周期
     */
    private String period;

    /**
     * 市场效率指数 (0-1之间，越接近1表示市场越有效)
     */
    private BigDecimal efficiencyIndex;

    /**
     * 价格发现效率
     */
    private BigDecimal priceDiscoveryEfficiency;

    /**
     * 信息传播速度
     */
    private BigDecimal informationSpreadSpeed;

    /**
     * 套利机会频率
     */
    private BigDecimal arbitrageOpportunityFrequency;

    /**
     * 市场深度指标
     */
    private MarketDepthMetrics marketDepth;

    /**
     * 流动性指标
     */
    private LiquidityMetrics liquidity;

    /**
     * 波动性指标
     */
    private VolatilityMetrics volatility;

    /**
     * 分析时间范围
     */
    private TimeRange timeRange;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 市场效率等级
     */
    private EfficiencyLevel efficiencyLevel;

    /**
     * 详细分析结果
     */
    private List<EfficiencyAnalysis> detailedAnalysis;

    /**
     * 市场深度指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketDepthMetrics {
        /**
         * 买卖价差
         */
        private BigDecimal bidAskSpread;

        /**
         * 订单簿深度
         */
        private BigDecimal orderBookDepth;

        /**
         * 市场冲击成本
         */
        private BigDecimal marketImpactCost;

        /**
         * 深度不平衡度
         */
        private BigDecimal depthImbalance;
    }

    /**
     * 流动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityMetrics {
        /**
         * 交易量
         */
        private BigDecimal volume;

        /**
         * 换手率
         */
        private BigDecimal turnoverRate;

        /**
         * 流动性比率
         */
        private BigDecimal liquidityRatio;

        /**
         * 平均交易规模
         */
        private BigDecimal averageTradeSize;

        /**
         * 交易频率
         */
        private BigDecimal tradeFrequency;
    }

    /**
     * 波动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityMetrics {
        /**
         * 历史波动率
         */
        private BigDecimal historicalVolatility;

        /**
         * 实现波动率
         */
        private BigDecimal realizedVolatility;

        /**
         * 波动率聚集性
         */
        private BigDecimal volatilityClustering;

        /**
         * 价格跳跃频率
         */
        private BigDecimal priceJumpFrequency;
    }

    /**
     * 时间范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeRange {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 数据点数量
         */
        private Integer dataPoints;
    }

    /**
     * 效率分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EfficiencyAnalysis {
        /**
         * 分析类型
         */
        private String analysisType;

        /**
         * 分析结果
         */
        private BigDecimal result;

        /**
         * 置信度
         */
        private BigDecimal confidence;

        /**
         * 描述
         */
        private String description;

        /**
         * 建议
         */
        private String recommendation;
    }

    /**
     * 市场效率等级
     */
    public enum EfficiencyLevel {
        /**
         * 高效
         */
        HIGH("高效", "市场价格能够快速反映所有可用信息"),

        /**
         * 中等效率
         */
        MEDIUM("中等效率", "市场价格能够较好地反映大部分信息"),

        /**
         * 低效
         */
        LOW("低效", "市场价格反映信息存在明显滞后"),

        /**
         * 极低效率
         */
        VERY_LOW("极低效率", "市场价格严重偏离基本面价值");

        private final String description;
        private final String detail;

        EfficiencyLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 根据效率指数获取效率等级
     */
    public static EfficiencyLevel getEfficiencyLevel(BigDecimal efficiencyIndex) {
        if (efficiencyIndex.compareTo(BigDecimal.valueOf(0.8)) >= 0) {
            return EfficiencyLevel.HIGH;
        } else if (efficiencyIndex.compareTo(BigDecimal.valueOf(0.6)) >= 0) {
            return EfficiencyLevel.MEDIUM;
        } else if (efficiencyIndex.compareTo(BigDecimal.valueOf(0.3)) >= 0) {
            return EfficiencyLevel.LOW;
        } else {
            return EfficiencyLevel.VERY_LOW;
        }
    }

    /**
     * 获取市场效率评估摘要
     */
    public String getEfficiencySummary() {
        if (efficiencyLevel == null) {
            return "效率等级未确定";
        }
        
        return String.format("市场效率等级: %s (指数: %s) - %s", 
            efficiencyLevel.getDescription(), 
            efficiencyIndex != null ? efficiencyIndex.toString() : "N/A",
            efficiencyLevel.getDetail());
    }
}