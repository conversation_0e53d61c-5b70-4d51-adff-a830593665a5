package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易对搜索响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易对搜索响应")
public class SymbolSearchResponse {

    @Schema(description = "搜索关键词")
    private String keyword;

    @Schema(description = "搜索类型")
    private String searchType;

    @Schema(description = "总结果数量")
    private Integer totalCount;

    @Schema(description = "当前页码")
    private Integer currentPage;

    @Schema(description = "每页大小")
    private Integer pageSize;

    @Schema(description = "总页数")
    private Integer totalPages;

    @Schema(description = "搜索结果列表")
    private List<SearchResult> results;

    @Schema(description = "热门搜索建议")
    private List<String> hotSuggestions;

    @Schema(description = "相关搜索建议")
    private List<String> relatedSuggestions;

    @Schema(description = "搜索用时(毫秒)")
    private Long searchTime;

    @Schema(description = "搜索时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime searchDateTime;

    /**
     * 搜索结果
     */
    @Data
    @Schema(description = "搜索结果")
    public static class SearchResult {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "交易对名称")
        private String symbolName;
        
        @Schema(description = "基础货币代码")
        private String baseAsset;
        
        @Schema(description = "基础货币名称")
        private String baseAssetName;
        
        @Schema(description = "计价货币代码")
        private String quoteAsset;
        
        @Schema(description = "计价货币名称")
        private String quoteAssetName;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时涨跌幅")
        private BigDecimal changePercent24h;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount24h;
        
        @Schema(description = "24小时最高价")
        private BigDecimal high24h;
        
        @Schema(description = "24小时最低价")
        private BigDecimal low24h;
        
        @Schema(description = "24小时开盘价")
        private BigDecimal open24h;
        
        @Schema(description = "市值")
        private BigDecimal marketCap;
        
        @Schema(description = "市值排名")
        private Integer marketCapRank;
        
        @Schema(description = "流通市值")
        private BigDecimal circulatingMarketCap;
        
        @Schema(description = "交易状态")
        private String status;
        
        @Schema(description = "是否支持交易")
        private Boolean tradingEnabled;
        
        @Schema(description = "是否支持充值")
        private Boolean depositEnabled;
        
        @Schema(description = "是否支持提现")
        private Boolean withdrawEnabled;
        
        @Schema(description = "交易对类型")
        private String symbolType;
        
        @Schema(description = "所属分类")
        private String category;
        
        @Schema(description = "分类名称")
        private String categoryName;
        
        @Schema(description = "是否为新币")
        private Boolean isNewListing;
        
        @Schema(description = "是否为热门")
        private Boolean isHot;
        
        @Schema(description = "是否为推荐")
        private Boolean isRecommended;
        
        @Schema(description = "上线时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime listingTime;
        
        @Schema(description = "价格精度")
        private Integer pricePrecision;
        
        @Schema(description = "数量精度")
        private Integer quantityPrecision;
        
        @Schema(description = "最小交易量")
        private BigDecimal minTradeQuantity;
        
        @Schema(description = "最大交易量")
        private BigDecimal maxTradeQuantity;
        
        @Schema(description = "最小交易额")
        private BigDecimal minTradeAmount;
        
        @Schema(description = "最大交易额")
        private BigDecimal maxTradeAmount;
        
        @Schema(description = "手续费率")
        private BigDecimal feeRate;
        
        @Schema(description = "流动性评分")
        private BigDecimal liquidityScore;
        
        @Schema(description = "波动率")
        private BigDecimal volatility;
        
        @Schema(description = "热度指数")
        private BigDecimal popularityIndex;
        
        @Schema(description = "风险等级")
        private String riskLevel;
        
        @Schema(description = "推荐等级")
        private String recommendationLevel;
        
        @Schema(description = "匹配度评分")
        private BigDecimal matchScore;
        
        @Schema(description = "匹配字段")
        private List<String> matchedFields;
        
        @Schema(description = "高亮关键词")
        private List<String> highlightKeywords;
        
        @Schema(description = "交易对图标")
        private String iconUrl;
        
        @Schema(description = "项目官网")
        private String officialWebsite;
        
        @Schema(description = "白皮书链接")
        private String whitePaperUrl;
        
        @Schema(description = "区块链浏览器")
        private String explorerUrl;
        
        @Schema(description = "社交媒体链接")
        private List<SocialMediaLink> socialMediaLinks;
        
        @Schema(description = "标签")
        private List<String> tags;
        
        @Schema(description = "备注")
        private String remarks;
    }

    /**
     * 社交媒体链接
     */
    @Data
    @Schema(description = "社交媒体链接")
    public static class SocialMediaLink {
        
        @Schema(description = "平台名称")
        private String platform;
        
        @Schema(description = "链接地址")
        private String url;
        
        @Schema(description = "关注者数量")
        private Integer followersCount;
    }
}