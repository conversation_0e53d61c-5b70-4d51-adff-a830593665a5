package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 充值地址响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DepositAddressResponse {
    
    /**
     * 地址ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 充值地址
     */
    private String address;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 网络名称
     */
    private String networkName;
    
    /**
     * 标签/备忘录
     */
    private String tag;
    
    /**
     * 地址类型 (LEGACY, SEGWIT, NATIVE_SEGWIT)
     */
    private String addressType;
    
    /**
     * 地址状态 (ACTIVE, INACTIVE, DISABLED)
     */
    private String status;
    
    /**
     * 是否为默认地址
     */
    private Boolean isDefault;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 最后使用时间
     */
    private LocalDateTime lastUsedTime;
    
    /**
     * 使用次数
     */
    private Integer usageCount;
    
    /**
     * 二维码数据
     */
    private String qrCodeData;
    
    /**
     * 地址别名
     */
    private String alias;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 支持的网络列表
     */
    private List<NetworkInfo> supportedNetworks;
    
    /**
     * 网络信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class NetworkInfo {
        /**
         * 网络代码
         */
        private String network;
        
        /**
         * 网络名称
         */
        private String networkName;
        
        /**
         * 网络状态
         */
        private String status;
        
        /**
         * 最小充值金额
         */
        private String minDeposit;
        
        /**
         * 确认数要求
         */
        private Integer confirmations;
        
        /**
         * 是否需要标签
         */
        private Boolean requiresTag;
        
        /**
         * 网络手续费
         */
        private String networkFee;
        
        /**
         * 是否支持充值
         */
        private Boolean depositEnabled;
        
        /**
         * 是否支持提现
         */
        private Boolean withdrawEnabled;
    }
}