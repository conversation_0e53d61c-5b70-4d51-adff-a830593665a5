package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 相关性分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorrelationAnalysisResponse {

    /**
     * 基准交易对符号
     */
    private String baseSymbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析时间范围
     */
    private String analysisTimeRange;

    /**
     * 相关性分析结果
     */
    private List<CorrelationResult> correlationResults;

    /**
     * 相关性矩阵
     */
    private CorrelationMatrix correlationMatrix;

    /**
     * 动态相关性分析
     */
    private DynamicCorrelationAnalysis dynamicCorrelationAnalysis;

    /**
     * 相关性统计
     */
    private CorrelationStatistics correlationStatistics;

    /**
     * 相关性分组
     */
    private CorrelationGrouping correlationGrouping;

    /**
     * 相关性预测
     */
    private CorrelationForecast correlationForecast;

    /**
     * 风险分散分析
     */
    private DiversificationAnalysis diversificationAnalysis;

    /**
     * 投资组合建议
     */
    private PortfolioRecommendation portfolioRecommendation;

    /**
     * 相关性警告
     */
    private List<CorrelationWarning> correlationWarnings;

    /**
     * 市场状态影响
     */
    private MarketStateImpact marketStateImpact;

    /**
     * 相关性结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationResult {
        /**
         * 目标交易对符号
         */
        private String targetSymbol;

        /**
         * 皮尔逊相关系数
         */
        private BigDecimal pearsonCorrelation;

        /**
         * 斯皮尔曼相关系数
         */
        private BigDecimal spearmanCorrelation;

        /**
         * 肯德尔相关系数
         */
        private BigDecimal kendallCorrelation;

        /**
         * 相关性强度
         */
        private CorrelationStrength correlationStrength;

        /**
         * 相关性方向
         */
        private CorrelationDirection correlationDirection;

        /**
         * 相关性稳定性
         */
        private BigDecimal correlationStability;

        /**
         * 统计显著性
         */
        private BigDecimal statisticalSignificance;

        /**
         * P值
         */
        private BigDecimal pValue;

        /**
         * 置信区间
         */
        private ConfidenceInterval confidenceInterval;

        /**
         * 滞后相关性
         */
        private List<LaggedCorrelation> laggedCorrelations;

        /**
         * 相关性变化趋势
         */
        private String correlationTrend;
    }

    /**
     * 相关性强度
     */
    public enum CorrelationStrength {
        VERY_STRONG("非常强", 0.8, 1.0),
        STRONG("强", 0.6, 0.8),
        MODERATE("中等", 0.4, 0.6),
        WEAK("弱", 0.2, 0.4),
        VERY_WEAK("非常弱", 0.0, 0.2);

        private final String description;
        private final double minValue;
        private final double maxValue;

        CorrelationStrength(String description, double minValue, double maxValue) {
            this.description = description;
            this.minValue = minValue;
            this.maxValue = maxValue;
        }

        public String getDescription() {
            return description;
        }

        public double getMinValue() {
            return minValue;
        }

        public double getMaxValue() {
            return maxValue;
        }

        public static CorrelationStrength fromValue(double value) {
            double absValue = Math.abs(value);
            for (CorrelationStrength strength : values()) {
                if (absValue >= strength.minValue && absValue < strength.maxValue) {
                    return strength;
                }
            }
            return VERY_WEAK;
        }
    }

    /**
     * 相关性方向
     */
    public enum CorrelationDirection {
        POSITIVE("正相关", "同向变动"),
        NEGATIVE("负相关", "反向变动"),
        NEUTRAL("无相关", "无明显关系");

        private final String description;
        private final String detail;

        CorrelationDirection(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }

        public static CorrelationDirection fromValue(double value) {
            if (value > 0.1) {
                return POSITIVE;
            } else if (value < -0.1) {
                return NEGATIVE;
            } else {
                return NEUTRAL;
            }
        }
    }

    /**
     * 置信区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfidenceInterval {
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;

        /**
         * 下界
         */
        private BigDecimal lowerBound;

        /**
         * 上界
         */
        private BigDecimal upperBound;

        /**
         * 区间宽度
         */
        private BigDecimal intervalWidth;
    }

    /**
     * 滞后相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LaggedCorrelation {
        /**
         * 滞后期数
         */
        private Integer lagPeriods;

        /**
         * 滞后相关系数
         */
        private BigDecimal laggedCorrelation;

        /**
         * 最大相关性滞后
         */
        private boolean isMaxCorrelationLag;

        /**
         * 统计显著性
         */
        private BigDecimal significance;
    }

    /**
     * 相关性矩阵
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationMatrix {
        /**
         * 交易对列表
         */
        private List<String> symbols;

        /**
         * 相关性矩阵数据
         */
        private Map<String, Map<String, BigDecimal>> matrixData;

        /**
         * 矩阵特征值
         */
        private List<BigDecimal> eigenvalues;

        /**
         * 矩阵条件数
         */
        private BigDecimal conditionNumber;

        /**
         * 矩阵行列式
         */
        private BigDecimal determinant;

        /**
         * 主成分分析
         */
        private PrincipalComponentAnalysis principalComponentAnalysis;
    }

    /**
     * 主成分分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrincipalComponentAnalysis {
        /**
         * 主成分
         */
        private List<PrincipalComponent> principalComponents;

        /**
         * 累计方差解释比例
         */
        private List<BigDecimal> cumulativeVarianceExplained;

        /**
         * 建议主成分数量
         */
        private Integer recommendedComponents;

        /**
         * 降维效果
         */
        private BigDecimal dimensionReductionEffectiveness;
    }

    /**
     * 主成分
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrincipalComponent {
        /**
         * 成分编号
         */
        private Integer componentNumber;

        /**
         * 特征值
         */
        private BigDecimal eigenvalue;

        /**
         * 方差解释比例
         */
        private BigDecimal varianceExplained;

        /**
         * 成分载荷
         */
        private Map<String, BigDecimal> componentLoadings;

        /**
         * 成分解释
         */
        private String componentInterpretation;
    }

    /**
     * 动态相关性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DynamicCorrelationAnalysis {
        /**
         * 滚动窗口相关性
         */
        private List<RollingCorrelation> rollingCorrelations;

        /**
         * 相关性变化点
         */
        private List<CorrelationChangePoint> correlationChangePoints;

        /**
         * 相关性波动性
         */
        private BigDecimal correlationVolatility;

        /**
         * 相关性趋势
         */
        private String correlationTrend;

        /**
         * 相关性周期性
         */
        private CorrelationCyclicality correlationCyclicality;

        /**
         * 结构性变化检测
         */
        private List<StructuralBreak> structuralBreaks;
    }

    /**
     * 滚动相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RollingCorrelation {
        /**
         * 时间点
         */
        private LocalDateTime timestamp;

        /**
         * 相关系数
         */
        private BigDecimal correlation;

        /**
         * 窗口大小
         */
        private Integer windowSize;

        /**
         * 置信区间
         */
        private ConfidenceInterval confidenceInterval;
    }

    /**
     * 相关性变化点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationChangePoint {
        /**
         * 变化时间
         */
        private LocalDateTime changeTime;

        /**
         * 变化前相关性
         */
        private BigDecimal beforeCorrelation;

        /**
         * 变化后相关性
         */
        private BigDecimal afterCorrelation;

        /**
         * 变化幅度
         */
        private BigDecimal changeMagnitude;

        /**
         * 变化显著性
         */
        private BigDecimal changeSignificance;

        /**
         * 变化原因
         */
        private String changeCause;
    }

    /**
     * 相关性周期性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationCyclicality {
        /**
         * 是否存在周期性
         */
        private boolean hasCyclicality;

        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 周期相位
         */
        private BigDecimal cyclePhase;

        /**
         * 周期预测
         */
        private List<BigDecimal> cycleForecast;
    }

    /**
     * 结构性变化
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StructuralBreak {
        /**
         * 变化时间
         */
        private LocalDateTime breakTime;

        /**
         * 变化类型
         */
        private String breakType;

        /**
         * 变化显著性
         */
        private BigDecimal breakSignificance;

        /**
         * 影响持续时间
         */
        private Integer impactDuration;

        /**
         * 变化描述
         */
        private String breakDescription;
    }

    /**
     * 相关性统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationStatistics {
        /**
         * 平均相关性
         */
        private BigDecimal averageCorrelation;

        /**
         * 相关性中位数
         */
        private BigDecimal medianCorrelation;

        /**
         * 相关性标准差
         */
        private BigDecimal correlationStandardDeviation;

        /**
         * 最大相关性
         */
        private BigDecimal maxCorrelation;

        /**
         * 最小相关性
         */
        private BigDecimal minCorrelation;

        /**
         * 正相关数量
         */
        private Integer positiveCorrelationCount;

        /**
         * 负相关数量
         */
        private Integer negativeCorrelationCount;

        /**
         * 强相关数量
         */
        private Integer strongCorrelationCount;

        /**
         * 相关性分布
         */
        private Map<String, Integer> correlationDistribution;
    }

    /**
     * 相关性分组
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationGrouping {
        /**
         * 高相关组
         */
        private List<CorrelationGroup> highCorrelationGroups;

        /**
         * 低相关组
         */
        private List<CorrelationGroup> lowCorrelationGroups;

        /**
         * 聚类分析结果
         */
        private ClusterAnalysisResult clusterAnalysisResult;

        /**
         * 分组建议
         */
        private List<String> groupingRecommendations;
    }

    /**
     * 相关性组
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationGroup {
        /**
         * 组名
         */
        private String groupName;

        /**
         * 组内交易对
         */
        private List<String> groupSymbols;

        /**
         * 组内平均相关性
         */
        private BigDecimal averageIntraGroupCorrelation;

        /**
         * 组间相关性
         */
        private BigDecimal interGroupCorrelation;

        /**
         * 组的稳定性
         */
        private BigDecimal groupStability;

        /**
         * 组的特征
         */
        private String groupCharacteristics;
    }

    /**
     * 聚类分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClusterAnalysisResult {
        /**
         * 最优聚类数
         */
        private Integer optimalClusterCount;

        /**
         * 聚类质量评分
         */
        private BigDecimal clusterQualityScore;

        /**
         * 轮廓系数
         */
        private BigDecimal silhouetteScore;

        /**
         * 聚类中心
         */
        private List<ClusterCenter> clusterCenters;

        /**
         * 聚类分配
         */
        private Map<String, Integer> clusterAssignments;
    }

    /**
     * 聚类中心
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClusterCenter {
        /**
         * 聚类编号
         */
        private Integer clusterNumber;

        /**
         * 中心坐标
         */
        private List<BigDecimal> centerCoordinates;

        /**
         * 聚类大小
         */
        private Integer clusterSize;

        /**
         * 聚类内方差
         */
        private BigDecimal withinClusterVariance;

        /**
         * 聚类描述
         */
        private String clusterDescription;
    }

    /**
     * 相关性预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationForecast {
        /**
         * 短期预测
         */
        private List<CorrelationPrediction> shortTermForecast;

        /**
         * 中期预测
         */
        private List<CorrelationPrediction> mediumTermForecast;

        /**
         * 长期预测
         */
        private List<CorrelationPrediction> longTermForecast;

        /**
         * 预测模型
         */
        private String forecastModel;

        /**
         * 预测准确性
         */
        private BigDecimal forecastAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;
    }

    /**
     * 相关性预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationPrediction {
        /**
         * 预测时间
         */
        private LocalDateTime forecastTime;

        /**
         * 预测相关性
         */
        private BigDecimal predictedCorrelation;

        /**
         * 预测区间
         */
        private ConfidenceInterval predictionInterval;

        /**
         * 预测变化方向
         */
        private String changeDirection;

        /**
         * 预测可靠性
         */
        private BigDecimal predictionReliability;
    }

    /**
     * 风险分散分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiversificationAnalysis {
        /**
         * 分散化效果
         */
        private BigDecimal diversificationEffectiveness;

        /**
         * 分散化比率
         */
        private BigDecimal diversificationRatio;

        /**
         * 有效资产数量
         */
        private BigDecimal effectiveAssetCount;

        /**
         * 集中度风险
         */
        private BigDecimal concentrationRisk;

        /**
         * 分散化建议
         */
        private List<String> diversificationRecommendations;

        /**
         * 最优权重分配
         */
        private Map<String, BigDecimal> optimalWeights;
    }

    /**
     * 投资组合建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PortfolioRecommendation {
        /**
         * 推荐资产组合
         */
        private List<String> recommendedAssets;

        /**
         * 权重建议
         */
        private Map<String, BigDecimal> weightRecommendations;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 预期风险
         */
        private BigDecimal expectedRisk;

        /**
         * 夏普比率
         */
        private BigDecimal sharpeRatio;

        /**
         * 再平衡频率
         */
        private String rebalancingFrequency;

        /**
         * 组合策略
         */
        private String portfolioStrategy;
    }

    /**
     * 相关性警告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationWarning {
        /**
         * 警告类型
         */
        private WarningType warningType;

        /**
         * 警告级别
         */
        private WarningLevel warningLevel;

        /**
         * 警告消息
         */
        private String warningMessage;

        /**
         * 涉及交易对
         */
        private List<String> affectedSymbols;

        /**
         * 警告时间
         */
        private LocalDateTime warningTime;

        /**
         * 建议行动
         */
        private List<String> recommendedActions;
    }

    /**
     * 警告类型
     */
    public enum WarningType {
        HIGH_CORRELATION("高相关性警告"),
        CORRELATION_BREAKDOWN("相关性失效警告"),
        STRUCTURAL_CHANGE("结构性变化警告"),
        CONCENTRATION_RISK("集中度风险警告"),
        DIVERSIFICATION_FAILURE("分散化失效警告");

        private final String description;

        WarningType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 警告级别
     */
    public enum WarningLevel {
        LOW("低", 1),
        MEDIUM("中", 2),
        HIGH("高", 3),
        CRITICAL("严重", 4);

        private final String description;
        private final int level;

        WarningLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 市场状态影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketStateImpact {
        /**
         * 牛市相关性
         */
        private BigDecimal bullMarketCorrelation;

        /**
         * 熊市相关性
         */
        private BigDecimal bearMarketCorrelation;

        /**
         * 震荡市相关性
         */
        private BigDecimal sidewaysMarketCorrelation;

        /**
         * 危机期相关性
         */
        private BigDecimal crisisCorrelation;

        /**
         * 当前市场状态
         */
        private String currentMarketState;

        /**
         * 状态转换影响
         */
        private Map<String, BigDecimal> stateTransitionImpact;

        /**
         * 市场情绪影响
         */
        private BigDecimal marketSentimentImpact;
    }

    /**
     * 获取相关性摘要
     */
    public String getCorrelationSummary() {
        if (correlationStatistics == null) {
            return "相关性统计不可用";
        }
        
        BigDecimal avgCorr = correlationStatistics.getAverageCorrelation();
        Integer strongCount = correlationStatistics.getStrongCorrelationCount();
        
        return String.format("平均相关性: %s, 强相关数量: %d",
            avgCorr != null ? avgCorr.toString() : "N/A",
            strongCount != null ? strongCount : 0);
    }

    /**
     * 检查是否有高风险相关性
     */
    public boolean hasHighRiskCorrelations() {
        if (correlationWarnings == null) {
            return false;
        }
        
        return correlationWarnings.stream()
            .anyMatch(warning -> warning.getWarningLevel() == WarningLevel.HIGH ||
                               warning.getWarningLevel() == WarningLevel.CRITICAL);
    }

    /**
     * 获取分散化建议
     */
    public String getDiversificationAdvice() {
        if (diversificationAnalysis == null || 
            diversificationAnalysis.getDiversificationRecommendations() == null) {
            return "暂无分散化建议";
        }
        
        List<String> recommendations = diversificationAnalysis.getDiversificationRecommendations();
        if (recommendations.isEmpty()) {
            return "当前分散化良好";
        }
        
        return String.join("; ", recommendations);
    }

    /**
     * 获取投资组合建议摘要
     */
    public String getPortfolioAdviceSummary() {
        if (portfolioRecommendation == null) {
            return "暂无投资组合建议";
        }
        
        BigDecimal expectedReturn = portfolioRecommendation.getExpectedReturn();
        BigDecimal expectedRisk = portfolioRecommendation.getExpectedRisk();
        BigDecimal sharpeRatio = portfolioRecommendation.getSharpeRatio();
        
        return String.format("预期收益: %s%%, 预期风险: %s%%, 夏普比率: %s",
            expectedReturn != null ? expectedReturn.toString() : "N/A",
            expectedRisk != null ? expectedRisk.toString() : "N/A",
            sharpeRatio != null ? sharpeRatio.toString() : "N/A");
    }

    /**
     * 获取最强相关性
     */
    public CorrelationResult getStrongestCorrelation() {
        if (correlationResults == null || correlationResults.isEmpty()) {
            return null;
        }
        
        return correlationResults.stream()
            .max((r1, r2) -> {
                BigDecimal corr1 = r1.getPearsonCorrelation();
                BigDecimal corr2 = r2.getPearsonCorrelation();
                if (corr1 == null && corr2 == null) return 0;
                if (corr1 == null) return -1;
                if (corr2 == null) return 1;
                return corr1.abs().compareTo(corr2.abs());
            })
            .orElse(null);
    }

    /**
     * 获取相关性变化趋势
     */
    public String getCorrelationChangeTrend() {
        if (dynamicCorrelationAnalysis == null) {
            return "趋势分析不可用";
        }
        
        String trend = dynamicCorrelationAnalysis.getCorrelationTrend();
        return trend != null ? trend : "趋势不明确";
    }

    /**
     * 检查是否需要重新平衡
     */
    public boolean needsRebalancing() {
        if (portfolioRecommendation == null) {
            return false;
        }
        
        // 检查是否有高风险相关性或分散化效果不佳
        boolean hasHighRisk = hasHighRiskCorrelations();
        
        boolean poorDiversification = false;
        if (diversificationAnalysis != null && 
            diversificationAnalysis.getDiversificationEffectiveness() != null) {
            poorDiversification = diversificationAnalysis.getDiversificationEffectiveness()
                .compareTo(new BigDecimal("0.6")) < 0;
        }
        
        return hasHighRisk || poorDiversification;
    }

    /**
     * 获取关键警告信息
     */
    public List<String> getCriticalWarnings() {
        if (correlationWarnings == null) {
            return List.of();
        }
        
        return correlationWarnings.stream()
            .filter(warning -> warning.getWarningLevel() == WarningLevel.CRITICAL)
            .map(CorrelationWarning::getWarningMessage)
            .toList();
    }

    /**
     * 获取相关性稳定性评估
     */
    public String getCorrelationStabilityAssessment() {
        if (correlationResults == null || correlationResults.isEmpty()) {
            return "稳定性评估不可用";
        }
        
        double avgStability = correlationResults.stream()
            .filter(result -> result.getCorrelationStability() != null)
            .mapToDouble(result -> result.getCorrelationStability().doubleValue())
            .average()
            .orElse(0.0);
        
        if (avgStability >= 0.8) {
            return "相关性高度稳定";
        } else if (avgStability >= 0.6) {
            return "相关性中等稳定";
        } else if (avgStability >= 0.4) {
            return "相关性较不稳定";
        } else {
            return "相关性极不稳定";
        }
    }
}