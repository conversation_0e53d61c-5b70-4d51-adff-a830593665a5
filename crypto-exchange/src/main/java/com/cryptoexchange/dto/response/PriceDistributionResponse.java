package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格分布分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceDistributionResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析时间范围
     */
    private String analysisTimeRange;

    /**
     * 价格分布类型
     */
    private PriceDistributionType distributionType;

    /**
     * 分布统计信息
     */
    private DistributionStatistics distributionStatistics;

    /**
     * 价格区间分析
     */
    private List<PriceRangeAnalysis> priceRangeAnalyses;

    /**
     * 分布形状分析
     */
    private DistributionShapeAnalysis distributionShapeAnalysis;

    /**
     * 异常值检测
     */
    private OutlierDetection outlierDetection;

    /**
     * 分布拟合分析
     */
    private DistributionFittingAnalysis distributionFittingAnalysis;

    /**
     * 价格密度分析
     */
    private PriceDensityAnalysis priceDensityAnalysis;

    /**
     * 分布比较分析
     */
    private DistributionComparativeAnalysis distributionComparativeAnalysis;

    /**
     * 分布预测
     */
    private DistributionForecast distributionForecast;

    /**
     * 交易建议
     */
    private DistributionTradingAdvice distributionTradingAdvice;

    /**
     * 风险评估
     */
    private DistributionRiskAssessment distributionRiskAssessment;

    /**
     * 价格分布类型
     */
    public enum PriceDistributionType {
        NORMAL("正态分布", "价格呈正态分布"),
        LOG_NORMAL("对数正态分布", "价格呈对数正态分布"),
        SKEWED_LEFT("左偏分布", "价格分布左偏"),
        SKEWED_RIGHT("右偏分布", "价格分布右偏"),
        BIMODAL("双峰分布", "价格呈双峰分布"),
        MULTIMODAL("多峰分布", "价格呈多峰分布"),
        UNIFORM("均匀分布", "价格呈均匀分布"),
        EXPONENTIAL("指数分布", "价格呈指数分布"),
        HEAVY_TAILED("厚尾分布", "价格分布具有厚尾特征"),
        UNKNOWN("未知分布", "无法确定分布类型");

        private final String description;
        private final String detail;

        PriceDistributionType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 分布统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionStatistics {
        /**
         * 样本数量
         */
        private Integer sampleCount;

        /**
         * 均值
         */
        private BigDecimal mean;

        /**
         * 中位数
         */
        private BigDecimal median;

        /**
         * 众数
         */
        private BigDecimal mode;

        /**
         * 标准差
         */
        private BigDecimal standardDeviation;

        /**
         * 方差
         */
        private BigDecimal variance;

        /**
         * 偏度
         */
        private BigDecimal skewness;

        /**
         * 峰度
         */
        private BigDecimal kurtosis;

        /**
         * 最小值
         */
        private BigDecimal minimum;

        /**
         * 最大值
         */
        private BigDecimal maximum;

        /**
         * 范围
         */
        private BigDecimal range;

        /**
         * 四分位数
         */
        private Quartiles quartiles;

        /**
         * 百分位数
         */
        private Map<Integer, BigDecimal> percentiles;
    }

    /**
     * 四分位数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Quartiles {
        /**
         * 第一四分位数 (Q1)
         */
        private BigDecimal q1;

        /**
         * 第二四分位数 (Q2) - 中位数
         */
        private BigDecimal q2;

        /**
         * 第三四分位数 (Q3)
         */
        private BigDecimal q3;

        /**
         * 四分位距 (IQR)
         */
        private BigDecimal iqr;
    }

    /**
     * 价格区间分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRangeAnalysis {
        /**
         * 价格区间下限
         */
        private BigDecimal lowerBound;

        /**
         * 价格区间上限
         */
        private BigDecimal upperBound;

        /**
         * 区间中点
         */
        private BigDecimal midPoint;

        /**
         * 区间宽度
         */
        private BigDecimal rangeWidth;

        /**
         * 频次
         */
        private Integer frequency;

        /**
         * 频率
         */
        private BigDecimal relativeFrequency;

        /**
         * 累积频率
         */
        private BigDecimal cumulativeFrequency;

        /**
         * 密度
         */
        private BigDecimal density;

        /**
         * 成交量
         */
        private BigDecimal volume;

        /**
         * 成交金额
         */
        private BigDecimal turnover;

        /**
         * 平均停留时间
         */
        private BigDecimal averageDwellTime;

        /**
         * 区间特征
         */
        private List<String> rangeCharacteristics;
    }

    /**
     * 分布形状分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionShapeAnalysis {
        /**
         * 分布形状类型
         */
        private String shapeType;

        /**
         * 对称性分析
         */
        private SymmetryAnalysis symmetryAnalysis;

        /**
         * 尾部分析
         */
        private TailAnalysis tailAnalysis;

        /**
         * 峰值分析
         */
        private PeakAnalysis peakAnalysis;

        /**
         * 形状稳定性
         */
        private BigDecimal shapeStability;

        /**
         * 形状演化趋势
         */
        private String shapeEvolutionTrend;
    }

    /**
     * 对称性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SymmetryAnalysis {
        /**
         * 是否对称
         */
        private boolean isSymmetric;

        /**
         * 对称性程度
         */
        private BigDecimal symmetryDegree;

        /**
         * 偏斜方向
         */
        private String skewDirection;

        /**
         * 偏斜强度
         */
        private BigDecimal skewIntensity;

        /**
         * 对称轴
         */
        private BigDecimal symmetryAxis;
    }

    /**
     * 尾部分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TailAnalysis {
        /**
         * 左尾特征
         */
        private TailCharacteristics leftTail;

        /**
         * 右尾特征
         */
        private TailCharacteristics rightTail;

        /**
         * 尾部风险
         */
        private TailRisk tailRisk;

        /**
         * 极值理论分析
         */
        private ExtremeValueAnalysis extremeValueAnalysis;
    }

    /**
     * 尾部特征
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TailCharacteristics {
        /**
         * 尾部厚度
         */
        private BigDecimal tailThickness;

        /**
         * 尾部长度
         */
        private BigDecimal tailLength;

        /**
         * 尾部概率
         */
        private BigDecimal tailProbability;

        /**
         * 尾部指数
         */
        private BigDecimal tailIndex;

        /**
         * 尾部类型
         */
        private String tailType;
    }

    /**
     * 尾部风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TailRisk {
        /**
         * VaR (风险价值)
         */
        private Map<BigDecimal, BigDecimal> valueAtRisk;

        /**
         * CVaR (条件风险价值)
         */
        private Map<BigDecimal, BigDecimal> conditionalValueAtRisk;

        /**
         * 预期损失
         */
        private BigDecimal expectedShortfall;

        /**
         * 尾部依赖
         */
        private BigDecimal tailDependence;
    }

    /**
     * 极值理论分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtremeValueAnalysis {
        /**
         * 极值分布类型
         */
        private String extremeValueDistribution;

        /**
         * 形状参数
         */
        private BigDecimal shapeParameter;

        /**
         * 尺度参数
         */
        private BigDecimal scaleParameter;

        /**
         * 位置参数
         */
        private BigDecimal locationParameter;

        /**
         * 回归期分析
         */
        private Map<BigDecimal, BigDecimal> returnPeriodAnalysis;
    }

    /**
     * 峰值分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeakAnalysis {
        /**
         * 峰值数量
         */
        private Integer peakCount;

        /**
         * 主峰值
         */
        private Peak primaryPeak;

        /**
         * 次峰值列表
         */
        private List<Peak> secondaryPeaks;

        /**
         * 峰值间距
         */
        private List<BigDecimal> peakDistances;

        /**
         * 峰值稳定性
         */
        private BigDecimal peakStability;
    }

    /**
     * 峰值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Peak {
        /**
         * 峰值位置
         */
        private BigDecimal peakPosition;

        /**
         * 峰值高度
         */
        private BigDecimal peakHeight;

        /**
         * 峰值宽度
         */
        private BigDecimal peakWidth;

        /**
         * 峰值面积
         */
        private BigDecimal peakArea;

        /**
         * 峰值显著性
         */
        private BigDecimal peakSignificance;

        /**
         * 峰值类型
         */
        private String peakType;
    }

    /**
     * 异常值检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutlierDetection {
        /**
         * 异常值列表
         */
        private List<Outlier> outliers;

        /**
         * 异常值比例
         */
        private BigDecimal outlierRatio;

        /**
         * 检测方法
         */
        private List<String> detectionMethods;

        /**
         * 异常值影响评估
         */
        private OutlierImpactAssessment outlierImpactAssessment;

        /**
         * 异常值处理建议
         */
        private List<String> outlierTreatmentRecommendations;
    }

    /**
     * 异常值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Outlier {
        /**
         * 异常值
         */
        private BigDecimal value;

        /**
         * 异常时间
         */
        private LocalDateTime timestamp;

        /**
         * 异常程度
         */
        private BigDecimal outlierDegree;

        /**
         * 异常类型
         */
        private String outlierType;

        /**
         * 异常原因
         */
        private List<String> possibleCauses;

        /**
         * 影响评估
         */
        private BigDecimal impactAssessment;
    }

    /**
     * 异常值影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutlierImpactAssessment {
        /**
         * 对均值的影响
         */
        private BigDecimal impactOnMean;

        /**
         * 对标准差的影响
         */
        private BigDecimal impactOnStandardDeviation;

        /**
         * 对分布形状的影响
         */
        private BigDecimal impactOnDistributionShape;

        /**
         * 对风险度量的影响
         */
        private BigDecimal impactOnRiskMeasures;

        /**
         * 整体影响评分
         */
        private BigDecimal overallImpactScore;
    }

    /**
     * 分布拟合分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionFittingAnalysis {
        /**
         * 拟合结果列表
         */
        private List<DistributionFit> distributionFits;

        /**
         * 最佳拟合分布
         */
        private DistributionFit bestFit;

        /**
         * 拟合质量评估
         */
        private FitQualityAssessment fitQualityAssessment;

        /**
         * 拟合稳定性
         */
        private BigDecimal fitStability;
    }

    /**
     * 分布拟合
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionFit {
        /**
         * 分布名称
         */
        private String distributionName;

        /**
         * 分布参数
         */
        private Map<String, BigDecimal> parameters;

        /**
         * 拟合优度统计
         */
        private GoodnessOfFitStatistics goodnessOfFitStatistics;

        /**
         * 拟合评分
         */
        private BigDecimal fitScore;

        /**
         * 拟合置信度
         */
        private BigDecimal fitConfidence;
    }

    /**
     * 拟合优度统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodnessOfFitStatistics {
        /**
         * 卡方统计量
         */
        private BigDecimal chiSquareStatistic;

        /**
         * 卡方p值
         */
        private BigDecimal chiSquarePValue;

        /**
         * KS统计量
         */
        private BigDecimal ksStatistic;

        /**
         * KS p值
         */
        private BigDecimal ksPValue;

        /**
         * AIC (赤池信息准则)
         */
        private BigDecimal aic;

        /**
         * BIC (贝叶斯信息准则)
         */
        private BigDecimal bic;

        /**
         * 对数似然
         */
        private BigDecimal logLikelihood;
    }

    /**
     * 拟合质量评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FitQualityAssessment {
        /**
         * 整体拟合质量
         */
        private String overallFitQuality;

        /**
         * 拟合可靠性
         */
        private BigDecimal fitReliability;

        /**
         * 预测准确性
         */
        private BigDecimal predictiveAccuracy;

        /**
         * 残差分析
         */
        private ResidualAnalysis residualAnalysis;
    }

    /**
     * 残差分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResidualAnalysis {
        /**
         * 残差均值
         */
        private BigDecimal residualMean;

        /**
         * 残差标准差
         */
        private BigDecimal residualStandardDeviation;

        /**
         * 残差偏度
         */
        private BigDecimal residualSkewness;

        /**
         * 残差峰度
         */
        private BigDecimal residualKurtosis;

        /**
         * 残差正态性检验
         */
        private NormalityTest residualNormalityTest;
    }

    /**
     * 正态性检验
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NormalityTest {
        /**
         * Shapiro-Wilk统计量
         */
        private BigDecimal shapiroWilkStatistic;

        /**
         * Shapiro-Wilk p值
         */
        private BigDecimal shapiroWilkPValue;

        /**
         * Jarque-Bera统计量
         */
        private BigDecimal jarqueBeraStatistic;

        /**
         * Jarque-Bera p值
         */
        private BigDecimal jarqueBeraPValue;

        /**
         * 正态性结论
         */
        private String normalityConclusion;
    }

    /**
     * 价格密度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceDensityAnalysis {
        /**
         * 密度函数类型
         */
        private String densityFunctionType;

        /**
         * 密度估计方法
         */
        private String densityEstimationMethod;

        /**
         * 密度峰值
         */
        private List<DensityPeak> densityPeaks;

        /**
         * 密度谷值
         */
        private List<DensityValley> densityValleys;

        /**
         * 密度梯度分析
         */
        private DensityGradientAnalysis densityGradientAnalysis;

        /**
         * 密度聚类分析
         */
        private DensityClusterAnalysis densityClusterAnalysis;
    }

    /**
     * 密度峰值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DensityPeak {
        /**
         * 峰值价格
         */
        private BigDecimal peakPrice;

        /**
         * 峰值密度
         */
        private BigDecimal peakDensity;

        /**
         * 峰值宽度
         */
        private BigDecimal peakWidth;

        /**
         * 峰值显著性
         */
        private BigDecimal peakSignificance;

        /**
         * 峰值稳定性
         */
        private BigDecimal peakStability;
    }

    /**
     * 密度谷值
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DensityValley {
        /**
         * 谷值价格
         */
        private BigDecimal valleyPrice;

        /**
         * 谷值密度
         */
        private BigDecimal valleyDensity;

        /**
         * 谷值宽度
         */
        private BigDecimal valleyWidth;

        /**
         * 谷值深度
         */
        private BigDecimal valleyDepth;
    }

    /**
     * 密度梯度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DensityGradientAnalysis {
        /**
         * 最大梯度位置
         */
        private BigDecimal maxGradientPosition;

        /**
         * 最大梯度值
         */
        private BigDecimal maxGradientValue;

        /**
         * 梯度变化率
         */
        private BigDecimal gradientChangeRate;

        /**
         * 梯度方向
         */
        private String gradientDirection;
    }

    /**
     * 密度聚类分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DensityClusterAnalysis {
        /**
         * 聚类数量
         */
        private Integer clusterCount;

        /**
         * 聚类列表
         */
        private List<DensityCluster> densityClusters;

        /**
         * 聚类质量
         */
        private BigDecimal clusterQuality;

        /**
         * 聚类稳定性
         */
        private BigDecimal clusterStability;
    }

    /**
     * 密度聚类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DensityCluster {
        /**
         * 聚类中心
         */
        private BigDecimal clusterCenter;

        /**
         * 聚类半径
         */
        private BigDecimal clusterRadius;

        /**
         * 聚类密度
         */
        private BigDecimal clusterDensity;

        /**
         * 聚类大小
         */
        private Integer clusterSize;

        /**
         * 聚类权重
         */
        private BigDecimal clusterWeight;
    }

    /**
     * 分布比较分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionComparativeAnalysis {
        /**
         * 历史分布比较
         */
        private HistoricalDistributionComparison historicalDistributionComparison;

        /**
         * 跨时段分布比较
         */
        private CrossPeriodDistributionComparison crossPeriodDistributionComparison;

        /**
         * 市场状态分布比较
         */
        private MarketStateDistributionComparison marketStateDistributionComparison;

        /**
         * 分布演化分析
         */
        private DistributionEvolutionAnalysis distributionEvolutionAnalysis;
    }

    /**
     * 历史分布比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalDistributionComparison {
        /**
         * 比较时间段
         */
        private List<String> comparisonPeriods;

        /**
         * 分布相似性
         */
        private Map<String, BigDecimal> distributionSimilarity;

        /**
         * 分布差异分析
         */
        private Map<String, DistributionDifference> distributionDifferences;

        /**
         * 分布稳定性评估
         */
        private BigDecimal distributionStability;
    }

    /**
     * 分布差异
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionDifference {
        /**
         * 均值差异
         */
        private BigDecimal meanDifference;

        /**
         * 方差差异
         */
        private BigDecimal varianceDifference;

        /**
         * 偏度差异
         */
        private BigDecimal skewnessDifference;

        /**
         * 峰度差异
         */
        private BigDecimal kurtosisDifference;

        /**
         * 形状差异
         */
        private BigDecimal shapeDifference;

        /**
         * 整体差异评分
         */
        private BigDecimal overallDifferenceScore;
    }

    /**
     * 跨时段分布比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrossPeriodDistributionComparison {
        /**
         * 时段分布特征
         */
        private Map<String, DistributionCharacteristics> periodDistributionCharacteristics;

        /**
         * 最佳分布时段
         */
        private String bestDistributionPeriod;

        /**
         * 最差分布时段
         */
        private String worstDistributionPeriod;

        /**
         * 时段分布评分
         */
        private Map<String, BigDecimal> periodDistributionScores;
    }

    /**
     * 分布特征
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionCharacteristics {
        /**
         * 分布类型
         */
        private String distributionType;

        /**
         * 分布参数
         */
        private Map<String, BigDecimal> distributionParameters;

        /**
         * 分布质量
         */
        private BigDecimal distributionQuality;

        /**
         * 分布特点
         */
        private List<String> distributionFeatures;
    }

    /**
     * 市场状态分布比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketStateDistributionComparison {
        /**
         * 牛市分布特征
         */
        private DistributionCharacteristics bullMarketDistribution;

        /**
         * 熊市分布特征
         */
        private DistributionCharacteristics bearMarketDistribution;

        /**
         * 震荡市分布特征
         */
        private DistributionCharacteristics sidewaysMarketDistribution;

        /**
         * 当前市场状态分布
         */
        private DistributionCharacteristics currentMarketDistribution;

        /**
         * 市场状态识别
         */
        private String identifiedMarketState;
    }

    /**
     * 分布演化分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionEvolutionAnalysis {
        /**
         * 演化趋势
         */
        private String evolutionTrend;

        /**
         * 演化速度
         */
        private BigDecimal evolutionSpeed;

        /**
         * 演化方向
         */
        private String evolutionDirection;

        /**
         * 演化稳定性
         */
        private BigDecimal evolutionStability;

        /**
         * 演化预测
         */
        private EvolutionForecast evolutionForecast;
    }

    /**
     * 演化预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EvolutionForecast {
        /**
         * 预测时间范围
         */
        private String forecastTimeRange;

        /**
         * 预测分布类型
         */
        private String forecastDistributionType;

        /**
         * 预测参数
         */
        private Map<String, BigDecimal> forecastParameters;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;
    }

    /**
     * 分布预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionForecast {
        /**
         * 短期预测
         */
        private DistributionPrediction shortTermPrediction;

        /**
         * 中期预测
         */
        private DistributionPrediction mediumTermPrediction;

        /**
         * 长期预测
         */
        private DistributionPrediction longTermPrediction;

        /**
         * 预测模型评估
         */
        private PredictionModelEvaluation predictionModelEvaluation;
    }

    /**
     * 分布预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionPrediction {
        /**
         * 预测时间范围
         */
        private String predictionTimeRange;

        /**
         * 预测分布类型
         */
        private String predictedDistributionType;

        /**
         * 预测统计参数
         */
        private DistributionStatistics predictedStatistics;

        /**
         * 预测置信区间
         */
        private Map<String, ConfidenceInterval> predictionConfidenceIntervals;

        /**
         * 预测准确性
         */
        private BigDecimal predictionAccuracy;
    }

    /**
     * 置信区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfidenceInterval {
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;

        /**
         * 下界
         */
        private BigDecimal lowerBound;

        /**
         * 上界
         */
        private BigDecimal upperBound;
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionModelEvaluation {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;

        /**
         * 模型可靠性
         */
        private BigDecimal modelReliability;

        /**
         * 模型性能指标
         */
        private Map<String, BigDecimal> modelPerformanceMetrics;
    }

    /**
     * 分布交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionTradingAdvice {
        /**
         * 基于分布的交易策略
         */
        private List<DistributionBasedStrategy> distributionBasedStrategies;

        /**
         * 价格区间交易建议
         */
        private List<PriceRangeTradingAdvice> priceRangeTradingAdvices;

        /**
         * 分布异常交易机会
         */
        private List<DistributionAnomalyOpportunity> distributionAnomalyOpportunities;

        /**
         * 风险管理建议
         */
        private List<String> riskManagementRecommendations;
    }

    /**
     * 基于分布的策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionBasedStrategy {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略类型
         */
        private String strategyType;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 适用分布类型
         */
        private List<String> applicableDistributionTypes;

        /**
         * 策略参数
         */
        private Map<String, String> strategyParameters;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险水平
         */
        private BigDecimal riskLevel;

        /**
         * 成功概率
         */
        private BigDecimal successProbability;
    }

    /**
     * 价格区间交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRangeTradingAdvice {
        /**
         * 价格区间
         */
        private PriceRange priceRange;

        /**
         * 交易方向
         */
        private String tradingDirection;

        /**
         * 入场建议
         */
        private String entryAdvice;

        /**
         * 出场建议
         */
        private String exitAdvice;

        /**
         * 止损建议
         */
        private BigDecimal stopLossRecommendation;

        /**
         * 止盈建议
         */
        private BigDecimal takeProfitRecommendation;

        /**
         * 仓位建议
         */
        private BigDecimal positionSizeRecommendation;
    }

    /**
     * 价格区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 中点
         */
        private BigDecimal midPoint;

        /**
         * 区间宽度
         */
        private BigDecimal rangeWidth;
    }

    /**
     * 分布异常交易机会
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionAnomalyOpportunity {
        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常描述
         */
        private String anomalyDescription;

        /**
         * 交易机会
         */
        private String tradingOpportunity;

        /**
         * 机会评分
         */
        private BigDecimal opportunityScore;

        /**
         * 时间窗口
         */
        private String timeWindow;

        /**
         * 风险评估
         */
        private BigDecimal riskAssessment;
    }

    /**
     * 分布风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 分布风险指标
         */
        private DistributionRiskMetrics distributionRiskMetrics;

        /**
         * 尾部风险评估
         */
        private TailRiskAssessment tailRiskAssessment;

        /**
         * 异常风险评估
         */
        private AnomalyRiskAssessment anomalyRiskAssessment;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 分布风险指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistributionRiskMetrics {
        /**
         * 波动性风险
         */
        private BigDecimal volatilityRisk;

        /**
         * 偏度风险
         */
        private BigDecimal skewnessRisk;

        /**
         * 峰度风险
         */
        private BigDecimal kurtosisRisk;

        /**
         * 集中度风险
         */
        private BigDecimal concentrationRisk;

        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;
    }

    /**
     * 尾部风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TailRiskAssessment {
        /**
         * 尾部风险等级
         */
        private String tailRiskLevel;

        /**
         * 极端损失概率
         */
        private BigDecimal extremeLossProbability;

        /**
         * 最大可能损失
         */
        private BigDecimal maximumPossibleLoss;

        /**
         * 尾部依赖风险
         */
        private BigDecimal tailDependenceRisk;
    }

    /**
     * 异常风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyRiskAssessment {
        /**
         * 异常风险等级
         */
        private String anomalyRiskLevel;

        /**
         * 异常发生概率
         */
        private BigDecimal anomalyOccurrenceProbability;

        /**
         * 异常影响程度
         */
        private BigDecimal anomalyImpactDegree;

        /**
         * 异常持续时间
         */
        private BigDecimal anomalyDuration;
    }

    /**
     * 获取分布分析摘要
     */
    public String getDistributionAnalysisSummary() {
        return String.format("分布类型: %s, 样本数: %d, 均值: %s, 标准差: %s",
            distributionType != null ? distributionType.getDescription() : "未知",
            distributionStatistics != null && distributionStatistics.getSampleCount() != null ? 
                distributionStatistics.getSampleCount() : 0,
            distributionStatistics != null && distributionStatistics.getMean() != null ? 
                distributionStatistics.getMean().toString() : "N/A",
            distributionStatistics != null && distributionStatistics.getStandardDeviation() != null ? 
                distributionStatistics.getStandardDeviation().toString() : "N/A");
    }

    /**
     * 检查是否为正态分布
     */
    public boolean isNormalDistribution() {
        return distributionType == PriceDistributionType.NORMAL;
    }

    /**
     * 检查是否存在异常值
     */
    public boolean hasOutliers() {
        return outlierDetection != null && 
               outlierDetection.getOutliers() != null && 
               !outlierDetection.getOutliers().isEmpty();
    }

    /**
     * 获取异常值数量
     */
    public int getOutlierCount() {
        if (outlierDetection == null || outlierDetection.getOutliers() == null) {
            return 0;
        }
        return outlierDetection.getOutliers().size();
    }

    /**
     * 获取最佳拟合分布
     */
    public String getBestFitDistribution() {
        if (distributionFittingAnalysis == null || 
            distributionFittingAnalysis.getBestFit() == null) {
            return "无法确定最佳拟合分布";
        }
        return distributionFittingAnalysis.getBestFit().getDistributionName();
    }

    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (distributionRiskAssessment == null) {
            return "风险等级未知";
        }
        return distributionRiskAssessment.getOverallRiskLevel();
    }

    /**
     * 检查是否为高风险分布
     */
    public boolean isHighRiskDistribution() {
        String riskLevel = getRiskLevel();
        return "HIGH".equalsIgnoreCase(riskLevel) || "VERY_HIGH".equalsIgnoreCase(riskLevel);
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (distributionTradingAdvice == null || 
            distributionTradingAdvice.getDistributionBasedStrategies() == null ||
            distributionTradingAdvice.getDistributionBasedStrategies().isEmpty()) {
            return "暂无交易建议";
        }
        
        DistributionBasedStrategy topStrategy = distributionTradingAdvice.getDistributionBasedStrategies().get(0);
        return String.format("%s - %s (成功概率: %s%%)",
            topStrategy.getStrategyName(),
            topStrategy.getStrategyDescription(),
            topStrategy.getSuccessProbability() != null ? 
                topStrategy.getSuccessProbability().toString() : "N/A");
    }

    /**
     * 获取分布特征描述
     */
    public String getDistributionCharacteristicsDescription() {
        if (distributionStatistics == null) {
            return "分布特征信息不可用";
        }
        
        StringBuilder description = new StringBuilder();
        
        // 对称性
        if (distributionShapeAnalysis != null && 
            distributionShapeAnalysis.getSymmetryAnalysis() != null) {
            SymmetryAnalysis symmetry = distributionShapeAnalysis.getSymmetryAnalysis();
            if (symmetry.isSymmetric()) {
                description.append("对称分布");
            } else {
                description.append(symmetry.getSkewDirection()).append("偏分布");
            }
        }
        
        // 峰值特征
        if (distributionShapeAnalysis != null && 
            distributionShapeAnalysis.getPeakAnalysis() != null) {
            PeakAnalysis peakAnalysis = distributionShapeAnalysis.getPeakAnalysis();
            if (peakAnalysis.getPeakCount() != null) {
                if (description.length() > 0) {
                    description.append(", ");
                }
                if (peakAnalysis.getPeakCount() == 1) {
                    description.append("单峰");
                } else if (peakAnalysis.getPeakCount() == 2) {
                    description.append("双峰");
                } else {
                    description.append("多峰");
                }
            }
        }
        
        // 尾部特征
        if (distributionShapeAnalysis != null && 
            distributionShapeAnalysis.getTailAnalysis() != null) {
            if (description.length() > 0) {
                description.append(", ");
            }
            description.append("具有尾部风险特征");
        }
        
        return description.length() > 0 ? description.toString() : "标准分布特征";
    }

    /**
     * 获取价格集中区间
     */
    public String getPriceConcentrationRange() {
        if (distributionStatistics == null || distributionStatistics.getQuartiles() == null) {
            return "价格集中区间不可用";
        }
        
        Quartiles quartiles = distributionStatistics.getQuartiles();
        return String.format("50%%的价格集中在 %s - %s 区间",
            quartiles.getQ1() != null ? quartiles.getQ1().toString() : "N/A",
            quartiles.getQ3() != null ? quartiles.getQ3().toString() : "N/A");
    }

    /**
     * 获取分布稳定性评估
     */
    public String getDistributionStabilityAssessment() {
        if (distributionShapeAnalysis == null) {
            return "分布稳定性评估不可用";
        }
        
        BigDecimal stability = distributionShapeAnalysis.getShapeStability();
        if (stability == null) {
            return "稳定性数据不可用";
        }
        
        if (stability.compareTo(BigDecimal.valueOf(0.8)) >= 0) {
            return "分布高度稳定";
        } else if (stability.compareTo(BigDecimal.valueOf(0.6)) >= 0) {
            return "分布中等稳定";
        } else if (stability.compareTo(BigDecimal.valueOf(0.4)) >= 0) {
            return "分布稳定性一般";
        } else {
            return "分布不稳定";
        }
    }

    /**
     * 检查是否有显著的分布异常
     */
    public boolean hasSignificantDistributionAnomaly() {
        // 检查异常值比例
        if (outlierDetection != null && outlierDetection.getOutlierRatio() != null) {
            if (outlierDetection.getOutlierRatio().compareTo(BigDecimal.valueOf(0.05)) > 0) {
                return true;
            }
        }
        
        // 检查分布形状异常
        if (distributionStatistics != null) {
            BigDecimal skewness = distributionStatistics.getSkewness();
            BigDecimal kurtosis = distributionStatistics.getKurtosis();
            
            // 偏度绝对值大于2或峰度大于7认为是异常
            if ((skewness != null && skewness.abs().compareTo(BigDecimal.valueOf(2)) > 0) ||
                (kurtosis != null && kurtosis.compareTo(BigDecimal.valueOf(7)) > 0)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取分布预测摘要
     */
    public String getDistributionForecastSummary() {
        if (distributionForecast == null || 
            distributionForecast.getShortTermPrediction() == null) {
            return "分布预测不可用";
        }
        
        DistributionPrediction shortTerm = distributionForecast.getShortTermPrediction();
        return String.format("短期预测: %s分布 (准确性: %s%%)",
            shortTerm.getPredictedDistributionType() != null ? 
                shortTerm.getPredictedDistributionType() : "未知",
            shortTerm.getPredictionAccuracy() != null ? 
                shortTerm.getPredictionAccuracy().toString() : "N/A");
    }
}