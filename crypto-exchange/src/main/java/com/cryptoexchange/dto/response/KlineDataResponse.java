package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * K线数据响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "K线数据响应")
public class KlineDataResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "K线周期")
    private String interval;

    @Schema(description = "周期描述")
    private String intervalDescription;

    @Schema(description = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "K线数据数量")
    private Integer klineCount;

    @Schema(description = "最大返回数量")
    private Integer maxLimit;

    @Schema(description = "K线数据列表")
    private List<KlineData> klines;

    @Schema(description = "统计信息")
    private KlineStatistics statistics;

    @Schema(description = "技术指标")
    private TechnicalIndicators technicalIndicators;

    @Schema(description = "价格分析")
    private PriceAnalysis priceAnalysis;

    @Schema(description = "成交量分析")
    private VolumeAnalysis volumeAnalysis;

    @Schema(description = "趋势分析")
    private TrendAnalysis trendAnalysis;

    @Schema(description = "波动率分析")
    private VolatilityAnalysis volatilityAnalysis;

    @Schema(description = "支撑阻力分析")
    private SupportResistanceAnalysis supportResistanceAnalysis;

    @Schema(description = "形态识别")
    private PatternRecognition patternRecognition;

    @Schema(description = "市场结构")
    private MarketStructure marketStructure;

    @Schema(description = "风险指标")
    private RiskMetrics riskMetrics;

    @Schema(description = "预测分析")
    private PredictiveAnalysis predictiveAnalysis;

    @Schema(description = "数据质量")
    private DataQuality dataQuality;

    @Schema(description = "查询时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime queryTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "数据版本")
    private String dataVersion;

    @Schema(description = "缓存状态")
    private String cacheStatus;

    @Schema(description = "查询耗时(毫秒)")
    private Long queryDuration;

    @Schema(description = "是否实时数据")
    private Boolean isRealTime;

    @Schema(description = "数据延迟(毫秒)")
    private Long dataLatency;

    @Schema(description = "备注")
    private String remarks;

    /**
     * K线数据
     */
    @Data
    @Schema(description = "K线数据")
    public static class KlineData {
        
        @Schema(description = "开盘时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime openTime;
        
        @Schema(description = "收盘时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime closeTime;
        
        @Schema(description = "时间戳")
        private Long timestamp;
        
        @Schema(description = "开盘价")
        private BigDecimal openPrice;
        
        @Schema(description = "最高价")
        private BigDecimal highPrice;
        
        @Schema(description = "最低价")
        private BigDecimal lowPrice;
        
        @Schema(description = "收盘价")
        private BigDecimal closePrice;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "成交额")
        private BigDecimal amount;
        
        @Schema(description = "成交笔数")
        private Long tradeCount;
        
        @Schema(description = "主动买入成交量")
        private BigDecimal takerBuyVolume;
        
        @Schema(description = "主动买入成交额")
        private BigDecimal takerBuyAmount;
        
        @Schema(description = "加权平均价格")
        private BigDecimal weightedAvgPrice;
        
        @Schema(description = "价格变化")
        private BigDecimal priceChange;
        
        @Schema(description = "价格变化百分比")
        private BigDecimal priceChangePercent;
        
        @Schema(description = "振幅")
        private BigDecimal amplitude;
        
        @Schema(description = "换手率")
        private BigDecimal turnoverRate;
        
        @Schema(description = "是否上涨")
        private Boolean isRising;
        
        @Schema(description = "实体大小")
        private BigDecimal bodySize;
        
        @Schema(description = "上影线长度")
        private BigDecimal upperShadow;
        
        @Schema(description = "下影线长度")
        private BigDecimal lowerShadow;
        
        @Schema(description = "K线类型")
        private String klineType;
        
        @Schema(description = "市场情绪")
        private String marketSentiment;
        
        @Schema(description = "流动性评分")
        private BigDecimal liquidityScore;
        
        @Schema(description = "异常标记")
        private Boolean isAnomalous;
        
        @Schema(description = "数据完整性")
        private Boolean dataIntegrity;
    }

    /**
     * K线统计
     */
    @Data
    @Schema(description = "K线统计")
    public static class KlineStatistics {
        
        @Schema(description = "总K线数量")
        private Integer totalKlines;
        
        @Schema(description = "上涨K线数量")
        private Integer risingKlines;
        
        @Schema(description = "下跌K线数量")
        private Integer fallingKlines;
        
        @Schema(description = "平盘K线数量")
        private Integer flatKlines;
        
        @Schema(description = "上涨概率")
        private BigDecimal risingProbability;
        
        @Schema(description = "平均涨幅")
        private BigDecimal averageRise;
        
        @Schema(description = "平均跌幅")
        private BigDecimal averageFall;
        
        @Schema(description = "最大单日涨幅")
        private BigDecimal maxDailyRise;
        
        @Schema(description = "最大单日跌幅")
        private BigDecimal maxDailyFall;
        
        @Schema(description = "平均成交量")
        private BigDecimal averageVolume;
        
        @Schema(description = "最大成交量")
        private BigDecimal maxVolume;
        
        @Schema(description = "最小成交量")
        private BigDecimal minVolume;
        
        @Schema(description = "成交量标准差")
        private BigDecimal volumeStdDev;
        
        @Schema(description = "平均振幅")
        private BigDecimal averageAmplitude;
        
        @Schema(description = "最大振幅")
        private BigDecimal maxAmplitude;
        
        @Schema(description = "连续上涨最大天数")
        private Integer maxConsecutiveRising;
        
        @Schema(description = "连续下跌最大天数")
        private Integer maxConsecutiveFalling;
    }

    /**
     * 技术指标
     */
    @Data
    @Schema(description = "技术指标")
    public static class TechnicalIndicators {
        
        @Schema(description = "移动平均线")
        private MovingAverages movingAverages;
        
        @Schema(description = "MACD指标")
        private MACD macd;
        
        @Schema(description = "RSI指标")
        private RSI rsi;
        
        @Schema(description = "布林带")
        private BollingerBands bollingerBands;
        
        @Schema(description = "KDJ指标")
        private KDJ kdj;
        
        @Schema(description = "威廉指标")
        private WilliamsR williamsR;
        
        @Schema(description = "随机指标")
        private Stochastic stochastic;
        
        @Schema(description = "CCI指标")
        private CCI cci;
        
        @Schema(description = "ATR指标")
        private ATR atr;
        
        @Schema(description = "OBV指标")
        private OBV obv;
    }

    /**
     * 移动平均线
     */
    @Data
    @Schema(description = "移动平均线")
    public static class MovingAverages {
        
        @Schema(description = "MA5")
        private BigDecimal ma5;
        
        @Schema(description = "MA10")
        private BigDecimal ma10;
        
        @Schema(description = "MA20")
        private BigDecimal ma20;
        
        @Schema(description = "MA50")
        private BigDecimal ma50;
        
        @Schema(description = "MA100")
        private BigDecimal ma100;
        
        @Schema(description = "MA200")
        private BigDecimal ma200;
        
        @Schema(description = "EMA12")
        private BigDecimal ema12;
        
        @Schema(description = "EMA26")
        private BigDecimal ema26;
        
        @Schema(description = "均线排列")
        private String maAlignment;
        
        @Schema(description = "金叉死叉信号")
        private String crossoverSignal;
    }

    /**
     * MACD指标
     */
    @Data
    @Schema(description = "MACD指标")
    public static class MACD {
        
        @Schema(description = "MACD线")
        private BigDecimal macdLine;
        
        @Schema(description = "信号线")
        private BigDecimal signalLine;
        
        @Schema(description = "柱状图")
        private BigDecimal histogram;
        
        @Schema(description = "MACD信号")
        private String macdSignal;
        
        @Schema(description = "背离信号")
        private String divergenceSignal;
    }

    /**
     * RSI指标
     */
    @Data
    @Schema(description = "RSI指标")
    public static class RSI {
        
        @Schema(description = "RSI6")
        private BigDecimal rsi6;
        
        @Schema(description = "RSI12")
        private BigDecimal rsi12;
        
        @Schema(description = "RSI24")
        private BigDecimal rsi24;
        
        @Schema(description = "RSI信号")
        private String rsiSignal;
        
        @Schema(description = "超买超卖状态")
        private String overboughtOversold;
    }

    /**
     * 布林带
     */
    @Data
    @Schema(description = "布林带")
    public static class BollingerBands {
        
        @Schema(description = "上轨")
        private BigDecimal upperBand;
        
        @Schema(description = "中轨")
        private BigDecimal middleBand;
        
        @Schema(description = "下轨")
        private BigDecimal lowerBand;
        
        @Schema(description = "带宽")
        private BigDecimal bandwidth;
        
        @Schema(description = "%B指标")
        private BigDecimal percentB;
        
        @Schema(description = "布林带信号")
        private String bollingerSignal;
    }

    /**
     * KDJ指标
     */
    @Data
    @Schema(description = "KDJ指标")
    public static class KDJ {
        
        @Schema(description = "K值")
        private BigDecimal kValue;
        
        @Schema(description = "D值")
        private BigDecimal dValue;
        
        @Schema(description = "J值")
        private BigDecimal jValue;
        
        @Schema(description = "KDJ信号")
        private String kdjSignal;
        
        @Schema(description = "金叉死叉")
        private String crossover;
    }

    /**
     * 威廉指标
     */
    @Data
    @Schema(description = "威廉指标")
    public static class WilliamsR {
        
        @Schema(description = "WR14")
        private BigDecimal wr14;
        
        @Schema(description = "WR信号")
        private String wrSignal;
        
        @Schema(description = "超买超卖状态")
        private String overboughtOversold;
    }

    /**
     * 随机指标
     */
    @Data
    @Schema(description = "随机指标")
    public static class Stochastic {
        
        @Schema(description = "%K")
        private BigDecimal percentK;
        
        @Schema(description = "%D")
        private BigDecimal percentD;
        
        @Schema(description = "随机指标信号")
        private String stochasticSignal;
    }

    /**
     * CCI指标
     */
    @Data
    @Schema(description = "CCI指标")
    public static class CCI {
        
        @Schema(description = "CCI值")
        private BigDecimal cciValue;
        
        @Schema(description = "CCI信号")
        private String cciSignal;
    }

    /**
     * ATR指标
     */
    @Data
    @Schema(description = "ATR指标")
    public static class ATR {
        
        @Schema(description = "ATR值")
        private BigDecimal atrValue;
        
        @Schema(description = "波动率水平")
        private String volatilityLevel;
    }

    /**
     * OBV指标
     */
    @Data
    @Schema(description = "OBV指标")
    public static class OBV {
        
        @Schema(description = "OBV值")
        private BigDecimal obvValue;
        
        @Schema(description = "OBV趋势")
        private String obvTrend;
        
        @Schema(description = "背离信号")
        private String divergenceSignal;
    }

    /**
     * 价格分析
     */
    @Data
    @Schema(description = "价格分析")
    public static class PriceAnalysis {
        
        @Schema(description = "价格趋势")
        private String priceTrend;
        
        @Schema(description = "趋势强度")
        private BigDecimal trendStrength;
        
        @Schema(description = "趋势持续时间")
        private Integer trendDuration;
        
        @Schema(description = "价格通道")
        private PriceChannel priceChannel;
        
        @Schema(description = "价格目标")
        private List<BigDecimal> priceTargets;
        
        @Schema(description = "关键价位")
        private List<KeyLevel> keyLevels;
        
        @Schema(description = "价格形态")
        private String pricePattern;
        
        @Schema(description = "突破概率")
        private BigDecimal breakoutProbability;
    }

    /**
     * 价格通道
     */
    @Data
    @Schema(description = "价格通道")
    public static class PriceChannel {
        
        @Schema(description = "上轨")
        private BigDecimal upperChannel;
        
        @Schema(description = "下轨")
        private BigDecimal lowerChannel;
        
        @Schema(description = "中轨")
        private BigDecimal middleChannel;
        
        @Schema(description = "通道宽度")
        private BigDecimal channelWidth;
        
        @Schema(description = "通道倾斜度")
        private BigDecimal channelSlope;
    }

    /**
     * 关键价位
     */
    @Data
    @Schema(description = "关键价位")
    public static class KeyLevel {
        
        @Schema(description = "价位")
        private BigDecimal level;
        
        @Schema(description = "类型")
        private String type;
        
        @Schema(description = "强度")
        private BigDecimal strength;
        
        @Schema(description = "测试次数")
        private Integer testCount;
    }

    /**
     * 成交量分析
     */
    @Data
    @Schema(description = "成交量分析")
    public static class VolumeAnalysis {
        
        @Schema(description = "成交量趋势")
        private String volumeTrend;
        
        @Schema(description = "量价关系")
        private String volumePriceRelation;
        
        @Schema(description = "成交量指标")
        private VolumeIndicators volumeIndicators;
        
        @Schema(description = "异常成交量")
        private List<VolumeAnomaly> volumeAnomalies;
        
        @Schema(description = "成交量分布")
        private VolumeDistribution volumeDistribution;
    }

    /**
     * 成交量指标
     */
    @Data
    @Schema(description = "成交量指标")
    public static class VolumeIndicators {
        
        @Schema(description = "成交量移动平均")
        private BigDecimal volumeMA;
        
        @Schema(description = "成交量比率")
        private BigDecimal volumeRatio;
        
        @Schema(description = "价量背离")
        private String volumePriceDivergence;
    }

    /**
     * 成交量异常
     */
    @Data
    @Schema(description = "成交量异常")
    public static class VolumeAnomaly {
        
        @Schema(description = "异常时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime anomalyTime;
        
        @Schema(description = "异常成交量")
        private BigDecimal anomalyVolume;
        
        @Schema(description = "异常倍数")
        private BigDecimal anomalyMultiple;
        
        @Schema(description = "异常类型")
        private String anomalyType;
    }

    /**
     * 成交量分布
     */
    @Data
    @Schema(description = "成交量分布")
    public static class VolumeDistribution {
        
        @Schema(description = "低量区间")
        private BigDecimal lowVolumeRange;
        
        @Schema(description = "正常区间")
        private BigDecimal normalVolumeRange;
        
        @Schema(description = "高量区间")
        private BigDecimal highVolumeRange;
        
        @Schema(description = "成交量集中度")
        private BigDecimal volumeConcentration;
    }

    /**
     * 趋势分析
     */
    @Data
    @Schema(description = "趋势分析")
    public static class TrendAnalysis {
        
        @Schema(description = "主趋势")
        private String primaryTrend;
        
        @Schema(description = "次级趋势")
        private String secondaryTrend;
        
        @Schema(description = "短期趋势")
        private String shortTermTrend;
        
        @Schema(description = "趋势确认")
        private Boolean trendConfirmation;
        
        @Schema(description = "趋势转折点")
        private List<TrendTurningPoint> turningPoints;
        
        @Schema(description = "趋势线")
        private List<TrendLine> trendLines;
    }

    /**
     * 趋势转折点
     */
    @Data
    @Schema(description = "趋势转折点")
    public static class TrendTurningPoint {
        
        @Schema(description = "转折时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime turningTime;
        
        @Schema(description = "转折价格")
        private BigDecimal turningPrice;
        
        @Schema(description = "转折类型")
        private String turningType;
        
        @Schema(description = "确认度")
        private BigDecimal confidence;
    }

    /**
     * 趋势线
     */
    @Data
    @Schema(description = "趋势线")
    public static class TrendLine {
        
        @Schema(description = "起始点")
        private TrendPoint startPoint;
        
        @Schema(description = "结束点")
        private TrendPoint endPoint;
        
        @Schema(description = "斜率")
        private BigDecimal slope;
        
        @Schema(description = "有效性")
        private BigDecimal validity;
        
        @Schema(description = "类型")
        private String type;
    }

    /**
     * 趋势点
     */
    @Data
    @Schema(description = "趋势点")
    public static class TrendPoint {
        
        @Schema(description = "时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime time;
        
        @Schema(description = "价格")
        private BigDecimal price;
    }

    /**
     * 波动率分析
     */
    @Data
    @Schema(description = "波动率分析")
    public static class VolatilityAnalysis {
        
        @Schema(description = "历史波动率")
        private BigDecimal historicalVolatility;
        
        @Schema(description = "实现波动率")
        private BigDecimal realizedVolatility;
        
        @Schema(description = "波动率趋势")
        private String volatilityTrend;
        
        @Schema(description = "波动率分位数")
        private BigDecimal volatilityPercentile;
        
        @Schema(description = "波动率聚类")
        private Boolean volatilityClustering;
        
        @Schema(description = "波动率预测")
        private BigDecimal volatilityForecast;
    }

    /**
     * 支撑阻力分析
     */
    @Data
    @Schema(description = "支撑阻力分析")
    public static class SupportResistanceAnalysis {
        
        @Schema(description = "支撑位")
        private List<SupportResistanceLevel> supportLevels;
        
        @Schema(description = "阻力位")
        private List<SupportResistanceLevel> resistanceLevels;
        
        @Schema(description = "关键支撑")
        private BigDecimal keySupportLevel;
        
        @Schema(description = "关键阻力")
        private BigDecimal keyResistanceLevel;
        
        @Schema(description = "突破概率")
        private BigDecimal breakoutProbability;
    }

    /**
     * 支撑阻力位
     */
    @Data
    @Schema(description = "支撑阻力位")
    public static class SupportResistanceLevel {
        
        @Schema(description = "价位")
        private BigDecimal level;
        
        @Schema(description = "强度")
        private BigDecimal strength;
        
        @Schema(description = "测试次数")
        private Integer testCount;
        
        @Schema(description = "最近测试时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastTestTime;
        
        @Schema(description = "有效性")
        private BigDecimal validity;
    }

    /**
     * 形态识别
     */
    @Data
    @Schema(description = "形态识别")
    public static class PatternRecognition {
        
        @Schema(description = "识别的形态")
        private List<ChartPattern> identifiedPatterns;
        
        @Schema(description = "形态完成度")
        private BigDecimal patternCompletion;
        
        @Schema(description = "形态可靠性")
        private BigDecimal patternReliability;
    }

    /**
     * 图表形态
     */
    @Data
    @Schema(description = "图表形态")
    public static class ChartPattern {
        
        @Schema(description = "形态名称")
        private String patternName;
        
        @Schema(description = "形态类型")
        private String patternType;
        
        @Schema(description = "形态方向")
        private String patternDirection;
        
        @Schema(description = "置信度")
        private BigDecimal confidence;
        
        @Schema(description = "目标价位")
        private BigDecimal targetPrice;
        
        @Schema(description = "止损价位")
        private BigDecimal stopLossPrice;
    }

    /**
     * 市场结构
     */
    @Data
    @Schema(description = "市场结构")
    public static class MarketStructure {
        
        @Schema(description = "市场阶段")
        private String marketPhase;
        
        @Schema(description = "市场状态")
        private String marketCondition;
        
        @Schema(description = "市场强度")
        private BigDecimal marketStrength;
        
        @Schema(description = "市场效率")
        private BigDecimal marketEfficiency;
        
        @Schema(description = "流动性状况")
        private String liquidityCondition;
    }

    /**
     * 风险指标
     */
    @Data
    @Schema(description = "风险指标")
    public static class RiskMetrics {
        
        @Schema(description = "最大回撤")
        private BigDecimal maxDrawdown;
        
        @Schema(description = "夏普比率")
        private BigDecimal sharpeRatio;
        
        @Schema(description = "索提诺比率")
        private BigDecimal sortinoRatio;
        
        @Schema(description = "卡尔马比率")
        private BigDecimal calmarRatio;
        
        @Schema(description = "VaR值")
        private BigDecimal valueAtRisk;
        
        @Schema(description = "CVaR值")
        private BigDecimal conditionalVaR;
        
        @Schema(description = "风险等级")
        private String riskLevel;
    }

    /**
     * 预测分析
     */
    @Data
    @Schema(description = "预测分析")
    public static class PredictiveAnalysis {
        
        @Schema(description = "短期预测")
        private PricePrediction shortTermPrediction;
        
        @Schema(description = "中期预测")
        private PricePrediction mediumTermPrediction;
        
        @Schema(description = "长期预测")
        private PricePrediction longTermPrediction;
        
        @Schema(description = "预测模型")
        private String predictionModel;
        
        @Schema(description = "模型准确率")
        private BigDecimal modelAccuracy;
    }

    /**
     * 价格预测
     */
    @Data
    @Schema(description = "价格预测")
    public static class PricePrediction {
        
        @Schema(description = "预测价格")
        private BigDecimal predictedPrice;
        
        @Schema(description = "预测方向")
        private String predictedDirection;
        
        @Schema(description = "置信度")
        private BigDecimal confidence;
        
        @Schema(description = "预测区间上限")
        private BigDecimal upperBound;
        
        @Schema(description = "预测区间下限")
        private BigDecimal lowerBound;
        
        @Schema(description = "预测时间范围")
        private String timeframe;
    }

    /**
     * 数据质量
     */
    @Data
    @Schema(description = "数据质量")
    public static class DataQuality {
        
        @Schema(description = "数据完整性")
        private BigDecimal dataCompleteness;
        
        @Schema(description = "数据准确性")
        private BigDecimal dataAccuracy;
        
        @Schema(description = "数据一致性")
        private BigDecimal dataConsistency;
        
        @Schema(description = "缺失数据数量")
        private Integer missingDataCount;
        
        @Schema(description = "异常数据数量")
        private Integer anomalousDataCount;
        
        @Schema(description = "数据质量评分")
        private BigDecimal dataQualityScore;
        
        @Schema(description = "质量问题")
        private List<String> qualityIssues;
    }
}