package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货账户信息响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesAccountInfoResponse {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 账户类型 (FUTURES)
     */
    private String accountType;
    
    /**
     * 总权益
     */
    private BigDecimal totalWalletBalance;
    
    /**
     * 总保证金余额
     */
    private BigDecimal totalMarginBalance;
    
    /**
     * 总可用余额
     */
    private BigDecimal totalAvailableBalance;
    
    /**
     * 总未实现盈亏
     */
    private BigDecimal totalUnrealizedProfit;
    
    /**
     * 总初始保证金
     */
    private BigDecimal totalInitialMargin;
    
    /**
     * 总维持保证金
     */
    private BigDecimal totalMaintMargin;
    
    /**
     * 总持仓初始保证金
     */
    private BigDecimal totalPositionInitialMargin;
    
    /**
     * 总挂单初始保证金
     */
    private BigDecimal totalOpenOrderInitialMargin;
    
    /**
     * 总交叉保证金
     */
    private BigDecimal totalCrossWalletBalance;
    
    /**
     * 总交叉未实现盈亏
     */
    private BigDecimal totalCrossUnPnl;
    
    /**
     * 可划转出金额
     */
    private BigDecimal availableBalance;
    
    /**
     * 最大可开仓金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 保证金率
     */
    private BigDecimal marginRatio;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRatio;
    
    /**
     * 是否可以交易
     */
    private Boolean canTrade;
    
    /**
     * 是否可以出金
     */
    private Boolean canWithdraw;
    
    /**
     * 是否可以入金
     */
    private Boolean canDeposit;
    
    /**
     * 账户更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 多空模式 (true: 双向持仓模式, false: 单向持仓模式)
     */
    private Boolean multiAssetsMargin;
    
    /**
     * 资产列表
     */
    private List<FuturesAsset> assets;
    
    /**
     * 持仓列表
     */
    private List<FuturesPosition> positions;
    
    /**
     * 期货资产信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FuturesAsset {
        /**
         * 资产名称
         */
        private String asset;
        
        /**
         * 钱包余额
         */
        private BigDecimal walletBalance;
        
        /**
         * 未实现盈亏
         */
        private BigDecimal unrealizedProfit;
        
        /**
         * 保证金余额
         */
        private BigDecimal marginBalance;
        
        /**
         * 维持保证金
         */
        private BigDecimal maintMargin;
        
        /**
         * 初始保证金
         */
        private BigDecimal initialMargin;
        
        /**
         * 持仓初始保证金
         */
        private BigDecimal positionInitialMargin;
        
        /**
         * 挂单初始保证金
         */
        private BigDecimal openOrderInitialMargin;
        
        /**
         * 交叉钱包余额
         */
        private BigDecimal crossWalletBalance;
        
        /**
         * 交叉未实现盈亏
         */
        private BigDecimal crossUnPnl;
        
        /**
         * 可用余额
         */
        private BigDecimal availableBalance;
        
        /**
         * 最大可提取金额
         */
        private BigDecimal maxWithdrawAmount;
        
        /**
         * 是否可以作为保证金
         */
        private Boolean marginAvailable;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
    
    /**
     * 期货持仓信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FuturesPosition {
        /**
         * 合约符号
         */
        private String symbol;
        
        /**
         * 初始保证金
         */
        private BigDecimal initialMargin;
        
        /**
         * 维持保证金
         */
        private BigDecimal maintMargin;
        
        /**
         * 未实现盈亏
         */
        private BigDecimal unrealizedProfit;
        
        /**
         * 持仓初始保证金
         */
        private BigDecimal positionInitialMargin;
        
        /**
         * 挂单初始保证金
         */
        private BigDecimal openOrderInitialMargin;
        
        /**
         * 杠杆倍数
         */
        private Integer leverage;
        
        /**
         * 是否为逐仓模式
         */
        private Boolean isolated;
        
        /**
         * 开仓均价
         */
        private BigDecimal entryPrice;
        
        /**
         * 最大可开仓数量
         */
        private BigDecimal maxNotional;
        
        /**
         * 持仓方向 (BOTH/LONG/SHORT)
         */
        private String positionSide;
        
        /**
         * 持仓数量
         */
        private BigDecimal positionAmt;
        
        /**
         * 标记价格
         */
        private BigDecimal markPrice;
        
        /**
         * 强平价格
         */
        private BigDecimal liquidationPrice;
        
        /**
         * 破产价格
         */
        private BigDecimal bankruptPrice;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
}