package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 异常交易分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbnormalTradingResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析时间范围
     */
    private String analysisTimeRange;

    /**
     * 异常交易风险等级
     */
    private AbnormalTradingRiskLevel riskLevel;

    /**
     * 异常交易检测结果
     */
    private List<AbnormalTradingDetection> abnormalTradingDetections;

    /**
     * 异常交易统计
     */
    private AbnormalTradingStatistics abnormalTradingStatistics;

    /**
     * 价格异常分析
     */
    private PriceAnomalyAnalysis priceAnomalyAnalysis;

    /**
     * 成交量异常分析
     */
    private VolumeAnomalyAnalysis volumeAnomalyAnalysis;

    /**
     * 交易模式异常分析
     */
    private TradingPatternAnomalyAnalysis tradingPatternAnomalyAnalysis;

    /**
     * 市场操纵检测
     */
    private MarketManipulationDetection marketManipulationDetection;

    /**
     * 异常交易影响评估
     */
    private AbnormalTradingImpactAssessment abnormalTradingImpactAssessment;

    /**
     * 异常交易预警
     */
    private List<AbnormalTradingAlert> abnormalTradingAlerts;

    /**
     * 异常交易建议
     */
    private AbnormalTradingRecommendation abnormalTradingRecommendation;

    /**
     * 监管合规分析
     */
    private RegulatoryComplianceAnalysis regulatoryComplianceAnalysis;

    /**
     * 异常交易风险等级
     */
    public enum AbnormalTradingRiskLevel {
        CRITICAL("严重", "发现严重异常交易行为，需要立即关注"),
        HIGH("高", "发现高风险异常交易行为，需要密切监控"),
        MEDIUM("中等", "发现中等风险异常交易行为，建议关注"),
        LOW("低", "发现低风险异常交易行为，正常监控"),
        NORMAL("正常", "未发现明显异常交易行为");

        private final String description;
        private final String detail;

        AbnormalTradingRiskLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 异常交易检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingDetection {
        /**
         * 异常类型
         */
        private AbnormalTradingType abnormalType;

        /**
         * 异常严重程度
         */
        private AbnormalSeverity severity;

        /**
         * 检测时间
         */
        private LocalDateTime detectionTime;

        /**
         * 异常描述
         */
        private String abnormalDescription;

        /**
         * 异常指标
         */
        private AbnormalIndicators abnormalIndicators;

        /**
         * 异常证据
         */
        private List<AbnormalEvidence> abnormalEvidences;

        /**
         * 置信度
         */
        private BigDecimal confidence;

        /**
         * 影响范围
         */
        private String impactScope;

        /**
         * 持续时间
         */
        private Integer duration;

        /**
         * 相关交易
         */
        private List<RelatedTrade> relatedTrades;
    }

    /**
     * 异常交易类型
     */
    public enum AbnormalTradingType {
        WASH_TRADING("洗盘交易", "虚假交易量制造"),
        PUMP_AND_DUMP("拉高出货", "人为拉高价格后抛售"),
        SPOOFING("虚假报价", "大量虚假订单操纵价格"),
        LAYERING("分层交易", "多层订单操纵"),
        FRONT_RUNNING("抢先交易", "利用信息优势抢先交易"),
        INSIDER_TRADING("内幕交易", "利用内幕信息交易"),
        MARKET_CORNERING("市场垄断", "控制市场供应"),
        CROSS_TRADING("交叉交易", "异常的交叉交易模式"),
        HIGH_FREQUENCY_MANIPULATION("高频操纵", "高频交易操纵"),
        COORDINATED_TRADING("协调交易", "多账户协调交易"),
        UNUSUAL_VOLUME("异常成交量", "异常大的交易量"),
        UNUSUAL_PRICE_MOVEMENT("异常价格波动", "异常的价格变动"),
        SUSPICIOUS_TIMING("可疑时机", "可疑的交易时机"),
        ABNORMAL_SPREAD("异常价差", "异常的买卖价差"),
        OTHER("其他", "其他类型的异常交易");

        private final String description;
        private final String detail;

        AbnormalTradingType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 异常严重程度
     */
    public enum AbnormalSeverity {
        CRITICAL("严重", 5),
        HIGH("高", 4),
        MEDIUM("中等", 3),
        LOW("低", 2),
        MINIMAL("轻微", 1);

        private final String description;
        private final int level;

        AbnormalSeverity(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 异常指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalIndicators {
        /**
         * 异常评分
         */
        private BigDecimal abnormalScore;

        /**
         * 偏离度
         */
        private BigDecimal deviationDegree;

        /**
         * 异常强度
         */
        private BigDecimal abnormalIntensity;

        /**
         * 异常频率
         */
        private BigDecimal abnormalFrequency;

        /**
         * 统计显著性
         */
        private BigDecimal statisticalSignificance;

        /**
         * Z分数
         */
        private BigDecimal zScore;

        /**
         * P值
         */
        private BigDecimal pValue;
    }

    /**
     * 异常证据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalEvidence {
        /**
         * 证据类型
         */
        private String evidenceType;

        /**
         * 证据描述
         */
        private String evidenceDescription;

        /**
         * 证据强度
         */
        private BigDecimal evidenceStrength;

        /**
         * 证据时间
         */
        private LocalDateTime evidenceTime;

        /**
         * 证据数据
         */
        private Map<String, Object> evidenceData;

        /**
         * 证据可靠性
         */
        private BigDecimal evidenceReliability;
    }

    /**
     * 相关交易
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedTrade {
        /**
         * 交易ID
         */
        private String tradeId;

        /**
         * 交易时间
         */
        private LocalDateTime tradeTime;

        /**
         * 交易价格
         */
        private BigDecimal tradePrice;

        /**
         * 交易数量
         */
        private BigDecimal tradeQuantity;

        /**
         * 交易方向
         */
        private String tradeSide;

        /**
         * 交易类型
         */
        private String tradeType;

        /**
         * 异常程度
         */
        private BigDecimal abnormalityDegree;
    }

    /**
     * 异常交易统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingStatistics {
        /**
         * 异常交易总数
         */
        private Integer totalAbnormalTrades;

        /**
         * 异常交易占比
         */
        private BigDecimal abnormalTradeRatio;

        /**
         * 异常交易总量
         */
        private BigDecimal totalAbnormalVolume;

        /**
         * 异常交易总价值
         */
        private BigDecimal totalAbnormalValue;

        /**
         * 按类型分组统计
         */
        private Map<AbnormalTradingType, Integer> abnormalTypeDistribution;

        /**
         * 按严重程度分组统计
         */
        private Map<AbnormalSeverity, Integer> severityDistribution;

        /**
         * 异常交易时间分布
         */
        private List<TimeDistribution> timeDistribution;

        /**
         * 异常交易趋势
         */
        private AbnormalTradingTrend abnormalTradingTrend;

        public Integer getTotalAbnormalTrades() {
            return totalAbnormalTrades;
        }
    }

    /**
     * 时间分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeDistribution {
        /**
         * 时间段
         */
        private String timePeriod;

        /**
         * 异常交易数量
         */
        private Integer abnormalTradeCount;

        /**
         * 异常交易占比
         */
        private BigDecimal abnormalTradeRatio;

        /**
         * 平均异常程度
         */
        private BigDecimal averageAbnormalityDegree;
    }

    /**
     * 异常交易趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingTrend {
        /**
         * 趋势方向
         */
        private String trendDirection;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 变化率
         */
        private BigDecimal changeRate;

        /**
         * 趋势持续时间
         */
        private Integer trendDuration;

        /**
         * 趋势预测
         */
        private String trendForecast;
    }

    /**
     * 价格异常分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceAnomalyAnalysis {
        /**
         * 价格异常检测结果
         */
        private List<PriceAnomaly> priceAnomalies;

        /**
         * 价格跳跃检测
         */
        private PriceJumpDetection priceJumpDetection;

        /**
         * 价格操纵检测
         */
        private PriceManipulationDetection priceManipulationDetection;

        /**
         * 价格异常统计
         */
        private PriceAnomalyStatistics priceAnomalyStatistics;
    }

    /**
     * 价格异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceAnomaly {
        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常时间
         */
        private LocalDateTime anomalyTime;

        /**
         * 异常价格
         */
        private BigDecimal anomalyPrice;

        /**
         * 正常价格范围
         */
        private PriceRange normalPriceRange;

        /**
         * 偏离程度
         */
        private BigDecimal deviationDegree;

        /**
         * 异常持续时间
         */
        private Integer anomalyDuration;

        /**
         * 异常严重性
         */
        private AbnormalSeverity anomalySeverity;
    }

    /**
     * 价格范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 中位数
         */
        private BigDecimal median;
    }

    /**
     * 价格跳跃检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceJumpDetection {
        /**
         * 检测到的价格跳跃
         */
        private List<PriceJump> priceJumps;

        /**
         * 跳跃频率
         */
        private BigDecimal jumpFrequency;

        /**
         * 平均跳跃幅度
         */
        private BigDecimal averageJumpMagnitude;

        /**
         * 最大跳跃幅度
         */
        private BigDecimal maxJumpMagnitude;
    }

    /**
     * 价格跳跃
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceJump {
        /**
         * 跳跃时间
         */
        private LocalDateTime jumpTime;

        /**
         * 跳跃前价格
         */
        private BigDecimal priceBeforeJump;

        /**
         * 跳跃后价格
         */
        private BigDecimal priceAfterJump;

        /**
         * 跳跃幅度
         */
        private BigDecimal jumpMagnitude;

        /**
         * 跳跃方向
         */
        private String jumpDirection;

        /**
         * 跳跃原因
         */
        private String jumpReason;
    }

    /**
     * 价格操纵检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceManipulationDetection {
        /**
         * 操纵类型
         */
        private List<String> manipulationTypes;

        /**
         * 操纵强度
         */
        private BigDecimal manipulationIntensity;

        /**
         * 操纵持续时间
         */
        private Integer manipulationDuration;

        /**
         * 操纵证据
         */
        private List<ManipulationEvidence> manipulationEvidences;

        /**
         * 操纵影响
         */
        private ManipulationImpact manipulationImpact;
    }

    /**
     * 操纵证据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationEvidence {
        /**
         * 证据类型
         */
        private String evidenceType;

        /**
         * 证据描述
         */
        private String evidenceDescription;

        /**
         * 证据强度
         */
        private BigDecimal evidenceStrength;

        /**
         * 证据时间
         */
        private LocalDateTime evidenceTime;
    }

    /**
     * 操纵影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationImpact {
        /**
         * 价格影响
         */
        private BigDecimal priceImpact;

        /**
         * 成交量影响
         */
        private BigDecimal volumeImpact;

        /**
         * 市场影响
         */
        private BigDecimal marketImpact;

        /**
         * 投资者影响
         */
        private BigDecimal investorImpact;
    }

    /**
     * 价格异常统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceAnomalyStatistics {
        /**
         * 异常事件总数
         */
        private Integer totalAnomalyEvents;

        /**
         * 异常事件频率
         */
        private BigDecimal anomalyFrequency;

        /**
         * 平均偏离程度
         */
        private BigDecimal averageDeviationDegree;

        /**
         * 最大偏离程度
         */
        private BigDecimal maxDeviationDegree;

        /**
         * 异常持续时间统计
         */
        private DurationStatistics durationStatistics;
    }

    /**
     * 持续时间统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DurationStatistics {
        /**
         * 平均持续时间
         */
        private BigDecimal averageDuration;

        /**
         * 最短持续时间
         */
        private Integer minDuration;

        /**
         * 最长持续时间
         */
        private Integer maxDuration;

        /**
         * 持续时间标准差
         */
        private BigDecimal durationStandardDeviation;
    }

    /**
     * 成交量异常分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnomalyAnalysis {
        /**
         * 成交量异常检测结果
         */
        private List<VolumeAnomaly> volumeAnomalies;

        /**
         * 成交量激增检测
         */
        private VolumeSurgeDetection volumeSurgeDetection;

        /**
         * 虚假成交量检测
         */
        private FakeVolumeDetection fakeVolumeDetection;

        /**
         * 成交量异常统计
         */
        private VolumeAnomalyStatistics volumeAnomalyStatistics;
    }

    /**
     * 成交量异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnomaly {
        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常时间
         */
        private LocalDateTime anomalyTime;

        /**
         * 异常成交量
         */
        private BigDecimal anomalyVolume;

        /**
         * 正常成交量范围
         */
        private VolumeRange normalVolumeRange;

        /**
         * 偏离程度
         */
        private BigDecimal deviationDegree;

        /**
         * 异常严重性
         */
        private AbnormalSeverity anomalySeverity;
    }

    /**
     * 成交量范围
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 平均值
         */
        private BigDecimal average;
    }

    /**
     * 成交量激增检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeSurgeDetection {
        /**
         * 检测到的成交量激增
         */
        private List<VolumeSurge> volumeSurges;

        /**
         * 激增频率
         */
        private BigDecimal surgeFrequency;

        /**
         * 平均激增倍数
         */
        private BigDecimal averageSurgeMultiplier;

        /**
         * 最大激增倍数
         */
        private BigDecimal maxSurgeMultiplier;
    }

    /**
     * 成交量激增
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeSurge {
        /**
         * 激增时间
         */
        private LocalDateTime surgeTime;

        /**
         * 激增前成交量
         */
        private BigDecimal volumeBeforeSurge;

        /**
         * 激增后成交量
         */
        private BigDecimal volumeAfterSurge;

        /**
         * 激增倍数
         */
        private BigDecimal surgeMultiplier;

        /**
         * 激增持续时间
         */
        private Integer surgeDuration;

        /**
         * 激增原因
         */
        private String surgeReason;
    }

    /**
     * 虚假成交量检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FakeVolumeDetection {
        /**
         * 虚假成交量比例
         */
        private BigDecimal fakeVolumeRatio;

        /**
         * 虚假成交量指标
         */
        private FakeVolumeIndicators fakeVolumeIndicators;

        /**
         * 虚假成交量模式
         */
        private List<FakeVolumePattern> fakeVolumePatterns;

        /**
         * 检测置信度
         */
        private BigDecimal detectionConfidence;
    }

    /**
     * 虚假成交量指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FakeVolumeIndicators {
        /**
         * 回转交易比例
         */
        private BigDecimal roundTripRatio;

        /**
         * 自成交比例
         */
        private BigDecimal selfTradingRatio;

        /**
         * 异常交易时间模式
         */
        private BigDecimal abnormalTimingPattern;

        /**
         * 价格影响异常
         */
        private BigDecimal priceImpactAnomaly;
    }

    /**
     * 虚假成交量模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FakeVolumePattern {
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 模式频率
         */
        private BigDecimal patternFrequency;
    }

    /**
     * 成交量异常统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnomalyStatistics {
        /**
         * 异常事件总数
         */
        private Integer totalAnomalyEvents;

        /**
         * 异常成交量总量
         */
        private BigDecimal totalAnomalyVolume;

        /**
         * 异常成交量占比
         */
        private BigDecimal anomalyVolumeRatio;

        /**
         * 平均异常程度
         */
        private BigDecimal averageAnomalyDegree;
    }

    /**
     * 交易模式异常分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingPatternAnomalyAnalysis {
        /**
         * 异常交易模式
         */
        private List<AbnormalTradingPattern> abnormalTradingPatterns;

        /**
         * 时间模式异常
         */
        private TimePatternAnomaly timePatternAnomaly;

        /**
         * 订单模式异常
         */
        private OrderPatternAnomaly orderPatternAnomaly;

        /**
         * 账户行为异常
         */
        private AccountBehaviorAnomaly accountBehaviorAnomaly;
    }

    /**
     * 异常交易模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingPattern {
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 检测强度
         */
        private BigDecimal detectionStrength;

        /**
         * 模式频率
         */
        private BigDecimal patternFrequency;

        /**
         * 风险等级
         */
        private AbnormalSeverity riskLevel;
    }

    /**
     * 时间模式异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimePatternAnomaly {
        /**
         * 异常时间段
         */
        private List<String> abnormalTimePeriods;

        /**
         * 集中交易时间异常
         */
        private BigDecimal concentratedTradingAnomaly;

        /**
         * 非正常时间交易
         */
        private BigDecimal offHoursTradingAnomaly;

        /**
         * 时间间隔异常
         */
        private BigDecimal timeIntervalAnomaly;
    }

    /**
     * 订单模式异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderPatternAnomaly {
        /**
         * 订单大小异常
         */
        private BigDecimal orderSizeAnomaly;

        /**
         * 订单频率异常
         */
        private BigDecimal orderFrequencyAnomaly;

        /**
         * 订单取消率异常
         */
        private BigDecimal orderCancellationAnomaly;

        /**
         * 订单修改异常
         */
        private BigDecimal orderModificationAnomaly;
    }

    /**
     * 账户行为异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountBehaviorAnomaly {
        /**
         * 多账户协调异常
         */
        private BigDecimal multiAccountCoordinationAnomaly;

        /**
         * 账户活动异常
         */
        private BigDecimal accountActivityAnomaly;

        /**
         * 资金流动异常
         */
        private BigDecimal fundFlowAnomaly;

        /**
         * 交易策略异常
         */
        private BigDecimal tradingStrategyAnomaly;
    }

    /**
     * 市场操纵检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketManipulationDetection {
        /**
         * 操纵类型检测
         */
        private List<ManipulationType> detectedManipulationTypes;

        /**
         * 操纵强度评估
         */
        private ManipulationIntensityAssessment manipulationIntensityAssessment;

        /**
         * 操纵网络分析
         */
        private ManipulationNetworkAnalysis manipulationNetworkAnalysis;

        /**
         * 操纵影响评估
         */
        private ManipulationImpactAssessment manipulationImpactAssessment;
    }

    /**
     * 操纵类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationType {
        /**
         * 操纵类型名称
         */
        private String typeName;

        /**
         * 检测置信度
         */
        private BigDecimal detectionConfidence;

        /**
         * 操纵特征
         */
        private List<String> manipulationCharacteristics;

        /**
         * 操纵证据
         */
        private List<String> manipulationEvidences;
    }

    /**
     * 操纵强度评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationIntensityAssessment {
        /**
         * 整体操纵强度
         */
        private BigDecimal overallIntensity;

        /**
         * 价格操纵强度
         */
        private BigDecimal priceManipulationIntensity;

        /**
         * 成交量操纵强度
         */
        private BigDecimal volumeManipulationIntensity;

        /**
         * 时间操纵强度
         */
        private BigDecimal timeManipulationIntensity;
    }

    /**
     * 操纵网络分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationNetworkAnalysis {
        /**
         * 网络节点数量
         */
        private Integer networkNodeCount;

        /**
         * 网络连接强度
         */
        private BigDecimal networkConnectionStrength;

        /**
         * 核心操纵者识别
         */
        private List<String> coreManipulators;

        /**
         * 网络影响范围
         */
        private BigDecimal networkInfluenceScope;
    }

    /**
     * 操纵影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ManipulationImpactAssessment {
        /**
         * 市场完整性影响
         */
        private BigDecimal marketIntegrityImpact;

        /**
         * 投资者信心影响
         */
        private BigDecimal investorConfidenceImpact;

        /**
         * 价格发现影响
         */
        private BigDecimal priceDiscoveryImpact;

        /**
         * 流动性影响
         */
        private BigDecimal liquidityImpact;
    }

    /**
     * 异常交易影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingImpactAssessment {
        /**
         * 整体影响评分
         */
        private BigDecimal overallImpactScore;

        /**
         * 市场影响
         */
        private MarketImpact marketImpact;

        /**
         * 投资者影响
         */
        private InvestorImpact investorImpact;

        /**
         * 监管影响
         */
        private RegulatoryImpact regulatoryImpact;

        /**
         * 声誉影响
         */
        private ReputationImpact reputationImpact;
    }

    /**
     * 市场影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketImpact {
        /**
         * 价格扭曲程度
         */
        private BigDecimal priceDistortionDegree;

        /**
         * 流动性损害程度
         */
        private BigDecimal liquidityDamageDegree;

        /**
         * 市场效率影响
         */
        private BigDecimal marketEfficiencyImpact;

        /**
         * 波动性影响
         */
        private BigDecimal volatilityImpact;
    }

    /**
     * 投资者影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvestorImpact {
        /**
         * 投资者损失
         */
        private BigDecimal investorLoss;

        /**
         * 信心损害程度
         */
        private BigDecimal confidenceDamageDegree;

        /**
         * 参与度影响
         */
        private BigDecimal participationImpact;

        /**
         * 风险暴露增加
         */
        private BigDecimal riskExposureIncrease;
    }

    /**
     * 监管影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryImpact {
        /**
         * 合规风险等级
         */
        private String complianceRiskLevel;

        /**
         * 监管关注度
         */
        private BigDecimal regulatoryAttention;

        /**
         * 潜在处罚风险
         */
        private BigDecimal potentialPenaltyRisk;

        /**
         * 监管报告要求
         */
        private List<String> regulatoryReportingRequirements;
    }

    /**
     * 声誉影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReputationImpact {
        /**
         * 声誉损害程度
         */
        private BigDecimal reputationDamageDegree;

        /**
         * 媒体关注度
         */
        private BigDecimal mediaAttention;

        /**
         * 公众信任影响
         */
        private BigDecimal publicTrustImpact;

        /**
         * 品牌价值影响
         */
        private BigDecimal brandValueImpact;
    }

    /**
     * 异常交易预警
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingAlert {
        /**
         * 预警ID
         */
        private String alertId;

        /**
         * 预警类型
         */
        private AlertType alertType;

        /**
         * 预警等级
         */
        private AlertLevel alertLevel;

        /**
         * 预警时间
         */
        private LocalDateTime alertTime;

        /**
         * 预警描述
         */
        private String alertDescription;

        /**
         * 触发条件
         */
        private List<String> triggerConditions;

        /**
         * 建议行动
         */
        private List<String> recommendedActions;

        /**
         * 预警状态
         */
        private String alertStatus;

        public AlertLevel getAlertSeverity() {
            return alertLevel;
        }
    }

    /**
     * 预警类型
     */
    public enum AlertType {
        PRICE_MANIPULATION("价格操纵"),
        VOLUME_MANIPULATION("成交量操纵"),
        WASH_TRADING("洗盘交易"),
        SPOOFING("虚假报价"),
        INSIDER_TRADING("内幕交易"),
        MARKET_ABUSE("市场滥用"),
        SUSPICIOUS_ACTIVITY("可疑活动"),
        REGULATORY_BREACH("监管违规"),
        OTHER("其他");

        private final String description;

        AlertType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 预警等级
     */
    public enum AlertLevel {
        CRITICAL("严重", 5),
        HIGH("高", 4),
        MEDIUM("中等", 3),
        LOW("低", 2),
        INFO("信息", 1);

        private final String description;
        private final int level;

        AlertLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 异常交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTradingRecommendation {
        /**
         * 即时行动建议
         */
        private List<String> immediateActions;

        /**
         * 监控建议
         */
        private List<String> monitoringRecommendations;

        /**
         * 风险缓解措施
         */
        private List<String> riskMitigationMeasures;

        /**
         * 合规建议
         */
        private List<String> complianceRecommendations;

        /**
         * 系统改进建议
         */
        private List<String> systemImprovementRecommendations;
    }

    /**
     * 监管合规分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryComplianceAnalysis {
        /**
         * 合规状态
         */
        private String complianceStatus;

        /**
         * 违规风险评估
         */
        private ViolationRiskAssessment violationRiskAssessment;

        /**
         * 监管要求检查
         */
        private List<RegulatoryRequirementCheck> regulatoryRequirementChecks;

        /**
         * 报告义务
         */
        private List<ReportingObligation> reportingObligations;

        /**
         * 合规改进建议
         */
        private List<String> complianceImprovementRecommendations;
    }

    /**
     * 违规风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ViolationRiskAssessment {
        /**
         * 整体违规风险等级
         */
        private String overallViolationRiskLevel;

        /**
         * 违规概率
         */
        private BigDecimal violationProbability;

        /**
         * 潜在处罚严重性
         */
        private String potentialPenaltySeverity;

        /**
         * 违规类型风险
         */
        private Map<String, BigDecimal> violationTypeRisks;

        public String getOverallRiskLevel() {
            return overallViolationRiskLevel;
        }
    }

    /**
     * 监管要求检查
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryRequirementCheck {
        /**
         * 要求名称
         */
        private String requirementName;

        /**
         * 要求描述
         */
        private String requirementDescription;

        /**
         * 合规状态
         */
        private String complianceStatus;

        /**
         * 违规程度
         */
        private BigDecimal violationDegree;

        /**
         * 改进建议
         */
        private List<String> improvementRecommendations;
    }

    /**
     * 报告义务
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReportingObligation {
        /**
         * 报告类型
         */
        private String reportType;

        /**
         * 报告要求
         */
        private String reportRequirement;

        /**
         * 报告截止时间
         */
        private LocalDateTime reportDeadline;

        /**
         * 报告状态
         */
        private String reportStatus;

        /**
         * 报告内容要求
         */
        private List<String> reportContentRequirements;
    }

    /**
     * 获取异常交易摘要
     */
    public String getAbnormalTradingSummary() {
        return String.format("风险等级: %s, 异常交易数: %d, 异常类型: %s",
            riskLevel != null ? riskLevel.getDescription() : "未知",
            abnormalTradingStatistics != null && abnormalTradingStatistics.getTotalAbnormalTrades() != null ? 
                abnormalTradingStatistics.getTotalAbnormalTrades() : 0,
            abnormalTradingDetections != null && !abnormalTradingDetections.isEmpty() ? 
                abnormalTradingDetections.get(0).getAbnormalType().getDescription() : "无");
    }

    /**
     * 检查是否存在严重异常
     */
    public boolean hasCriticalAbnormalities() {
        return riskLevel == AbnormalTradingRiskLevel.CRITICAL;
    }

    /**
     * 检查是否存在市场操纵
     */
    public boolean hasMarketManipulation() {
        return marketManipulationDetection != null && 
               marketManipulationDetection.getDetectedManipulationTypes() != null &&
               !marketManipulationDetection.getDetectedManipulationTypes().isEmpty();
    }

    /**
     * 获取最高优先级预警
     */
    public AbnormalTradingAlert getHighestPriorityAlert() {
        if (abnormalTradingAlerts == null || abnormalTradingAlerts.isEmpty()) {
            return null;
        }
        
        return abnormalTradingAlerts.stream()
            .max((a1, a2) -> Integer.compare(a1.getAlertLevel().getLevel(), a2.getAlertLevel().getLevel()))
            .orElse(null);
    }

    /**
     * 获取合规状态
     */
    public String getComplianceStatus() {
        if (regulatoryComplianceAnalysis == null) {
            return "合规状态未知";
        }
        return regulatoryComplianceAnalysis.getComplianceStatus();
    }

    /**
     * 检查是否需要立即行动
     */
    public boolean requiresImmediateAction() {
        return riskLevel == AbnormalTradingRiskLevel.CRITICAL || 
               riskLevel == AbnormalTradingRiskLevel.HIGH;
    }

    /**
     * 获取主要风险因素
     */
    public String getMainRiskFactors() {
        if (abnormalTradingDetections == null || abnormalTradingDetections.isEmpty()) {
            return "无明显风险因素";
        }
        
        StringBuilder riskFactors = new StringBuilder();
        abnormalTradingDetections.stream()
            .limit(3)
            .forEach(detection -> {
                if (riskFactors.length() > 0) {
                    riskFactors.append(", ");
                }
                riskFactors.append(detection.getAbnormalType().getDescription());
            });
        
        return riskFactors.toString();
    }

    /**
     * 获取影响评估摘要
     */
    public String getImpactAssessmentSummary() {
        if (abnormalTradingImpactAssessment == null) {
            return "影响评估不可用";
        }
        
        BigDecimal impactScore = abnormalTradingImpactAssessment.getOverallImpactScore();
        return String.format("整体影响评分: %s", 
            impactScore != null ? impactScore.toString() : "N/A");
    }

    /**
     * 获取监管风险等级
     */
    public String getRegulatoryRiskLevel() {
        if (regulatoryComplianceAnalysis == null || 
            regulatoryComplianceAnalysis.getViolationRiskAssessment() == null) {
            return "监管风险未评估";
        }
        
        return regulatoryComplianceAnalysis.getViolationRiskAssessment().getOverallViolationRiskLevel();
    }

    /**
     * 检查是否有虚假交易
     */
    public boolean hasFakeTrading() {
        if (volumeAnomalyAnalysis == null || 
            volumeAnomalyAnalysis.getFakeVolumeDetection() == null) {
            return false;
        }
        
        BigDecimal fakeVolumeRatio = volumeAnomalyAnalysis.getFakeVolumeDetection().getFakeVolumeRatio();
        return fakeVolumeRatio != null && fakeVolumeRatio.compareTo(BigDecimal.valueOf(0.1)) > 0;
    }

    /**
     * 获取操纵强度评估
     */
    public String getManipulationIntensityAssessment() {
        if (marketManipulationDetection == null || 
            marketManipulationDetection.getManipulationIntensityAssessment() == null) {
            return "操纵强度未评估";
        }
        
        BigDecimal intensity = marketManipulationDetection.getManipulationIntensityAssessment().getOverallIntensity();
        if (intensity == null) {
            return "操纵强度数据不可用";
        }
        
        if (intensity.compareTo(BigDecimal.valueOf(0.8)) >= 0) {
            return "高强度操纵";
        } else if (intensity.compareTo(BigDecimal.valueOf(0.5)) >= 0) {
            return "中等强度操纵";
        } else if (intensity.compareTo(BigDecimal.valueOf(0.2)) >= 0) {
            return "低强度操纵";
        } else {
            return "无明显操纵";
        }
    }
}