package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 24小时价格变动统计响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Ticker24hrResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 加权平均价格
     */
    private BigDecimal weightedAvgPrice;
    
    /**
     * 前一个收盘价
     */
    private BigDecimal prevClosePrice;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 最新成交量
     */
    private BigDecimal lastQty;
    
    /**
     * 最佳买价
     */
    private BigDecimal bidPrice;
    
    /**
     * 最佳买量
     */
    private BigDecimal bidQty;
    
    /**
     * 最佳卖价
     */
    private BigDecimal askPrice;
    
    /**
     * 最佳卖量
     */
    private BigDecimal askQty;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume;
    
    /**
     * 24小时交易额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 统计开始时间
     */
    private Long openTime;
    
    /**
     * 统计结束时间
     */
    private Long closeTime;
    
    /**
     * 首次交易ID
     */
    private Long firstId;
    
    /**
     * 最后交易ID
     */
    private Long lastId;
    
    /**
     * 24小时交易次数
     */
    private Long count;
    
    /**
     * 统计开始时间（格式化）
     */
    private LocalDateTime openDateTime;
    
    /**
     * 统计结束时间（格式化）
     */
    private LocalDateTime closeDateTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 市值
     */
    private BigDecimal marketCap;
    
    /**
     * 市值排名
     */
    private Integer marketCapRank;
    
    /**
     * 换手率
     */
    private BigDecimal turnoverRate;
    
    /**
     * 振幅
     */
    private BigDecimal amplitude;
}