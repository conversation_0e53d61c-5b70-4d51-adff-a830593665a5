package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格行为分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceBehaviorAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 价格行为评分 (0-100)
     */
    private BigDecimal behaviorScore;

    /**
     * 价格行为类型
     */
    private PriceBehaviorType behaviorType;

    /**
     * 趋势分析
     */
    private TrendAnalysis trendAnalysis;

    /**
     * 波动性分析
     */
    private VolatilityAnalysis volatilityAnalysis;

    /**
     * 动量分析
     */
    private MomentumAnalysis momentumAnalysis;

    /**
     * 支撑阻力分析
     */
    private SupportResistanceAnalysis supportResistanceAnalysis;

    /**
     * 价格模式识别
     */
    private PricePatternRecognition pricePatternRecognition;

    /**
     * 异常行为检测
     */
    private AnomalousBehaviorDetection anomalousBehaviorDetection;

    /**
     * 市场情绪分析
     */
    private MarketSentimentAnalysis marketSentimentAnalysis;

    /**
     * 价格预测
     */
    private PricePrediction pricePrediction;

    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;

    /**
     * 交易信号
     */
    private List<TradingSignal> tradingSignals;

    /**
     * 历史对比
     */
    private HistoricalComparison historicalComparison;

    /**
     * 价格行为类型
     */
    public enum PriceBehaviorType {
        TRENDING_UP("上升趋势", "价格呈现明显上升趋势"),
        TRENDING_DOWN("下降趋势", "价格呈现明显下降趋势"),
        SIDEWAYS("横盘整理", "价格在区间内震荡"),
        VOLATILE("高波动", "价格波动剧烈"),
        STABLE("稳定", "价格相对稳定"),
        BREAKOUT("突破", "价格突破关键位置"),
        REVERSAL("反转", "价格出现反转信号"),
        CONSOLIDATION("整固", "价格进入整固阶段");

        private final String description;
        private final String detail;

        PriceBehaviorType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        /**
         * 短期趋势方向
         */
        private String shortTermTrend;

        /**
         * 中期趋势方向
         */
        private String mediumTermTrend;

        /**
         * 长期趋势方向
         */
        private String longTermTrend;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续性
         */
        private BigDecimal trendPersistence;

        /**
         * 趋势变化概率
         */
        private BigDecimal trendChangeProbability;

        /**
         * 趋势线斜率
         */
        private BigDecimal trendLineSlope;

        /**
         * 趋势确认指标
         */
        private List<String> trendConfirmationIndicators;

        public String getShortTermTrend() {
            return shortTermTrend;
        }

        public String getMediumTermTrend() {
            return mediumTermTrend;
        }

        public String getLongTermTrend() {
            return longTermTrend;
        }
    }

    /**
     * 波动性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityAnalysis {
        /**
         * 历史波动率
         */
        private BigDecimal historicalVolatility;

        /**
         * 隐含波动率
         */
        private BigDecimal impliedVolatility;

        /**
         * 波动率等级
         */
        private String volatilityLevel;

        /**
         * 波动率趋势
         */
        private String volatilityTrend;

        /**
         * 波动率聚集性
         */
        private BigDecimal volatilityClustering;

        /**
         * 波动率不对称性
         */
        private BigDecimal volatilityAsymmetry;

        /**
         * 波动率预测
         */
        private BigDecimal volatilityForecast;

        /**
         * 波动率分解
         */
        private Map<String, BigDecimal> volatilityDecomposition;
    }

    /**
     * 动量分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MomentumAnalysis {
        /**
         * 价格动量
         */
        private BigDecimal priceMomentum;

        /**
         * 成交量动量
         */
        private BigDecimal volumeMomentum;

        /**
         * 动量强度
         */
        private BigDecimal momentumStrength;

        /**
         * 动量方向
         */
        private String momentumDirection;

        /**
         * 动量持续性
         */
        private BigDecimal momentumPersistence;

        /**
         * 动量背离
         */
        private String momentumDivergence;

        /**
         * 动量指标
         */
        private Map<String, BigDecimal> momentumIndicators;

        /**
         * 动量信号
         */
        private List<String> momentumSignals;
    }

    /**
     * 支撑阻力分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupportResistanceAnalysis {
        /**
         * 关键支撑位
         */
        private List<BigDecimal> keySupportLevels;

        /**
         * 关键阻力位
         */
        private List<BigDecimal> keyResistanceLevels;

        /**
         * 当前支撑强度
         */
        private BigDecimal currentSupportStrength;

        /**
         * 当前阻力强度
         */
        private BigDecimal currentResistanceStrength;

        /**
         * 突破概率
         */
        private BigDecimal breakoutProbability;

        /**
         * 回调概率
         */
        private BigDecimal pullbackProbability;

        /**
         * 支撑阻力转换
         */
        private List<String> supportResistanceFlips;

        /**
         * 心理价位
         */
        private List<BigDecimal> psychologicalLevels;
    }

    /**
     * 价格模式识别
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PricePatternRecognition {
        /**
         * 识别到的模式
         */
        private List<String> identifiedPatterns;

        /**
         * 模式完成度
         */
        private Map<String, BigDecimal> patternCompleteness;

        /**
         * 模式可靠性
         */
        private Map<String, BigDecimal> patternReliability;

        /**
         * 预期目标价位
         */
        private Map<String, BigDecimal> expectedTargets;

        /**
         * 模式失效价位
         */
        private Map<String, BigDecimal> invalidationLevels;

        /**
         * 经典形态
         */
        private List<String> classicalFormations;

        /**
         * 蜡烛图模式
         */
        private List<String> candlestickPatterns;
    }

    /**
     * 异常行为检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalousBehaviorDetection {
        /**
         * 异常事件列表
         */
        private List<String> anomalousEvents;

        /**
         * 异常严重程度
         */
        private Map<String, String> anomalySeverity;

        /**
         * 价格跳空
         */
        private List<PriceGap> priceGaps;

        /**
         * 异常交易量
         */
        private List<String> abnormalVolume;

        /**
         * 闪崩风险
         */
        private BigDecimal flashCrashRisk;

        /**
         * 操纵嫌疑
         */
        private List<String> manipulationSuspicion;

        /**
         * 异常持续时间
         */
        private Map<String, String> anomalyDuration;

        public List<String> getAnomalousEvents() {
            return anomalousEvents;
        }
    }

    /**
     * 价格跳空
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceGap {
        /**
         * 跳空类型
         */
        private String gapType;

        /**
         * 跳空大小
         */
        private BigDecimal gapSize;

        /**
         * 跳空时间
         */
        private LocalDateTime gapTime;

        /**
         * 填补概率
         */
        private BigDecimal fillProbability;

        /**
         * 跳空意义
         */
        private String gapSignificance;
    }

    /**
     * 市场情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketSentimentAnalysis {
        /**
         * 整体情绪
         */
        private String overallSentiment;

        /**
         * 情绪强度
         */
        private BigDecimal sentimentIntensity;

        /**
         * 恐慌贪婪指数
         */
        private BigDecimal fearGreedIndex;

        /**
         * 投资者信心
         */
        private BigDecimal investorConfidence;

        /**
         * 情绪变化趋势
         */
        private String sentimentTrend;

        /**
         * 情绪极值警告
         */
        private List<String> sentimentExtremeWarnings;

        /**
         * 社交媒体情绪
         */
        private BigDecimal socialMediaSentiment;
    }

    /**
     * 价格预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PricePrediction {
        /**
         * 短期预测（1小时）
         */
        private BigDecimal shortTermPrediction;

        /**
         * 中期预测（24小时）
         */
        private BigDecimal mediumTermPrediction;

        /**
         * 长期预测（7天）
         */
        private BigDecimal longTermPrediction;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 预测区间
         */
        private Map<String, BigDecimal> predictionRange;

        /**
         * 关键影响因素
         */
        private List<String> keyInfluencingFactors;

        /**
         * 预测模型
         */
        private String predictionModel;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;
    }

    /**
     * 风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 价格风险
         */
        private BigDecimal priceRisk;

        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;

        /**
         * 波动性风险
         */
        private BigDecimal volatilityRisk;

        /**
         * 最大回撤风险
         */
        private BigDecimal maxDrawdownRisk;

        /**
         * VaR (风险价值)
         */
        private BigDecimal valueAtRisk;

        /**
         * 压力测试结果
         */
        private Map<String, BigDecimal> stressTestResults;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationSuggestions;

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }
    }

    /**
     * 交易信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 信号方向
         */
        private String signalDirection;

        /**
         * 信号时间
         */
        private LocalDateTime signalTime;

        /**
         * 目标价位
         */
        private BigDecimal targetPrice;

        /**
         * 止损价位
         */
        private BigDecimal stopLossPrice;

        /**
         * 信号有效期
         */
        private String signalValidity;

        /**
         * 信号描述
         */
        private String signalDescription;

        public BigDecimal getSignalStrength() {
            return signalStrength;
        }

        public String getSignalDirection() {
            return signalDirection;
        }

        public String getSignalDescription() {
            return signalDescription;
        }
    }

    /**
     * 历史对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalComparison {
        /**
         * 相似历史时期
         */
        private List<String> similarHistoricalPeriods;

        /**
         * 历史模式匹配度
         */
        private BigDecimal historicalPatternMatch;

        /**
         * 历史表现对比
         */
        private Map<String, BigDecimal> historicalPerformanceComparison;

        /**
         * 历史波动率对比
         */
        private BigDecimal historicalVolatilityComparison;

        /**
         * 历史趋势对比
         */
        private String historicalTrendComparison;

        /**
         * 历史经验教训
         */
        private List<String> historicalLessons;
    }

    /**
     * 获取价格行为摘要
     */
    public String getBehaviorSummary() {
        return String.format("价格行为: %s (评分: %s) - %s",
            behaviorType != null ? behaviorType.getDescription() : "未知",
            behaviorScore != null ? behaviorScore.toString() : "N/A",
            behaviorType != null ? behaviorType.getDetail() : "无详细信息");
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (tradingSignals == null || tradingSignals.isEmpty()) {
            return "暂无交易信号";
        }
        
        TradingSignal strongestSignal = tradingSignals.stream()
            .max((s1, s2) -> s1.getSignalStrength().compareTo(s2.getSignalStrength()))
            .orElse(null);
        
        if (strongestSignal != null) {
            return String.format("%s信号 - %s (强度: %s)",
                strongestSignal.getSignalDirection(),
                strongestSignal.getSignalDescription(),
                strongestSignal.getSignalStrength());
        }
        
        return "无明确交易信号";
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (riskAssessment == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = riskAssessment.getOverallRiskLevel();
        if (riskLevel == null) {
            return "风险等级未知";
        }
        
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：建议谨慎交易，严格控制仓位";
            case "MEDIUM":
                return "中等风险：注意风险管理，适度交易";
            case "LOW":
                return "低风险：可正常交易，但仍需关注市场变化";
            default:
                return "风险等级: " + riskLevel;
        }
    }

    /**
     * 检查是否有异常行为
     */
    public boolean hasAnomalousBehavior() {
        return anomalousBehaviorDetection != null && 
               anomalousBehaviorDetection.getAnomalousEvents() != null &&
               !anomalousBehaviorDetection.getAnomalousEvents().isEmpty();
    }

    /**
     * 获取趋势状态
     */
    public String getTrendStatus() {
        if (trendAnalysis == null) {
            return "趋势分析不可用";
        }
        
        return String.format("短期: %s, 中期: %s, 长期: %s",
            trendAnalysis.getShortTermTrend() != null ? trendAnalysis.getShortTermTrend() : "未知",
            trendAnalysis.getMediumTermTrend() != null ? trendAnalysis.getMediumTermTrend() : "未知",
            trendAnalysis.getLongTermTrend() != null ? trendAnalysis.getLongTermTrend() : "未知");
    }
}