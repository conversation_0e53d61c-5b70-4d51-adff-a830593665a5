package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawRecordResponse {
    
    /**
     * 提现记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 提现货币
     */
    private String currency;
    
    /**
     * 提现金额
     */
    private BigDecimal amount;
    
    /**
     * 提现地址
     */
    private String address;
    
    /**
     * 交易哈希
     */
    private String txHash;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 提现状态 (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际提现金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 标签/备忘录
     */
    private String tag;
    
    /**
     * 审核状态
     */
    private String auditStatus;
    
    /**
     * 审核备注
     */
    private String auditRemark;
    
    /**
     * 审核人
     */
    private String auditor;
    
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    
    /**
     * 风险等级
     */
    private String riskLevel;
    
    /**
     * 确认数
     */
    private Integer confirmations;
    
    /**
     * 区块高度
     */
    private Long blockHeight;
    
    /**
     * 提现类型 (NORMAL, FAST)
     */
    private String withdrawType;
    
    /**
     * 钱包类型
     */
    private String walletType;
    
    /**
     * 提现前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 提现后余额
     */
    private BigDecimal balanceAfter;
}