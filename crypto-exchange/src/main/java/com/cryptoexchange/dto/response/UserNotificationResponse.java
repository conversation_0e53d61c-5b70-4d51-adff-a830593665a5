package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户通知响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户通知响应")
public class UserNotificationResponse {

    @Schema(description = "通知ID", example = "123456")
    private Long notificationId;

    @Schema(description = "通知类型", example = "TRADE")
    private String type;

    @Schema(description = "通知类型名称", example = "交易通知")
    private String typeName;

    @Schema(description = "通知标题", example = "交易成功")
    private String title;

    @Schema(description = "通知内容", example = "您的BTC/USDT买入订单已成功执行")
    private String content;

    @Schema(description = "通知级别", example = "INFO")
    private String level;

    @Schema(description = "是否已读", example = "false")
    private Boolean isRead;

    @Schema(description = "是否重要", example = "false")
    private Boolean isImportant;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "读取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime readTime;

    @Schema(description = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    @Schema(description = "发送渠道", example = "SYSTEM")
    private String channel;

    @Schema(description = "关联业务ID", example = "order_123456")
    private String relatedId;

    @Schema(description = "关联业务类型", example = "ORDER")
    private String relatedType;

    @Schema(description = "扩展数据")
    private Map<String, Object> extraData;

    @Schema(description = "操作按钮")
    private String actionButton;

    @Schema(description = "操作链接")
    private String actionUrl;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "颜色")
    private String color;

    public UserNotificationResponse() {}

    public UserNotificationResponse(Long notificationId, String type, String title, 
                                  String content, String level, Boolean isRead, 
                                  LocalDateTime createTime) {
        this.notificationId = notificationId;
        this.type = type;
        this.title = title;
        this.content = content;
        this.level = level;
        this.isRead = isRead;
        this.createTime = createTime;
        this.isImportant = false;
    }
}