package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 网络配置响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NetworkConfigResponse {
    
    /**
     * 网络ID
     */
    private Long id;
    
    /**
     * 网络代码
     */
    private String network;
    
    /**
     * 网络名称
     */
    private String networkName;
    
    /**
     * 网络全称
     */
    private String networkFullName;
    
    /**
     * 网络类型 (MAINNET, TESTNET)
     */
    private String networkType;
    
    /**
     * 网络状态 (ACTIVE, INACTIVE, MAINTENANCE)
     */
    private String status;
    
    /**
     * 是否支持充值
     */
    private Boolean depositEnabled;
    
    /**
     * 是否支持提现
     */
    private Boolean withdrawEnabled;
    
    /**
     * 支持的货币列表
     */
    private List<CurrencyConfig> supportedCurrencies;
    
    /**
     * 网络确认数要求
     */
    private Integer confirmations;
    
    /**
     * 快速确认数
     */
    private Integer fastConfirmations;
    
    /**
     * 网络手续费
     */
    private BigDecimal networkFee;
    
    /**
     * 网络手续费货币
     */
    private String networkFeeCurrency;
    
    /**
     * 最小网络手续费
     */
    private BigDecimal minNetworkFee;
    
    /**
     * 最大网络手续费
     */
    private BigDecimal maxNetworkFee;
    
    /**
     * 是否支持动态手续费
     */
    private Boolean dynamicFeeEnabled;
    
    /**
     * 区块时间（秒）
     */
    private Integer blockTime;
    
    /**
     * 区块浏览器URL
     */
    private String explorerUrl;
    
    /**
     * RPC节点URL
     */
    private String rpcUrl;
    
    /**
     * WebSocket URL
     */
    private String wsUrl;
    
    /**
     * 网络图标URL
     */
    private String iconUrl;
    
    /**
     * 网络描述
     */
    private String description;
    
    /**
     * 是否需要标签/备忘录
     */
    private Boolean requiresTag;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 地址格式正则表达式
     */
    private String addressRegex;
    
    /**
     * 标签格式正则表达式
     */
    private String tagRegex;
    
    /**
     * 网络精度
     */
    private Integer precision;
    
    /**
     * 是否为EVM兼容
     */
    private Boolean evmCompatible;
    
    /**
     * 链ID（EVM网络）
     */
    private Long chainId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 排序权重
     */
    private Integer sortWeight;
    
    /**
     * 维护信息
     */
    private MaintenanceInfo maintenanceInfo;
    
    /**
     * 货币配置内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CurrencyConfig {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 合约地址（代币）
         */
        private String contractAddress;
        
        /**
         * 代币精度
         */
        private Integer tokenDecimals;
        
        /**
         * 最小充值金额
         */
        private BigDecimal minDepositAmount;
        
        /**
         * 最小提现金额
         */
        private BigDecimal minWithdrawAmount;
        
        /**
         * 最大提现金额
         */
        private BigDecimal maxWithdrawAmount;
        
        /**
         * 提现手续费
         */
        private BigDecimal withdrawFee;
        
        /**
         * 是否支持充值
         */
        private Boolean depositEnabled;
        
        /**
         * 是否支持提现
         */
        private Boolean withdrawEnabled;
        
        /**
         * 货币状态
         */
        private String status;
    }
    
    /**
     * 维护信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MaintenanceInfo {
        /**
         * 是否在维护中
         */
        private Boolean inMaintenance;
        
        /**
         * 维护开始时间
         */
        private LocalDateTime maintenanceStartTime;
        
        /**
         * 维护结束时间
         */
        private LocalDateTime maintenanceEndTime;
        
        /**
         * 维护原因
         */
        private String maintenanceReason;
        
        /**
         * 维护公告
         */
        private String maintenanceNotice;
    }
}