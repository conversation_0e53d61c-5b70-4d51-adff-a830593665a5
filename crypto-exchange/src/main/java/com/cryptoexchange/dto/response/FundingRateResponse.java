package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 资金费率响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class FundingRateResponse {

    /**
     * 资金费率ID
     */
    private Long id;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 资金费率
     */
    private BigDecimal fundingRate;

    /**
     * 预测资金费率
     */
    private BigDecimal predictedFundingRate;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 指数价格
     */
    private BigDecimal indexPrice;

    /**
     * 溢价指数
     */
    private BigDecimal premiumIndex;

    /**
     * 利率
     */
    private BigDecimal interestRate;

    /**
     * 资金费用收取时间
     */
    private LocalDateTime fundingTime;

    /**
     * 下次资金费用时间
     */
    private LocalDateTime nextFundingTime;

    /**
     * 资金费率计算周期（小时）
     */
    private Integer fundingInterval;

    /**
     * 最大资金费率
     */
    private BigDecimal maxFundingRate;

    /**
     * 最小资金费率
     */
    private BigDecimal minFundingRate;

    /**
     * 8小时平均资金费率
     */
    private BigDecimal avgFundingRate8h;

    /**
     * 24小时平均资金费率
     */
    private BigDecimal avgFundingRate24h;

    /**
     * 7天平均资金费率
     */
    private BigDecimal avgFundingRate7d;

    /**
     * 30天平均资金费率
     */
    private BigDecimal avgFundingRate30d;

    /**
     * 年化资金费率
     */
    private BigDecimal annualizedFundingRate;

    /**
     * 资金费率状态：1-正常，2-异常
     */
    private Integer status;

    /**
     * 计算方式：1-标准，2-自定义
     */
    private Integer calculationMethod;

    /**
     * 基础利率
     */
    private BigDecimal baseRate;

    /**
     * 溢价权重
     */
    private BigDecimal premiumWeight;

    /**
     * 利率权重
     */
    private BigDecimal interestWeight;

    /**
     * 阻尼系数
     */
    private BigDecimal dampingCoefficient;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}