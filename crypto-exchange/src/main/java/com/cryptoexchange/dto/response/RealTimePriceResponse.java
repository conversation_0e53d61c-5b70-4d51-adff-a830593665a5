package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 实时价格响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "实时价格响应")
public class RealTimePriceResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "当前价格")
    private BigDecimal price;

    @Schema(description = "最新成交价")
    private BigDecimal lastPrice;

    @Schema(description = "买一价")
    private BigDecimal bidPrice;

    @Schema(description = "卖一价")
    private BigDecimal askPrice;

    @Schema(description = "买一量")
    private BigDecimal bidQuantity;

    @Schema(description = "卖一量")
    private BigDecimal askQuantity;

    @Schema(description = "24小时开盘价")
    private BigDecimal openPrice;

    @Schema(description = "24小时最高价")
    private BigDecimal highPrice;

    @Schema(description = "24小时最低价")
    private BigDecimal lowPrice;

    @Schema(description = "24小时收盘价")
    private BigDecimal closePrice;

    @Schema(description = "前收盘价")
    private BigDecimal prevClosePrice;

    @Schema(description = "价格变化")
    private BigDecimal priceChange;

    @Schema(description = "价格变化百分比")
    private BigDecimal priceChangePercent;

    @Schema(description = "24小时成交量")
    private BigDecimal volume;

    @Schema(description = "24小时成交额")
    private BigDecimal amount;

    @Schema(description = "24小时成交笔数")
    private Long tradeCount;

    @Schema(description = "加权平均价格")
    private BigDecimal weightedAvgPrice;

    @Schema(description = "成交量加权平均价格")
    private BigDecimal vwap;

    @Schema(description = "买卖价差")
    private BigDecimal bidAskSpread;

    @Schema(description = "买卖价差百分比")
    private BigDecimal bidAskSpreadPercent;

    @Schema(description = "市值")
    private BigDecimal marketCap;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "市值排名")
    private Integer marketCapRank;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "振幅")
    private BigDecimal amplitude;

    @Schema(description = "换手率")
    private BigDecimal turnoverRate;

    @Schema(description = "价格趋势")
    private String priceTrend;

    @Schema(description = "趋势强度")
    private BigDecimal trendStrength;

    @Schema(description = "价格方向")
    private String priceDirection;

    @Schema(description = "最后交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTradeTime;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据时间戳")
    private Long timestamp;

    @Schema(description = "数据延迟(毫秒)")
    private Long dataLatency;

    @Schema(description = "是否实时数据")
    private Boolean isRealTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "数据质量评分")
    private BigDecimal dataQuality;

    @Schema(description = "订单簿深度")
    private OrderBookDepth orderBookDepth;

    @Schema(description = "最近交易")
    private List<RecentTrade> recentTrades;

    @Schema(description = "技术指标")
    private TechnicalIndicators technicalIndicators;

    @Schema(description = "市场情绪")
    private MarketSentiment marketSentiment;

    @Schema(description = "资金流向")
    private MoneyFlow moneyFlow;

    @Schema(description = "大单监控")
    private LargeOrderMonitor largeOrderMonitor;

    @Schema(description = "价格预警")
    private List<PriceAlert> priceAlerts;

    @Schema(description = "交易状态")
    private String tradingStatus;

    @Schema(description = "是否暂停交易")
    private Boolean tradingSuspended;

    @Schema(description = "暂停原因")
    private String suspensionReason;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "风险评分")
    private BigDecimal riskScore;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "关注度")
    private BigDecimal attentionScore;

    @Schema(description = "社交媒体提及次数")
    private Integer socialMentions;

    @Schema(description = "新闻报道数量")
    private Integer newsCount;

    @Schema(description = "分析师评级")
    private String analystRating;

    @Schema(description = "机构持仓比例")
    private BigDecimal institutionalHolding;

    @Schema(description = "散户持仓比例")
    private BigDecimal retailHolding;

    @Schema(description = "鲸鱼地址数量")
    private Integer whaleAddressCount;

    @Schema(description = "活跃地址数量")
    private Integer activeAddressCount;

    @Schema(description = "链上交易量")
    private BigDecimal onChainVolume;

    @Schema(description = "链上交易笔数")
    private Long onChainTransactions;

    @Schema(description = "网络费用")
    private BigDecimal networkFee;

    @Schema(description = "确认时间")
    private Integer confirmationTime;

    @Schema(description = "网络拥堵程度")
    private String networkCongestion;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 订单簿深度
     */
    @Data
    @Schema(description = "订单簿深度")
    public static class OrderBookDepth {
        
        @Schema(description = "买单深度")
        private List<PriceLevel> bids;
        
        @Schema(description = "卖单深度")
        private List<PriceLevel> asks;
        
        @Schema(description = "总买单量")
        private BigDecimal totalBidVolume;
        
        @Schema(description = "总卖单量")
        private BigDecimal totalAskVolume;
        
        @Schema(description = "买卖比例")
        private BigDecimal bidAskRatio;
        
        @Schema(description = "深度质量评分")
        private BigDecimal depthQuality;
    }

    /**
     * 价格档位
     */
    @Data
    @Schema(description = "价格档位")
    public static class PriceLevel {
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量")
        private BigDecimal quantity;
        
        @Schema(description = "累计数量")
        private BigDecimal cumulativeQuantity;
        
        @Schema(description = "订单数量")
        private Integer orderCount;
    }

    /**
     * 最近交易
     */
    @Data
    @Schema(description = "最近交易")
    public static class RecentTrade {
        
        @Schema(description = "交易ID")
        private String tradeId;
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量")
        private BigDecimal quantity;
        
        @Schema(description = "成交金额")
        private BigDecimal amount;
        
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeTime;
        
        @Schema(description = "是否为买单成交")
        private Boolean isBuyerMaker;
        
        @Schema(description = "交易方向")
        private String side;
        
        @Schema(description = "是否为大单")
        private Boolean isLargeOrder;
    }

    /**
     * 技术指标
     */
    @Data
    @Schema(description = "技术指标")
    public static class TechnicalIndicators {
        
        @Schema(description = "RSI指标")
        private BigDecimal rsi;
        
        @Schema(description = "MACD指标")
        private BigDecimal macd;
        
        @Schema(description = "MACD信号线")
        private BigDecimal macdSignal;
        
        @Schema(description = "布林带上轨")
        private BigDecimal bollingerUpper;
        
        @Schema(description = "布林带下轨")
        private BigDecimal bollingerLower;
        
        @Schema(description = "移动平均线MA20")
        private BigDecimal ma20;
        
        @Schema(description = "移动平均线MA50")
        private BigDecimal ma50;
        
        @Schema(description = "KDJ-K值")
        private BigDecimal kdjK;
        
        @Schema(description = "KDJ-D值")
        private BigDecimal kdjD;
        
        @Schema(description = "KDJ-J值")
        private BigDecimal kdjJ;
        
        @Schema(description = "威廉指标")
        private BigDecimal williamsR;
        
        @Schema(description = "随机指标")
        private BigDecimal stochastic;
    }

    /**
     * 市场情绪
     */
    @Data
    @Schema(description = "市场情绪")
    public static class MarketSentiment {
        
        @Schema(description = "恐慌贪婪指数")
        private BigDecimal fearGreedIndex;
        
        @Schema(description = "市场情绪")
        private String sentiment;
        
        @Schema(description = "情绪强度")
        private BigDecimal sentimentStrength;
        
        @Schema(description = "看涨比例")
        private BigDecimal bullishRatio;
        
        @Schema(description = "看跌比例")
        private BigDecimal bearishRatio;
        
        @Schema(description = "中性比例")
        private BigDecimal neutralRatio;
    }

    /**
     * 资金流向
     */
    @Data
    @Schema(description = "资金流向")
    public static class MoneyFlow {
        
        @Schema(description = "净资金流入")
        private BigDecimal netFlow;
        
        @Schema(description = "主力资金流入")
        private BigDecimal mainFlow;
        
        @Schema(description = "散户资金流入")
        private BigDecimal retailFlow;
        
        @Schema(description = "资金流入强度")
        private BigDecimal flowIntensity;
        
        @Schema(description = "资金流向趋势")
        private String flowTrend;
    }

    /**
     * 大单监控
     */
    @Data
    @Schema(description = "大单监控")
    public static class LargeOrderMonitor {
        
        @Schema(description = "大单买入量")
        private BigDecimal largeBuyVolume;
        
        @Schema(description = "大单卖出量")
        private BigDecimal largeSellVolume;
        
        @Schema(description = "大单净买入")
        private BigDecimal largeNetBuy;
        
        @Schema(description = "大单占比")
        private BigDecimal largeOrderRatio;
        
        @Schema(description = "最近大单")
        private List<LargeOrder> recentLargeOrders;
    }

    /**
     * 大单交易
     */
    @Data
    @Schema(description = "大单交易")
    public static class LargeOrder {
        
        @Schema(description = "交易ID")
        private String tradeId;
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量")
        private BigDecimal quantity;
        
        @Schema(description = "成交金额")
        private BigDecimal amount;
        
        @Schema(description = "交易方向")
        private String side;
        
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeTime;
        
        @Schema(description = "市场影响")
        private BigDecimal marketImpact;
    }

    /**
     * 价格预警
     */
    @Data
    @Schema(description = "价格预警")
    public static class PriceAlert {
        
        @Schema(description = "预警ID")
        private String alertId;
        
        @Schema(description = "预警类型")
        private String alertType;
        
        @Schema(description = "预警级别")
        private String alertLevel;
        
        @Schema(description = "预警价格")
        private BigDecimal alertPrice;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "偏差百分比")
        private BigDecimal deviationPercent;
        
        @Schema(description = "预警消息")
        private String alertMessage;
        
        @Schema(description = "触发时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime triggerTime;
        
        @Schema(description = "是否已处理")
        private Boolean isProcessed;
    }
}