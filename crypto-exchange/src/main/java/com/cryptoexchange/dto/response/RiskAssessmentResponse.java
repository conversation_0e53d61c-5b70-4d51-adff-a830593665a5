package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 风险评估响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "风险评估响应")
public class RiskAssessmentResponse {

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "风险分数")
    private BigDecimal riskScore;

    @Schema(description = "是否允许交易")
    private Boolean allowTrading;

    @Schema(description = "风险描述")
    private String riskDescription;

    @Schema(description = "建议操作")
    private String recommendation;

    @Schema(description = "评估时间")
    private LocalDateTime assessmentTime;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "风险因子")
    private String riskFactors;

    @Schema(description = "最大允许交易金额")
    private BigDecimal maxTradeAmount;

    @Schema(description = "当前仓位风险")
    private BigDecimal positionRisk;
}