package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户交易概览响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户交易概览响应")
public class UserTradingOverviewResponse {

    @Schema(description = "总交易次数", example = "1250")
    private Integer totalTradeCount;

    @Schema(description = "总交易量（USDT）")
    private BigDecimal totalTradeVolume;

    @Schema(description = "总手续费（USDT）")
    private BigDecimal totalFees;

    @Schema(description = "总盈亏（USDT）")
    private BigDecimal totalPnl;

    @Schema(description = "胜率百分比")
    private BigDecimal winRate;

    @Schema(description = "平均盈利")
    private BigDecimal avgProfit;

    @Schema(description = "平均亏损")
    private BigDecimal avgLoss;

    @Schema(description = "最大盈利")
    private BigDecimal maxProfit;

    @Schema(description = "最大亏损")
    private BigDecimal maxLoss;

    @Schema(description = "今日交易统计")
    private DailyTradingStats todayStats;

    @Schema(description = "本周交易统计")
    private WeeklyTradingStats weeklyStats;

    @Schema(description = "本月交易统计")
    private MonthlyTradingStats monthlyStats;

    @Schema(description = "交易类型分布")
    private List<TradingTypeDistribution> tradingTypeDistribution;

    @Schema(description = "最近交易记录")
    private List<RecentTrade> recentTrades;

    @Schema(description = "交易偏好")
    private TradingPreferences preferences;

    @Data
    @Schema(description = "今日交易统计")
    public static class DailyTradingStats {
        @Schema(description = "交易次数", example = "15")
        private Integer tradeCount;
        
        @Schema(description = "交易量（USDT）")
        private BigDecimal tradeVolume;
        
        @Schema(description = "盈亏（USDT）")
        private BigDecimal pnl;
        
        @Schema(description = "手续费（USDT）")
        private BigDecimal fees;
        
        @Schema(description = "胜率百分比")
        private BigDecimal winRate;
    }

    @Data
    @Schema(description = "本周交易统计")
    public static class WeeklyTradingStats {
        @Schema(description = "交易次数", example = "85")
        private Integer tradeCount;
        
        @Schema(description = "交易量（USDT）")
        private BigDecimal tradeVolume;
        
        @Schema(description = "盈亏（USDT）")
        private BigDecimal pnl;
        
        @Schema(description = "手续费（USDT）")
        private BigDecimal fees;
        
        @Schema(description = "胜率百分比")
        private BigDecimal winRate;
    }

    @Data
    @Schema(description = "本月交易统计")
    public static class MonthlyTradingStats {
        @Schema(description = "交易次数", example = "320")
        private Integer tradeCount;
        
        @Schema(description = "交易量（USDT）")
        private BigDecimal tradeVolume;
        
        @Schema(description = "盈亏（USDT）")
        private BigDecimal pnl;
        
        @Schema(description = "手续费（USDT）")
        private BigDecimal fees;
        
        @Schema(description = "胜率百分比")
        private BigDecimal winRate;
    }

    @Data
    @Schema(description = "交易类型分布")
    public static class TradingTypeDistribution {
        @Schema(description = "交易类型", example = "SPOT")
        private String tradeType;
        
        @Schema(description = "交易类型名称", example = "现货交易")
        private String tradeTypeName;
        
        @Schema(description = "交易次数", example = "800")
        private Integer tradeCount;
        
        @Schema(description = "交易量（USDT）")
        private BigDecimal tradeVolume;
        
        @Schema(description = "占比百分比")
        private BigDecimal percentage;
    }

    @Data
    @Schema(description = "最近交易记录")
    public static class RecentTrade {
        @Schema(description = "交易对", example = "BTC/USDT")
        private String symbol;
        
        @Schema(description = "交易类型", example = "BUY")
        private String side;
        
        @Schema(description = "交易数量")
        private BigDecimal quantity;
        
        @Schema(description = "交易价格")
        private BigDecimal price;
        
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeTime;
        
        @Schema(description = "盈亏")
        private BigDecimal pnl;
        
        @Schema(description = "手续费")
        private BigDecimal fee;
    }

    @Data
    @Schema(description = "交易偏好")
    public static class TradingPreferences {
        @Schema(description = "偏好交易对")
        private List<String> favoriteSymbols;
        
        @Schema(description = "平均持仓时间（小时）")
        private BigDecimal avgHoldingTime;
        
        @Schema(description = "风险偏好", example = "MODERATE")
        private String riskPreference;
        
        @Schema(description = "交易活跃时段")
        private List<String> activeHours;
    }

    public UserTradingOverviewResponse() {}

    public UserTradingOverviewResponse(Integer totalTradeCount, BigDecimal totalTradeVolume, 
                                     BigDecimal totalFees, BigDecimal totalPnl) {
        this.totalTradeCount = totalTradeCount;
        this.totalTradeVolume = totalTradeVolume;
        this.totalFees = totalFees;
        this.totalPnl = totalPnl;
    }
}