package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 标记价格响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarkPriceResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 指数价格
     */
    private BigDecimal indexPrice;
    
    /**
     * 预估结算价格
     */
    private BigDecimal estimatedSettlePrice;
    
    /**
     * 最新资金费率
     */
    private BigDecimal lastFundingRate;
    
    /**
     * 利率
     */
    private BigDecimal interestRate;
    
    /**
     * 下次资金费率时间
     */
    private LocalDateTime nextFundingTime;
    
    /**
     * 数据时间
     */
    private LocalDateTime time;
    
    /**
     * 溢价指数
     */
    private BigDecimal premiumIndex;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 24小时成交量
     */
    private BigDecimal volume;
    
    /**
     * 24小时成交额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 未平仓合约数量
     */
    private BigDecimal openInterest;
}