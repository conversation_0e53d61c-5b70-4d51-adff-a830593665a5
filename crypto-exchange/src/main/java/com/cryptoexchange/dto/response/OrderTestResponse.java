package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单测试响应
 */
public class OrderTestResponse {
    
    /**
     * 测试结果状态
     */
    private String status;
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 订单类型
     */
    private String orderType;
    
    /**
     * 订单方向
     */
    private String side;
    
    /**
     * 数量
     */
    private BigDecimal quantity;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 预估手续费
     */
    private BigDecimal estimatedFee;
    
    /**
     * 手续费资产
     */
    private String feeAsset;
    
    /**
     * 预估成交金额
     */
    private BigDecimal estimatedQuoteQty;
    
    /**
     * 账户余额是否充足
     */
    private Boolean sufficientBalance;
    
    /**
     * 测试时间
     */
    private LocalDateTime testTime;
    
    /**
     * 错误信息（如果测试失败）
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    public OrderTestResponse() {}
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public String getOrderType() {
        return orderType;
    }
    
    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }
    
    public String getSide() {
        return side;
    }
    
    public void setSide(String side) {
        this.side = side;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getEstimatedFee() {
        return estimatedFee;
    }
    
    public void setEstimatedFee(BigDecimal estimatedFee) {
        this.estimatedFee = estimatedFee;
    }
    
    public String getFeeAsset() {
        return feeAsset;
    }
    
    public void setFeeAsset(String feeAsset) {
        this.feeAsset = feeAsset;
    }
    
    public BigDecimal getEstimatedQuoteQty() {
        return estimatedQuoteQty;
    }
    
    public void setEstimatedQuoteQty(BigDecimal estimatedQuoteQty) {
        this.estimatedQuoteQty = estimatedQuoteQty;
    }
    
    public Boolean getSufficientBalance() {
        return sufficientBalance;
    }
    
    public void setSufficientBalance(Boolean sufficientBalance) {
        this.sufficientBalance = sufficientBalance;
    }
    
    public LocalDateTime getTestTime() {
        return testTime;
    }
    
    public void setTestTime(LocalDateTime testTime) {
        this.testTime = testTime;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}