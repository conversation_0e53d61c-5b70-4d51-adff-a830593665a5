package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 价格变化统计响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "价格变化统计响应")
public class PriceChangeStatsResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "统计周期")
    private String period;

    @Schema(description = "统计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "统计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "前收盘价")
    private BigDecimal prevClosePrice;

    @Schema(description = "价格变化")
    private BigDecimal priceChange;

    @Schema(description = "价格变化百分比")
    private BigDecimal priceChangePercent;

    @Schema(description = "加权平均价格")
    private BigDecimal weightedAvgPrice;

    @Schema(description = "成交量加权平均价格")
    private BigDecimal vwap;

    @Schema(description = "24小时价格变化")
    private BigDecimal priceChange24h;

    @Schema(description = "24小时价格变化百分比")
    private BigDecimal priceChangePercent24h;

    @Schema(description = "7天价格变化")
    private BigDecimal priceChange7d;

    @Schema(description = "7天价格变化百分比")
    private BigDecimal priceChangePercent7d;

    @Schema(description = "30天价格变化")
    private BigDecimal priceChange30d;

    @Schema(description = "30天价格变化百分比")
    private BigDecimal priceChangePercent30d;

    @Schema(description = "成交量")
    private BigDecimal volume;

    @Schema(description = "成交额")
    private BigDecimal amount;

    @Schema(description = "成交笔数")
    private Long tradeCount;

    @Schema(description = "买单成交量")
    private BigDecimal buyVolume;

    @Schema(description = "卖单成交量")
    private BigDecimal sellVolume;

    @Schema(description = "买单成交额")
    private BigDecimal buyAmount;

    @Schema(description = "卖单成交额")
    private BigDecimal sellAmount;

    @Schema(description = "买卖比例")
    private BigDecimal buySellRatio;

    @Schema(description = "振幅")
    private BigDecimal amplitude;

    @Schema(description = "换手率")
    private BigDecimal turnoverRate;

    @Schema(description = "市值")
    private BigDecimal marketCap;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "市值排名")
    private Integer marketCapRank;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "标准差")
    private BigDecimal standardDeviation;

    @Schema(description = "方差")
    private BigDecimal variance;

    @Schema(description = "偏度")
    private BigDecimal skewness;

    @Schema(description = "峰度")
    private BigDecimal kurtosis;

    @Schema(description = "夏普比率")
    private BigDecimal sharpeRatio;

    @Schema(description = "最大回撤")
    private BigDecimal maxDrawdown;

    @Schema(description = "价格趋势")
    private String priceTrend;

    @Schema(description = "趋势强度")
    private BigDecimal trendStrength;

    @Schema(description = "支撑位")
    private List<BigDecimal> supportLevels;

    @Schema(description = "阻力位")
    private List<BigDecimal> resistanceLevels;

    @Schema(description = "技术指标")
    private TechnicalIndicators technicalIndicators;

    @Schema(description = "价格分布")
    private PriceDistribution priceDistribution;

    @Schema(description = "时段统计")
    private List<HourlyStats> hourlyStats;

    @Schema(description = "历史对比")
    private HistoricalComparison historicalComparison;

    @Schema(description = "异常检测")
    private AnomalyDetection anomalyDetection;

    @Schema(description = "流动性指标")
    private LiquidityMetrics liquidityMetrics;

    @Schema(description = "资金流向")
    private MoneyFlow moneyFlow;

    @Schema(description = "大单分析")
    private LargeOrderAnalysis largeOrderAnalysis;

    @Schema(description = "情绪指标")
    private SentimentIndicators sentimentIndicators;

    @Schema(description = "相关性分析")
    private CorrelationAnalysis correlationAnalysis;

    @Schema(description = "预测指标")
    private PredictionMetrics predictionMetrics;

    @Schema(description = "风险指标")
    private RiskMetrics riskMetrics;

    @Schema(description = "质量评分")
    private BigDecimal qualityScore;

    @Schema(description = "数据完整性")
    private BigDecimal dataCompleteness;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    /**
     * 技术指标
     */
    @Data
    @Schema(description = "技术指标")
    public static class TechnicalIndicators {
        
        @Schema(description = "RSI指标")
        private BigDecimal rsi;
        
        @Schema(description = "MACD指标")
        private BigDecimal macd;
        
        @Schema(description = "MACD信号线")
        private BigDecimal macdSignal;
        
        @Schema(description = "MACD柱状图")
        private BigDecimal macdHistogram;
        
        @Schema(description = "布林带上轨")
        private BigDecimal bollingerUpper;
        
        @Schema(description = "布林带中轨")
        private BigDecimal bollingerMiddle;
        
        @Schema(description = "布林带下轨")
        private BigDecimal bollingerLower;
        
        @Schema(description = "KDJ-K值")
        private BigDecimal kdjK;
        
        @Schema(description = "KDJ-D值")
        private BigDecimal kdjD;
        
        @Schema(description = "KDJ-J值")
        private BigDecimal kdjJ;
        
        @Schema(description = "移动平均线MA5")
        private BigDecimal ma5;
        
        @Schema(description = "移动平均线MA10")
        private BigDecimal ma10;
        
        @Schema(description = "移动平均线MA20")
        private BigDecimal ma20;
        
        @Schema(description = "移动平均线MA50")
        private BigDecimal ma50;
        
        @Schema(description = "移动平均线MA200")
        private BigDecimal ma200;
        
        @Schema(description = "指数移动平均线EMA12")
        private BigDecimal ema12;
        
        @Schema(description = "指数移动平均线EMA26")
        private BigDecimal ema26;
    }

    /**
     * 价格分布
     */
    @Data
    @Schema(description = "价格分布")
    public static class PriceDistribution {
        
        @Schema(description = "价格区间")
        private List<PriceRange> priceRanges;
        
        @Schema(description = "众数价格")
        private BigDecimal modePrice;
        
        @Schema(description = "中位数价格")
        private BigDecimal medianPrice;
        
        @Schema(description = "平均价格")
        private BigDecimal meanPrice;
        
        @Schema(description = "价格分布偏度")
        private BigDecimal distributionSkewness;
        
        @Schema(description = "价格分布峰度")
        private BigDecimal distributionKurtosis;
    }

    /**
     * 价格区间
     */
    @Data
    @Schema(description = "价格区间")
    public static class PriceRange {
        
        @Schema(description = "最低价")
        private BigDecimal minPrice;
        
        @Schema(description = "最高价")
        private BigDecimal maxPrice;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "成交笔数")
        private Long tradeCount;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 时段统计
     */
    @Data
    @Schema(description = "时段统计")
    public static class HourlyStats {
        
        @Schema(description = "小时")
        private Integer hour;
        
        @Schema(description = "开盘价")
        private BigDecimal openPrice;
        
        @Schema(description = "收盘价")
        private BigDecimal closePrice;
        
        @Schema(description = "最高价")
        private BigDecimal highPrice;
        
        @Schema(description = "最低价")
        private BigDecimal lowPrice;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "成交额")
        private BigDecimal amount;
        
        @Schema(description = "价格变化百分比")
        private BigDecimal priceChangePercent;
    }

    /**
     * 历史对比
     */
    @Data
    @Schema(description = "历史对比")
    public static class HistoricalComparison {
        
        @Schema(description = "与昨日同期对比")
        private BigDecimal yesterdayComparison;
        
        @Schema(description = "与上周同期对比")
        private BigDecimal lastWeekComparison;
        
        @Schema(description = "与上月同期对比")
        private BigDecimal lastMonthComparison;
        
        @Schema(description = "与去年同期对比")
        private BigDecimal lastYearComparison;
        
        @Schema(description = "历史排名")
        private Integer historicalRank;
        
        @Schema(description = "历史百分位")
        private BigDecimal historicalPercentile;
    }

    /**
     * 异常检测
     */
    @Data
    @Schema(description = "异常检测")
    public static class AnomalyDetection {
        
        @Schema(description = "是否存在异常")
        private Boolean hasAnomaly;
        
        @Schema(description = "异常类型")
        private List<String> anomalyTypes;
        
        @Schema(description = "异常评分")
        private BigDecimal anomalyScore;
        
        @Schema(description = "异常时间点")
        private List<LocalDateTime> anomalyTimestamps;
        
        @Schema(description = "异常描述")
        private String anomalyDescription;
    }

    /**
     * 流动性指标
     */
    @Data
    @Schema(description = "流动性指标")
    public static class LiquidityMetrics {
        
        @Schema(description = "买卖价差")
        private BigDecimal bidAskSpread;
        
        @Schema(description = "买卖价差百分比")
        private BigDecimal bidAskSpreadPercent;
        
        @Schema(description = "市场深度")
        private BigDecimal marketDepth;
        
        @Schema(description = "流动性评分")
        private BigDecimal liquidityScore;
        
        @Schema(description = "成交密度")
        private BigDecimal tradeDensity;
        
        @Schema(description = "价格影响")
        private BigDecimal priceImpact;
    }

    /**
     * 资金流向
     */
    @Data
    @Schema(description = "资金流向")
    public static class MoneyFlow {
        
        @Schema(description = "净资金流入")
        private BigDecimal netMoneyFlow;
        
        @Schema(description = "主力资金流入")
        private BigDecimal mainMoneyFlow;
        
        @Schema(description = "散户资金流入")
        private BigDecimal retailMoneyFlow;
        
        @Schema(description = "资金流入强度")
        private BigDecimal moneyFlowIntensity;
        
        @Schema(description = "资金流向趋势")
        private String moneyFlowTrend;
    }

    /**
     * 大单分析
     */
    @Data
    @Schema(description = "大单分析")
    public static class LargeOrderAnalysis {
        
        @Schema(description = "大单买入量")
        private BigDecimal largeBuyVolume;
        
        @Schema(description = "大单卖出量")
        private BigDecimal largeSellVolume;
        
        @Schema(description = "大单净买入")
        private BigDecimal largeNetBuy;
        
        @Schema(description = "大单占比")
        private BigDecimal largeOrderRatio;
        
        @Schema(description = "大单平均价格")
        private BigDecimal largeOrderAvgPrice;
    }

    /**
     * 情绪指标
     */
    @Data
    @Schema(description = "情绪指标")
    public static class SentimentIndicators {
        
        @Schema(description = "恐慌贪婪指数")
        private BigDecimal fearGreedIndex;
        
        @Schema(description = "市场情绪")
        private String marketSentiment;
        
        @Schema(description = "社交媒体情绪")
        private BigDecimal socialSentiment;
        
        @Schema(description = "新闻情绪")
        private BigDecimal newsSentiment;
        
        @Schema(description = "投资者信心指数")
        private BigDecimal confidenceIndex;
    }

    /**
     * 相关性分析
     */
    @Data
    @Schema(description = "相关性分析")
    public static class CorrelationAnalysis {
        
        @Schema(description = "与BTC相关性")
        private BigDecimal btcCorrelation;
        
        @Schema(description = "与ETH相关性")
        private BigDecimal ethCorrelation;
        
        @Schema(description = "与市场相关性")
        private BigDecimal marketCorrelation;
        
        @Schema(description = "相关性强度")
        private String correlationStrength;
    }

    /**
     * 预测指标
     */
    @Data
    @Schema(description = "预测指标")
    public static class PredictionMetrics {
        
        @Schema(description = "短期趋势预测")
        private String shortTermTrend;
        
        @Schema(description = "中期趋势预测")
        private String mediumTermTrend;
        
        @Schema(description = "长期趋势预测")
        private String longTermTrend;
        
        @Schema(description = "预测置信度")
        private BigDecimal predictionConfidence;
        
        @Schema(description = "目标价格")
        private BigDecimal targetPrice;
        
        @Schema(description = "止损价格")
        private BigDecimal stopLossPrice;
    }

    /**
     * 风险指标
     */
    @Data
    @Schema(description = "风险指标")
    public static class RiskMetrics {
        
        @Schema(description = "VaR风险值")
        private BigDecimal valueAtRisk;
        
        @Schema(description = "CVaR条件风险值")
        private BigDecimal conditionalVaR;
        
        @Schema(description = "风险等级")
        private String riskLevel;
        
        @Schema(description = "风险评分")
        private BigDecimal riskScore;
        
        @Schema(description = "下行风险")
        private BigDecimal downsideRisk;
        
        @Schema(description = "上行潜力")
        private BigDecimal upsidePotential;
    }
}