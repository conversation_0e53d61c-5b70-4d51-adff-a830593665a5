package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场指数响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketIndexResponse {
    
    /**
     * 指数名称
     */
    private String indexName;
    
    /**
     * 指数代码
     */
    private String indexCode;
    
    /**
     * 当前指数值
     */
    private BigDecimal currentValue;
    
    /**
     * 基准值
     */
    private BigDecimal baseValue;
    
    /**
     * 计算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calculationTime;
    
    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
    
    /**
     * 指数类型
     */
    private IndexType indexType;
    
    /**
     * 指数变化
     */
    private IndexChange indexChange;
    
    /**
     * 指数组成
     */
    private IndexComposition composition;
    
    /**
     * 市场概览
     */
    private MarketOverview marketOverview;
    
    /**
     * 板块表现
     */
    private List<SectorPerformance> sectorPerformances;
    
    /**
     * 成分币种表现
     */
    private List<ComponentPerformance> componentPerformances;
    
    /**
     * 技术分析
     */
    private TechnicalAnalysis technicalAnalysis;
    
    /**
     * 历史数据
     */
    private List<HistoricalIndexData> historicalData;
    
    /**
     * 统计分析
     */
    private StatisticalAnalysis statistics;
    
    /**
     * 相关性分析
     */
    private CorrelationAnalysis correlationAnalysis;
    
    /**
     * 风险指标
     */
    private RiskMetrics riskMetrics;
    
    /**
     * 指数预测
     */
    private IndexForecast forecast;
    
    /**
     * 交易建议
     */
    private TradingRecommendation tradingRecommendation;
    
    /**
     * 指数类型枚举
     */
    public enum IndexType {
        MARKET_CAP_WEIGHTED,
        EQUAL_WEIGHTED,
        PRICE_WEIGHTED,
        VOLUME_WEIGHTED,
        CUSTOM_WEIGHTED,
        SECTOR_INDEX,
        THEME_INDEX
    }
    
    /**
     * 指数变化
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexChange {
        /**
         * 绝对变化
         */
        private BigDecimal absoluteChange;
        
        /**
         * 百分比变化
         */
        private BigDecimal percentageChange;
        
        /**
         * 1小时变化
         */
        private BigDecimal change1h;
        
        /**
         * 24小时变化
         */
        private BigDecimal change24h;
        
        /**
         * 7天变化
         */
        private BigDecimal change7d;
        
        /**
         * 30天变化
         */
        private BigDecimal change30d;
        
        /**
         * 90天变化
         */
        private BigDecimal change90d;
        
        /**
         * 年初至今变化
         */
        private BigDecimal changeYTD;
        
        /**
         * 1年变化
         */
        private BigDecimal change1y;
        
        /**
         * 变化趋势
         */
        private String trend;
        
        /**
         * 变化动量
         */
        private String momentum;
    }
    
    /**
     * 指数组成
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexComposition {
        /**
         * 总成分数量
         */
        private Integer totalComponents;
        
        /**
         * 权重方法
         */
        private String weightingMethod;
        
        /**
         * 重新平衡频率
         */
        private String rebalanceFrequency;
        
        /**
         * 最后重新平衡时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastRebalanceTime;
        
        /**
         * 下次重新平衡时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextRebalanceTime;
        
        /**
         * 权重分布
         */
        private WeightDistribution weightDistribution;
        
        /**
         * 包含标准
         */
        private List<String> inclusionCriteria;
        
        /**
         * 排除标准
         */
        private List<String> exclusionCriteria;
    }
    
    /**
     * 权重分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeightDistribution {
        /**
         * 前5大权重
         */
        private BigDecimal top5Weight;
        
        /**
         * 前10大权重
         */
        private BigDecimal top10Weight;
        
        /**
         * 最大单一权重
         */
        private BigDecimal maxSingleWeight;
        
        /**
         * 最小单一权重
         */
        private BigDecimal minSingleWeight;
        
        /**
         * 权重集中度
         */
        private BigDecimal concentration;
        
        /**
         * 有效成分数量
         */
        private BigDecimal effectiveComponents;
    }
    
    /**
     * 市场概览
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketOverview {
        /**
         * 总市值
         */
        private BigDecimal totalMarketCap;
        
        /**
         * 24小时成交量
         */
        private BigDecimal volume24h;
        
        /**
         * 上涨币种数量
         */
        private Integer gainersCount;
        
        /**
         * 下跌币种数量
         */
        private Integer losersCount;
        
        /**
         * 平盘币种数量
         */
        private Integer unchangedCount;
        
        /**
         * 市场情绪
         */
        private String marketSentiment;
        
        /**
         * 市场阶段
         */
        private String marketPhase;
        
        /**
         * 波动率
         */
        private BigDecimal volatility;
        
        /**
         * 流动性指标
         */
        private BigDecimal liquidityScore;
    }
    
    /**
     * 板块表现
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SectorPerformance {
        /**
         * 板块名称
         */
        private String sectorName;
        
        /**
         * 板块代码
         */
        private String sectorCode;
        
        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 表现
         */
        private BigDecimal performance;
        
        /**
         * 24小时变化
         */
        private BigDecimal change24h;
        
        /**
         * 7天变化
         */
        private BigDecimal change7d;
        
        /**
         * 成交量
         */
        private BigDecimal volume;
        
        /**
         * 市值
         */
        private BigDecimal marketCap;
        
        /**
         * 币种数量
         */
        private Integer coinCount;
        
        /**
         * 排名
         */
        private Integer rank;
        
        /**
         * 趋势
         */
        private String trend;
    }
    
    /**
     * 成分币种表现
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComponentPerformance {
        /**
         * 币种符号
         */
        private String symbol;
        
        /**
         * 币种名称
         */
        private String name;
        
        /**
         * 权重
         */
        private BigDecimal weight;
        
        /**
         * 当前价格
         */
        private BigDecimal currentPrice;
        
        /**
         * 24小时变化
         */
        private BigDecimal change24h;
        
        /**
         * 7天变化
         */
        private BigDecimal change7d;
        
        /**
         * 市值
         */
        private BigDecimal marketCap;
        
        /**
         * 成交量
         */
        private BigDecimal volume24h;
        
        /**
         * 对指数贡献
         */
        private BigDecimal indexContribution;
        
        /**
         * 排名
         */
        private Integer rank;
        
        /**
         * 表现等级
         */
        private String performanceGrade;
    }
    
    /**
     * 技术分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalAnalysis {
        /**
         * 趋势分析
         */
        private TrendAnalysis trendAnalysis;
        
        /**
         * 动量指标
         */
        private MomentumIndicators momentumIndicators;
        
        /**
         * 支撑阻力位
         */
        private SupportResistanceLevels supportResistance;
        
        /**
         * 技术信号
         */
        private List<TechnicalSignal> technicalSignals;
        
        /**
         * 技术评级
         */
        private String technicalRating;
        
        /**
         * 技术总结
         */
        private String technicalSummary;
    }
    
    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        private String shortTermTrend;
        private String mediumTermTrend;
        private String longTermTrend;
        private BigDecimal trendStrength;
        private String trendDirection;
        private BigDecimal trendDuration;
    }
    
    /**
     * 动量指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MomentumIndicators {
        private BigDecimal rsi;
        private BigDecimal macd;
        private BigDecimal stochastic;
        private BigDecimal momentum;
        private String momentumSignal;
    }
    
    /**
     * 支撑阻力位
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SupportResistanceLevels {
        private List<BigDecimal> supportLevels;
        private List<BigDecimal> resistanceLevels;
        private BigDecimal nearestSupport;
        private BigDecimal nearestResistance;
        private String levelStrength;
    }
    
    /**
     * 技术信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalSignal {
        private String signalType;
        private String signal;
        private String strength;
        private String timeframe;
        private String description;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime signalTime;
    }
    
    /**
     * 历史指数数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalIndexData {
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        private BigDecimal value;
        private BigDecimal change;
        private BigDecimal volume;
        private BigDecimal volatility;
        private String note;
    }
    
    /**
     * 统计分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatisticalAnalysis {
        private BigDecimal mean;
        private BigDecimal median;
        private BigDecimal standardDeviation;
        private BigDecimal variance;
        private BigDecimal skewness;
        private BigDecimal kurtosis;
        private BigDecimal sharpeRatio;
        private BigDecimal maxDrawdown;
        private BigDecimal volatility;
        private BigDecimal beta;
        private BigDecimal alpha;
        private BigDecimal informationRatio;
    }
    
    /**
     * 相关性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationAnalysis {
        private List<IndexCorrelation> correlations;
        private BigDecimal averageCorrelation;
        private String correlationTrend;
        private String diversificationBenefit;
    }
    
    /**
     * 指数相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexCorrelation {
        private String indexName;
        private BigDecimal correlationCoefficient;
        private String correlationStrength;
        private String timeframe;
        private String relationship;
    }
    
    /**
     * 风险指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskMetrics {
        private BigDecimal valueAtRisk;
        private BigDecimal conditionalVaR;
        private BigDecimal maxDrawdown;
        private BigDecimal downsideDeviation;
        private BigDecimal sortinoRatio;
        private BigDecimal calmarRatio;
        private String riskLevel;
        private List<String> riskFactors;
    }
    
    /**
     * 指数预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexForecast {
        private List<ForecastPeriod> forecasts;
        private String methodology;
        private BigDecimal confidence;
        private List<String> assumptions;
        private String disclaimer;
    }
    
    /**
     * 预测周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastPeriod {
        private String period;
        private BigDecimal predictedValue;
        private BigDecimal confidence;
        private String scenario;
        private BigDecimal upside;
        private BigDecimal downside;
    }
    
    /**
     * 交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingRecommendation {
        private String overallRecommendation;
        private String strategy;
        private String timeframe;
        private BigDecimal targetPrice;
        private BigDecimal stopLoss;
        private String riskLevel;
        private List<String> keyFactors;
        private String reasoning;
    }
}