package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场分类响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场分类响应")
public class MarketCategoryResponse {

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "分类代码")
    private String categoryCode;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "分类描述")
    private String description;

    @Schema(description = "分类图标")
    private String iconUrl;

    @Schema(description = "分类颜色")
    private String color;

    @Schema(description = "父分类ID")
    private String parentCategoryId;

    @Schema(description = "分类层级")
    private Integer level;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "是否热门")
    private Boolean isHot;

    @Schema(description = "是否新分类")
    private Boolean isNew;

    @Schema(description = "交易对数量")
    private Integer symbolCount;

    @Schema(description = "活跃交易对数量")
    private Integer activeSymbolCount;

    @Schema(description = "总市值")
    private BigDecimal totalMarketCap;

    @Schema(description = "24小时总成交量")
    private BigDecimal total24hVolume;

    @Schema(description = "24小时总成交额")
    private BigDecimal total24hAmount;

    @Schema(description = "24小时涨跌幅")
    private BigDecimal changePercent24h;

    @Schema(description = "7天涨跌幅")
    private BigDecimal changePercent7d;

    @Schema(description = "30天涨跌幅")
    private BigDecimal changePercent30d;

    @Schema(description = "平均价格")
    private BigDecimal avgPrice;

    @Schema(description = "市值占比")
    private BigDecimal marketCapRatio;

    @Schema(description = "成交量占比")
    private BigDecimal volumeRatio;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "子分类列表")
    private List<SubCategory> subCategories;

    @Schema(description = "热门交易对")
    private List<CategorySymbol> hotSymbols;

    @Schema(description = "涨幅榜前5")
    private List<CategorySymbol> topGainers;

    @Schema(description = "跌幅榜前5")
    private List<CategorySymbol> topLosers;

    @Schema(description = "成交量榜前5")
    private List<CategorySymbol> topVolumes;

    @Schema(description = "新上线交易对")
    private List<CategorySymbol> newListings;

    @Schema(description = "分类标签")
    private List<String> tags;

    @Schema(description = "相关新闻数量")
    private Integer newsCount;

    @Schema(description = "社交媒体提及次数")
    private Integer socialMentions;

    @Schema(description = "技术发展指数")
    private BigDecimal developmentIndex;

    @Schema(description = "生态活跃度")
    private BigDecimal ecosystemActivity;

    @Schema(description = "机构关注度")
    private BigDecimal institutionalInterest;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 子分类
     */
    @Data
    @Schema(description = "子分类")
    public static class SubCategory {
        
        @Schema(description = "子分类ID")
        private String categoryId;
        
        @Schema(description = "子分类代码")
        private String categoryCode;
        
        @Schema(description = "子分类名称")
        private String categoryName;
        
        @Schema(description = "子分类描述")
        private String description;
        
        @Schema(description = "交易对数量")
        private Integer symbolCount;
        
        @Schema(description = "总市值")
        private BigDecimal totalMarketCap;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时涨跌幅")
        private BigDecimal changePercent24h;
        
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 分类交易对
     */
    @Data
    @Schema(description = "分类交易对")
    public static class CategorySymbol {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "交易对名称")
        private String symbolName;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时涨跌幅")
        private BigDecimal changePercent24h;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount24h;
        
        @Schema(description = "市值")
        private BigDecimal marketCap;
        
        @Schema(description = "市值排名")
        private Integer marketCapRank;
        
        @Schema(description = "是否新币")
        private Boolean isNewListing;
        
        @Schema(description = "热度评分")
        private BigDecimal hotScore;
    }
}