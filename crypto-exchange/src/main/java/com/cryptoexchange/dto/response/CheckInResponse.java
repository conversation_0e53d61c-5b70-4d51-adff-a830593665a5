package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 签到响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "签到响应")
public class CheckInResponse {

    @Schema(description = "是否签到成功", example = "true")
    private Boolean success;

    @Schema(description = "签到消息", example = "签到成功！")
    private String message;

    @Schema(description = "签到日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate checkInDate;

    @Schema(description = "签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkInTime;

    @Schema(description = "连续签到天数", example = "7")
    private Integer consecutiveDays;

    @Schema(description = "本月签到天数", example = "15")
    private Integer monthlyCheckInDays;

    @Schema(description = "总签到天数", example = "120")
    private Integer totalCheckInDays;

    @Schema(description = "今日奖励")
    private CheckInReward todayReward;

    @Schema(description = "下一天奖励")
    private CheckInReward nextDayReward;

    @Schema(description = "签到状态")
    private CheckInStatus status;

    @Schema(description = "本月签到记录")
    private List<MonthlyCheckIn> monthlyRecord;

    @Schema(description = "签到统计")
    private CheckInStats stats;

    @Data
    @Schema(description = "签到奖励")
    public static class CheckInReward {
        @Schema(description = "奖励类型", example = "POINTS")
        private String rewardType;
        
        @Schema(description = "奖励名称", example = "积分")
        private String rewardName;
        
        @Schema(description = "奖励数量")
        private BigDecimal amount;
        
        @Schema(description = "奖励币种", example = "USDT")
        private String currency;
        
        @Schema(description = "奖励描述", example = "签到获得10积分")
        private String description;
    }

    @Data
    @Schema(description = "签到状态")
    public static class CheckInStatus {
        @Schema(description = "今日是否已签到", example = "true")
        private Boolean todayCheckedIn;
        
        @Schema(description = "是否可以签到", example = "false")
        private Boolean canCheckIn;
        
        @Schema(description = "下次可签到时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextCheckInTime;
        
        @Schema(description = "签到倒计时（秒）", example = "3600")
        private Long countdownSeconds;
    }

    @Data
    @Schema(description = "本月签到记录")
    public static class MonthlyCheckIn {
        @Schema(description = "签到日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate date;
        
        @Schema(description = "是否已签到", example = "true")
        private Boolean checkedIn;
        
        @Schema(description = "奖励信息")
        private CheckInReward reward;
    }

    @Data
    @Schema(description = "签到统计")
    public static class CheckInStats {
        @Schema(description = "最长连续签到天数", example = "30")
        private Integer maxConsecutiveDays;
        
        @Schema(description = "本年签到天数", example = "200")
        private Integer yearlyCheckInDays;
        
        @Schema(description = "签到率百分比")
        private BigDecimal checkInRate;
        
        @Schema(description = "累计获得奖励")
        private BigDecimal totalRewards;
        
        @Schema(description = "当前等级", example = "白银")
        private String currentLevel;
        
        @Schema(description = "下一等级", example = "黄金")
        private String nextLevel;
        
        @Schema(description = "升级所需天数", example = "10")
        private Integer daysToNextLevel;
    }

    public CheckInResponse() {}

    public CheckInResponse(Boolean success, String message, LocalDate checkInDate, 
                         Integer consecutiveDays, CheckInReward todayReward) {
        this.success = success;
        this.message = message;
        this.checkInDate = checkInDate;
        this.consecutiveDays = consecutiveDays;
        this.todayReward = todayReward;
        this.checkInTime = LocalDateTime.now();
    }
}