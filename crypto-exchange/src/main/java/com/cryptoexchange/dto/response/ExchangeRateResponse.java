package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 汇率响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeRateResponse {
    
    /**
     * 基础货币
     */
    private String baseCurrency;
    
    /**
     * 目标货币
     */
    private String targetCurrency;
    
    /**
     * 汇率
     */
    private BigDecimal rate;
    
    /**
     * 反向汇率
     */
    private BigDecimal inverseRate;
    
    /**
     * 汇率来源
     */
    private String source;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 有效期（秒）
     */
    private Integer validityPeriod;
    
    /**
     * 24小时变化
     */
    private BigDecimal change24h;
    
    /**
     * 24小时变化百分比
     */
    private BigDecimal changePercent24h;
    
    /**
     * 24小时最高价
     */
    private BigDecimal high24h;
    
    /**
     * 24小时最低价
     */
    private BigDecimal low24h;
    
    /**
     * 24小时开盘价
     */
    private BigDecimal open24h;
    
    /**
     * 24小时收盘价
     */
    private BigDecimal close24h;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume24h;
    
    /**
     * 买入价
     */
    private BigDecimal bidPrice;
    
    /**
     * 卖出价
     */
    private BigDecimal askPrice;
    
    /**
     * 买卖价差
     */
    private BigDecimal spread;
    
    /**
     * 买卖价差百分比
     */
    private BigDecimal spreadPercent;
    
    /**
     * 市场深度
     */
    private MarketDepth marketDepth;
    
    /**
     * 历史汇率
     */
    private List<HistoricalRate> historicalRates;
    
    /**
     * 汇率精度
     */
    private Integer precision;
    
    /**
     * 是否为实时汇率
     */
    private Boolean isRealTime;
    
    /**
     * 延迟时间（毫秒）
     */
    private Long delayMs;
    
    /**
     * 市场深度内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class MarketDepth {
        /**
         * 买盘深度
         */
        private List<PriceLevel> bids;
        
        /**
         * 卖盘深度
         */
        private List<PriceLevel> asks;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
    
    /**
     * 价格档位内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PriceLevel {
        /**
         * 价格
         */
        private BigDecimal price;
        
        /**
         * 数量
         */
        private BigDecimal quantity;
    }
    
    /**
     * 历史汇率内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class HistoricalRate {
        /**
         * 时间戳
         */
        private LocalDateTime timestamp;
        
        /**
         * 汇率
         */
        private BigDecimal rate;
        
        /**
         * 开盘价
         */
        private BigDecimal open;
        
        /**
         * 最高价
         */
        private BigDecimal high;
        
        /**
         * 最低价
         */
        private BigDecimal low;
        
        /**
         * 收盘价
         */
        private BigDecimal close;
        
        /**
         * 交易量
         */
        private BigDecimal volume;
    }
}