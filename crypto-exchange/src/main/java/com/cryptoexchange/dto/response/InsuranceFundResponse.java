package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保险基金响应
 */
public class InsuranceFundResponse {
    
    /**
     * 资产名称
     */
    private String asset;
    
    /**
     * 保险基金余额
     */
    private BigDecimal balance;
    
    /**
     * 24小时变化量
     */
    private BigDecimal change24h;
    
    /**
     * 24小时变化率
     */
    private BigDecimal changePercent24h;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 历史最高余额
     */
    private BigDecimal historicalHigh;
    
    /**
     * 历史最低余额
     */
    private BigDecimal historicalLow;
    
    public InsuranceFundResponse() {}
    
    public String getAsset() {
        return asset;
    }
    
    public void setAsset(String asset) {
        this.asset = asset;
    }
    
    public BigDecimal getBalance() {
        return balance;
    }
    
    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }
    
    public BigDecimal getChange24h() {
        return change24h;
    }
    
    public void setChange24h(BigDecimal change24h) {
        this.change24h = change24h;
    }
    
    public BigDecimal getChangePercent24h() {
        return changePercent24h;
    }
    
    public void setChangePercent24h(BigDecimal changePercent24h) {
        this.changePercent24h = changePercent24h;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    
    public BigDecimal getHistoricalHigh() {
        return historicalHigh;
    }
    
    public void setHistoricalHigh(BigDecimal historicalHigh) {
        this.historicalHigh = historicalHigh;
    }
    
    public BigDecimal getHistoricalLow() {
        return historicalLow;
    }
    
    public void setHistoricalLow(BigDecimal historicalLow) {
        this.historicalLow = historicalLow;
    }
}