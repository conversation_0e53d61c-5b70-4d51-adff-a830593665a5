package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统状态响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "系统状态响应")
public class SystemStatusResponse {

    @Schema(description = "系统状态")
    private String systemStatus;

    @Schema(description = "系统健康度(%)")
    private Double systemHealth;

    @Schema(description = "系统版本")
    private String systemVersion;

    @Schema(description = "API版本")
    private String apiVersion;

    @Schema(description = "构建版本")
    private String buildVersion;

    @Schema(description = "环境")
    private String environment;

    @Schema(description = "部署时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deploymentTime;

    @Schema(description = "系统启动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime systemStartTime;

    @Schema(description = "系统运行时长(毫秒)")
    private Long systemUptime;

    @Schema(description = "系统运行时长(可读格式)")
    private String systemUptimeReadable;

    @Schema(description = "最后重启时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastRestartTime;

    @Schema(description = "重启次数")
    private Integer restartCount;

    @Schema(description = "服务器节点信息")
    private List<ServerNode> serverNodes;

    @Schema(description = "数据库状态")
    private DatabaseStatus databaseStatus;

    @Schema(description = "缓存状态")
    private CacheStatus cacheStatus;

    @Schema(description = "消息队列状态")
    private MessageQueueStatus messageQueueStatus;

    @Schema(description = "外部服务状态")
    private Map<String, ExternalServiceStatus> externalServices;

    @Schema(description = "系统性能指标")
    private PerformanceMetrics performanceMetrics;

    @Schema(description = "网络状态")
    private NetworkStatus networkStatus;

    @Schema(description = "安全状态")
    private SecurityStatus securityStatus;

    @Schema(description = "交易系统状态")
    private TradingSystemStatus tradingSystemStatus;

    @Schema(description = "钱包系统状态")
    private WalletSystemStatus walletSystemStatus;

    @Schema(description = "风控系统状态")
    private RiskControlStatus riskControlStatus;

    @Schema(description = "监控告警")
    private List<SystemAlert> systemAlerts;

    @Schema(description = "维护计划")
    private List<MaintenancePlan> maintenancePlans;

    @Schema(description = "系统限制")
    private SystemLimits systemLimits;

    @Schema(description = "功能开关")
    private Map<String, Boolean> featureFlags;

    @Schema(description = "地区服务状态")
    private Map<String, String> regionServiceStatus;

    @Schema(description = "合规状态")
    private String complianceStatus;

    @Schema(description = "审计状态")
    private String auditStatus;

    @Schema(description = "备份状态")
    private BackupStatus backupStatus;

    @Schema(description = "灾备状态")
    private DisasterRecoveryStatus disasterRecoveryStatus;

    @Schema(description = "最后检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastCheckTime;

    @Schema(description = "下次检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextCheckTime;

    @Schema(description = "检查间隔(秒)")
    private Integer checkInterval;

    @Schema(description = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dataUpdateTime;

    /**
     * 服务器节点
     */
    @Data
    @Schema(description = "服务器节点")
    public static class ServerNode {
        
        @Schema(description = "节点ID")
        private String nodeId;
        
        @Schema(description = "节点名称")
        private String nodeName;
        
        @Schema(description = "节点类型")
        private String nodeType;
        
        @Schema(description = "节点状态")
        private String status;
        
        @Schema(description = "IP地址")
        private String ipAddress;
        
        @Schema(description = "端口")
        private Integer port;
        
        @Schema(description = "地理位置")
        private String location;
        
        @Schema(description = "CPU使用率(%)")
        private Double cpuUsage;
        
        @Schema(description = "内存使用率(%)")
        private Double memoryUsage;
        
        @Schema(description = "磁盘使用率(%)")
        private Double diskUsage;
        
        @Schema(description = "网络延迟(毫秒)")
        private Long networkLatency;
        
        @Schema(description = "活跃连接数")
        private Integer activeConnections;
        
        @Schema(description = "负载均衡权重")
        private Integer weight;
        
        @Schema(description = "最后心跳时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastHeartbeat;
    }

    /**
     * 数据库状态
     */
    @Data
    @Schema(description = "数据库状态")
    public static class DatabaseStatus {
        
        @Schema(description = "主库状态")
        private String masterStatus;
        
        @Schema(description = "从库状态")
        private List<String> slaveStatus;
        
        @Schema(description = "连接池状态")
        private String connectionPoolStatus;
        
        @Schema(description = "活跃连接数")
        private Integer activeConnections;
        
        @Schema(description = "最大连接数")
        private Integer maxConnections;
        
        @Schema(description = "查询响应时间(毫秒)")
        private Long queryResponseTime;
        
        @Schema(description = "事务处理速度(TPS)")
        private Double transactionPerSecond;
        
        @Schema(description = "数据库大小(GB)")
        private BigDecimal databaseSize;
        
        @Schema(description = "最后备份时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastBackupTime;
    }

    /**
     * 缓存状态
     */
    @Data
    @Schema(description = "缓存状态")
    public static class CacheStatus {
        
        @Schema(description = "Redis状态")
        private String redisStatus;
        
        @Schema(description = "缓存命中率(%)")
        private Double hitRate;
        
        @Schema(description = "缓存大小(MB)")
        private BigDecimal cacheSize;
        
        @Schema(description = "已用内存(MB)")
        private BigDecimal usedMemory;
        
        @Schema(description = "最大内存(MB)")
        private BigDecimal maxMemory;
        
        @Schema(description = "连接数")
        private Integer connections;
        
        @Schema(description = "操作响应时间(毫秒)")
        private Long responseTime;
    }

    /**
     * 消息队列状态
     */
    @Data
    @Schema(description = "消息队列状态")
    public static class MessageQueueStatus {
        
        @Schema(description = "队列状态")
        private String queueStatus;
        
        @Schema(description = "待处理消息数")
        private Long pendingMessages;
        
        @Schema(description = "处理速度(消息/秒)")
        private Double processingRate;
        
        @Schema(description = "错误消息数")
        private Long errorMessages;
        
        @Schema(description = "死信队列消息数")
        private Long deadLetterMessages;
        
        @Schema(description = "消费者数量")
        private Integer consumerCount;
    }

    /**
     * 外部服务状态
     */
    @Data
    @Schema(description = "外部服务状态")
    public static class ExternalServiceStatus {
        
        @Schema(description = "服务名称")
        private String serviceName;
        
        @Schema(description = "服务状态")
        private String status;
        
        @Schema(description = "响应时间(毫秒)")
        private Long responseTime;
        
        @Schema(description = "可用性(%)")
        private Double availability;
        
        @Schema(description = "最后检查时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastCheckTime;
        
        @Schema(description = "错误信息")
        private String errorMessage;
    }

    /**
     * 性能指标
     */
    @Data
    @Schema(description = "性能指标")
    public static class PerformanceMetrics {
        
        @Schema(description = "平均响应时间(毫秒)")
        private Double avgResponseTime;
        
        @Schema(description = "每秒请求数")
        private Double requestsPerSecond;
        
        @Schema(description = "错误率(%)")
        private Double errorRate;
        
        @Schema(description = "吞吐量")
        private Double throughput;
        
        @Schema(description = "并发用户数")
        private Integer concurrentUsers;
        
        @Schema(description = "系统负载")
        private Double systemLoad;
    }

    /**
     * 网络状态
     */
    @Data
    @Schema(description = "网络状态")
    public static class NetworkStatus {
        
        @Schema(description = "网络连通性")
        private String connectivity;
        
        @Schema(description = "带宽使用率(%)")
        private Double bandwidthUsage;
        
        @Schema(description = "网络延迟(毫秒)")
        private Long latency;
        
        @Schema(description = "丢包率(%)")
        private Double packetLoss;
        
        @Schema(description = "DNS解析时间(毫秒)")
        private Long dnsResolutionTime;
    }

    /**
     * 安全状态
     */
    @Data
    @Schema(description = "安全状态")
    public static class SecurityStatus {
        
        @Schema(description = "安全等级")
        private String securityLevel;
        
        @Schema(description = "威胁检测状态")
        private String threatDetectionStatus;
        
        @Schema(description = "防火墙状态")
        private String firewallStatus;
        
        @Schema(description = "SSL证书状态")
        private String sslCertificateStatus;
        
        @Schema(description = "证书过期时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime certificateExpiryTime;
        
        @Schema(description = "安全事件数量")
        private Integer securityEventCount;
    }

    /**
     * 交易系统状态
     */
    @Data
    @Schema(description = "交易系统状态")
    public static class TradingSystemStatus {
        
        @Schema(description = "交易引擎状态")
        private String tradingEngineStatus;
        
        @Schema(description = "撮合引擎状态")
        private String matchingEngineStatus;
        
        @Schema(description = "订单处理速度(订单/秒)")
        private Double orderProcessingRate;
        
        @Schema(description = "撮合延迟(毫秒)")
        private Long matchingLatency;
        
        @Schema(description = "待处理订单数")
        private Long pendingOrders;
        
        @Schema(description = "市场数据延迟(毫秒)")
        private Long marketDataLatency;
    }

    /**
     * 钱包系统状态
     */
    @Data
    @Schema(description = "钱包系统状态")
    public static class WalletSystemStatus {
        
        @Schema(description = "钱包服务状态")
        private String walletServiceStatus;
        
        @Schema(description = "充值服务状态")
        private String depositServiceStatus;
        
        @Schema(description = "提现服务状态")
        private String withdrawServiceStatus;
        
        @Schema(description = "区块链同步状态")
        private Map<String, String> blockchainSyncStatus;
        
        @Schema(description = "热钱包余额检查")
        private String hotWalletBalanceCheck;
        
        @Schema(description = "冷钱包状态")
        private String coldWalletStatus;
    }

    /**
     * 风控系统状态
     */
    @Data
    @Schema(description = "风控系统状态")
    public static class RiskControlStatus {
        
        @Schema(description = "风控引擎状态")
        private String riskEngineStatus;
        
        @Schema(description = "反洗钱系统状态")
        private String amlSystemStatus;
        
        @Schema(description = "KYC系统状态")
        private String kycSystemStatus;
        
        @Schema(description = "风险规则数量")
        private Integer riskRuleCount;
        
        @Schema(description = "风险事件数量")
        private Integer riskEventCount;
        
        @Schema(description = "风险评估响应时间(毫秒)")
        private Long riskAssessmentTime;
    }

    /**
     * 系统告警
     */
    @Data
    @Schema(description = "系统告警")
    public static class SystemAlert {
        
        @Schema(description = "告警ID")
        private String alertId;
        
        @Schema(description = "告警类型")
        private String alertType;
        
        @Schema(description = "告警级别")
        private String severity;
        
        @Schema(description = "告警消息")
        private String message;
        
        @Schema(description = "告警时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime alertTime;
        
        @Schema(description = "告警状态")
        private String status;
        
        @Schema(description = "影响组件")
        private String affectedComponent;
    }

    /**
     * 维护计划
     */
    @Data
    @Schema(description = "维护计划")
    public static class MaintenancePlan {
        
        @Schema(description = "维护ID")
        private String maintenanceId;
        
        @Schema(description = "维护类型")
        private String maintenanceType;
        
        @Schema(description = "维护描述")
        private String description;
        
        @Schema(description = "开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startTime;
        
        @Schema(description = "结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime endTime;
        
        @Schema(description = "影响服务")
        private List<String> affectedServices;
        
        @Schema(description = "维护状态")
        private String status;
    }

    /**
     * 系统限制
     */
    @Data
    @Schema(description = "系统限制")
    public static class SystemLimits {
        
        @Schema(description = "最大并发用户数")
        private Integer maxConcurrentUsers;
        
        @Schema(description = "最大API请求频率")
        private Integer maxApiRequestRate;
        
        @Schema(description = "最大订单数量")
        private Integer maxOrderCount;
        
        @Schema(description = "最大交易金额")
        private BigDecimal maxTradeAmount;
        
        @Schema(description = "系统维护模式")
        private Boolean maintenanceMode;
    }

    /**
     * 备份状态
     */
    @Data
    @Schema(description = "备份状态")
    public static class BackupStatus {
        
        @Schema(description = "最后备份时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastBackupTime;
        
        @Schema(description = "下次备份时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime nextBackupTime;
        
        @Schema(description = "备份状态")
        private String backupStatus;
        
        @Schema(description = "备份大小(GB)")
        private BigDecimal backupSize;
        
        @Schema(description = "备份保留天数")
        private Integer retentionDays;
    }

    /**
     * 灾备状态
     */
    @Data
    @Schema(description = "灾备状态")
    public static class DisasterRecoveryStatus {
        
        @Schema(description = "灾备状态")
        private String drStatus;
        
        @Schema(description = "主备切换状态")
        private String failoverStatus;
        
        @Schema(description = "数据同步状态")
        private String dataSyncStatus;
        
        @Schema(description = "RTO(恢复时间目标)")
        private Integer rto;
        
        @Schema(description = "RPO(恢复点目标)")
        private Integer rpo;
        
        @Schema(description = "最后演练时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastDrillTime;
    }
}