package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String refreshToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "访问令牌过期时间（秒）", example = "86400")
    private Long expiresIn;

    @Schema(description = "刷新令牌过期时间（秒）", example = "604800")
    private Long refreshExpiresIn;

    @Schema(description = "用户信息")
    private UserInfo userInfo;

    @Schema(description = "权限列表")
    private List<String> permissions;

    @Schema(description = "角色列表")
    private List<String> roles;

    @Schema(description = "是否需要两步验证", example = "false")
    private Boolean requireTwoFactor = false;

    @Schema(description = "是否首次登录", example = "false")
    private Boolean firstLogin = false;

    @Schema(description = "登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    @Schema(description = "上次登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "登录IP", example = "*************")
    private String loginIp;

    @Schema(description = "用户基本信息")
    @Data
    public static class UserInfo {
        
        @Schema(description = "用户ID", example = "1")
        private Long id;
        
        @Schema(description = "用户名", example = "admin")
        private String username;
        
        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "手机号", example = "13800138000")
        private String phone;
        
        @Schema(description = "昵称", example = "管理员")
        private String nickname;
        
        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;
        
        @Schema(description = "用户状态：0-禁用，1-正常，2-锁定", example = "1")
        private Integer status;
        
        @Schema(description = "用户类型：0-普通用户，1-VIP用户，2-机构用户", example = "0")
        private Integer userType;
        
        @Schema(description = "实名认证状态：0-未认证，1-待审核，2-已认证，3-认证失败", example = "0")
        private Integer kycStatus;
        
        @Schema(description = "真实姓名", example = "张三")
        private String realName;
        
        @Schema(description = "国家/地区", example = "CN")
        private String country;
        
        @Schema(description = "语言偏好", example = "zh-CN")
        private String language;
        
        @Schema(description = "时区", example = "Asia/Shanghai")
        private String timezone;
        
        @Schema(description = "是否启用两步验证", example = "false")
        private Boolean twoFactorEnabled;
        
        @Schema(description = "手续费等级：0-默认，1-VIP1，2-VIP2，3-VIP3", example = "0")
        private Integer feeLevel;
        
        @Schema(description = "推荐码", example = "ABC123")
        private String referralCode;
        
        @Schema(description = "注册时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createTime;
    }
}