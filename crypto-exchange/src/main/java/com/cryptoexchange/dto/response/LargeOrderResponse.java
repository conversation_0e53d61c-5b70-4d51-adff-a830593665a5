package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 大单分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LargeOrderResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析时间范围
     */
    private String analysisTimeRange;

    /**
     * 大单定义阈值
     */
    private BigDecimal largeOrderThreshold;

    /**
     * 大单活动等级
     */
    private LargeOrderActivityLevel activityLevel;

    /**
     * 大单统计信息
     */
    private LargeOrderStatistics largeOrderStatistics;

    /**
     * 大单检测结果
     */
    private List<LargeOrderDetection> largeOrderDetections;

    /**
     * 大单流向分析
     */
    private LargeOrderFlowAnalysis largeOrderFlowAnalysis;

    /**
     * 大单时间分析
     */
    private LargeOrderTimeAnalysis largeOrderTimeAnalysis;

    /**
     * 大单价格影响分析
     */
    private LargeOrderPriceImpactAnalysis largeOrderPriceImpactAnalysis;

    /**
     * 大单模式识别
     */
    private LargeOrderPatternRecognition largeOrderPatternRecognition;

    /**
     * 大单风险评估
     */
    private LargeOrderRiskAssessment largeOrderRiskAssessment;

    /**
     * 大单预测分析
     */
    private LargeOrderForecastAnalysis largeOrderForecastAnalysis;

    /**
     * 大单交易建议
     */
    private LargeOrderTradingAdvice largeOrderTradingAdvice;

    /**
     * 大单监控预警
     */
    private List<LargeOrderAlert> largeOrderAlerts;

    /**
     * 大单活动等级
     */
    public enum LargeOrderActivityLevel {
        EXTREMELY_HIGH("极高", "大单活动极其频繁，市场可能面临重大变化"),
        HIGH("高", "大单活动频繁，需要密切关注"),
        MODERATE("中等", "大单活动正常，保持常规监控"),
        LOW("低", "大单活动较少，市场相对平静"),
        MINIMAL("极低", "大单活动极少，市场缺乏大资金参与");

        private final String description;
        private final String detail;

        LargeOrderActivityLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 大单统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderStatistics {
        /**
         * 大单总数
         */
        private Integer totalLargeOrders;

        /**
         * 大单总量
         */
        private BigDecimal totalLargeOrderVolume;

        /**
         * 大单总价值
         */
        private BigDecimal totalLargeOrderValue;

        /**
         * 大单占总交易量比例
         */
        private BigDecimal largeOrderVolumeRatio;

        /**
         * 大单占总交易数比例
         */
        private BigDecimal largeOrderCountRatio;

        /**
         * 买单大单数量
         */
        private Integer buyLargeOrderCount;

        /**
         * 卖单大单数量
         */
        private Integer sellLargeOrderCount;

        /**
         * 买单大单总量
         */
        private BigDecimal buyLargeOrderVolume;

        /**
         * 卖单大单总量
         */
        private BigDecimal sellLargeOrderVolume;

        /**
         * 平均大单规模
         */
        private BigDecimal averageLargeOrderSize;

        /**
         * 最大单笔大单
         */
        private BigDecimal maxLargeOrderSize;

        /**
         * 大单频率（每小时）
         */
        private BigDecimal largeOrderFrequency;

        /**
         * 大单集中度
         */
        private BigDecimal largeOrderConcentration;
        
        // 手动添加getter方法以解决编译问题
        public Integer getTotalLargeOrders() {
            return totalLargeOrders;
        }
    }

    /**
     * 大单检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderDetection {
        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 检测时间
         */
        private LocalDateTime detectionTime;

        /**
         * 订单类型
         */
        private LargeOrderType orderType;

        /**
         * 订单方向
         */
        private String orderSide;

        /**
         * 订单价格
         */
        private BigDecimal orderPrice;

        /**
         * 订单数量
         */
        private BigDecimal orderQuantity;

        /**
         * 订单价值
         */
        private BigDecimal orderValue;

        /**
         * 相对规模
         */
        private BigDecimal relativeSize;

        /**
         * 市场影响预估
         */
        private BigDecimal estimatedMarketImpact;

        /**
         * 订单特征
         */
        private LargeOrderCharacteristics orderCharacteristics;

        /**
         * 执行状态
         */
        private String executionStatus;

        /**
         * 分割信息
         */
        private OrderSplitInfo orderSplitInfo;
    }

    /**
     * 大单类型
     */
    public enum LargeOrderType {
        MARKET_ORDER("市价单", "以市场价格立即执行的大单"),
        LIMIT_ORDER("限价单", "指定价格的大单"),
        ICEBERG_ORDER("冰山单", "分批显示的大单"),
        BLOCK_ORDER("大宗交易单", "大宗交易的订单"),
        ALGORITHMIC_ORDER("算法单", "算法交易的大单"),
        INSTITUTIONAL_ORDER("机构单", "机构投资者的大单"),
        WHALE_ORDER("巨鲸单", "超大规模的订单"),
        SWEEP_ORDER("扫单", "快速扫荡订单簿的大单"),
        OTHER("其他", "其他类型的大单");

        private final String description;
        private final String detail;

        LargeOrderType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 大单特征
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderCharacteristics {
        /**
         * 紧急程度
         */
        private String urgencyLevel;

        /**
         * 隐蔽性
         */
        private BigDecimal stealthiness;

        /**
         * 执行策略
         */
        private String executionStrategy;

        /**
         * 时间敏感性
         */
        private BigDecimal timeSensitivity;

        /**
         * 价格敏感性
         */
        private BigDecimal priceSensitivity;

        /**
         * 流动性消耗
         */
        private BigDecimal liquidityConsumption;

        /**
         * 市场冲击
         */
        private BigDecimal marketImpact;
    }

    /**
     * 订单分割信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderSplitInfo {
        /**
         * 是否分割
         */
        private boolean isSplit;

        /**
         * 分割数量
         */
        private Integer splitCount;

        /**
         * 分割策略
         */
        private String splitStrategy;

        /**
         * 平均分割大小
         */
        private BigDecimal averageSplitSize;

        /**
         * 分割时间间隔
         */
        private BigDecimal splitTimeInterval;

        /**
         * 已执行分割
         */
        private Integer executedSplits;

        /**
         * 剩余分割
         */
        private Integer remainingSplits;
    }

    /**
     * 大单流向分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderFlowAnalysis {
        /**
         * 净流向
         */
        private BigDecimal netFlow;

        /**
         * 流向方向
         */
        private String flowDirection;

        /**
         * 买入流向
         */
        private LargeOrderFlow buyFlow;

        /**
         * 卖出流向
         */
        private LargeOrderFlow sellFlow;

        /**
         * 流向不平衡度
         */
        private BigDecimal flowImbalance;

        /**
         * 流向强度
         */
        private BigDecimal flowIntensity;

        /**
         * 流向持续性
         */
        private BigDecimal flowPersistence;

        /**
         * 流向变化趋势
         */
        private FlowTrend flowTrend;

        /**
         * 资金来源分析
         */
        private FundSourceAnalysis fundSourceAnalysis;

        /**
         * 获取流向方向
         */
        public String getFlowDirection() {
            return flowDirection;
        }

        public BigDecimal getFlowIntensity() {
            return flowIntensity;
        }
    }

    /**
     * 大单流向
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderFlow {
        /**
         * 流向方向
         */
        private String direction;

        /**
         * 流向量
         */
        private BigDecimal flowVolume;

        /**
         * 流向价值
         */
        private BigDecimal flowValue;

        /**
         * 流向订单数
         */
        private Integer flowOrderCount;

        /**
         * 平均订单大小
         */
        private BigDecimal averageOrderSize;

        /**
         * 流向速度
         */
        private BigDecimal flowVelocity;

        /**
         * 流向加速度
         */
        private BigDecimal flowAcceleration;
    }

    /**
     * 流向趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlowTrend {
        /**
         * 趋势方向
         */
        private String trendDirection;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续时间
         */
        private Integer trendDuration;

        /**
         * 趋势稳定性
         */
        private BigDecimal trendStability;

        /**
         * 趋势预测
         */
        private String trendForecast;
    }

    /**
     * 资金来源分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FundSourceAnalysis {
        /**
         * 机构资金比例
         */
        private BigDecimal institutionalFundRatio;

        /**
         * 散户资金比例
         */
        private BigDecimal retailFundRatio;

        /**
         * 算法交易比例
         */
        private BigDecimal algorithmicTradingRatio;

        /**
         * 高频交易比例
         */
        private BigDecimal highFrequencyTradingRatio;

        /**
         * 资金来源多样性
         */
        private BigDecimal fundSourceDiversity;

        /**
         * 主要资金来源
         */
        private List<String> majorFundSources;
    }

    /**
     * 大单时间分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderTimeAnalysis {
        /**
         * 时间分布
         */
        private List<TimeDistribution> timeDistributions;

        /**
         * 高峰时段
         */
        private List<PeakPeriod> peakPeriods;

        /**
         * 时间模式
         */
        private List<TimePattern> timePatterns;

        /**
         * 时间集中度
         */
        private BigDecimal timeConcentration;

        /**
         * 时间规律性
         */
        private BigDecimal timeRegularity;

        /**
         * 异常时间活动
         */
        private List<AbnormalTimeActivity> abnormalTimeActivities;
    }

    /**
     * 时间分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeDistribution {
        /**
         * 时间段
         */
        private String timePeriod;

        /**
         * 大单数量
         */
        private Integer largeOrderCount;

        /**
         * 大单总量
         */
        private BigDecimal largeOrderVolume;

        /**
         * 占比
         */
        private BigDecimal percentage;

        /**
         * 平均订单大小
         */
        private BigDecimal averageOrderSize;
    }

    /**
     * 高峰时段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeakPeriod {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 峰值强度
         */
        private BigDecimal peakIntensity;

        /**
         * 大单数量
         */
        private Integer largeOrderCount;

        /**
         * 总交易量
         */
        private BigDecimal totalVolume;

        /**
         * 峰值原因
         */
        private String peakReason;
    }

    /**
     * 时间模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimePattern {
        /**
         * 模式名称
         */
        private String patternName;
        
        /**
         * 检测强度
         */
        private BigDecimal detectionStrength;
        
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 模式频率
         */
        private BigDecimal patternFrequency;

        public String getPatternName() {
            return patternName;
        }

        public BigDecimal getDetectionStrength() {
            return detectionStrength;
        }

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;
    }

    /**
     * 异常时间活动
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTimeActivity {
        /**
         * 异常时间
         */
        private LocalDateTime abnormalTime;

        /**
         * 异常类型
         */
        private String abnormalType;

        /**
         * 异常程度
         */
        private BigDecimal abnormalDegree;

        /**
         * 异常描述
         */
        private String abnormalDescription;

        /**
         * 可能原因
         */
        private List<String> possibleReasons;
    }

    /**
     * 大单价格影响分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderPriceImpactAnalysis {
        /**
         * 整体价格影响
         */
        private BigDecimal overallPriceImpact;

        /**
         * 即时价格影响
         */
        private ImmediatePriceImpact immediatePriceImpact;

        /**
         * 持续价格影响
         */
        private PersistentPriceImpact persistentPriceImpact;

        /**
         * 价格恢复分析
         */
        private PriceRecoveryAnalysis priceRecoveryAnalysis;

        /**
         * 影响因子分析
         */
        private ImpactFactorAnalysis impactFactorAnalysis;

        /**
         * 价格影响预测
         */
        private PriceImpactForecast priceImpactForecast;

        public BigDecimal getOverallPriceImpact() {
            return overallPriceImpact;
        }
    }

    /**
     * 即时价格影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImmediatePriceImpact {
        /**
         * 平均即时影响
         */
        private BigDecimal averageImmediateImpact;

        /**
         * 最大即时影响
         */
        private BigDecimal maxImmediateImpact;

        /**
         * 即时影响标准差
         */
        private BigDecimal immediateImpactStdDev;

        /**
         * 影响持续时间
         */
        private BigDecimal impactDuration;

        /**
         * 影响强度分布
         */
        private Map<String, BigDecimal> impactIntensityDistribution;
    }

    /**
     * 持续价格影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersistentPriceImpact {
        /**
         * 平均持续影响
         */
        private BigDecimal averagePersistentImpact;

        /**
         * 影响衰减率
         */
        private BigDecimal impactDecayRate;

        /**
         * 影响半衰期
         */
        private BigDecimal impactHalfLife;

        /**
         * 长期影响
         */
        private BigDecimal longTermImpact;

        /**
         * 影响持续性
         */
        private BigDecimal impactPersistence;
    }

    /**
     * 价格恢复分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRecoveryAnalysis {
        /**
         * 平均恢复时间
         */
        private BigDecimal averageRecoveryTime;

        /**
         * 恢复完整性
         */
        private BigDecimal recoveryCompleteness;

        /**
         * 恢复速度
         */
        private BigDecimal recoverySpeed;

        /**
         * 恢复模式
         */
        private String recoveryPattern;

        /**
         * 恢复成功率
         */
        private BigDecimal recoverySuccessRate;
    }

    /**
     * 影响因子分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpactFactorAnalysis {
        /**
         * 订单大小影响
         */
        private BigDecimal orderSizeImpact;

        /**
         * 市场流动性影响
         */
        private BigDecimal marketLiquidityImpact;

        /**
         * 市场波动性影响
         */
        private BigDecimal marketVolatilityImpact;

        /**
         * 时间因子影响
         */
        private BigDecimal timeFactorImpact;

        /**
         * 执行策略影响
         */
        private BigDecimal executionStrategyImpact;

        /**
         * 市场深度影响
         */
        private BigDecimal marketDepthImpact;
    }

    /**
     * 价格影响预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceImpactForecast {
        /**
         * 预测模型类型
         */
        private String forecastModelType;

        /**
         * 短期影响预测
         */
        private BigDecimal shortTermImpactForecast;

        /**
         * 中期影响预测
         */
        private BigDecimal mediumTermImpactForecast;

        /**
         * 长期影响预测
         */
        private BigDecimal longTermImpactForecast;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 预测准确性
         */
        private BigDecimal forecastAccuracy;
    }

    /**
     * 大单模式识别
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderPatternRecognition {
        /**
         * 识别的模式
         */
        private List<LargeOrderPattern> identifiedPatterns;

        /**
         * 模式强度评估
         */
        private PatternStrengthAssessment patternStrengthAssessment;

        /**
         * 模式预测
         */
        private PatternForecast patternForecast;

        /**
         * 模式交易信号
         */
        private List<PatternTradingSignal> patternTradingSignals;

        public List<LargeOrderPattern> getIdentifiedPatterns() {
            return identifiedPatterns;
        }
    }

    /**
     * 大单模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderPattern {
        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 检测强度
         */
        private BigDecimal detectionStrength;

        /**
         * 模式频率
         */
        private BigDecimal patternFrequency;

        public String getPatternName() {
            return patternName;
        }

        public BigDecimal getDetectionStrength() {
            return detectionStrength;
        }

        /**
         * 历史成功率
         */
        private BigDecimal historicalSuccessRate;

        /**
         * 模式特征
         */
        private List<String> patternCharacteristics;

        /**
         * 模式参数
         */
        private Map<String, BigDecimal> patternParameters;
    }

    /**
     * 模式强度评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternStrengthAssessment {
        /**
         * 整体模式强度
         */
        private BigDecimal overallPatternStrength;

        /**
         * 最强模式
         */
        private String strongestPattern;

        /**
         * 模式一致性
         */
        private BigDecimal patternConsistency;

        /**
         * 模式稳定性
         */
        private BigDecimal patternStability;

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;
    }

    /**
     * 模式预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternForecast {
        /**
         * 下一个可能模式
         */
        private String nextLikelyPattern;

        /**
         * 模式出现概率
         */
        private BigDecimal patternProbability;

        /**
         * 预期时间窗口
         */
        private String expectedTimeWindow;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;
    }

    /**
     * 模式交易信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternTradingSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 信号方向
         */
        private String signalDirection;

        /**
         * 信号时效性
         */
        private String signalValidity;

        /**
         * 建议行动
         */
        private String recommendedAction;

        /**
         * 风险等级
         */
        private String riskLevel;
    }

    /**
     * 大单风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }

        /**
         * 风险评分
         */
        private BigDecimal riskScore;

        /**
         * 流动性风险
         */
        private LiquidityRisk liquidityRisk;

        /**
         * 执行风险
         */
        private ExecutionRisk executionRisk;

        /**
         * 市场冲击风险
         */
        private MarketImpactRisk marketImpactRisk;

        /**
         * 时机风险
         */
        private TimingRisk timingRisk;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 流动性风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityRisk {
        /**
         * 流动性不足风险
         */
        private BigDecimal liquidityShortageRisk;

        /**
         * 流动性成本风险
         */
        private BigDecimal liquidityCostRisk;

        /**
         * 流动性时间风险
         */
        private BigDecimal liquidityTimingRisk;

        /**
         * 流动性集中风险
         */
        private BigDecimal liquidityConcentrationRisk;
    }

    /**
     * 执行风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionRisk {
        /**
         * 部分成交风险
         */
        private BigDecimal partialFillRisk;

        /**
         * 执行延迟风险
         */
        private BigDecimal executionDelayRisk;

        /**
         * 滑点风险
         */
        private BigDecimal slippageRisk;

        /**
         * 执行成本风险
         */
        private BigDecimal executionCostRisk;
    }

    /**
     * 市场冲击风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketImpactRisk {
        /**
         * 价格冲击风险
         */
        private BigDecimal priceImpactRisk;

        /**
         * 波动性增加风险
         */
        private BigDecimal volatilityIncreaseRisk;

        /**
         * 市场失衡风险
         */
        private BigDecimal marketImbalanceRisk;

        /**
         * 连锁反应风险
         */
        private BigDecimal chainReactionRisk;
    }

    /**
     * 时机风险
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimingRisk {
        /**
         * 时机选择风险
         */
        private BigDecimal timingSelectionRisk;

        /**
         * 市场时机风险
         */
        private BigDecimal marketTimingRisk;

        /**
         * 执行时机风险
         */
        private BigDecimal executionTimingRisk;

        /**
         * 机会成本风险
         */
        private BigDecimal opportunityCostRisk;
    }

    /**
     * 大单预测分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderForecastAnalysis {
        /**
         * 短期预测
         */
        private LargeOrderForecast shortTermForecast;

        /**
         * 中期预测
         */
        private LargeOrderForecast mediumTermForecast;

        /**
         * 长期预测
         */
        private LargeOrderForecast longTermForecast;

        /**
         * 预测模型评估
         */
        private ForecastModelEvaluation forecastModelEvaluation;

        /**
         * 获取短期预测
         */
        public LargeOrderForecast getShortTermForecast() {
            return shortTermForecast;
        }
    }

    /**
     * 大单预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderForecast {
        /**
         * 预测时间范围
         */
        private String forecastTimeRange;

        /**
         * 预测大单数量
         */
        private Integer predictedLargeOrderCount;

        /**
         * 预测大单总量
         */
        private BigDecimal predictedLargeOrderVolume;

        /**
         * 预测活动等级
         */
        private LargeOrderActivityLevel predictedActivityLevel;

        /**
         * 预测流向
         */
        private String predictedFlowDirection;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 预测区间
         */
        private ForecastRange forecastRange;

        /**
         * 获取预测大单数量
         */
        public Integer getPredictedLargeOrderCount() {
            return predictedLargeOrderCount;
        }

        /**
         * 获取预测活动等级
         */
        public LargeOrderActivityLevel getPredictedActivityLevel() {
            return predictedActivityLevel;
        }
    }

    /**
     * 预测区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastModelEvaluation {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型精确度
         */
        private BigDecimal modelPrecision;

        /**
         * 模型召回率
         */
        private BigDecimal modelRecall;

        /**
         * 模型F1分数
         */
        private BigDecimal modelF1Score;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;
    }

    /**
     * 大单交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderTradingAdvice {
        /**
         * 执行策略建议
         */
        private List<ExecutionStrategyAdvice> executionStrategyAdvices;

        /**
         * 时机建议
         */
        private TimingAdvice timingAdvice;

        /**
         * 风险管理建议
         */
        private List<String> riskManagementAdvices;

        /**
         * 跟随策略
         */
        private List<FollowingStrategy> followingStrategies;

        /**
         * 对冲建议
         */
        private List<HedgingAdvice> hedgingAdvices;

        /**
         * 获取跟随策略
         */
        public List<FollowingStrategy> getFollowingStrategies() {
            return followingStrategies;
        }

        /**
         * 获取对冲建议
         */
        public List<HedgingAdvice> getHedgingAdvices() {
            return hedgingAdvices;
        }
    }

    /**
     * 执行策略建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionStrategyAdvice {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 适用场景
         */
        private List<String> applicableScenarios;

        /**
         * 预期效果
         */
        private String expectedEffect;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 成功概率
         */
        private BigDecimal successProbability;
    }

    /**
     * 时机建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimingAdvice {
        /**
         * 最佳执行时间
         */
        private String optimalExecutionTime;

        /**
         * 避免时间段
         */
        private List<String> timePeriodsToAvoid;

        /**
         * 时机评分
         */
        private BigDecimal timingScore;

        /**
         * 时机风险
         */
        private BigDecimal timingRisk;

        /**
         * 时机建议理由
         */
        private String timingRationale;

        /**
         * 获取时机评分
         */
        public BigDecimal getTimingScore() {
            return timingScore;
        }

        public String getOptimalExecutionTime() {
            return optimalExecutionTime;
        }
    }

    /**
     * 跟随策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FollowingStrategy {
        /**
         * 策略类型
         */
        private String strategyType;

        /**
         * 跟随目标
         */
        private String followingTarget;

        /**
         * 跟随强度
         */
        private BigDecimal followingIntensity;

        /**
         * 跟随时机
         */
        private String followingTiming;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险评估
         */
        private BigDecimal riskAssessment;
    }

    /**
     * 对冲建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HedgingAdvice {
        /**
         * 对冲类型
         */
        private String hedgingType;

        /**
         * 对冲工具
         */
        private String hedgingInstrument;

        /**
         * 对冲比例
         */
        private BigDecimal hedgingRatio;

        /**
         * 对冲成本
         */
        private BigDecimal hedgingCost;

        /**
         * 对冲效果
         */
        private BigDecimal hedgingEffectiveness;

        /**
         * 对冲期限
         */
        private String hedgingDuration;
    }

    /**
     * 大单预警
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LargeOrderAlert {
        /**
         * 预警ID
         */
        private String alertId;

        /**
         * 预警类型
         */
        private AlertType alertType;

        /**
         * 预警等级
         */
        private AlertLevel alertLevel;

        /**
         * 预警时间
         */
        private LocalDateTime alertTime;

        /**
         * 预警描述
         */
        private String alertDescription;

        /**
         * 触发条件
         */
        private List<String> triggerConditions;

        /**
         * 相关大单
         */
        private List<String> relatedLargeOrders;

        /**
         * 建议行动
         */
        private List<String> recommendedActions;

        /**
         * 预警状态
         */
        private String alertStatus;

        public AlertLevel getAlertLevel() {
            return alertLevel;
        }
    }

    /**
     * 预警类型
     */
    public enum AlertType {
        LARGE_ORDER_DETECTED("大单检测"),
        UNUSUAL_ACTIVITY("异常活动"),
        MARKET_IMPACT_WARNING("市场冲击预警"),
        LIQUIDITY_RISK("流动性风险"),
        EXECUTION_RISK("执行风险"),
        PATTERN_DETECTED("模式检测"),
        FLOW_IMBALANCE("流向失衡"),
        TIMING_ALERT("时机预警"),
        OTHER("其他");

        private final String description;

        AlertType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 预警等级
     */
    public enum AlertLevel {
        CRITICAL("严重", 5),
        HIGH("高", 4),
        MEDIUM("中等", 3),
        LOW("低", 2),
        INFO("信息", 1);

        private final String description;
        private final int level;

        AlertLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 获取大单分析摘要
     */
    public String getLargeOrderAnalysisSummary() {
        return String.format("活动等级: %s, 大单总数: %d, 净流向: %s",
            activityLevel != null ? activityLevel.getDescription() : "未知",
            largeOrderStatistics != null && largeOrderStatistics.getTotalLargeOrders() != null ? 
                largeOrderStatistics.getTotalLargeOrders() : 0,
            largeOrderFlowAnalysis != null && largeOrderFlowAnalysis.getFlowDirection() != null ? 
                largeOrderFlowAnalysis.getFlowDirection() : "未知");
    }

    /**
     * 检查是否为高活动等级
     */
    public boolean isHighActivity() {
        return activityLevel == LargeOrderActivityLevel.HIGH || 
               activityLevel == LargeOrderActivityLevel.EXTREMELY_HIGH;
    }

    /**
     * 检查是否存在流向失衡
     */
    public boolean hasFlowImbalance() {
        if (largeOrderFlowAnalysis == null || largeOrderFlowAnalysis.getFlowImbalance() == null) {
            return false;
        }
        return largeOrderFlowAnalysis.getFlowImbalance().compareTo(BigDecimal.valueOf(0.3)) > 0;
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (largeOrderTradingAdvice == null || 
            largeOrderTradingAdvice.getExecutionStrategyAdvices() == null ||
            largeOrderTradingAdvice.getExecutionStrategyAdvices().isEmpty()) {
            return "暂无交易建议";
        }
        
        ExecutionStrategyAdvice topAdvice = largeOrderTradingAdvice.getExecutionStrategyAdvices().get(0);
        return String.format("%s - %s", topAdvice.getStrategyName(), topAdvice.getStrategyDescription());
    }

    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (largeOrderRiskAssessment == null) {
            return "风险等级未评估";
        }
        return largeOrderRiskAssessment.getOverallRiskLevel();
    }

    /**
     * 检查是否需要立即关注
     */
    public boolean requiresImmediateAttention() {
        return isHighActivity() || hasFlowImbalance() || 
               (largeOrderAlerts != null && largeOrderAlerts.stream()
                   .anyMatch(alert -> alert.getAlertLevel() == AlertLevel.CRITICAL || 
                                    alert.getAlertLevel() == AlertLevel.HIGH));
    }

    /**
     * 获取最高优先级预警
     */
    public LargeOrderAlert getHighestPriorityAlert() {
        if (largeOrderAlerts == null || largeOrderAlerts.isEmpty()) {
            return null;
        }
        
        return largeOrderAlerts.stream()
            .max((a1, a2) -> Integer.compare(a1.getAlertLevel().getLevel(), a2.getAlertLevel().getLevel()))
            .orElse(null);
    }

    /**
     * 获取价格影响评估
     */
    public String getPriceImpactAssessment() {
        if (largeOrderPriceImpactAnalysis == null) {
            return "价格影响未评估";
        }
        
        BigDecimal overallImpact = largeOrderPriceImpactAnalysis.getOverallPriceImpact();
        if (overallImpact == null) {
            return "价格影响数据不可用";
        }
        
        if (overallImpact.compareTo(BigDecimal.valueOf(0.05)) >= 0) {
            return "高价格影响";
        } else if (overallImpact.compareTo(BigDecimal.valueOf(0.02)) >= 0) {
            return "中等价格影响";
        } else {
            return "低价格影响";
        }
    }

    /**
     * 获取流向分析摘要
     */
    public String getFlowAnalysisSummary() {
        if (largeOrderFlowAnalysis == null) {
            return "流向分析不可用";
        }
        
        String direction = largeOrderFlowAnalysis.getFlowDirection();
        BigDecimal intensity = largeOrderFlowAnalysis.getFlowIntensity();
        
        return String.format("流向: %s, 强度: %s",
            direction != null ? direction : "未知",
            intensity != null ? intensity.toString() : "N/A");
    }

    /**
     * 获取模式识别摘要
     */
    public String getPatternRecognitionSummary() {
        if (largeOrderPatternRecognition == null || 
            largeOrderPatternRecognition.getIdentifiedPatterns() == null ||
            largeOrderPatternRecognition.getIdentifiedPatterns().isEmpty()) {
            return "未识别到明显模式";
        }
        
        LargeOrderPattern topPattern = largeOrderPatternRecognition.getIdentifiedPatterns().get(0);
        return String.format("主要模式: %s (强度: %s)",
            topPattern.getPatternName(),
            topPattern.getDetectionStrength() != null ? topPattern.getDetectionStrength().toString() : "N/A");
    }

    /**
     * 获取预测摘要
     */
    public String getForecastSummary() {
        if (largeOrderForecastAnalysis == null || 
            largeOrderForecastAnalysis.getShortTermForecast() == null) {
            return "预测分析不可用";
        }
        
        LargeOrderForecast shortTerm = largeOrderForecastAnalysis.getShortTermForecast();
        return String.format("短期预测: %s活动, 预测大单数: %d",
            shortTerm.getPredictedActivityLevel() != null ? 
                shortTerm.getPredictedActivityLevel().getDescription() : "未知",
            shortTerm.getPredictedLargeOrderCount() != null ? 
                shortTerm.getPredictedLargeOrderCount() : 0);
    }

    /**
     * 检查是否有跟随机会
     */
    public boolean hasFollowingOpportunities() {
        return largeOrderTradingAdvice != null && 
               largeOrderTradingAdvice.getFollowingStrategies() != null &&
               !largeOrderTradingAdvice.getFollowingStrategies().isEmpty();
    }

    /**
     * 检查是否需要对冲
     */
    public boolean needsHedging() {
        return largeOrderTradingAdvice != null && 
               largeOrderTradingAdvice.getHedgingAdvices() != null &&
               !largeOrderTradingAdvice.getHedgingAdvices().isEmpty();
    }

    /**
     * 获取执行建议摘要
     */
    public String getExecutionAdviceSummary() {
        if (largeOrderTradingAdvice == null || 
            largeOrderTradingAdvice.getTimingAdvice() == null) {
            return "执行建议不可用";
        }
        
        TimingAdvice timing = largeOrderTradingAdvice.getTimingAdvice();
        return String.format("最佳执行时间: %s (评分: %s)",
            timing.getOptimalExecutionTime() != null ? timing.getOptimalExecutionTime() : "未指定",
            timing.getTimingScore() != null ? timing.getTimingScore().toString() : "N/A");
    }
}