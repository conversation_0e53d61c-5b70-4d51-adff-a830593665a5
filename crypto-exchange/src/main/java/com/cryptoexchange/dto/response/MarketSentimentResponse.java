package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场情绪分析响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketSentimentResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 分析时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime analysisTime;
    
    /**
     * 分析时间范围
     */
    private String timeRange;
    
    /**
     * 整体情绪等级
     */
    private SentimentLevel overallSentiment;
    
    /**
     * 情绪得分 (0-100)
     */
    private BigDecimal sentimentScore;
    
    /**
     * 情绪变化趋势
     */
    private SentimentTrend sentimentTrend;
    
    /**
     * 社交媒体情绪分析
     */
    private SocialMediaSentiment socialMediaSentiment;
    
    /**
     * 新闻情绪分析
     */
    private NewsSentiment newsSentiment;
    
    /**
     * 交易情绪分析
     */
    private TradingSentiment tradingSentiment;
    
    /**
     * 恐慌贪婪指数
     */
    private FearGreedIndex fearGreedIndex;
    
    /**
     * 市场参与者情绪
     */
    private MarketParticipantSentiment participantSentiment;
    
    /**
     * 技术指标情绪
     */
    private TechnicalSentiment technicalSentiment;
    
    /**
     * 基本面情绪
     */
    private FundamentalSentiment fundamentalSentiment;
    
    /**
     * 情绪驱动因素
     */
    private List<SentimentDriver> sentimentDrivers;
    
    /**
     * 情绪历史对比
     */
    private SentimentHistoricalComparison historicalComparison;
    
    /**
     * 情绪预测
     */
    private SentimentForecast sentimentForecast;
    
    /**
     * 情绪交易建议
     */
    private SentimentTradingAdvice tradingAdvice;
    
    /**
     * 情绪风险评估
     */
    private SentimentRiskAssessment riskAssessment;
    
    /**
     * 情绪警报
     */
    private List<SentimentAlert> sentimentAlerts;
    
    /**
     * 情绪等级枚举
     */
    public enum SentimentLevel {
        EXTREMELY_BEARISH,
        VERY_BEARISH,
        BEARISH,
        SLIGHTLY_BEARISH,
        NEUTRAL,
        SLIGHTLY_BULLISH,
        BULLISH,
        VERY_BULLISH,
        EXTREMELY_BULLISH
    }
    
    /**
     * 情绪趋势
     */
    public enum SentimentTrend {
        RAPIDLY_IMPROVING,
        IMPROVING,
        STABLE,
        DETERIORATING,
        RAPIDLY_DETERIORATING
    }
    
    /**
     * 社交媒体情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SocialMediaSentiment {
        private BigDecimal overallScore;
        private SentimentLevel sentimentLevel;
        private TwitterSentiment twitterSentiment;
        private RedditSentiment redditSentiment;
        private TelegramSentiment telegramSentiment;
        private DiscordSentiment discordSentiment;
        private BigDecimal mentionVolume;
        private BigDecimal engagementRate;
        private List<String> trendingTopics;
        private List<String> influencerOpinions;
        private BigDecimal viralityScore;
        private SentimentTrend trend;
    }
    
    /**
     * Twitter情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TwitterSentiment {
        private BigDecimal sentimentScore;
        private Integer positiveCount;
        private Integer negativeCount;
        private Integer neutralCount;
        private BigDecimal engagementRate;
        private List<String> topHashtags;
        private List<String> influencerTweets;
        private BigDecimal retweetVolume;
    }
    
    /**
     * Reddit情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RedditSentiment {
        private BigDecimal sentimentScore;
        private Integer upvotes;
        private Integer downvotes;
        private Integer commentCount;
        private List<String> topSubreddits;
        private List<String> hotPosts;
        private BigDecimal communityEngagement;
    }
    
    /**
     * Telegram情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TelegramSentiment {
        private BigDecimal sentimentScore;
        private Integer messageCount;
        private Integer activeUsers;
        private List<String> topChannels;
        private BigDecimal activityLevel;
    }
    
    /**
     * Discord情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DiscordSentiment {
        private BigDecimal sentimentScore;
        private Integer messageCount;
        private Integer activeUsers;
        private List<String> topServers;
        private BigDecimal activityLevel;
    }
    
    /**
     * 新闻情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsSentiment {
        private BigDecimal overallScore;
        private SentimentLevel sentimentLevel;
        private Integer positiveNewsCount;
        private Integer negativeNewsCount;
        private Integer neutralNewsCount;
        private List<NewsArticle> topNews;
        private BigDecimal mediaAttention;
        private List<String> keyTopics;
        private SentimentTrend trend;
        private BigDecimal credibilityScore;
    }
    
    /**
     * 新闻文章
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsArticle {
        private String title;
        private String source;
        private BigDecimal sentimentScore;
        private SentimentLevel sentimentLevel;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime publishTime;
        private String url;
        private BigDecimal influence;
        private List<String> keywords;
    }
    
    /**
     * 交易情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingSentiment {
        private BigDecimal overallScore;
        private SentimentLevel sentimentLevel;
        private OrderBookSentiment orderBookSentiment;
        private VolumeBasedSentiment volumeSentiment;
        private PriceActionSentiment priceActionSentiment;
        private DerivativesSentiment derivativesSentiment;
        private BigDecimal buyPressure;
        private BigDecimal sellPressure;
        private SentimentTrend trend;
    }
    
    /**
     * 订单簿情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderBookSentiment {
        private BigDecimal bidAskRatio;
        private BigDecimal orderBookImbalance;
        private BigDecimal largeOrderRatio;
        private String dominantSide;
        private BigDecimal liquidityScore;
    }
    
    /**
     * 成交量情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeBasedSentiment {
        private BigDecimal buyVolumeRatio;
        private BigDecimal sellVolumeRatio;
        private BigDecimal volumeWeightedSentiment;
        private BigDecimal unusualVolumeActivity;
        private String volumeTrend;
    }
    
    /**
     * 价格行为情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceActionSentiment {
        private BigDecimal priceVelocity;
        private BigDecimal volatilityScore;
        private String pricePattern;
        private BigDecimal momentumScore;
        private String trendStrength;
    }
    
    /**
     * 衍生品情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DerivativesSentiment {
        private BigDecimal futuresOpenInterest;
        private BigDecimal optionsCallPutRatio;
        private BigDecimal fundingRate;
        private BigDecimal perpetualPremium;
        private String leverageUsage;
    }
    
    /**
     * 恐慌贪婪指数
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FearGreedIndex {
        private BigDecimal currentValue;
        private FearGreedLevel level;
        private BigDecimal volatilityComponent;
        private BigDecimal volumeComponent;
        private BigDecimal socialMediaComponent;
        private BigDecimal dominanceComponent;
        private BigDecimal trendsComponent;
        private SentimentTrend trend;
        private String interpretation;
    }
    
    /**
     * 恐慌贪婪等级
     */
    public enum FearGreedLevel {
        EXTREME_FEAR,
        FEAR,
        NEUTRAL,
        GREED,
        EXTREME_GREED
    }
    
    /**
     * 市场参与者情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketParticipantSentiment {
        private RetailInvestorSentiment retailSentiment;
        private InstitutionalSentiment institutionalSentiment;
        private WhaleActivitySentiment whaleSentiment;
        private DeveloperSentiment developerSentiment;
        private BigDecimal participationRate;
        private String marketMood;
    }
    
    /**
     * 散户投资者情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetailInvestorSentiment {
        private BigDecimal sentimentScore;
        private SentimentLevel level;
        private BigDecimal fomoBehavior;
        private BigDecimal panicSelling;
        private BigDecimal hodlBehavior;
        private String tradingPattern;
    }
    
    /**
     * 机构投资者情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InstitutionalSentiment {
        private BigDecimal sentimentScore;
        private SentimentLevel level;
        private BigDecimal inflowOutflow;
        private BigDecimal adoptionRate;
        private List<String> institutionalNews;
        private String investmentTrend;
    }
    
    /**
     * 巨鲸活动情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WhaleActivitySentiment {
        private BigDecimal activityScore;
        private String activityType;
        private BigDecimal accumulationScore;
        private BigDecimal distributionScore;
        private List<String> whaleTransactions;
        private String whaleIntent;
    }
    
    /**
     * 开发者情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeveloperSentiment {
        private BigDecimal activityScore;
        private Integer githubCommits;
        private Integer activeContributors;
        private BigDecimal developmentProgress;
        private List<String> recentUpdates;
        private String communityHealth;
    }
    
    /**
     * 技术指标情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalSentiment {
        private BigDecimal overallScore;
        private SentimentLevel level;
        private BigDecimal trendScore;
        private BigDecimal momentumScore;
        private BigDecimal volatilityScore;
        private BigDecimal supportResistanceScore;
        private String technicalOutlook;
        private List<String> keyIndicators;
    }
    
    /**
     * 基本面情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FundamentalSentiment {
        private BigDecimal overallScore;
        private SentimentLevel level;
        private BigDecimal adoptionScore;
        private BigDecimal utilityScore;
        private BigDecimal competitionScore;
        private BigDecimal regulatoryScore;
        private String fundamentalOutlook;
        private List<String> keyFactors;
    }
    
    /**
     * 情绪驱动因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentDriver {
        private String factor;
        private String description;
        private BigDecimal impact;
        private String impactDirection;
        private BigDecimal confidence;
        private String category;
        private String timeframe;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime identifiedTime;
    }
    
    /**
     * 情绪历史对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentHistoricalComparison {
        private BigDecimal currentScore;
        private BigDecimal weekAgoScore;
        private BigDecimal monthAgoScore;
        private BigDecimal yearAgoScore;
        private BigDecimal percentileRank;
        private String historicalContext;
        private List<String> similarPeriods;
        private String cyclicalPattern;
    }
    
    /**
     * 情绪预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentForecast {
        private List<SentimentPrediction> predictions;
        private BigDecimal confidence;
        private String methodology;
        private List<String> keyAssumptions;
        private List<String> riskFactors;
        private String disclaimer;
    }
    
    /**
     * 情绪预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentPrediction {
        private String timeframe;
        private SentimentLevel predictedLevel;
        private BigDecimal predictedScore;
        private BigDecimal confidence;
        private String scenario;
        private List<String> catalysts;
    }
    
    /**
     * 情绪交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentTradingAdvice {
        private String overallRecommendation;
        private String strategy;
        private String timing;
        private BigDecimal positionSize;
        private String riskLevel;
        private List<String> entrySignals;
        private List<String> exitSignals;
        private String contrarian;
        private String momentum;
    }
    
    /**
     * 情绪风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentRiskAssessment {
        private String overallRisk;
        private BigDecimal riskScore;
        private List<SentimentRiskFactor> riskFactors;
        private String volatilityRisk;
        private String liquidityRisk;
        private String crowdBehaviorRisk;
        private List<String> mitigationStrategies;
    }
    
    /**
     * 情绪风险因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentRiskFactor {
        private String factor;
        private String description;
        private String severity;
        private BigDecimal probability;
        private String impact;
        private String mitigation;
    }
    
    /**
     * 情绪警报
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentAlert {
        private String alertType;
        private String severity;
        private String message;
        private String trigger;
        private BigDecimal threshold;
        private String action;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime alertTime;
        private String recommendation;
    }
}