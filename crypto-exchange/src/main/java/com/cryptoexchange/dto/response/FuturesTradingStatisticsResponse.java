package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货交易统计响应
 */
public class FuturesTradingStatisticsResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总交易量
     */
    private BigDecimal totalVolume;
    
    /**
     * 总交易次数
     */
    private Long totalTrades;
    
    /**
     * 总盈亏
     */
    private BigDecimal totalPnl;
    
    /**
     * 胜率
     */
    private BigDecimal winRate;
    
    /**
     * 平均持仓时间（小时）
     */
    private BigDecimal avgHoldingTime;
    
    /**
     * 最大盈利
     */
    private BigDecimal maxProfit;
    
    /**
     * 最大亏损
     */
    private BigDecimal maxLoss;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    public FuturesTradingStatisticsResponse() {}
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public BigDecimal getTotalVolume() {
        return totalVolume;
    }
    
    public void setTotalVolume(BigDecimal totalVolume) {
        this.totalVolume = totalVolume;
    }
    
    public Long getTotalTrades() {
        return totalTrades;
    }
    
    public void setTotalTrades(Long totalTrades) {
        this.totalTrades = totalTrades;
    }
    
    public BigDecimal getTotalPnl() {
        return totalPnl;
    }
    
    public void setTotalPnl(BigDecimal totalPnl) {
        this.totalPnl = totalPnl;
    }
    
    public BigDecimal getWinRate() {
        return winRate;
    }
    
    public void setWinRate(BigDecimal winRate) {
        this.winRate = winRate;
    }
    
    public BigDecimal getAvgHoldingTime() {
        return avgHoldingTime;
    }
    
    public void setAvgHoldingTime(BigDecimal avgHoldingTime) {
        this.avgHoldingTime = avgHoldingTime;
    }
    
    public BigDecimal getMaxProfit() {
        return maxProfit;
    }
    
    public void setMaxProfit(BigDecimal maxProfit) {
        this.maxProfit = maxProfit;
    }
    
    public BigDecimal getMaxLoss() {
        return maxLoss;
    }
    
    public void setMaxLoss(BigDecimal maxLoss) {
        this.maxLoss = maxLoss;
    }
    
    public LocalDateTime getStartTime() {
        return startTime;
    }
    
    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }
    
    public LocalDateTime getEndTime() {
        return endTime;
    }
    
    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }
}