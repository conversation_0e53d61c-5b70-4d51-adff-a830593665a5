package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;

/**
 * 服务器时间响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "服务器时间响应")
public class ServerTimeResponse {

    @Schema(description = "服务器时间戳(毫秒)")
    private Long serverTime;

    @Schema(description = "服务器时间戳(秒)")
    private Long serverTimeSeconds;

    @Schema(description = "服务器时间戳(纳秒)")
    private Long serverTimeNanos;

    @Schema(description = "服务器本地时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime serverLocalTime;

    @Schema(description = "UTC时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime utcTime;

    @Schema(description = "服务器时区")
    private String serverTimeZone;

    @Schema(description = "服务器时区ID")
    private ZoneId serverZoneId;

    @Schema(description = "时区偏移量(小时)")
    private Integer timezoneOffset;

    @Schema(description = "是否夏令时")
    private Boolean isDaylightSaving;

    @Schema(description = "ISO 8601格式时间")
    private String iso8601Time;

    @Schema(description = "RFC 3339格式时间")
    private String rfc3339Time;

    @Schema(description = "Unix时间戳")
    private Long unixTimestamp;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "月份")
    private Integer month;

    @Schema(description = "日期")
    private Integer day;

    @Schema(description = "小时")
    private Integer hour;

    @Schema(description = "分钟")
    private Integer minute;

    @Schema(description = "秒")
    private Integer second;

    @Schema(description = "毫秒")
    private Integer millisecond;

    @Schema(description = "星期几(1-7)")
    private Integer dayOfWeek;

    @Schema(description = "星期几名称")
    private String dayOfWeekName;

    @Schema(description = "一年中的第几天")
    private Integer dayOfYear;

    @Schema(description = "一年中的第几周")
    private Integer weekOfYear;

    @Schema(description = "一月中的第几周")
    private Integer weekOfMonth;

    @Schema(description = "季度")
    private Integer quarter;

    @Schema(description = "是否闰年")
    private Boolean isLeapYear;

    @Schema(description = "服务器启动时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime serverStartTime;

    @Schema(description = "服务器运行时长(毫秒)")
    private Long serverUptime;

    @Schema(description = "服务器运行时长(可读格式)")
    private String serverUptimeReadable;

    @Schema(description = "系统负载")
    private Double systemLoad;

    @Schema(description = "CPU使用率(%)")
    private Double cpuUsage;

    @Schema(description = "内存使用率(%)")
    private Double memoryUsage;

    @Schema(description = "磁盘使用率(%)")
    private Double diskUsage;

    @Schema(description = "网络延迟(毫秒)")
    private Long networkLatency;

    @Schema(description = "数据库连接状态")
    private String databaseStatus;

    @Schema(description = "Redis连接状态")
    private String redisStatus;

    @Schema(description = "消息队列状态")
    private String messageQueueStatus;

    @Schema(description = "API版本")
    private String apiVersion;

    @Schema(description = "服务器版本")
    private String serverVersion;

    @Schema(description = "构建版本")
    private String buildVersion;

    @Schema(description = "构建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime buildTime;

    @Schema(description = "环境")
    private String environment;

    @Schema(description = "服务器节点ID")
    private String nodeId;

    @Schema(description = "服务器节点名称")
    private String nodeName;

    @Schema(description = "数据中心")
    private String dataCenter;

    @Schema(description = "地理位置")
    private String geoLocation;

    @Schema(description = "全球时区时间")
    private Map<String, String> globalTimezones;

    @Schema(description = "主要交易所时间")
    private Map<String, String> exchangeTimes;

    @Schema(description = "市场开盘状态")
    private Map<String, Boolean> marketOpenStatus;

    @Schema(description = "下次维护时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextMaintenanceTime;

    @Schema(description = "系统状态")
    private String systemStatus;

    @Schema(description = "系统健康度(%)")
    private Double systemHealth;

    @Schema(description = "活跃连接数")
    private Integer activeConnections;

    @Schema(description = "总请求数")
    private Long totalRequests;

    @Schema(description = "每秒请求数")
    private Double requestsPerSecond;

    @Schema(description = "平均响应时间(毫秒)")
    private Double avgResponseTime;

    @Schema(description = "错误率(%)")
    private Double errorRate;

    @Schema(description = "限流状态")
    private String rateLimitStatus;

    @Schema(description = "剩余限流次数")
    private Integer remainingRateLimit;

    @Schema(description = "限流重置时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rateLimitResetTime;

    @Schema(description = "时间同步状态")
    private String timeSyncStatus;

    @Schema(description = "时间同步偏差(毫秒)")
    private Long timeSyncOffset;

    @Schema(description = "最后时间同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastTimeSyncTime;

    @Schema(description = "NTP服务器")
    private String ntpServer;

    @Schema(description = "时间精度(毫秒)")
    private Double timePrecision;

    @Schema(description = "响应生成时间(毫秒)")
    private Long responseGenerationTime;

    @Schema(description = "请求处理时间(毫秒)")
    private Long requestProcessingTime;

    @Schema(description = "缓存命中率(%)")
    private Double cacheHitRate;

    @Schema(description = "数据库查询时间(毫秒)")
    private Long databaseQueryTime;

    @Schema(description = "外部API调用时间(毫秒)")
    private Long externalApiCallTime;

    @Schema(description = "备注")
    private String remarks;
}