package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货持仓响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class FuturesPositionResponse {

    /**
     * 持仓ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 持仓方向：1-多头，2-空头
     */
    private Integer side;

    /**
     * 持仓数量
     */
    private BigDecimal quantity;

    /**
     * 可平仓数量
     */
    private BigDecimal availableQty;

    /**
     * 平均开仓价格
     */
    private BigDecimal avgOpenPrice;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 强平价格
     */
    private BigDecimal liquidationPrice;

    /**
     * 破产价格
     */
    private BigDecimal bankruptcyPrice;

    /**
     * 保证金
     */
    private BigDecimal margin;

    /**
     * 初始保证金
     */
    private BigDecimal initialMargin;

    /**
     * 维持保证金
     */
    private BigDecimal maintenanceMargin;

    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedPnl;

    /**
     * 已实现盈亏
     */
    private BigDecimal realizedPnl;

    /**
     * 持仓价值
     */
    private BigDecimal positionValue;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 保证金模式：1-逐仓，2-全仓
     */
    private Integer marginMode;

    /**
     * 持仓状态：1-持仓中，2-平仓中，3-已平仓
     */
    private Integer status;

    /**
     * 风险率
     */
    private BigDecimal riskRatio;

    /**
     * 收益率
     */
    private BigDecimal returnRate;

    /**
     * 收益率百分比
     */
    private BigDecimal returnRatePercent;

    /**
     * 持仓成本
     */
    private BigDecimal positionCost;

    /**
     * 最大可加仓数量
     */
    private BigDecimal maxIncreaseQty;

    /**
     * 最大可减仓数量
     */
    private BigDecimal maxDecreaseQty;

    /**
     * 自动追加保证金
     */
    private Boolean autoAddMargin;

    /**
     * 是否为对冲模式
     */
    private Boolean isHedgeMode;

    /**
     * 持仓开始时间
     */
    private LocalDateTime openTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}