package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 聚合交易响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "聚合交易响应")
public class AggTradeResponse {

    @Schema(description = "聚合交易ID")
    private Long aggTradeId;

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "成交金额")
    private BigDecimal amount;

    @Schema(description = "首个交易ID")
    private Long firstTradeId;

    @Schema(description = "最后交易ID")
    private Long lastTradeId;

    @Schema(description = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeTime;

    @Schema(description = "交易时间戳")
    private Long timestamp;

    @Schema(description = "是否为买单成交")
    private Boolean isBuyerMaker;

    @Schema(description = "交易方向")
    private String side;

    @Schema(description = "聚合的交易笔数")
    private Integer tradeCount;

    @Schema(description = "聚合时间窗口（毫秒）")
    private Long aggregationWindow;

    @Schema(description = "平均价格")
    private BigDecimal avgPrice;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "成交量加权平均价格")
    private BigDecimal vwap;

    @Schema(description = "标准差")
    private BigDecimal standardDeviation;

    @Schema(description = "买单数量")
    private BigDecimal buyQuantity;

    @Schema(description = "卖单数量")
    private BigDecimal sellQuantity;

    @Schema(description = "买单金额")
    private BigDecimal buyAmount;

    @Schema(description = "卖单金额")
    private BigDecimal sellAmount;

    @Schema(description = "买卖比例")
    private BigDecimal buySellRatio;

    @Schema(description = "总手续费")
    private BigDecimal totalFee;

    @Schema(description = "手续费货币")
    private String feeCurrency;

    @Schema(description = "市场影响")
    private BigDecimal marketImpact;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "交易密度")
    private BigDecimal tradeDensity;

    @Schema(description = "价格趋势")
    private String priceTrend;

    @Schema(description = "成交量趋势")
    private String volumeTrend;

    @Schema(description = "聚合方法")
    private String aggregationMethod;

    @Schema(description = "数据质量")
    private String dataQuality;

    @Schema(description = "是否为异常交易")
    private Boolean isAbnormal;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}