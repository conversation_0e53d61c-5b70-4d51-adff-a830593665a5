package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.util.List;

/**
 * 杠杆分层响应
 */
public class LeverageBracketResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 杠杆分层信息列表
     */
    private List<LeverageBracket> brackets;
    
    public LeverageBracketResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public List<LeverageBracket> getBrackets() {
        return brackets;
    }
    
    public void setBrackets(List<LeverageBracket> brackets) {
        this.brackets = brackets;
    }
    
    /**
     * 杠杆分层信息
     */
    public static class LeverageBracket {
        
        /**
         * 分层等级
         */
        private Integer bracket;
        
        /**
         * 该分层对应的最大名义价值
         */
        private BigDecimal notionalCap;
        
        /**
         * 该分层对应的最大杠杆倍数
         */
        private Integer maxLeverage;
        
        /**
         * 维持保证金率
         */
        private BigDecimal maintMarginRatio;
        
        /**
         * 速算数（用于计算维持保证金）
         */
        private BigDecimal cum;
        
        public LeverageBracket() {}
        
        public Integer getBracket() {
            return bracket;
        }
        
        public void setBracket(Integer bracket) {
            this.bracket = bracket;
        }
        
        public BigDecimal getNotionalCap() {
            return notionalCap;
        }
        
        public void setNotionalCap(BigDecimal notionalCap) {
            this.notionalCap = notionalCap;
        }
        
        public Integer getMaxLeverage() {
            return maxLeverage;
        }
        
        public void setMaxLeverage(Integer maxLeverage) {
            this.maxLeverage = maxLeverage;
        }
        
        public BigDecimal getMaintMarginRatio() {
            return maintMarginRatio;
        }
        
        public void setMaintMarginRatio(BigDecimal maintMarginRatio) {
            this.maintMarginRatio = maintMarginRatio;
        }
        
        public BigDecimal getCum() {
            return cum;
        }
        
        public void setCum(BigDecimal cum) {
            this.cum = cum;
        }
    }
}