package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易对价格响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TickerPriceResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 时间戳
     */
    private Long timestamp;
    
    /**
     * 数据来源
     */
    private String source;
    
    /**
     * 价格类型 (LAST, BID, ASK, MARK, INDEX)
     */
    private String priceType;
    
    /**
     * 价格精度
     */
    private Integer precision;
    
    /**
     * 是否为实时价格
     */
    private Boolean isRealTime;
    
    /**
     * 延迟时间（毫秒）
     */
    private Long latency;
}