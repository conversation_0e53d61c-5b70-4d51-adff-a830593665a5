package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场深度响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DepthResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 最后更新ID
     */
    private Long lastUpdateId;
    
    /**
     * 买单深度
     */
    private List<DepthEntry> bids;
    
    /**
     * 卖单深度
     */
    private List<DepthEntry> asks;
    
    /**
     * 数据时间戳
     */
    private Long timestamp;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 深度级别
     */
    private Integer level;
    
    /**
     * 是否为快照数据
     */
    private Boolean isSnapshot;
    
    /**
     * 序列号
     */
    private Long sequence;
    
    /**
     * 深度条目
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DepthEntry {
        /**
         * 价格
         */
        private BigDecimal price;
        
        /**
         * 数量
         */
        private BigDecimal quantity;
        
        /**
         * 订单数量
         */
        private Integer orderCount;
        
        /**
         * 累计数量
         */
        private BigDecimal cumulativeQuantity;
        
        /**
         * 累计金额
         */
        private BigDecimal cumulativeAmount;
        
        /**
         * 更新时间
         */
        private LocalDateTime updateTime;
    }
}