package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 双因子认证响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "双因子认证响应")
public class TwoFactorAuthResponse {

    @Schema(description = "密钥", example = "JBSWY3DPEHPK3PXP")
    private String secret;

    @Schema(description = "二维码URL", example = "otpauth://totp/CryptoExchange:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=CryptoExchange")
    private String qrCodeUrl;

    @Schema(description = "备份码列表")
    private String[] backupCodes;

    @Schema(description = "是否已启用", example = "true")
    private Boolean enabled;

    @Schema(description = "认证类型", example = "TOTP")
    private String authType;

    public TwoFactorAuthResponse() {}

    public TwoFactorAuthResponse(String secret, String qrCodeUrl, String[] backupCodes, Boolean enabled, String authType) {
        this.secret = secret;
        this.qrCodeUrl = qrCodeUrl;
        this.backupCodes = backupCodes;
        this.enabled = enabled;
        this.authType = authType;
    }
}