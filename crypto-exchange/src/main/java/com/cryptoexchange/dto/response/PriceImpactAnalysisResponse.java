package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格冲击分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceImpactAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 交易金额
     */
    private BigDecimal tradeAmount;

    /**
     * 交易方向（BUY/SELL）
     */
    private String tradeDirection;

    /**
     * 预期价格冲击
     */
    private BigDecimal expectedPriceImpact;

    /**
     * 价格冲击百分比
     */
    private BigDecimal priceImpactPercentage;

    /**
     * 市场深度分析
     */
    private MarketDepthAnalysis marketDepthAnalysis;

    /**
     * 流动性分析
     */
    private LiquidityAnalysis liquidityAnalysis;

    /**
     * 订单簿影响
     */
    private OrderBookImpact orderBookImpact;

    /**
     * 执行成本分析
     */
    private ExecutionCostAnalysis executionCostAnalysis;

    /**
     * 时间衰减分析
     */
    private TimeDecayAnalysis timeDecayAnalysis;

    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;

    /**
     * 执行建议
     */
    private List<ExecutionRecommendation> executionRecommendations;

    /**
     * 历史对比数据
     */
    private HistoricalComparison historicalComparison;

    /**
     * 市场深度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketDepthAnalysis {
        /**
         * 买单深度
         */
        private BigDecimal bidDepth;

        /**
         * 卖单深度
         */
        private BigDecimal askDepth;

        /**
         * 总深度
         */
        private BigDecimal totalDepth;

        /**
         * 深度不平衡度
         */
        private BigDecimal depthImbalance;

        /**
         * 有效深度（在合理价格范围内）
         */
        private BigDecimal effectiveDepth;

        /**
         * 深度分布
         */
        private Map<String, BigDecimal> depthDistribution;
    }

    /**
     * 流动性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityAnalysis {
        /**
         * 流动性指数
         */
        private BigDecimal liquidityIndex;

        /**
         * 买卖价差
         */
        private BigDecimal bidAskSpread;

        /**
         * 价差百分比
         */
        private BigDecimal spreadPercentage;

        /**
         * 平均交易规模
         */
        private BigDecimal averageTradeSize;

        /**
         * 交易频率
         */
        private BigDecimal tradeFrequency;

        /**
         * 流动性等级
         */
        private LiquidityLevel liquidityLevel;
    }

    /**
     * 订单簿影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderBookImpact {
        /**
         * 影响的订单层数
         */
        private Integer affectedLevels;

        /**
         * 消耗的流动性
         */
        private BigDecimal consumedLiquidity;

        /**
         * 剩余流动性
         */
        private BigDecimal remainingLiquidity;

        /**
         * 价格滑点
         */
        private BigDecimal priceSlippage;

        /**
         * 执行价格
         */
        private BigDecimal executionPrice;

        /**
         * 加权平均价格
         */
        private BigDecimal weightedAveragePrice;
    }

    /**
     * 执行成本分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionCostAnalysis {
        /**
         * 市场冲击成本
         */
        private BigDecimal marketImpactCost;

        /**
         * 时机成本
         */
        private BigDecimal timingCost;

        /**
         * 机会成本
         */
        private BigDecimal opportunityCost;

        /**
         * 交易费用
         */
        private BigDecimal tradingFees;

        /**
         * 总执行成本
         */
        private BigDecimal totalExecutionCost;

        /**
         * 成本百分比
         */
        private BigDecimal costPercentage;
    }

    /**
     * 时间衰减分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeDecayAnalysis {
        /**
         * 即时冲击
         */
        private BigDecimal immediateImpact;

        /**
         * 短期冲击（1分钟）
         */
        private BigDecimal shortTermImpact;

        /**
         * 中期冲击（5分钟）
         */
        private BigDecimal mediumTermImpact;

        /**
         * 长期冲击（15分钟）
         */
        private BigDecimal longTermImpact;

        /**
         * 恢复时间预估
         */
        private Integer estimatedRecoveryTime;

        /**
         * 衰减率
         */
        private BigDecimal decayRate;
    }

    /**
     * 风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        /**
         * 风险等级
         */
        private RiskLevel riskLevel;

        /**
         * 最大可能损失
         */
        private BigDecimal maxPotentialLoss;

        /**
         * 风险价值（VaR）
         */
        private BigDecimal valueAtRisk;

        /**
         * 置信区间
         */
        private BigDecimal confidenceInterval;

        /**
         * 压力测试结果
         */
        private Map<String, BigDecimal> stressTestResults;
    }

    /**
     * 执行建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecutionRecommendation {
        /**
         * 建议类型
         */
        private String recommendationType;

        /**
         * 建议策略
         */
        private String strategy;

        /**
         * 建议分批数量
         */
        private Integer recommendedBatches;

        /**
         * 建议时间间隔（秒）
         */
        private Integer recommendedInterval;

        /**
         * 预期改善效果
         */
        private BigDecimal expectedImprovement;

        /**
         * 优先级
         */
        private Integer priority;
    }

    /**
     * 历史对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalComparison {
        /**
         * 历史平均冲击
         */
        private BigDecimal historicalAverageImpact;

        /**
         * 相对历史水平
         */
        private BigDecimal relativeToHistorical;

        /**
         * 百分位排名
         */
        private BigDecimal percentileRank;

        /**
         * 同类交易对比
         */
        private Map<String, BigDecimal> similarTradesComparison;
    }

    /**
     * 流动性等级
     */
    public enum LiquidityLevel {
        VERY_HIGH("极高流动性", 5),
        HIGH("高流动性", 4),
        MEDIUM("中等流动性", 3),
        LOW("低流动性", 2),
        VERY_LOW("极低流动性", 1);

        private final String description;
        private final Integer level;

        LiquidityLevel(String description, Integer level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }
    }

    /**
     * 风险等级
     */
    public enum RiskLevel {
        LOW("低风险", 1),
        MEDIUM("中等风险", 2),
        HIGH("高风险", 3),
        VERY_HIGH("极高风险", 4);

        private final String description;
        private final Integer level;

        RiskLevel(String description, Integer level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }
    }

    /**
     * 获取冲击严重程度
     */
    public String getImpactSeverity() {
        if (priceImpactPercentage == null) {
            return "未知";
        }
        
        BigDecimal impact = priceImpactPercentage.abs();
        if (impact.compareTo(BigDecimal.valueOf(0.1)) <= 0) {
            return "轻微";
        } else if (impact.compareTo(BigDecimal.valueOf(0.5)) <= 0) {
            return "中等";
        } else if (impact.compareTo(BigDecimal.valueOf(1.0)) <= 0) {
            return "严重";
        } else {
            return "极严重";
        }
    }

    /**
     * 获取执行建议摘要
     */
    public String getExecutionSummary() {
        return String.format("预期价格冲击: %s%% (%s), 建议: %s",
            priceImpactPercentage != null ? priceImpactPercentage.multiply(BigDecimal.valueOf(100)).toString() : "N/A",
            getImpactSeverity(),
            executionRecommendations != null && !executionRecommendations.isEmpty() 
                ? executionRecommendations.get(0).getStrategy() : "无特殊建议");
    }

    /**
     * 检查是否建议分批执行
     */
    public boolean shouldUseBatchExecution() {
        return executionRecommendations != null && 
               executionRecommendations.stream()
                   .anyMatch(rec -> rec.getRecommendedBatches() != null && rec.getRecommendedBatches() > 1);
    }
}