package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货合约响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class FuturesContractResponse {

    /**
     * 合约ID
     */
    private Long id;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 合约名称
     */
    private String name;

    /**
     * 基础资产
     */
    private String baseAsset;

    /**
     * 报价资产
     */
    private String quoteAsset;

    /**
     * 合约类型：1-永续合约，2-交割合约
     */
    private Integer contractType;

    /**
     * 合约大小
     */
    private BigDecimal contractSize;

    /**
     * 最小价格变动
     */
    private BigDecimal tickSize;

    /**
     * 最小数量变动
     */
    private BigDecimal stepSize;

    /**
     * 最小下单数量
     */
    private BigDecimal minOrderQty;

    /**
     * 最大下单数量
     */
    private BigDecimal maxOrderQty;

    /**
     * 最小下单金额
     */
    private BigDecimal minOrderValue;

    /**
     * 最大下单金额
     */
    private BigDecimal maxOrderValue;

    /**
     * 最大杠杆倍数
     */
    private Integer maxLeverage;

    /**
     * 维持保证金率
     */
    private BigDecimal maintenanceMarginRate;

    /**
     * 初始保证金率
     */
    private BigDecimal initialMarginRate;

    /**
     * Maker手续费率
     */
    private BigDecimal makerFeeRate;

    /**
     * Taker手续费率
     */
    private BigDecimal takerFeeRate;

    /**
     * 资金费率
     */
    private BigDecimal fundingRate;

    /**
     * 资金费率收取间隔（小时）
     */
    private Integer fundingInterval;

    /**
     * 交割时间（仅交割合约）
     */
    private LocalDateTime deliveryTime;

    /**
     * 上市时间
     */
    private LocalDateTime listingTime;

    /**
     * 下市时间
     */
    private LocalDateTime delistingTime;

    /**
     * 合约状态：1-正常交易，2-暂停交易，3-仅平仓，4-已下市
     */
    private Integer status;

    /**
     * 是否支持做多
     */
    private Boolean longEnabled;

    /**
     * 是否支持做空
     */
    private Boolean shortEnabled;

    /**
     * 价格精度
     */
    private Integer pricePrecision;

    /**
     * 数量精度
     */
    private Integer quantityPrecision;

    /**
     * 基础资产精度
     */
    private Integer baseAssetPrecision;

    /**
     * 报价资产精度
     */
    private Integer quoteAssetPrecision;

    /**
     * 风险限额
     */
    private BigDecimal riskLimit;

    /**
     * 最大持仓数量
     */
    private BigDecimal maxPositionSize;

    /**
     * 当前标记价格
     */
    private BigDecimal markPrice;

    /**
     * 当前指数价格
     */
    private BigDecimal indexPrice;

    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange24h;

    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent24h;

    /**
     * 24小时最高价
     */
    private BigDecimal highPrice24h;

    /**
     * 24小时最低价
     */
    private BigDecimal lowPrice24h;

    /**
     * 24小时成交量
     */
    private BigDecimal volume24h;

    /**
     * 24小时成交额
     */
    private BigDecimal turnover24h;

    /**
     * 未平仓合约数量
     */
    private BigDecimal openInterest;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}