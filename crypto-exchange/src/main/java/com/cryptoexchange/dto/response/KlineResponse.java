package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * K线数据响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KlineResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * K线间隔
     */
    private String interval;
    
    /**
     * 开盘时间
     */
    private Long openTime;
    
    /**
     * 收盘时间
     */
    private Long closeTime;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 收盘价
     */
    private BigDecimal closePrice;
    
    /**
     * 交易量
     */
    private BigDecimal volume;
    
    /**
     * 交易额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 交易次数
     */
    private Long tradeCount;
    
    /**
     * 主动买入交易量
     */
    private BigDecimal takerBuyVolume;
    
    /**
     * 主动买入交易额
     */
    private BigDecimal takerBuyQuoteVolume;
    
    /**
     * 是否K线结束
     */
    private Boolean isClosed;
    
    /**
     * 开盘时间（格式化）
     */
    private LocalDateTime openDateTime;
    
    /**
     * 收盘时间（格式化）
     */
    private LocalDateTime closeDateTime;
    
    /**
     * 价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 加权平均价格
     */
    private BigDecimal weightedAvgPrice;
    
    /**
     * 振幅
     */
    private BigDecimal amplitude;
    
    /**
     * 换手率
     */
    private BigDecimal turnoverRate;
    
    /**
     * 成交金额占比
     */
    private BigDecimal volumeRatio;
    
    /**
     * VWAP (成交量加权平均价格)
     */
    private BigDecimal vwap;
    
    /**
     * 数据来源
     */
    private String source;
    
    /**
     * 数据质量
     */
    private String quality;
}