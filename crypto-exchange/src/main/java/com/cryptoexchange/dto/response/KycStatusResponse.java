package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * KYC状态响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "KYC状态响应")
public class KycStatusResponse {

    @Schema(description = "KYC状态", example = "PENDING")
    private String status;

    @Schema(description = "状态描述", example = "审核中")
    private String statusDescription;

    @Schema(description = "真实姓名", example = "张三")
    private String realName;

    @Schema(description = "身份证号（脱敏）", example = "110101********1234")
    private String maskedIdNumber;

    @Schema(description = "申请类型", example = "INDIVIDUAL")
    private String applicationType;

    @Schema(description = "申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applicationTime;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reviewTime;

    @Schema(description = "拒绝原因")
    private String rejectReason;

    @Schema(description = "审核员备注")
    private String reviewerNote;

    @Schema(description = "KYC等级", example = "1")
    private Integer kycLevel;

    @Schema(description = "是否可以重新申请", example = "true")
    private Boolean canReapply;

    public KycStatusResponse() {}

    public KycStatusResponse(String status, String statusDescription, String realName, 
                           String maskedIdNumber, String applicationType) {
        this.status = status;
        this.statusDescription = statusDescription;
        this.realName = realName;
        this.maskedIdNumber = maskedIdNumber;
        this.applicationType = applicationType;
    }
}