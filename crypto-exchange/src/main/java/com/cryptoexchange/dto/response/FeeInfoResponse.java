package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 手续费信息响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeeInfoResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户等级
     */
    private String userLevel;
    
    /**
     * VIP等级
     */
    private Integer vipLevel;
    
    /**
     * 交易手续费信息
     */
    private TradingFeeInfo tradingFeeInfo;
    
    /**
     * 提现手续费信息
     */
    private List<WithdrawFeeInfo> withdrawFeeInfos;
    
    /**
     * 充值手续费信息
     */
    private List<DepositFeeInfo> depositFeeInfos;
    
    /**
     * 转账手续费信息
     */
    private TransferFeeInfo transferFeeInfo;
    
    /**
     * 期货手续费信息
     */
    private FuturesFeeInfo futuresFeeInfo;
    
    /**
     * 手续费折扣信息
     */
    private FeeDiscountInfo feeDiscountInfo;
    
    /**
     * 手续费统计
     */
    private FeeStatistics feeStatistics;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 交易手续费信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TradingFeeInfo {
        /**
         * Maker手续费率
         */
        private BigDecimal makerFeeRate;
        
        /**
         * Taker手续费率
         */
        private BigDecimal takerFeeRate;
        
        /**
         * 现货交易手续费率
         */
        private BigDecimal spotFeeRate;
        
        /**
         * 杠杆交易手续费率
         */
        private BigDecimal marginFeeRate;
        
        /**
         * 最小手续费
         */
        private BigDecimal minTradingFee;
        
        /**
         * 最大手续费
         */
        private BigDecimal maxTradingFee;
        
        /**
         * 手续费货币
         */
        private String feeCurrency;
        
        /**
         * 是否支持BNB抵扣
         */
        private Boolean bnbDeductionEnabled;
        
        /**
         * BNB抵扣折扣率
         */
        private BigDecimal bnbDiscountRate;
    }
    
    /**
     * 提现手续费信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WithdrawFeeInfo {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 网络类型
         */
        private String network;
        
        /**
         * 手续费
         */
        private BigDecimal fee;
        
        /**
         * 手续费类型 (FIXED, PERCENTAGE)
         */
        private String feeType;
        
        /**
         * 最小手续费
         */
        private BigDecimal minFee;
        
        /**
         * 最大手续费
         */
        private BigDecimal maxFee;
        
        /**
         * 网络手续费
         */
        private BigDecimal networkFee;
        
        /**
         * 平台手续费
         */
        private BigDecimal platformFee;
        
        /**
         * 是否支持免费提现
         */
        private Boolean freeWithdrawEnabled;
        
        /**
         * 免费提现次数
         */
        private Integer freeWithdrawCount;
        
        /**
         * 已使用免费次数
         */
        private Integer usedFreeCount;
    }
    
    /**
     * 充值手续费信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DepositFeeInfo {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 网络类型
         */
        private String network;
        
        /**
         * 手续费
         */
        private BigDecimal fee;
        
        /**
         * 手续费类型 (FIXED, PERCENTAGE)
         */
        private String feeType;
        
        /**
         * 最小手续费
         */
        private BigDecimal minFee;
        
        /**
         * 最大手续费
         */
        private BigDecimal maxFee;
        
        /**
         * 是否免费充值
         */
        private Boolean isFree;
    }
    
    /**
     * 转账手续费信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransferFeeInfo {
        /**
         * 内部转账手续费率
         */
        private BigDecimal internalTransferFeeRate;
        
        /**
         * 外部转账手续费率
         */
        private BigDecimal externalTransferFeeRate;
        
        /**
         * 现货到期货转账手续费
         */
        private BigDecimal spotToFuturesFee;
        
        /**
         * 期货到现货转账手续费
         */
        private BigDecimal futuresToSpotFee;
        
        /**
         * 最小转账手续费
         */
        private BigDecimal minTransferFee;
        
        /**
         * 最大转账手续费
         */
        private BigDecimal maxTransferFee;
        
        /**
         * 是否支持免费转账
         */
        private Boolean freeTransferEnabled;
    }
    
    /**
     * 期货手续费信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FuturesFeeInfo {
        /**
         * 期货Maker手续费率
         */
        private BigDecimal futuresMakerFeeRate;
        
        /**
         * 期货Taker手续费率
         */
        private BigDecimal futuresTakerFeeRate;
        
        /**
         * 资金费率
         */
        private BigDecimal fundingFeeRate;
        
        /**
         * 强平手续费率
         */
        private BigDecimal liquidationFeeRate;
        
        /**
         * 交割手续费率
         */
        private BigDecimal deliveryFeeRate;
        
        /**
         * 最小期货手续费
         */
        private BigDecimal minFuturesFee;
        
        /**
         * 最大期货手续费
         */
        private BigDecimal maxFuturesFee;
    }
    
    /**
     * 手续费折扣信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FeeDiscountInfo {
        /**
         * 总折扣率
         */
        private BigDecimal totalDiscountRate;
        
        /**
         * VIP折扣率
         */
        private BigDecimal vipDiscountRate;
        
        /**
         * 持币折扣率
         */
        private BigDecimal holdingDiscountRate;
        
        /**
         * 交易量折扣率
         */
        private BigDecimal volumeDiscountRate;
        
        /**
         * 推荐折扣率
         */
        private BigDecimal referralDiscountRate;
        
        /**
         * 活动折扣率
         */
        private BigDecimal promotionDiscountRate;
        
        /**
         * 折扣有效期
         */
        private LocalDateTime discountExpireTime;
    }
    
    /**
     * 手续费统计内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FeeStatistics {
        /**
         * 今日手续费总额
         */
        private BigDecimal todayTotalFee;
        
        /**
         * 本月手续费总额
         */
        private BigDecimal monthlyTotalFee;
        
        /**
         * 年度手续费总额
         */
        private BigDecimal yearlyTotalFee;
        
        /**
         * 历史手续费总额
         */
        private BigDecimal historicalTotalFee;
        
        /**
         * 今日节省手续费
         */
        private BigDecimal todaySavedFee;
        
        /**
         * 本月节省手续费
         */
        private BigDecimal monthlySavedFee;
        
        /**
         * 年度节省手续费
         */
        private BigDecimal yearlySavedFee;
        
        /**
         * 手续费货币
         */
        private String feeCurrency;
    }
}