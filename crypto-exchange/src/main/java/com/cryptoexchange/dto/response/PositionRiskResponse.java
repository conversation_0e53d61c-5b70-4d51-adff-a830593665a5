package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 持仓风险响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PositionRiskResponse {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 持仓方向：1-多头，2-空头
     */
    private Integer positionSide;

    /**
     * 持仓数量
     */
    private BigDecimal positionSize;

    /**
     * 持仓价值
     */
    private BigDecimal positionValue;

    /**
     * 平均开仓价格
     */
    private BigDecimal avgOpenPrice;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 强平价格
     */
    private BigDecimal liquidationPrice;

    /**
     * 破产价格
     */
    private BigDecimal bankruptcyPrice;

    /**
     * 保证金
     */
    private BigDecimal margin;

    /**
     * 初始保证金
     */
    private BigDecimal initialMargin;

    /**
     * 维持保证金
     */
    private BigDecimal maintenanceMargin;

    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedPnl;

    /**
     * 风险率
     */
    private BigDecimal riskRatio;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 保证金模式：1-逐仓，2-全仓
     */
    private Integer marginMode;

    /**
     * 风险等级：1-安全，2-警告，3-危险，4-强平
     */
    private Integer riskLevel;

    /**
     * 距离强平价格百分比
     */
    private BigDecimal liquidationDistancePercent;

    /**
     * 最大可加仓数量
     */
    private BigDecimal maxIncreaseSize;

    /**
     * 最大可减仓数量
     */
    private BigDecimal maxDecreaseSize;

    /**
     * 收益率
     */
    private BigDecimal returnRate;

    /**
     * 收益率百分比
     */
    private BigDecimal returnRatePercent;

    /**
     * 持仓天数
     */
    private Integer holdingDays;

    /**
     * 累计资金费用
     */
    private BigDecimal cumulativeFundingFee;

    /**
     * 预计下次资金费用
     */
    private BigDecimal nextFundingFee;

    /**
     * 自动追加保证金
     */
    private Boolean autoAddMargin;

    /**
     * 风险提醒阈值
     */
    private BigDecimal riskAlertThreshold;

    /**
     * 最后风险评估时间
     */
    private LocalDateTime lastRiskAssessmentTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}