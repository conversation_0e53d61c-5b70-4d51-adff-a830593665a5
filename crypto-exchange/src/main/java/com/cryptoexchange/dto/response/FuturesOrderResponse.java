package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货订单响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class FuturesOrderResponse {

    /**
     * 订单ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 订单类型：1-限价单，2-市价单，3-止损单，4-止盈单，5-条件单
     */
    private Integer orderType;

    /**
     * 订单方向：1-买入开多，2-卖出开空，3-买入平空，4-卖出平多
     */
    private Integer side;

    /**
     * 持仓方向：1-多头，2-空头
     */
    private Integer positionSide;

    /**
     * 订单数量
     */
    private BigDecimal quantity;

    /**
     * 订单价格
     */
    private BigDecimal price;

    /**
     * 止损价格
     */
    private BigDecimal stopPrice;

    /**
     * 已成交数量
     */
    private BigDecimal executedQty;

    /**
     * 已成交金额
     */
    private BigDecimal executedAmount;

    /**
     * 平均成交价格
     */
    private BigDecimal avgPrice;

    /**
     * 订单状态：1-待成交，2-部分成交，3-完全成交，4-已取消，5-已拒绝
     */
    private Integer status;

    /**
     * 时效类型：1-GTC(Good Till Cancel)，2-IOC(Immediate or Cancel)，3-FOK(Fill or Kill)
     */
    private Integer timeInForce;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 保证金模式：1-逐仓，2-全仓
     */
    private Integer marginMode;

    /**
     * 保证金金额
     */
    private BigDecimal marginAmount;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 手续费币种
     */
    private String feeCurrency;

    /**
     * 是否为Maker订单
     */
    private Boolean isMaker;

    /**
     * 是否为减仓单
     */
    private Boolean isReduceOnly;

    /**
     * 是否为PostOnly订单
     */
    private Boolean isPostOnly;

    /**
     * 客户端订单ID
     */
    private String clientOrderId;

    /**
     * 触发价格
     */
    private BigDecimal triggerPrice;

    /**
     * 触发条件：1-大于等于，2-小于等于
     */
    private Integer triggerCondition;

    /**
     * 工作类型：1-标记价格，2-合约价格，3-指数价格
     */
    private Integer workingType;

    /**
     * 激活价格
     */
    private BigDecimal activationPrice;

    /**
     * 回调比率
     */
    private BigDecimal callbackRate;

    /**
     * 订单来源：1-WEB，2-APP，3-API
     */
    private Integer source;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderTime;

    /**
     * 订单更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 订单完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}