package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货交易响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class FuturesTradeResponse {

    /**
     * 交易ID
     */
    private Long id;

    /**
     * 交易编号
     */
    private String tradeNo;

    /**
     * 买方订单ID
     */
    private Long buyOrderId;

    /**
     * 卖方订单ID
     */
    private Long sellOrderId;

    /**
     * 买方用户ID
     */
    private Long buyUserId;

    /**
     * 卖方用户ID
     */
    private Long sellUserId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 交易价格
     */
    private BigDecimal price;

    /**
     * 交易数量
     */
    private BigDecimal quantity;

    /**
     * 交易金额
     */
    private BigDecimal amount;

    /**
     * 买方手续费
     */
    private BigDecimal buyFee;

    /**
     * 卖方手续费
     */
    private BigDecimal sellFee;

    /**
     * 买方手续费币种
     */
    private String buyFeeCurrency;

    /**
     * 卖方手续费币种
     */
    private String sellFeeCurrency;

    /**
     * 交易方向：1-买入，2-卖出
     */
    private Integer side;

    /**
     * 交易类型：1-开仓，2-平仓，3-强平
     */
    private Integer tradeType;

    /**
     * 是否为Maker交易
     */
    private Boolean isMaker;

    /**
     * 买方是否为Maker
     */
    private Boolean buyIsMaker;

    /**
     * 卖方是否为Maker
     */
    private Boolean sellIsMaker;

    /**
     * 交易时间
     */
    private LocalDateTime tradeTime;

    /**
     * 买方订单号
     */
    private String buyOrderNo;

    /**
     * 卖方订单号
     */
    private String sellOrderNo;

    /**
     * 买方客户端订单ID
     */
    private String buyClientOrderId;

    /**
     * 卖方客户端订单ID
     */
    private String sellClientOrderId;

    /**
     * 买方杠杆倍数
     */
    private Integer buyLeverage;

    /**
     * 卖方杠杆倍数
     */
    private Integer sellLeverage;

    /**
     * 买方保证金模式
     */
    private Integer buyMarginMode;

    /**
     * 卖方保证金模式
     */
    private Integer sellMarginMode;

    /**
     * 买方实现盈亏
     */
    private BigDecimal buyRealizedPnl;

    /**
     * 卖方实现盈亏
     */
    private BigDecimal sellRealizedPnl;

    /**
     * 交易哈希
     */
    private String tradeHash;

    /**
     * 区块高度
     */
    private Long blockHeight;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}