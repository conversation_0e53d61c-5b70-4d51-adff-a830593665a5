package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 新币上线响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "新币上线响应")
public class NewListingResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseCurrency;

    @Schema(description = "基础货币名称")
    private String baseCurrencyName;

    @Schema(description = "计价货币代码")
    private String quoteCurrency;

    @Schema(description = "计价货币名称")
    private String quoteCurrencyName;

    @Schema(description = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingTime;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "上线以来涨跌幅")
    private BigDecimal changePercent;

    @Schema(description = "24小时成交量")
    private BigDecimal volume24h;

    @Schema(description = "24小时成交额")
    private BigDecimal amount24h;

    @Schema(description = "上线以来总成交量")
    private BigDecimal totalVolume;

    @Schema(description = "上线以来总成交额")
    private BigDecimal totalAmount;

    @Schema(description = "成交笔数")
    private Long tradeCount;

    @Schema(description = "参与用户数")
    private Integer participantCount;

    @Schema(description = "初始流通量")
    private BigDecimal initialSupply;

    @Schema(description = "当前流通量")
    private BigDecimal currentSupply;

    @Schema(description = "总供应量")
    private BigDecimal totalSupply;

    @Schema(description = "最大供应量")
    private BigDecimal maxSupply;

    @Schema(description = "初始市值")
    private BigDecimal initialMarketCap;

    @Schema(description = "当前市值")
    private BigDecimal currentMarketCap;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "项目类型")
    private String projectType;

    @Schema(description = "项目分类")
    private List<String> categories;

    @Schema(description = "区块链网络")
    private String blockchain;

    @Schema(description = "合约地址")
    private String contractAddress;

    @Schema(description = "项目官网")
    private String officialWebsite;

    @Schema(description = "白皮书链接")
    private String whitepaper;

    @Schema(description = "项目描述")
    private String description;

    @Schema(description = "团队信息")
    private String teamInfo;

    @Schema(description = "投资机构")
    private List<String> investors;

    @Schema(description = "融资轮次")
    private String fundingRound;

    @Schema(description = "融资金额")
    private BigDecimal fundingAmount;

    @Schema(description = "代币分配")
    private TokenDistribution tokenDistribution;

    @Schema(description = "解锁计划")
    private List<UnlockSchedule> unlockSchedule;

    @Schema(description = "技术评分")
    private BigDecimal technicalScore;

    @Schema(description = "团队评分")
    private BigDecimal teamScore;

    @Schema(description = "社区评分")
    private BigDecimal communityScore;

    @Schema(description = "综合评分")
    private BigDecimal overallScore;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "社交媒体关注度")
    private SocialMediaStats socialMediaStats;

    @Schema(description = "交易状态")
    private String tradingStatus;

    @Schema(description = "是否支持充值")
    private Boolean depositEnabled;

    @Schema(description = "是否支持提现")
    private Boolean withdrawEnabled;

    @Schema(description = "最小交易量")
    private BigDecimal minTradeAmount;

    @Schema(description = "最大交易量")
    private BigDecimal maxTradeAmount;

    @Schema(description = "价格精度")
    private Integer pricePrecision;

    @Schema(description = "数量精度")
    private Integer quantityPrecision;

    @Schema(description = "特殊标记")
    private List<String> specialTags;

    @Schema(description = "上线公告")
    private String listingAnnouncement;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 代币分配
     */
    @Data
    @Schema(description = "代币分配")
    public static class TokenDistribution {
        
        @Schema(description = "公开销售比例")
        private BigDecimal publicSale;
        
        @Schema(description = "私募比例")
        private BigDecimal privateSale;
        
        @Schema(description = "团队持有比例")
        private BigDecimal team;
        
        @Schema(description = "基金会比例")
        private BigDecimal foundation;
        
        @Schema(description = "生态建设比例")
        private BigDecimal ecosystem;
        
        @Schema(description = "流动性挖矿比例")
        private BigDecimal liquidityMining;
        
        @Schema(description = "其他比例")
        private BigDecimal others;
    }

    /**
     * 解锁计划
     */
    @Data
    @Schema(description = "解锁计划")
    public static class UnlockSchedule {
        
        @Schema(description = "解锁时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime unlockTime;
        
        @Schema(description = "解锁数量")
        private BigDecimal unlockAmount;
        
        @Schema(description = "解锁比例")
        private BigDecimal unlockPercent;
        
        @Schema(description = "解锁类型")
        private String unlockType;
        
        @Schema(description = "受益方")
        private String beneficiary;
    }

    /**
     * 社交媒体统计
     */
    @Data
    @Schema(description = "社交媒体统计")
    public static class SocialMediaStats {
        
        @Schema(description = "Twitter关注者")
        private Integer twitterFollowers;
        
        @Schema(description = "Telegram成员")
        private Integer telegramMembers;
        
        @Schema(description = "Discord成员")
        private Integer discordMembers;
        
        @Schema(description = "Reddit订阅者")
        private Integer redditSubscribers;
        
        @Schema(description = "GitHub星标")
        private Integer githubStars;
        
        @Schema(description = "Medium关注者")
        private Integer mediumFollowers;
    }
}