package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 指数价格响应
 */
public class IndexPriceResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 指数价格
     */
    private BigDecimal indexPrice;
    
    /**
     * 标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 下次资金费率
     */
    private BigDecimal nextFundingRate;
    
    /**
     * 下次资金费率时间
     */
    private LocalDateTime nextFundingTime;
    
    public IndexPriceResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public BigDecimal getIndexPrice() {
        return indexPrice;
    }
    
    public void setIndexPrice(BigDecimal indexPrice) {
        this.indexPrice = indexPrice;
    }
    
    public BigDecimal getMarkPrice() {
        return markPrice;
    }
    
    public void setMarkPrice(BigDecimal markPrice) {
        this.markPrice = markPrice;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
    
    public BigDecimal getNextFundingRate() {
        return nextFundingRate;
    }
    
    public void setNextFundingRate(BigDecimal nextFundingRate) {
        this.nextFundingRate = nextFundingRate;
    }
    
    public LocalDateTime getNextFundingTime() {
        return nextFundingTime;
    }
    
    public void setNextFundingTime(LocalDateTime nextFundingTime) {
        this.nextFundingTime = nextFundingTime;
    }
}