package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 订单簿响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "订单簿响应")
public class OrderBookResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "订单簿深度级别")
    private Integer depthLevel;

    @Schema(description = "最大显示档位数")
    private Integer maxLevels;

    @Schema(description = "买单列表")
    private List<OrderLevel> bids;

    @Schema(description = "卖单列表")
    private List<OrderLevel> asks;

    @Schema(description = "最优买价")
    private BigDecimal bestBid;

    @Schema(description = "最优卖价")
    private BigDecimal bestAsk;

    @Schema(description = "最优买量")
    private BigDecimal bestBidQuantity;

    @Schema(description = "最优卖量")
    private BigDecimal bestAskQuantity;

    @Schema(description = "买卖价差")
    private BigDecimal bidAskSpread;

    @Schema(description = "买卖价差百分比")
    private BigDecimal bidAskSpreadPercent;

    @Schema(description = "中间价")
    private BigDecimal midPrice;

    @Schema(description = "总买单量")
    private BigDecimal totalBidVolume;

    @Schema(description = "总卖单量")
    private BigDecimal totalAskVolume;

    @Schema(description = "总买单金额")
    private BigDecimal totalBidAmount;

    @Schema(description = "总卖单金额")
    private BigDecimal totalAskAmount;

    @Schema(description = "买单订单数")
    private Integer totalBidOrders;

    @Schema(description = "卖单订单数")
    private Integer totalAskOrders;

    @Schema(description = "买卖量比例")
    private BigDecimal bidAskVolumeRatio;

    @Schema(description = "买卖金额比例")
    private BigDecimal bidAskAmountRatio;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "深度质量评分")
    private BigDecimal depthQuality;

    @Schema(description = "市场冲击成本")
    private BigDecimal marketImpactCost;

    @Schema(description = "订单簿不平衡度")
    private BigDecimal orderBookImbalance;

    @Schema(description = "价格集中度")
    private BigDecimal priceConcentration;

    @Schema(description = "订单簿统计")
    private OrderBookStatistics statistics;

    @Schema(description = "深度分析")
    private DepthAnalysis depthAnalysis;

    @Schema(description = "流动性指标")
    private LiquidityMetrics liquidityMetrics;

    @Schema(description = "大单分析")
    private LargeOrderAnalysis largeOrderAnalysis;

    @Schema(description = "价格档位分布")
    private PriceLevelDistribution priceLevelDistribution;

    @Schema(description = "订单簿变化")
    private List<OrderBookChange> recentChanges;

    @Schema(description = "历史深度对比")
    private HistoricalDepthComparison historicalComparison;

    @Schema(description = "异常检测")
    private AnomalyDetection anomalyDetection;

    @Schema(description = "预测指标")
    private PredictionMetrics predictionMetrics;

    @Schema(description = "数据时间戳")
    private Long timestamp;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据延迟(毫秒)")
    private Long dataLatency;

    @Schema(description = "是否实时数据")
    private Boolean isRealTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "数据版本")
    private String dataVersion;

    @Schema(description = "数据完整性")
    private Boolean dataIntegrity;

    @Schema(description = "数据质量评分")
    private BigDecimal dataQuality;

    @Schema(description = "订单簿状态")
    private String orderBookStatus;

    @Schema(description = "是否暂停更新")
    private Boolean updateSuspended;

    @Schema(description = "暂停原因")
    private String suspensionReason;

    @Schema(description = "下次更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextUpdateTime;

    @Schema(description = "更新频率(毫秒)")
    private Long updateFrequency;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 订单档位
     */
    @Data
    @Schema(description = "订单档位")
    public static class OrderLevel {
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量")
        private BigDecimal quantity;
        
        @Schema(description = "金额")
        private BigDecimal amount;
        
        @Schema(description = "累计数量")
        private BigDecimal cumulativeQuantity;
        
        @Schema(description = "累计金额")
        private BigDecimal cumulativeAmount;
        
        @Schema(description = "订单数量")
        private Integer orderCount;
        
        @Schema(description = "平均订单大小")
        private BigDecimal averageOrderSize;
        
        @Schema(description = "价格偏离度")
        private BigDecimal priceDeviation;
        
        @Schema(description = "档位权重")
        private BigDecimal levelWeight;
        
        @Schema(description = "是否为大单")
        private Boolean isLargeOrder;
        
        @Schema(description = "更新时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updateTime;
    }

    /**
     * 订单簿统计
     */
    @Data
    @Schema(description = "订单簿统计")
    public static class OrderBookStatistics {
        
        @Schema(description = "有效买单档位数")
        private Integer activeBidLevels;
        
        @Schema(description = "有效卖单档位数")
        private Integer activeAskLevels;
        
        @Schema(description = "平均买单大小")
        private BigDecimal averageBidSize;
        
        @Schema(description = "平均卖单大小")
        private BigDecimal averageAskSize;
        
        @Schema(description = "最大买单")
        private BigDecimal maxBidSize;
        
        @Schema(description = "最大卖单")
        private BigDecimal maxAskSize;
        
        @Schema(description = "最小买单")
        private BigDecimal minBidSize;
        
        @Schema(description = "最小卖单")
        private BigDecimal minAskSize;
        
        @Schema(description = "买单标准差")
        private BigDecimal bidStandardDeviation;
        
        @Schema(description = "卖单标准差")
        private BigDecimal askStandardDeviation;
        
        @Schema(description = "价格范围")
        private BigDecimal priceRange;
        
        @Schema(description = "价格中位数")
        private BigDecimal priceMedian;
    }

    /**
     * 深度分析
     */
    @Data
    @Schema(description = "深度分析")
    public static class DepthAnalysis {
        
        @Schema(description = "1%深度买量")
        private BigDecimal depth1PercentBid;
        
        @Schema(description = "1%深度卖量")
        private BigDecimal depth1PercentAsk;
        
        @Schema(description = "5%深度买量")
        private BigDecimal depth5PercentBid;
        
        @Schema(description = "5%深度卖量")
        private BigDecimal depth5PercentAsk;
        
        @Schema(description = "10%深度买量")
        private BigDecimal depth10PercentBid;
        
        @Schema(description = "10%深度卖量")
        private BigDecimal depth10PercentAsk;
        
        @Schema(description = "深度不对称性")
        private BigDecimal depthAsymmetry;
        
        @Schema(description = "深度稳定性")
        private BigDecimal depthStability;
        
        @Schema(description = "深度变化率")
        private BigDecimal depthChangeRate;
    }

    /**
     * 流动性指标
     */
    @Data
    @Schema(description = "流动性指标")
    public static class LiquidityMetrics {
        
        @Schema(description = "有效价差")
        private BigDecimal effectiveSpread;
        
        @Schema(description = "实现价差")
        private BigDecimal realizedSpread;
        
        @Schema(description = "价格影响")
        private BigDecimal priceImpact;
        
        @Schema(description = "流动性比率")
        private BigDecimal liquidityRatio;
        
        @Schema(description = "市场深度")
        private BigDecimal marketDepth;
        
        @Schema(description = "弹性指标")
        private BigDecimal resilienceMetric;
        
        @Schema(description = "流动性成本")
        private BigDecimal liquidityCost;
        
        @Schema(description = "流动性风险")
        private BigDecimal liquidityRisk;
    }

    /**
     * 大单分析
     */
    @Data
    @Schema(description = "大单分析")
    public static class LargeOrderAnalysis {
        
        @Schema(description = "大单买量")
        private BigDecimal largeBidVolume;
        
        @Schema(description = "大单卖量")
        private BigDecimal largeAskVolume;
        
        @Schema(description = "大单买单数")
        private Integer largeBidCount;
        
        @Schema(description = "大单卖单数")
        private Integer largeAskCount;
        
        @Schema(description = "大单占比")
        private BigDecimal largeOrderRatio;
        
        @Schema(description = "大单集中度")
        private BigDecimal largeOrderConcentration;
        
        @Schema(description = "大单平均大小")
        private BigDecimal averageLargeOrderSize;
        
        @Schema(description = "大单价格影响")
        private BigDecimal largeOrderPriceImpact;
    }

    /**
     * 价格档位分布
     */
    @Data
    @Schema(description = "价格档位分布")
    public static class PriceLevelDistribution {
        
        @Schema(description = "买单价格分布")
        private List<PriceDistributionBucket> bidDistribution;
        
        @Schema(description = "卖单价格分布")
        private List<PriceDistributionBucket> askDistribution;
        
        @Schema(description = "价格集中度指数")
        private BigDecimal priceConcentrationIndex;
        
        @Schema(description = "分布偏度")
        private BigDecimal distributionSkewness;
        
        @Schema(description = "分布峰度")
        private BigDecimal distributionKurtosis;
    }

    /**
     * 价格分布桶
     */
    @Data
    @Schema(description = "价格分布桶")
    public static class PriceDistributionBucket {
        
        @Schema(description = "价格区间下限")
        private BigDecimal priceRangeMin;
        
        @Schema(description = "价格区间上限")
        private BigDecimal priceRangeMax;
        
        @Schema(description = "区间中位价")
        private BigDecimal midPrice;
        
        @Schema(description = "区间总量")
        private BigDecimal totalVolume;
        
        @Schema(description = "区间订单数")
        private Integer orderCount;
        
        @Schema(description = "区间占比")
        private BigDecimal percentage;
    }

    /**
     * 订单簿变化
     */
    @Data
    @Schema(description = "订单簿变化")
    public static class OrderBookChange {
        
        @Schema(description = "变化类型")
        private String changeType;
        
        @Schema(description = "变化方向")
        private String side;
        
        @Schema(description = "价格")
        private BigDecimal price;
        
        @Schema(description = "数量变化")
        private BigDecimal quantityChange;
        
        @Schema(description = "变化前数量")
        private BigDecimal previousQuantity;
        
        @Schema(description = "变化后数量")
        private BigDecimal newQuantity;
        
        @Schema(description = "变化时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime changeTime;
        
        @Schema(description = "变化影响")
        private BigDecimal changeImpact;
    }

    /**
     * 历史深度对比
     */
    @Data
    @Schema(description = "历史深度对比")
    public static class HistoricalDepthComparison {
        
        @Schema(description = "1小时前深度")
        private BigDecimal depth1HourAgo;
        
        @Schema(description = "1天前深度")
        private BigDecimal depth1DayAgo;
        
        @Schema(description = "1周前深度")
        private BigDecimal depth1WeekAgo;
        
        @Schema(description = "深度变化趋势")
        private String depthTrend;
        
        @Schema(description = "深度改善率")
        private BigDecimal depthImprovementRate;
        
        @Schema(description = "历史最高深度")
        private BigDecimal historicalMaxDepth;
        
        @Schema(description = "历史最低深度")
        private BigDecimal historicalMinDepth;
        
        @Schema(description = "深度排名")
        private Integer depthRanking;
    }

    /**
     * 异常检测
     */
    @Data
    @Schema(description = "异常检测")
    public static class AnomalyDetection {
        
        @Schema(description = "是否检测到异常")
        private Boolean anomalyDetected;
        
        @Schema(description = "异常类型")
        private List<String> anomalyTypes;
        
        @Schema(description = "异常严重程度")
        private String anomalySeverity;
        
        @Schema(description = "异常评分")
        private BigDecimal anomalyScore;
        
        @Schema(description = "异常描述")
        private String anomalyDescription;
        
        @Schema(description = "检测时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime detectionTime;
        
        @Schema(description = "建议操作")
        private String recommendedAction;
    }

    /**
     * 预测指标
     */
    @Data
    @Schema(description = "预测指标")
    public static class PredictionMetrics {
        
        @Schema(description = "价格方向预测")
        private String priceDirectionPrediction;
        
        @Schema(description = "预测置信度")
        private BigDecimal predictionConfidence;
        
        @Schema(description = "预测时间范围")
        private String predictionTimeframe;
        
        @Schema(description = "支撑位预测")
        private BigDecimal predictedSupportLevel;
        
        @Schema(description = "阻力位预测")
        private BigDecimal predictedResistanceLevel;
        
        @Schema(description = "流动性预测")
        private BigDecimal predictedLiquidity;
        
        @Schema(description = "波动率预测")
        private BigDecimal predictedVolatility;
        
        @Schema(description = "预测模型")
        private String predictionModel;
        
        @Schema(description = "模型准确率")
        private BigDecimal modelAccuracy;
    }
}