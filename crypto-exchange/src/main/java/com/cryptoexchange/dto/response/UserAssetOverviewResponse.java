package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户资产概览响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户资产概览响应")
public class UserAssetOverviewResponse {

    @Schema(description = "总资产价值（USDT）")
    private BigDecimal totalAssetValue;

    @Schema(description = "可用资产价值（USDT）")
    private BigDecimal availableAssetValue;

    @Schema(description = "冻结资产价值（USDT）")
    private BigDecimal frozenAssetValue;

    @Schema(description = "现货资产价值（USDT）")
    private BigDecimal spotAssetValue;

    @Schema(description = "合约资产价值（USDT）")
    private BigDecimal futuresAssetValue;

    @Schema(description = "杠杆资产价值（USDT）")
    private BigDecimal marginAssetValue;

    @Schema(description = "理财资产价值（USDT）")
    private BigDecimal savingsAssetValue;

    @Schema(description = "24小时资产变化")
    private BigDecimal dailyChange;

    @Schema(description = "24小时资产变化百分比")
    private BigDecimal dailyChangePercent;

    @Schema(description = "7天资产变化")
    private BigDecimal weeklyChange;

    @Schema(description = "7天资产变化百分比")
    private BigDecimal weeklyChangePercent;

    @Schema(description = "30天资产变化")
    private BigDecimal monthlyChange;

    @Schema(description = "30天资产变化百分比")
    private BigDecimal monthlyChangePercent;

    @Schema(description = "资产分布")
    private List<AssetDistribution> assetDistribution;

    @Schema(description = "主要持仓")
    private List<MajorHolding> majorHoldings;

    @Schema(description = "盈亏统计")
    private ProfitLossStats profitLossStats;

    @Data
    @Schema(description = "资产分布")
    public static class AssetDistribution {
        @Schema(description = "资产类型", example = "SPOT")
        private String assetType;
        
        @Schema(description = "资产类型名称", example = "现货")
        private String assetTypeName;
        
        @Schema(description = "资产价值（USDT）")
        private BigDecimal value;
        
        @Schema(description = "占比百分比")
        private BigDecimal percentage;
    }

    @Data
    @Schema(description = "主要持仓")
    public static class MajorHolding {
        @Schema(description = "币种", example = "BTC")
        private String currency;
        
        @Schema(description = "持仓数量")
        private BigDecimal amount;
        
        @Schema(description = "价值（USDT）")
        private BigDecimal value;
        
        @Schema(description = "占比百分比")
        private BigDecimal percentage;
        
        @Schema(description = "24小时变化")
        private BigDecimal dailyChange;
        
        @Schema(description = "24小时变化百分比")
        private BigDecimal dailyChangePercent;
    }

    @Data
    @Schema(description = "盈亏统计")
    public static class ProfitLossStats {
        @Schema(description = "今日盈亏")
        private BigDecimal todayPnl;
        
        @Schema(description = "今日盈亏百分比")
        private BigDecimal todayPnlPercent;
        
        @Schema(description = "累计盈亏")
        private BigDecimal totalPnl;
        
        @Schema(description = "累计盈亏百分比")
        private BigDecimal totalPnlPercent;
        
        @Schema(description = "最大盈利")
        private BigDecimal maxProfit;
        
        @Schema(description = "最大亏损")
        private BigDecimal maxLoss;
    }

    public UserAssetOverviewResponse() {}

    public UserAssetOverviewResponse(BigDecimal totalAssetValue, BigDecimal availableAssetValue, 
                                   BigDecimal frozenAssetValue, BigDecimal dailyChange) {
        this.totalAssetValue = totalAssetValue;
        this.availableAssetValue = availableAssetValue;
        this.frozenAssetValue = frozenAssetValue;
        this.dailyChange = dailyChange;
        
        if (totalAssetValue != null && totalAssetValue.compareTo(BigDecimal.ZERO) > 0 && dailyChange != null) {
            this.dailyChangePercent = dailyChange.divide(totalAssetValue, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }
    }
}