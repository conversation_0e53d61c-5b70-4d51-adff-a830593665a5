package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 资金流向分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MoneyFlowResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 分析时间范围
     */
    private String analysisTimeRange;

    /**
     * 资金流向等级
     */
    private MoneyFlowLevel moneyFlowLevel;

    /**
     * 净资金流向
     */
    private BigDecimal netMoneyFlow;

    /**
     * 资金流向统计
     */
    private MoneyFlowStatistics moneyFlowStatistics;

    /**
     * 主力资金分析
     */
    private MainFundAnalysis mainFundAnalysis;

    /**
     * 散户资金分析
     */
    private RetailFundAnalysis retailFundAnalysis;

    /**
     * 机构资金分析
     */
    private InstitutionalFundAnalysis institutionalFundAnalysis;

    /**
     * 资金流向时间分析
     */
    private MoneyFlowTimeAnalysis moneyFlowTimeAnalysis;

    /**
     * 资金流向强度分析
     */
    private MoneyFlowIntensityAnalysis moneyFlowIntensityAnalysis;

    /**
     * 资金流向趋势分析
     */
    private MoneyFlowTrendAnalysis moneyFlowTrendAnalysis;

    /**
     * 资金流向预测
     */
    private MoneyFlowForecast moneyFlowForecast;

    /**
     * 资金流向交易建议
     */
    private MoneyFlowTradingAdvice moneyFlowTradingAdvice;

    /**
     * 资金流向风险评估
     */
    private MoneyFlowRiskAssessment moneyFlowRiskAssessment;

    /**
     * 资金流向预警
     */
    private List<MoneyFlowAlert> moneyFlowAlerts;

    /**
     * 资金流向等级
     */
    public enum MoneyFlowLevel {
        MASSIVE_INFLOW("大量流入", "资金大量流入，市场看涨情绪强烈"),
        MODERATE_INFLOW("适度流入", "资金适度流入，市场情绪偏乐观"),
        SLIGHT_INFLOW("轻微流入", "资金轻微流入，市场情绪中性偏乐观"),
        BALANCED("平衡", "资金流入流出基本平衡"),
        SLIGHT_OUTFLOW("轻微流出", "资金轻微流出，市场情绪中性偏悲观"),
        MODERATE_OUTFLOW("适度流出", "资金适度流出，市场情绪偏悲观"),
        MASSIVE_OUTFLOW("大量流出", "资金大量流出，市场看跌情绪强烈");

        private final String description;
        private final String detail;

        MoneyFlowLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 资金流向统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowStatistics {
        /**
         * 总流入资金
         */
        private BigDecimal totalInflowAmount;

        /**
         * 总流出资金
         */
        private BigDecimal totalOutflowAmount;

        /**
         * 净流入资金
         */
        private BigDecimal netInflowAmount;

        /**
         * 流入流出比
         */
        private BigDecimal inflowOutflowRatio;

        /**
         * 流入交易笔数
         */
        private Integer inflowTradeCount;

        /**
         * 流出交易笔数
         */
        private Integer outflowTradeCount;

        /**
         * 平均流入金额
         */
        private BigDecimal averageInflowAmount;

        /**
         * 平均流出金额
         */
        private BigDecimal averageOutflowAmount;

        /**
         * 最大单笔流入
         */
        private BigDecimal maxSingleInflow;

        /**
         * 最大单笔流出
         */
        private BigDecimal maxSingleOutflow;

        /**
         * 资金活跃度
         */
        private BigDecimal fundActivity;

        /**
         * 资金集中度
         */
        private BigDecimal fundConcentration;
    }

    /**
     * 主力资金分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MainFundAnalysis {
        /**
         * 主力资金净流入
         */
        private BigDecimal mainFundNetInflow;

        /**
         * 主力资金流入
         */
        private BigDecimal mainFundInflow;

        /**
         * 主力资金流出
         */
        private BigDecimal mainFundOutflow;

        /**
         * 主力资金占比
         */
        private BigDecimal mainFundRatio;

        /**
         * 主力资金活跃度
         */
        private BigDecimal mainFundActivity;

        /**
         * 主力资金行为分析
         */
        private MainFundBehaviorAnalysis mainFundBehaviorAnalysis;

        /**
         * 主力资金类型分布
         */
        private Map<String, BigDecimal> mainFundTypeDistribution;

        /**
         * 主力资金操作模式
         */
        private List<MainFundOperationPattern> mainFundOperationPatterns;

        /**
         * 获取主力资金活跃度
         */
        public BigDecimal getMainFundActivity() {
            return mainFundActivity;
        }
    }

    /**
     * 主力资金行为分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MainFundBehaviorAnalysis {
        /**
         * 建仓行为
         */
        private BigDecimal positionBuildingBehavior;

        /**
         * 减仓行为
         */
        private BigDecimal positionReducingBehavior;

        /**
         * 洗盘行为
         */
        private BigDecimal washTradingBehavior;

        /**
         * 拉升行为
         */
        private BigDecimal pumpingBehavior;

        /**
         * 出货行为
         */
        private BigDecimal distributionBehavior;

        /**
         * 护盘行为
         */
        private BigDecimal supportBehavior;

        /**
         * 行为强度
         */
        private BigDecimal behaviorIntensity;

        /**
         * 行为持续性
         */
        private BigDecimal behaviorPersistence;
    }

    /**
     * 主力资金操作模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MainFundOperationPattern {
        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 模式持续时间
         */
        private Integer patternDuration;

        /**
         * 模式成功率
         */
        private BigDecimal patternSuccessRate;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 预期影响
         */
        private String expectedImpact;
    }

    /**
     * 散户资金分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetailFundAnalysis {
        /**
         * 散户资金净流入
         */
        private BigDecimal retailFundNetInflow;

        /**
         * 散户资金流入
         */
        private BigDecimal retailFundInflow;

        /**
         * 散户资金流出
         */
        private BigDecimal retailFundOutflow;

        /**
         * 散户资金占比
         */
        private BigDecimal retailFundRatio;

        /**
         * 散户参与度
         */
        private BigDecimal retailParticipation;

        /**
         * 散户情绪指标
         */
        private RetailSentimentIndicators retailSentimentIndicators;

        /**
         * 散户行为特征
         */
        private RetailBehaviorCharacteristics retailBehaviorCharacteristics;
        
        // 手动添加getter方法以解决编译问题
        public RetailSentimentIndicators getRetailSentimentIndicators() {
            return retailSentimentIndicators;
        }
    }

    /**
     * 散户情绪指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetailSentimentIndicators {
        /**
         * 恐慌指数
         */
        private BigDecimal panicIndex;

        /**
         * 贪婪指数
         */
        private BigDecimal greedIndex;

        /**
         * 情绪波动性
         */
        private BigDecimal sentimentVolatility;

        /**
         * 跟风程度
         */
        private BigDecimal herdingDegree;

        /**
         * 理性程度
         */
        private BigDecimal rationalityDegree;
        
        // 手动添加getter方法以解决编译问题
        public BigDecimal getPanicIndex() {
            return panicIndex;
        }
        
        public BigDecimal getGreedIndex() {
            return greedIndex;
        }
        
        public BigDecimal getSentimentVolatility() {
            return sentimentVolatility;
        }
        
        public BigDecimal getHerdingDegree() {
            return herdingDegree;
        }
        
        public BigDecimal getRationalityDegree() {
            return rationalityDegree;
        }
    }

    /**
     * 散户行为特征
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RetailBehaviorCharacteristics {
        /**
         * 追涨杀跌倾向
         */
        private BigDecimal momentumChasingTendency;

        /**
         * 止损执行率
         */
        private BigDecimal stopLossExecutionRate;

        /**
         * 持仓时间偏好
         */
        private String holdingTimePreference;

        /**
         * 风险承受能力
         */
        private BigDecimal riskTolerance;

        /**
         * 交易频率
         */
        private BigDecimal tradingFrequency;

        /**
         * 平均交易规模
         */
        private BigDecimal averageTradeSize;
    }

    /**
     * 机构资金分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InstitutionalFundAnalysis {
        /**
         * 机构资金净流入
         */
        private BigDecimal institutionalFundNetInflow;

        /**
         * 机构资金流入
         */
        private BigDecimal institutionalFundInflow;

        /**
         * 机构资金流出
         */
        private BigDecimal institutionalFundOutflow;

        /**
         * 机构资金占比
         */
        private BigDecimal institutionalFundRatio;

        /**
         * 机构类型分布
         */
        private Map<String, BigDecimal> institutionalTypeDistribution;

        /**
         * 机构策略分析
         */
        private InstitutionalStrategyAnalysis institutionalStrategyAnalysis;

        /**
         * 机构影响力评估
         */
        private InstitutionalInfluenceAssessment institutionalInfluenceAssessment;

        /**
         * 获取机构影响力评估
         */
        public InstitutionalInfluenceAssessment getInstitutionalInfluenceAssessment() {
            return institutionalInfluenceAssessment;
        }
    }

    /**
     * 机构策略分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InstitutionalStrategyAnalysis {
        /**
         * 长期投资策略
         */
        private BigDecimal longTermInvestmentStrategy;

        /**
         * 短期交易策略
         */
        private BigDecimal shortTermTradingStrategy;

        /**
         * 套利策略
         */
        private BigDecimal arbitrageStrategy;

        /**
         * 对冲策略
         */
        private BigDecimal hedgingStrategy;

        /**
         * 算法交易策略
         */
        private BigDecimal algorithmicTradingStrategy;

        /**
         * 策略多样性
         */
        private BigDecimal strategyDiversity;
    }

    /**
     * 机构影响力评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InstitutionalInfluenceAssessment {
        /**
         * 价格影响力
         */
        private BigDecimal priceInfluence;

        /**
         * 市场引导力
         */
        private BigDecimal marketLeadership;

        /**
         * 流动性提供能力
         */
        private BigDecimal liquidityProvision;

        /**
         * 市场稳定性贡献
         */
        private BigDecimal marketStabilityContribution;

        /**
         * 信息传递效率
         */
        private BigDecimal informationTransmissionEfficiency;
        
        // 手动添加getter方法以解决编译问题
        public BigDecimal getPriceInfluence() {
            return priceInfluence;
        }
        
        public BigDecimal getMarketLeadership() {
            return marketLeadership;
        }
        
        public BigDecimal getLiquidityProvision() {
            return liquidityProvision;
        }
        
        public BigDecimal getMarketStabilityContribution() {
            return marketStabilityContribution;
        }
        
        public BigDecimal getInformationTransmissionEfficiency() {
            return informationTransmissionEfficiency;
        }
    }

    /**
     * 资金流向时间分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowTimeAnalysis {
        /**
         * 时间分布
         */
        private List<TimeFlowDistribution> timeFlowDistributions;

        /**
         * 高峰流入时段
         */
        private List<PeakInflowPeriod> peakInflowPeriods;

        /**
         * 高峰流出时段
         */
        private List<PeakOutflowPeriod> peakOutflowPeriods;

        /**
         * 时间规律性
         */
        private BigDecimal timeRegularity;

        /**
         * 时间集中度
         */
        private BigDecimal timeConcentration;

        /**
         * 异常时间流向
         */
        private List<AbnormalTimeFlow> abnormalTimeFlows;
    }

    /**
     * 时间流向分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeFlowDistribution {
        /**
         * 时间段
         */
        private String timePeriod;

        /**
         * 净流入金额
         */
        private BigDecimal netInflowAmount;

        /**
         * 流入金额
         */
        private BigDecimal inflowAmount;

        /**
         * 流出金额
         */
        private BigDecimal outflowAmount;

        /**
         * 交易笔数
         */
        private Integer tradeCount;

        /**
         * 活跃度
         */
        private BigDecimal activity;
    }

    /**
     * 高峰流入时段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeakInflowPeriod {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 峰值流入金额
         */
        private BigDecimal peakInflowAmount;

        /**
         * 流入强度
         */
        private BigDecimal inflowIntensity;

        /**
         * 持续时间
         */
        private Integer duration;

        /**
         * 触发因素
         */
        private List<String> triggerFactors;
    }

    /**
     * 高峰流出时段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PeakOutflowPeriod {
        /**
         * 开始时间
         */
        private LocalDateTime startTime;

        /**
         * 结束时间
         */
        private LocalDateTime endTime;

        /**
         * 峰值流出金额
         */
        private BigDecimal peakOutflowAmount;

        /**
         * 流出强度
         */
        private BigDecimal outflowIntensity;

        /**
         * 持续时间
         */
        private Integer duration;

        /**
         * 触发因素
         */
        private List<String> triggerFactors;
    }

    /**
     * 异常时间流向
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalTimeFlow {
        /**
         * 异常时间
         */
        private LocalDateTime abnormalTime;

        /**
         * 异常类型
         */
        private String abnormalType;

        /**
         * 异常程度
         */
        private BigDecimal abnormalDegree;

        /**
         * 异常金额
         */
        private BigDecimal abnormalAmount;

        /**
         * 异常描述
         */
        private String abnormalDescription;

        /**
         * 可能原因
         */
        private List<String> possibleReasons;
    }

    /**
     * 资金流向强度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowIntensityAnalysis {
        /**
         * 整体流向强度
         */
        private BigDecimal overallFlowIntensity;

        /**
         * 流入强度
         */
        private BigDecimal inflowIntensity;

        /**
         * 流出强度
         */
        private BigDecimal outflowIntensity;

        /**
         * 强度变化率
         */
        private BigDecimal intensityChangeRate;

        /**
         * 强度稳定性
         */
        private BigDecimal intensityStability;

        /**
         * 强度分级
         */
        private IntensityGrading intensityGrading;

        /**
         * 强度驱动因素
         */
        private List<IntensityDrivingFactor> intensityDrivingFactors;

        /**
         * 获取整体流向强度
         */
        public BigDecimal getOverallFlowIntensity() {
            return overallFlowIntensity;
        }

        /**
         * 获取强度分级
         */
        public IntensityGrading getIntensityGrading() {
            return intensityGrading;
        }
    }

    /**
     * 强度分级
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntensityGrading {
        /**
         * 强度等级
         */
        private String intensityLevel;

        /**
         * 等级描述
         */
        private String levelDescription;

        /**
         * 等级评分
         */
        private BigDecimal levelScore;

        /**
         * 历史百分位
         */
        private BigDecimal historicalPercentile;
    }

    /**
     * 强度驱动因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntensityDrivingFactor {
        /**
         * 因素名称
         */
        private String factorName;

        /**
         * 因素类型
         */
        private String factorType;

        /**
         * 影响权重
         */
        private BigDecimal impactWeight;

        /**
         * 影响方向
         */
        private String impactDirection;

        /**
         * 因素强度
         */
        private BigDecimal factorIntensity;
    }

    /**
     * 资金流向趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowTrendAnalysis {
        /**
         * 短期趋势
         */
        private FlowTrend shortTermTrend;

        /**
         * 中期趋势
         */
        private FlowTrend mediumTermTrend;

        /**
         * 长期趋势
         */
        private FlowTrend longTermTrend;

        /**
         * 趋势一致性
         */
        private BigDecimal trendConsistency;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势转折点
         */
        private List<TrendTurningPoint> trendTurningPoints;

        /**
         * 趋势预测
         */
        private TrendForecast trendForecast;
        
        // 手动添加getter方法以解决编译问题
        public FlowTrend getShortTermTrend() {
            return shortTermTrend;
        }
        
        public FlowTrend getMediumTermTrend() {
            return mediumTermTrend;
        }
        
        public FlowTrend getLongTermTrend() {
            return longTermTrend;
        }
    }

    /**
     * 流向趋势
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlowTrend {
        /**
         * 趋势方向
         */
        private String trendDirection;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续时间
         */
        private Integer trendDuration;

        /**
         * 趋势稳定性
         */
        private BigDecimal trendStability;

        /**
         * 趋势加速度
         */
        private BigDecimal trendAcceleration;

        /**
         * 趋势可靠性
         */
        private BigDecimal trendReliability;

        /**
         * 获取趋势方向
         */
        public String getTrendDirection() {
            return trendDirection;
        }

        /**
         * 获取趋势强度
         */
        public BigDecimal getTrendStrength() {
            return trendStrength;
        }
    }

    /**
     * 趋势转折点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendTurningPoint {
        /**
         * 转折时间
         */
        private LocalDateTime turningTime;

        /**
         * 转折类型
         */
        private String turningType;

        /**
         * 转折强度
         */
        private BigDecimal turningIntensity;

        /**
         * 转折前趋势
         */
        private String beforeTrend;

        /**
         * 转折后趋势
         */
        private String afterTrend;

        /**
         * 转折原因
         */
        private List<String> turningReasons;
    }

    /**
     * 趋势预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendForecast {
        /**
         * 预测趋势方向
         */
        private String predictedTrendDirection;

        /**
         * 预测趋势强度
         */
        private BigDecimal predictedTrendStrength;

        /**
         * 预测持续时间
         */
        private Integer predictedDuration;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 关键转折点预测
         */
        private List<LocalDateTime> predictedTurningPoints;
    }

    /**
     * 资金流向预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowForecast {
        /**
         * 短期预测
         */
        private FlowForecast shortTermForecast;

        /**
         * 中期预测
         */
        private FlowForecast mediumTermForecast;

        /**
         * 长期预测
         */
        private FlowForecast longTermForecast;

        /**
         * 预测模型评估
         */
        private ForecastModelEvaluation forecastModelEvaluation;

        /**
         * 获取短期预测
         */
        public FlowForecast getShortTermForecast() {
            return shortTermForecast;
        }
    }

    /**
     * 流向预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FlowForecast {
        /**
         * 预测时间范围
         */
        private String forecastTimeRange;

        /**
         * 预测净流入
         */
        private BigDecimal predictedNetInflow;

        /**
         * 预测流向等级
         */
        private MoneyFlowLevel predictedFlowLevel;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 预测区间
         */
        private ForecastRange forecastRange;

        /**
         * 关键影响因素
         */
        private List<String> keyInfluencingFactors;

        /**
         * 获取预测流向等级
         */
        public MoneyFlowLevel getPredictedFlowLevel() {
            return predictedFlowLevel;
        }

        /**
         * 获取预测置信度
         */
        public BigDecimal getForecastConfidence() {
            return forecastConfidence;
        }
    }

    /**
     * 预测区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastRange {
        /**
         * 下限
         */
        private BigDecimal lowerBound;

        /**
         * 上限
         */
        private BigDecimal upperBound;

        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastModelEvaluation {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型精确度
         */
        private BigDecimal modelPrecision;

        /**
         * 模型召回率
         */
        private BigDecimal modelRecall;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;

        /**
         * 历史表现
         */
        private BigDecimal historicalPerformance;
    }

    /**
     * 资金流向交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowTradingAdvice {
        /**
         * 主要交易建议
         */
        private String mainTradingAdvice;

        /**
         * 建议强度
         */
        private BigDecimal adviceStrength;

        /**
         * 建议方向
         */
        private String adviceDirection;

        /**
         * 入场时机建议
         */
        private EntryTimingAdvice entryTimingAdvice;

        /**
         * 出场时机建议
         */
        private ExitTimingAdvice exitTimingAdvice;

        /**
         * 仓位管理建议
         */
        private PositionManagementAdvice positionManagementAdvice;

        /**
         * 风险控制建议
         */
        private List<String> riskControlAdvices;

        /**
         * 策略组合建议
         */
        private List<StrategyComboAdvice> strategyComboAdvices;

        /**
         * 获取主要交易建议
         */
        public String getMainTradingAdvice() {
            return mainTradingAdvice;
        }

        /**
         * 获取入场时机建议
         */
        public EntryTimingAdvice getEntryTimingAdvice() {
            return entryTimingAdvice;
        }

        /**
         * 获取风险控制建议
         */
        public List<String> getRiskControlAdvices() {
            return riskControlAdvices;
        }
    }

    /**
     * 入场时机建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EntryTimingAdvice {
        /**
         * 最佳入场时机
         */
        private String optimalEntryTiming;

        /**
         * 入场信号强度
         */
        private BigDecimal entrySignalStrength;

        /**
         * 入场条件
         */
        private List<String> entryConditions;

        /**
         * 入场风险评估
         */
        private BigDecimal entryRiskAssessment;

        /**
         * 预期成功率
         */
        private BigDecimal expectedSuccessRate;
    }

    /**
     * 出场时机建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExitTimingAdvice {
        /**
         * 止盈建议
         */
        private String takeProfitAdvice;

        /**
         * 止损建议
         */
        private String stopLossAdvice;

        /**
         * 出场信号
         */
        private List<String> exitSignals;

        /**
         * 分批出场建议
         */
        private String gradualExitAdvice;

        /**
         * 出场风险评估
         */
        private BigDecimal exitRiskAssessment;
    }

    /**
     * 仓位管理建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PositionManagementAdvice {
        /**
         * 建议仓位比例
         */
        private BigDecimal recommendedPositionRatio;

        /**
         * 最大仓位限制
         */
        private BigDecimal maxPositionLimit;

        /**
         * 分批建仓建议
         */
        private String gradualPositionBuildingAdvice;

        /**
         * 仓位调整建议
         */
        private List<String> positionAdjustmentAdvices;

        /**
         * 资金配置建议
         */
        private String fundAllocationAdvice;
    }

    /**
     * 策略组合建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StrategyComboAdvice {
        /**
         * 策略组合名称
         */
        private String comboName;

        /**
         * 策略组合描述
         */
        private String comboDescription;

        /**
         * 组合策略
         */
        private List<String> comboStrategies;

        /**
         * 组合权重
         */
        private Map<String, BigDecimal> comboWeights;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险评级
         */
        private String riskRating;
    }

    /**
     * 资金流向风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 风险评分
         */
        private BigDecimal riskScore;

        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;

        /**
         * 集中度风险
         */
        private BigDecimal concentrationRisk;

        /**
         * 波动性风险
         */
        private BigDecimal volatilityRisk;

        /**
         * 趋势反转风险
         */
        private BigDecimal trendReversalRisk;

        /**
         * 外部冲击风险
         */
        private BigDecimal externalShockRisk;

        /**
         * 风险因素分析
         */
        private List<RiskFactorAnalysis> riskFactorAnalyses;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;

        /**
         * 获取整体风险等级
         */
        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }
    }

    /**
     * 风险因素分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactorAnalysis {
        /**
         * 风险因素名称
         */
        private String riskFactorName;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 风险概率
         */
        private BigDecimal riskProbability;

        /**
         * 潜在影响
         */
        private BigDecimal potentialImpact;

        /**
         * 风险描述
         */
        private String riskDescription;

        /**
         * 应对措施
         */
        private List<String> countermeasures;
    }

    /**
     * 资金流向预警
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MoneyFlowAlert {
        /**
         * 预警ID
         */
        private String alertId;

        /**
         * 预警类型
         */
        private AlertType alertType;

        /**
         * 预警等级
         */
        private AlertLevel alertLevel;

        /**
         * 预警时间
         */
        private LocalDateTime alertTime;

        /**
         * 预警描述
         */
        private String alertDescription;

        /**
         * 触发条件
         */
        private List<String> triggerConditions;

        /**
         * 预警值
         */
        private BigDecimal alertValue;

        /**
         * 阈值
         */
        private BigDecimal threshold;

        /**
         * 建议行动
         */
        private List<String> recommendedActions;

        /**
         * 预警状态
         */
        private String alertStatus;

        /**
         * 获取预警等级
         */
        public AlertLevel getAlertLevel() {
            return alertLevel;
        }
    }

    /**
     * 预警类型
     */
    public enum AlertType {
        MASSIVE_INFLOW("大量资金流入"),
        MASSIVE_OUTFLOW("大量资金流出"),
        FLOW_REVERSAL("流向反转"),
        ABNORMAL_CONCENTRATION("异常集中"),
        MAIN_FUND_ACTIVITY("主力资金异动"),
        INSTITUTIONAL_MOVEMENT("机构资金变动"),
        RETAIL_PANIC("散户恐慌"),
        LIQUIDITY_SHORTAGE("流动性不足"),
        TREND_CHANGE("趋势变化"),
        OTHER("其他");

        private final String description;

        AlertType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 预警等级
     */
    public enum AlertLevel {
        CRITICAL("严重", 5),
        HIGH("高", 4),
        MEDIUM("中等", 3),
        LOW("低", 2),
        INFO("信息", 1);

        private final String description;
        private final int level;

        AlertLevel(String description, int level) {
            this.description = description;
            this.level = level;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }
    }

    /**
     * 获取资金流向分析摘要
     */
    public String getMoneyFlowAnalysisSummary() {
        return String.format("流向等级: %s, 净流入: %s, 主力资金: %s",
            moneyFlowLevel != null ? moneyFlowLevel.getDescription() : "未知",
            netMoneyFlow != null ? netMoneyFlow.toString() : "N/A",
            mainFundAnalysis != null && mainFundAnalysis.getMainFundNetInflow() != null ? 
                mainFundAnalysis.getMainFundNetInflow().toString() : "N/A");
    }

    /**
     * 检查是否为强势流入
     */
    public boolean isStrongInflow() {
        return moneyFlowLevel == MoneyFlowLevel.MASSIVE_INFLOW || 
               moneyFlowLevel == MoneyFlowLevel.MODERATE_INFLOW;
    }

    /**
     * 检查是否为强势流出
     */
    public boolean isStrongOutflow() {
        return moneyFlowLevel == MoneyFlowLevel.MASSIVE_OUTFLOW || 
               moneyFlowLevel == MoneyFlowLevel.MODERATE_OUTFLOW;
    }

    /**
     * 检查主力资金是否活跃
     */
    public boolean isMainFundActive() {
        if (mainFundAnalysis == null || mainFundAnalysis.getMainFundActivity() == null) {
            return false;
        }
        return mainFundAnalysis.getMainFundActivity().compareTo(BigDecimal.valueOf(0.7)) > 0;
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (moneyFlowTradingAdvice == null) {
            return "暂无交易建议";
        }
        return moneyFlowTradingAdvice.getMainTradingAdvice();
    }

    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (moneyFlowRiskAssessment == null) {
            return "风险等级未评估";
        }
        return moneyFlowRiskAssessment.getOverallRiskLevel();
    }

    /**
     * 检查是否需要立即关注
     */
    public boolean requiresImmediateAttention() {
        return isStrongInflow() || isStrongOutflow() || isMainFundActive() ||
               (moneyFlowAlerts != null && moneyFlowAlerts.stream()
                   .anyMatch(alert -> alert.getAlertLevel() == AlertLevel.CRITICAL || 
                                    alert.getAlertLevel() == AlertLevel.HIGH));
    }

    /**
     * 获取最高优先级预警
     */
    public MoneyFlowAlert getHighestPriorityAlert() {
        if (moneyFlowAlerts == null || moneyFlowAlerts.isEmpty()) {
            return null;
        }
        
        return moneyFlowAlerts.stream()
            .max((a1, a2) -> Integer.compare(a1.getAlertLevel().getLevel(), a2.getAlertLevel().getLevel()))
            .orElse(null);
    }

    /**
     * 获取趋势分析摘要
     */
    public String getTrendAnalysisSummary() {
        if (moneyFlowTrendAnalysis == null || moneyFlowTrendAnalysis.getShortTermTrend() == null) {
            return "趋势分析不可用";
        }
        
        FlowTrend shortTerm = moneyFlowTrendAnalysis.getShortTermTrend();
        return String.format("短期趋势: %s (强度: %s)",
            shortTerm.getTrendDirection() != null ? shortTerm.getTrendDirection() : "未知",
            shortTerm.getTrendStrength() != null ? shortTerm.getTrendStrength().toString() : "N/A");
    }

    /**
     * 获取强度分析摘要
     */
    public String getIntensityAnalysisSummary() {
        if (moneyFlowIntensityAnalysis == null) {
            return "强度分析不可用";
        }
        
        BigDecimal overallIntensity = moneyFlowIntensityAnalysis.getOverallFlowIntensity();
        String intensityLevel = moneyFlowIntensityAnalysis.getIntensityGrading() != null ? 
            moneyFlowIntensityAnalysis.getIntensityGrading().getIntensityLevel() : "未知";
        
        return String.format("流向强度: %s (等级: %s)",
            overallIntensity != null ? overallIntensity.toString() : "N/A",
            intensityLevel);
    }

    /**
     * 获取预测摘要
     */
    public String getForecastSummary() {
        if (moneyFlowForecast == null || moneyFlowForecast.getShortTermForecast() == null) {
            return "预测分析不可用";
        }
        
        FlowForecast shortTerm = moneyFlowForecast.getShortTermForecast();
        return String.format("短期预测: %s (置信度: %s)",
            shortTerm.getPredictedFlowLevel() != null ? 
                shortTerm.getPredictedFlowLevel().getDescription() : "未知",
            shortTerm.getForecastConfidence() != null ? 
                shortTerm.getForecastConfidence().toString() : "N/A");
    }

    /**
     * 检查是否有入场机会
     */
    public boolean hasEntryOpportunity() {
        if (moneyFlowTradingAdvice == null || 
            moneyFlowTradingAdvice.getEntryTimingAdvice() == null) {
            return false;
        }
        
        BigDecimal signalStrength = moneyFlowTradingAdvice.getEntryTimingAdvice().getEntrySignalStrength();
        return signalStrength != null && signalStrength.compareTo(BigDecimal.valueOf(0.7)) > 0;
    }

    /**
     * 检查是否需要风险控制
     */
    public boolean needsRiskControl() {
        return moneyFlowTradingAdvice != null && 
               moneyFlowTradingAdvice.getRiskControlAdvices() != null &&
               !moneyFlowTradingAdvice.getRiskControlAdvices().isEmpty();
    }

    /**
     * 获取机构资金影响评估
     */
    public String getInstitutionalImpactAssessment() {
        if (institutionalFundAnalysis == null || 
            institutionalFundAnalysis.getInstitutionalInfluenceAssessment() == null) {
            return "机构影响评估不可用";
        }
        
        InstitutionalInfluenceAssessment assessment = institutionalFundAnalysis.getInstitutionalInfluenceAssessment();
        BigDecimal priceInfluence = assessment.getPriceInfluence();
        BigDecimal marketLeadership = assessment.getMarketLeadership();
        
        return String.format("价格影响力: %s, 市场引导力: %s",
            priceInfluence != null ? priceInfluence.toString() : "N/A",
            marketLeadership != null ? marketLeadership.toString() : "N/A");
    }

    /**
     * 获取散户情绪评估
     */
    public String getRetailSentimentAssessment() {
        if (retailFundAnalysis == null || 
            retailFundAnalysis.getRetailSentimentIndicators() == null) {
            return "散户情绪评估不可用";
        }
        
        RetailSentimentIndicators sentiment = retailFundAnalysis.getRetailSentimentIndicators();
        BigDecimal panicIndex = sentiment.getPanicIndex();
        BigDecimal greedIndex = sentiment.getGreedIndex();
        
        return String.format("恐慌指数: %s, 贪婪指数: %s",
            panicIndex != null ? panicIndex.toString() : "N/A",
            greedIndex != null ? greedIndex.toString() : "N/A");
    }
}