package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * API限制响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "API限制响应")
public class ApiLimitsResponse {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "API密钥ID")
    private String apiKeyId;

    @Schema(description = "用户等级")
    private String userLevel;

    @Schema(description = "VIP等级")
    private String vipLevel;

    @Schema(description = "全局限制")
    private GlobalLimits globalLimits;

    @Schema(description = "接口限制")
    private Map<String, EndpointLimit> endpointLimits;

    @Schema(description = "交易限制")
    private TradingLimits tradingLimits;

    @Schema(description = "提现限制")
    private WithdrawLimits withdrawLimits;

    @Schema(description = "充值限制")
    private DepositLimits depositLimits;

    @Schema(description = "当前使用情况")
    private CurrentUsage currentUsage;

    @Schema(description = "限制重置时间")
    private Map<String, LocalDateTime> resetTimes;

    @Schema(description = "IP白名单")
    private List<String> ipWhitelist;

    @Schema(description = "是否启用IP限制")
    private Boolean ipRestrictionEnabled;

    @Schema(description = "地区限制")
    private List<String> regionRestrictions;

    @Schema(description = "功能权限")
    private Map<String, Boolean> featurePermissions;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "KYC状态")
    private String kycStatus;

    @Schema(description = "账户状态")
    private String accountStatus;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "下次评估时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime nextAssessmentTime;

    /**
     * 全局限制
     */
    @Data
    @Schema(description = "全局限制")
    public static class GlobalLimits {
        
        @Schema(description = "每秒请求数限制")
        private Integer requestsPerSecond;
        
        @Schema(description = "每分钟请求数限制")
        private Integer requestsPerMinute;
        
        @Schema(description = "每小时请求数限制")
        private Integer requestsPerHour;
        
        @Schema(description = "每日请求数限制")
        private Integer requestsPerDay;
        
        @Schema(description = "并发连接数限制")
        private Integer maxConcurrentConnections;
        
        @Schema(description = "WebSocket连接数限制")
        private Integer maxWebSocketConnections;
        
        @Schema(description = "订单簿深度限制")
        private Integer maxOrderBookDepth;
        
        @Schema(description = "K线数据条数限制")
        private Integer maxKlineLimit;
        
        @Schema(description = "历史数据查询天数限制")
        private Integer maxHistoryDays;
    }

    /**
     * 接口限制
     */
    @Data
    @Schema(description = "接口限制")
    public static class EndpointLimit {
        
        @Schema(description = "接口路径")
        private String endpoint;
        
        @Schema(description = "接口名称")
        private String endpointName;
        
        @Schema(description = "权重")
        private Integer weight;
        
        @Schema(description = "每秒请求数限制")
        private Integer requestsPerSecond;
        
        @Schema(description = "每分钟请求数限制")
        private Integer requestsPerMinute;
        
        @Schema(description = "每小时请求数限制")
        private Integer requestsPerHour;
        
        @Schema(description = "每日请求数限制")
        private Integer requestsPerDay;
        
        @Schema(description = "是否需要认证")
        private Boolean requiresAuth;
        
        @Schema(description = "所需权限")
        private List<String> requiredPermissions;
        
        @Schema(description = "是否启用")
        private Boolean enabled;
    }

    /**
     * 交易限制
     */
    @Data
    @Schema(description = "交易限制")
    public static class TradingLimits {
        
        @Schema(description = "每秒订单数限制")
        private Integer ordersPerSecond;
        
        @Schema(description = "每分钟订单数限制")
        private Integer ordersPerMinute;
        
        @Schema(description = "每小时订单数限制")
        private Integer ordersPerHour;
        
        @Schema(description = "每日订单数限制")
        private Integer ordersPerDay;
        
        @Schema(description = "最大持仓订单数")
        private Integer maxOpenOrders;
        
        @Schema(description = "单笔最大交易金额")
        private String maxTradeAmount;
        
        @Schema(description = "每日最大交易金额")
        private String maxDailyTradeAmount;
        
        @Schema(description = "每月最大交易金额")
        private String maxMonthlyTradeAmount;
        
        @Schema(description = "支持的交易对")
        private List<String> allowedSymbols;
        
        @Schema(description = "禁止的交易对")
        private List<String> restrictedSymbols;
        
        @Schema(description = "是否支持杠杆交易")
        private Boolean marginTradingEnabled;
        
        @Schema(description = "最大杠杆倍数")
        private Integer maxLeverage;
    }

    /**
     * 提现限制
     */
    @Data
    @Schema(description = "提现限制")
    public static class WithdrawLimits {
        
        @Schema(description = "每日提现次数限制")
        private Integer dailyWithdrawCount;
        
        @Schema(description = "每日提现金额限制")
        private Map<String, String> dailyWithdrawAmounts;
        
        @Schema(description = "每月提现金额限制")
        private Map<String, String> monthlyWithdrawAmounts;
        
        @Schema(description = "单笔最小提现金额")
        private Map<String, String> minWithdrawAmounts;
        
        @Schema(description = "单笔最大提现金额")
        private Map<String, String> maxWithdrawAmounts;
        
        @Schema(description = "提现手续费")
        private Map<String, String> withdrawFees;
        
        @Schema(description = "支持提现的币种")
        private List<String> allowedAssets;
        
        @Schema(description = "禁止提现的币种")
        private List<String> restrictedAssets;
        
        @Schema(description = "是否需要邮箱确认")
        private Boolean requiresEmailConfirmation;
        
        @Schema(description = "是否需要短信确认")
        private Boolean requiresSmsConfirmation;
        
        @Schema(description = "是否需要谷歌验证")
        private Boolean requiresGoogleAuth;
    }

    /**
     * 充值限制
     */
    @Data
    @Schema(description = "充值限制")
    public static class DepositLimits {
        
        @Schema(description = "每日充值次数限制")
        private Integer dailyDepositCount;
        
        @Schema(description = "每日充值金额限制")
        private Map<String, String> dailyDepositAmounts;
        
        @Schema(description = "每月充值金额限制")
        private Map<String, String> monthlyDepositAmounts;
        
        @Schema(description = "单笔最小充值金额")
        private Map<String, String> minDepositAmounts;
        
        @Schema(description = "单笔最大充值金额")
        private Map<String, String> maxDepositAmounts;
        
        @Schema(description = "支持充值的币种")
        private List<String> allowedAssets;
        
        @Schema(description = "禁止充值的币种")
        private List<String> restrictedAssets;
        
        @Schema(description = "充值确认数要求")
        private Map<String, Integer> confirmationRequirements;
        
        @Schema(description = "是否启用充值")
        private Boolean depositEnabled;
    }

    /**
     * 当前使用情况
     */
    @Data
    @Schema(description = "当前使用情况")
    public static class CurrentUsage {
        
        @Schema(description = "当前秒级请求数")
        private Integer currentRequestsPerSecond;
        
        @Schema(description = "当前分钟级请求数")
        private Integer currentRequestsPerMinute;
        
        @Schema(description = "当前小时级请求数")
        private Integer currentRequestsPerHour;
        
        @Schema(description = "当前日级请求数")
        private Integer currentRequestsPerDay;
        
        @Schema(description = "当前活跃连接数")
        private Integer currentConnections;
        
        @Schema(description = "当前WebSocket连接数")
        private Integer currentWebSocketConnections;
        
        @Schema(description = "当前持仓订单数")
        private Integer currentOpenOrders;
        
        @Schema(description = "今日交易次数")
        private Integer todayTradeCount;
        
        @Schema(description = "今日交易金额")
        private String todayTradeAmount;
        
        @Schema(description = "今日提现次数")
        private Integer todayWithdrawCount;
        
        @Schema(description = "今日提现金额")
        private Map<String, String> todayWithdrawAmounts;
        
        @Schema(description = "今日充值次数")
        private Integer todayDepositCount;
        
        @Schema(description = "今日充值金额")
        private Map<String, String> todayDepositAmounts;
        
        @Schema(description = "剩余请求配额")
        private Map<String, Integer> remainingQuotas;
        
        @Schema(description = "配额使用率")
        private Map<String, Double> quotaUsageRates;
    }
}