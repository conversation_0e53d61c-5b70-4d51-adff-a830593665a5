package com.cryptoexchange.dto.response;

import java.util.List;

/**
 * ADL分位数响应
 */
public class AdlQuantileResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * ADL分位数指标
     * 1, 2, 3, 4, 5分别代表0-20%, 20-40%, 40-60%, 60-80%, 80-100%
     */
    private List<Integer> adlQuantile;
    
    /**
     * 当前仓位方向 (LONG/SHORT)
     */
    private String side;
    
    /**
     * 仓位大小
     */
    private String size;
    
    /**
     * 仓位价值
     */
    private String notional;
    
    public AdlQuantileResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public List<Integer> getAdlQuantile() {
        return adlQuantile;
    }
    
    public void setAdlQuantile(List<Integer> adlQuantile) {
        this.adlQuantile = adlQuantile;
    }
    
    public String getSide() {
        return side;
    }
    
    public void setSide(String side) {
        this.side = side;
    }
    
    public String getSize() {
        return size;
    }
    
    public void setSize(String size) {
        this.size = size;
    }
    
    public String getNotional() {
        return notional;
    }
    
    public void setNotional(String notional) {
        this.notional = notional;
    }
}