package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 未读通知数量响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "未读通知数量响应")
public class UnreadNotificationCountResponse {

    @Schema(description = "总未读数量", example = "15")
    private Integer totalUnreadCount;

    @Schema(description = "重要未读数量", example = "3")
    private Integer importantUnreadCount;

    @Schema(description = "系统通知未读数量", example = "5")
    private Integer systemUnreadCount;

    @Schema(description = "交易通知未读数量", example = "7")
    private Integer tradeUnreadCount;

    @Schema(description = "安全通知未读数量", example = "2")
    private Integer securityUnreadCount;

    @Schema(description = "活动通知未读数量", example = "1")
    private Integer activityUnreadCount;

    @Schema(description = "各类型未读数量详情")
    private Map<String, Integer> typeUnreadCounts;

    @Schema(description = "是否有新的重要通知", example = "true")
    private Boolean hasNewImportant;

    @Schema(description = "最新通知时间戳", example = "1640995200000")
    private Long latestNotificationTime;

    @Schema(description = "是否需要显示红点", example = "true")
    private Boolean showRedDot;

    @Schema(description = "推送设置状态", example = "ENABLED")
    private String pushStatus;

    public UnreadNotificationCountResponse() {
        this.totalUnreadCount = 0;
        this.importantUnreadCount = 0;
        this.systemUnreadCount = 0;
        this.tradeUnreadCount = 0;
        this.securityUnreadCount = 0;
        this.activityUnreadCount = 0;
        this.hasNewImportant = false;
        this.showRedDot = false;
        this.pushStatus = "ENABLED";
    }

    public UnreadNotificationCountResponse(Integer totalUnreadCount, Integer importantUnreadCount) {
        this();
        this.totalUnreadCount = totalUnreadCount;
        this.importantUnreadCount = importantUnreadCount;
        this.showRedDot = totalUnreadCount > 0;
        this.hasNewImportant = importantUnreadCount > 0;
    }

    /**
     * 计算总未读数量
     */
    public void calculateTotalUnreadCount() {
        this.totalUnreadCount = (systemUnreadCount != null ? systemUnreadCount : 0) +
                               (tradeUnreadCount != null ? tradeUnreadCount : 0) +
                               (securityUnreadCount != null ? securityUnreadCount : 0) +
                               (activityUnreadCount != null ? activityUnreadCount : 0);
        this.showRedDot = this.totalUnreadCount > 0;
    }
}