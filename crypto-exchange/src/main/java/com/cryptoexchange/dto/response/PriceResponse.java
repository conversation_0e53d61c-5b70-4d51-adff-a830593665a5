package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格响应
 */
public class PriceResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 当前价格
     */
    private BigDecimal price;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 24小时最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 24小时最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 24小时成交量
     */
    private BigDecimal volume;
    
    /**
     * 24小时成交额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 前收盘价
     */
    private BigDecimal prevClosePrice;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    public PriceResponse() {}
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getPriceChange() {
        return priceChange;
    }
    
    public void setPriceChange(BigDecimal priceChange) {
        this.priceChange = priceChange;
    }
    
    public BigDecimal getPriceChangePercent() {
        return priceChangePercent;
    }
    
    public void setPriceChangePercent(BigDecimal priceChangePercent) {
        this.priceChangePercent = priceChangePercent;
    }
    
    public BigDecimal getHighPrice() {
        return highPrice;
    }
    
    public void setHighPrice(BigDecimal highPrice) {
        this.highPrice = highPrice;
    }
    
    public BigDecimal getLowPrice() {
        return lowPrice;
    }
    
    public void setLowPrice(BigDecimal lowPrice) {
        this.lowPrice = lowPrice;
    }
    
    public BigDecimal getVolume() {
        return volume;
    }
    
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
    
    public BigDecimal getQuoteVolume() {
        return quoteVolume;
    }
    
    public void setQuoteVolume(BigDecimal quoteVolume) {
        this.quoteVolume = quoteVolume;
    }
    
    public BigDecimal getOpenPrice() {
        return openPrice;
    }
    
    public void setOpenPrice(BigDecimal openPrice) {
        this.openPrice = openPrice;
    }
    
    public BigDecimal getPrevClosePrice() {
        return prevClosePrice;
    }
    
    public void setPrevClosePrice(BigDecimal prevClosePrice) {
        this.prevClosePrice = prevClosePrice;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public void setLastUpdateTime(LocalDateTime lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}