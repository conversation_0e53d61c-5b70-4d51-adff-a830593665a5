package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 价格预测响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PricePredictionResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 预测生成时间
     */
    private LocalDateTime predictionTime;

    /**
     * 预测时间范围
     */
    private String predictionTimeRange;

    /**
     * 当前价格
     */
    private BigDecimal currentPrice;

    /**
     * 预测置信度等级
     */
    private PredictionConfidenceLevel confidenceLevel;

    /**
     * 短期价格预测
     */
    private ShortTermPrediction shortTermPrediction;

    /**
     * 中期价格预测
     */
    private MediumTermPrediction mediumTermPrediction;

    /**
     * 长期价格预测
     */
    private LongTermPrediction longTermPrediction;

    /**
     * 技术分析预测
     */
    private TechnicalAnalysisPrediction technicalAnalysisPrediction;

    /**
     * 基本面分析预测
     */
    private FundamentalAnalysisPrediction fundamentalAnalysisPrediction;

    /**
     * 情绪分析预测
     */
    private SentimentAnalysisPrediction sentimentAnalysisPrediction;

    /**
     * 机器学习预测
     */
    private MachineLearningPrediction machineLearningPrediction;

    /**
     * 价格目标分析
     */
    private PriceTargetAnalysis priceTargetAnalysis;

    /**
     * 风险评估
     */
    private PredictionRiskAssessment riskAssessment;

    /**
     * 预测模型评估
     */
    private PredictionModelEvaluation modelEvaluation;

    /**
     * 预测建议
     */
    private List<PredictionRecommendation> recommendations;

    /**
     * 预测警告
     */
    private List<PredictionWarning> warnings;

    /**
     * 预测置信度等级
     */
    public enum PredictionConfidenceLevel {
        VERY_HIGH("非常高", "预测置信度非常高，准确性很高", 0.9),
        HIGH("高", "预测置信度高，准确性较高", 0.8),
        MEDIUM("中等", "预测置信度中等，准确性一般", 0.6),
        LOW("低", "预测置信度低，准确性较低", 0.4),
        VERY_LOW("非常低", "预测置信度非常低，准确性很低", 0.2);

        private final String description;
        private final String detail;
        private final double confidenceScore;

        PredictionConfidenceLevel(String description, String detail, double confidenceScore) {
            this.description = description;
            this.detail = detail;
            this.confidenceScore = confidenceScore;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }

        public double getConfidenceScore() {
            return confidenceScore;
        }
    }

    /**
     * 短期价格预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShortTermPrediction {
        /**
         * 预测时间范围（小时）
         */
        private Integer timeRangeHours;

        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;

        /**
         * 预测区间
         */
        private PriceRange priceRange;

        /**
         * 置信度
         */
        private BigDecimal confidence;

        /**
         * 关键支撑位
         */
        private List<BigDecimal> supportLevels;

        /**
         * 关键阻力位
         */
        private List<BigDecimal> resistanceLevels;

        /**
         * 预测趋势
         */
        private String predictedTrend;

        /**
         * 波动率预测
         */
        private BigDecimal volatilityForecast;

        /**
         * 影响因素
         */
        private List<String> influencingFactors;

        public BigDecimal getPredictedPrice() {
            return predictedPrice;
        }
    }

    /**
     * 中期价格预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MediumTermPrediction {
        /**
         * 预测时间范围（天）
         */
        private Integer timeRangeDays;

        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;

        /**
         * 预测区间
         */
        private PriceRange priceRange;

        /**
         * 置信度
         */
        private BigDecimal confidence;

        /**
         * 趋势分析
         */
        private TrendAnalysis trendAnalysis;

        /**
         * 周期性分析
         */
        private CyclicalAnalysis cyclicalAnalysis;

        /**
         * 关键价格水平
         */
        private List<KeyPriceLevel> keyPriceLevels;

        /**
         * 预测路径
         */
        private List<PricePath> pricePaths;

        public BigDecimal getPredictedPrice() {
            return predictedPrice;
        }
    }

    /**
     * 长期价格预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LongTermPrediction {
        /**
         * 预测时间范围（月）
         */
        private Integer timeRangeMonths;

        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;

        /**
         * 预测区间
         */
        private PriceRange priceRange;

        /**
         * 置信度
         */
        private BigDecimal confidence;

        /**
         * 基本面驱动因素
         */
        private List<FundamentalDriver> fundamentalDrivers;

        /**
         * 宏观经济影响
         */
        private MacroeconomicImpact macroeconomicImpact;

        /**
         * 技术发展影响
         */
        private TechnologyDevelopmentImpact technologyImpact;

        /**
         * 监管环境影响
         */
        private RegulatoryEnvironmentImpact regulatoryImpact;

        /**
         * 市场成熟度影响
         */
        private MarketMaturityImpact marketMaturityImpact;

        public BigDecimal getPredictedPrice() {
            return predictedPrice;
        }
    }

    /**
     * 价格区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceRange {
        /**
         * 最低价格
         */
        private BigDecimal minPrice;

        /**
         * 最高价格
         */
        private BigDecimal maxPrice;

        /**
         * 最可能价格
         */
        private BigDecimal mostLikelyPrice;

        /**
         * 置信区间
         */
        private BigDecimal confidenceInterval;
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        /**
         * 主要趋势
         */
        private String primaryTrend;

        /**
         * 次要趋势
         */
        private String secondaryTrend;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续性
         */
        private BigDecimal trendPersistence;

        /**
         * 趋势转折概率
         */
        private BigDecimal trendReversalProbability;
    }

    /**
     * 周期性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CyclicalAnalysis {
        /**
         * 周期类型
         */
        private String cycleType;

        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 当前周期位置
         */
        private String currentCyclePosition;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 下一个周期转折点
         */
        private LocalDateTime nextTurningPoint;
    }

    /**
     * 关键价格水平
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeyPriceLevel {
        /**
         * 价格水平
         */
        private BigDecimal priceLevel;

        /**
         * 水平类型
         */
        private String levelType;

        /**
         * 强度
         */
        private BigDecimal strength;

        /**
         * 重要性
         */
        private String importance;

        /**
         * 预期反应
         */
        private String expectedReaction;
    }

    /**
     * 价格路径
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PricePath {
        /**
         * 路径名称
         */
        private String pathName;

        /**
         * 路径概率
         */
        private BigDecimal pathProbability;

        /**
         * 路径描述
         */
        private String pathDescription;

        /**
         * 关键里程碑
         */
        private List<PriceMilestone> milestones;
    }

    /**
     * 价格里程碑
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceMilestone {
        /**
         * 时间点
         */
        private LocalDateTime timePoint;

        /**
         * 预期价格
         */
        private BigDecimal expectedPrice;

        /**
         * 里程碑描述
         */
        private String description;

        /**
         * 置信度
         */
        private BigDecimal confidence;
    }

    /**
     * 基本面驱动因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FundamentalDriver {
        /**
         * 驱动因素名称
         */
        private String driverName;

        /**
         * 影响权重
         */
        private BigDecimal impactWeight;

        /**
         * 影响方向
         */
        private String impactDirection;

        /**
         * 当前状态
         */
        private String currentStatus;

        /**
         * 预期变化
         */
        private String expectedChange;

        /**
         * 时间框架
         */
        private String timeFrame;
    }

    /**
     * 宏观经济影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MacroeconomicImpact {
        /**
         * 通胀影响
         */
        private BigDecimal inflationImpact;

        /**
         * 利率影响
         */
        private BigDecimal interestRateImpact;

        /**
         * 货币政策影响
         */
        private BigDecimal monetaryPolicyImpact;

        /**
         * 经济增长影响
         */
        private BigDecimal economicGrowthImpact;

        /**
         * 地缘政治影响
         */
        private BigDecimal geopoliticalImpact;
    }

    /**
     * 技术发展影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnologyDevelopmentImpact {
        /**
         * 区块链技术进展
         */
        private BigDecimal blockchainAdvancement;

        /**
         * 扩容解决方案
         */
        private BigDecimal scalingSolutions;

        /**
         * 互操作性改进
         */
        private BigDecimal interoperabilityImprovements;

        /**
         * 安全性增强
         */
        private BigDecimal securityEnhancements;

        /**
         * 用户体验改善
         */
        private BigDecimal userExperienceImprovements;
    }

    /**
     * 监管环境影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegulatoryEnvironmentImpact {
        /**
         * 监管清晰度
         */
        private BigDecimal regulatoryClarity;

        /**
         * 合规成本
         */
        private BigDecimal complianceCosts;

        /**
         * 市场准入
         */
        private BigDecimal marketAccess;

        /**
         * 机构采用
         */
        private BigDecimal institutionalAdoption;

        /**
         * 国际协调
         */
        private BigDecimal internationalCoordination;
    }

    /**
     * 市场成熟度影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketMaturityImpact {
        /**
         * 流动性改善
         */
        private BigDecimal liquidityImprovement;

        /**
         * 价格发现效率
         */
        private BigDecimal priceDiscoveryEfficiency;

        /**
         * 市场基础设施
         */
        private BigDecimal marketInfrastructure;

        /**
         * 投资者教育
         */
        private BigDecimal investorEducation;

        /**
         * 产品多样化
         */
        private BigDecimal productDiversification;
    }

    /**
     * 技术分析预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalAnalysisPrediction {
        /**
         * 技术指标分析
         */
        private TechnicalIndicatorAnalysis technicalIndicatorAnalysis;

        /**
         * 图表模式分析
         */
        private ChartPatternAnalysis chartPatternAnalysis;

        /**
         * 波浪理论分析
         */
        private ElliottWaveAnalysis elliottWaveAnalysis;

        /**
         * 斐波那契分析
         */
        private FibonacciAnalysis fibonacciAnalysis;

        /**
         * 成交量分析
         */
        private VolumeAnalysis volumeAnalysis;

        /**
         * 技术预测置信度
         */
        private BigDecimal technicalConfidence;

        public TechnicalIndicatorAnalysis getTechnicalIndicatorAnalysis() {
            return technicalIndicatorAnalysis;
        }
    }

    /**
     * 技术指标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalIndicatorAnalysis {
        /**
         * RSI分析
         */
        private IndicatorSignal rsiSignal;

        /**
         * MACD分析
         */
        private IndicatorSignal macdSignal;

        /**
         * 布林带分析
         */
        private IndicatorSignal bollingerSignal;

        /**
         * 移动平均线分析
         */
        private IndicatorSignal movingAverageSignal;

        /**
         * 随机指标分析
         */
        private IndicatorSignal stochasticSignal;

        /**
         * 综合技术信号
         */
        private String overallTechnicalSignal;

        public String getOverallTechnicalSignal() {
            return overallTechnicalSignal;
        }
    }

    /**
     * 指标信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndicatorSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 信号方向
         */
        private String signalDirection;

        /**
         * 当前值
         */
        private BigDecimal currentValue;

        /**
         * 信号描述
         */
        private String signalDescription;
    }

    /**
     * 图表模式分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChartPatternAnalysis {
        /**
         * 识别的模式
         */
        private List<ChartPattern> identifiedPatterns;

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;

        /**
         * 预期突破方向
         */
        private String expectedBreakoutDirection;

        /**
         * 目标价位
         */
        private BigDecimal targetPrice;

        /**
         * 止损价位
         */
        private BigDecimal stopLossPrice;
    }

    /**
     * 图表模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChartPattern {
        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 完成度
         */
        private BigDecimal completionPercentage;

        /**
         * 预期影响
         */
        private String expectedImpact;
    }

    /**
     * 波浪理论分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ElliottWaveAnalysis {
        /**
         * 当前波浪位置
         */
        private String currentWavePosition;

        /**
         * 波浪计数
         */
        private String waveCount;

        /**
         * 下一个波浪预测
         */
        private String nextWavePrediction;

        /**
         * 波浪目标
         */
        private BigDecimal waveTarget;

        /**
         * 波浪置信度
         */
        private BigDecimal waveConfidence;
    }

    /**
     * 斐波那契分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FibonacciAnalysis {
        /**
         * 回撤水平
         */
        private List<FibonacciLevel> retracementLevels;

        /**
         * 扩展水平
         */
        private List<FibonacciLevel> extensionLevels;

        /**
         * 关键斐波那契水平
         */
        private BigDecimal keyFibonacciLevel;

        /**
         * 斐波那契信号
         */
        private String fibonacciSignal;
    }

    /**
     * 斐波那契水平
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FibonacciLevel {
        /**
         * 水平比例
         */
        private BigDecimal levelRatio;

        /**
         * 价格水平
         */
        private BigDecimal priceLevel;

        /**
         * 水平强度
         */
        private BigDecimal levelStrength;

        /**
         * 水平类型
         */
        private String levelType;
    }

    /**
     * 成交量分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnalysis {
        /**
         * 成交量趋势
         */
        private String volumeTrend;

        /**
         * 成交量确认
         */
        private String volumeConfirmation;

        /**
         * 成交量异常
         */
        private List<String> volumeAnomalies;

        /**
         * 成交量预测
         */
        private BigDecimal volumeForecast;

        /**
         * 成交量信号
         */
        private String volumeSignal;
    }

    /**
     * 基本面分析预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FundamentalAnalysisPrediction {
        /**
         * 项目基本面评估
         */
        private ProjectFundamentalAssessment projectAssessment;

        /**
         * 代币经济学分析
         */
        private TokenomicsAnalysis tokenomicsAnalysis;

        /**
         * 生态系统分析
         */
        private EcosystemAnalysis ecosystemAnalysis;

        /**
         * 竞争分析
         */
        private CompetitiveAnalysis competitiveAnalysis;

        /**
         * 基本面预测置信度
         */
        private BigDecimal fundamentalConfidence;

        public ProjectFundamentalAssessment getProjectAssessment() {
            return projectAssessment;
        }
    }

    /**
     * 项目基本面评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProjectFundamentalAssessment {
        /**
         * 团队评分
         */
        private BigDecimal teamScore;

        /**
         * 技术评分
         */
        private BigDecimal technologyScore;

        /**
         * 产品评分
         */
        private BigDecimal productScore;

        /**
         * 市场评分
         */
        private BigDecimal marketScore;

        /**
         * 综合评分
         */
        private BigDecimal overallScore;

        public BigDecimal getOverallScore() {
            return overallScore;
        }
    }

    /**
     * 代币经济学分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenomicsAnalysis {
        /**
         * 供应机制评估
         */
        private String supplyMechanismAssessment;

        /**
         * 通胀率分析
         */
        private BigDecimal inflationRateAnalysis;

        /**
         * 代币分配评估
         */
        private String tokenDistributionAssessment;

        /**
         * 实用性评估
         */
        private String utilityAssessment;

        /**
         * 代币经济学评分
         */
        private BigDecimal tokenomicsScore;
    }

    /**
     * 生态系统分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EcosystemAnalysis {
        /**
         * 开发者活跃度
         */
        private BigDecimal developerActivity;

        /**
         * 社区参与度
         */
        private BigDecimal communityEngagement;

        /**
         * 合作伙伴关系
         */
        private String partnerships;

        /**
         * 采用率
         */
        private BigDecimal adoptionRate;

        /**
         * 生态系统健康度
         */
        private BigDecimal ecosystemHealth;
    }

    /**
     * 竞争分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompetitiveAnalysis {
        /**
         * 市场地位
         */
        private String marketPosition;

        /**
         * 竞争优势
         */
        private List<String> competitiveAdvantages;

        /**
         * 竞争劣势
         */
        private List<String> competitiveDisadvantages;

        /**
         * 市场份额
         */
        private BigDecimal marketShare;

        /**
         * 竞争力评分
         */
        private BigDecimal competitivenessScore;
    }

    /**
     * 情绪分析预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentAnalysisPrediction {
        /**
         * 当前市场情绪
         */
        private String currentMarketSentiment;

        /**
         * 情绪趋势
         */
        private String sentimentTrend;

        /**
         * 社交媒体情绪
         */
        private SocialMediaSentiment socialMediaSentiment;

        /**
         * 新闻情绪
         */
        private NewsSentiment newsSentiment;

        /**
         * 恐慌贪婪指数
         */
        private BigDecimal fearGreedIndex;

        /**
         * 情绪预测置信度
         */
        private BigDecimal sentimentConfidence;

        public String getCurrentMarketSentiment() {
            return currentMarketSentiment;
        }
    }

    /**
     * 社交媒体情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SocialMediaSentiment {
        /**
         * Twitter情绪
         */
        private BigDecimal twitterSentiment;

        /**
         * Reddit情绪
         */
        private BigDecimal redditSentiment;

        /**
         * Telegram情绪
         */
        private BigDecimal telegramSentiment;

        /**
         * 综合社交媒体情绪
         */
        private BigDecimal overallSocialSentiment;

        /**
         * 情绪变化趋势
         */
        private String sentimentChangeTrend;
    }

    /**
     * 新闻情绪
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NewsSentiment {
        /**
         * 正面新闻数量
         */
        private Integer positiveNewsCount;

        /**
         * 负面新闻数量
         */
        private Integer negativeNewsCount;

        /**
         * 中性新闻数量
         */
        private Integer neutralNewsCount;

        /**
         * 新闻情绪评分
         */
        private BigDecimal newsSentimentScore;

        /**
         * 关键新闻影响
         */
        private List<String> keyNewsImpacts;
    }

    /**
     * 机器学习预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MachineLearningPrediction {
        /**
         * 神经网络预测
         */
        private NeuralNetworkPrediction neuralNetworkPrediction;

        /**
         * 随机森林预测
         */
        private RandomForestPrediction randomForestPrediction;

        /**
         * 支持向量机预测
         */
        private SVMPrediction svmPrediction;

        /**
         * 时间序列预测
         */
        private TimeSeriesPrediction timeSeriesPrediction;

        /**
         * 集成模型预测
         */
        private EnsemblePrediction ensemblePrediction;

        /**
         * 机器学习置信度
         */
        private BigDecimal mlConfidence;

        public BigDecimal getMlConfidence() {
            return mlConfidence;
        }
    }

    /**
     * 神经网络预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NeuralNetworkPrediction {
        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 特征重要性
         */
        private Map<String, BigDecimal> featureImportance;
    }

    /**
     * 随机森林预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RandomForestPrediction {
        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 树的数量
         */
        private Integer numberOfTrees;

        /**
         * 特征重要性
         */
        private Map<String, BigDecimal> featureImportance;
    }

    /**
     * 支持向量机预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SVMPrediction {
        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 核函数类型
         */
        private String kernelType;

        /**
         * 支持向量数量
         */
        private Integer supportVectorCount;
    }

    /**
     * 时间序列预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeSeriesPrediction {
        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 季节性检测
         */
        private String seasonalityDetection;

        /**
         * 趋势检测
         */
        private String trendDetection;
    }

    /**
     * 集成模型预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EnsemblePrediction {
        /**
         * 预测价格
         */
        private BigDecimal predictedPrice;

        /**
         * 模型准确率
         */
        private BigDecimal modelAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal predictionConfidence;

        /**
         * 集成方法
         */
        private String ensembleMethod;

        /**
         * 子模型权重
         */
        private Map<String, BigDecimal> subModelWeights;

        /**
         * 模型一致性
         */
        private BigDecimal modelConsistency;
    }

    /**
     * 价格目标分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceTargetAnalysis {
        /**
         * 保守目标
         */
        private PriceTarget conservativeTarget;

        /**
         * 适中目标
         */
        private PriceTarget moderateTarget;

        /**
         * 乐观目标
         */
        private PriceTarget optimisticTarget;

        /**
         * 最可能目标
         */
        private PriceTarget mostLikelyTarget;

        /**
         * 目标实现概率
         */
        private Map<String, BigDecimal> targetProbabilities;
    }

    /**
     * 价格目标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceTarget {
        /**
         * 目标价格
         */
        private BigDecimal targetPrice;

        /**
         * 预期时间
         */
        private LocalDateTime expectedTime;

        /**
         * 实现概率
         */
        private BigDecimal achievementProbability;

        /**
         * 目标理由
         */
        private List<String> targetReasons;

        /**
         * 风险因素
         */
        private List<String> riskFactors;
    }

    /**
     * 预测风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 预测不确定性
         */
        private BigDecimal predictionUncertainty;

        /**
         * 模型风险
         */
        private BigDecimal modelRisk;

        /**
         * 数据质量风险
         */
        private BigDecimal dataQualityRisk;

        /**
         * 市场风险
         */
        private BigDecimal marketRisk;

        /**
         * 黑天鹅事件风险
         */
        private BigDecimal blackSwanRisk;

        /**
         * 风险缓解措施
         */
        private List<String> riskMitigationMeasures;

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionModelEvaluation {
        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;

        /**
         * 模型可解释性
         */
        private BigDecimal modelInterpretability;

        /**
         * 历史表现
         */
        private HistoricalPerformance historicalPerformance;

        /**
         * 模型限制
         */
        private List<String> modelLimitations;

        /**
         * 改进建议
         */
        private List<String> improvementSuggestions;

        public BigDecimal getModelAccuracy() {
            return modelAccuracy;
        }
    }

    /**
     * 历史表现
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalPerformance {
        /**
         * 平均绝对误差
         */
        private BigDecimal meanAbsoluteError;

        /**
         * 均方根误差
         */
        private BigDecimal rootMeanSquareError;

        /**
         * 方向准确率
         */
        private BigDecimal directionalAccuracy;

        /**
         * 回测结果
         */
        private String backtestResults;

        /**
         * 表现趋势
         */
        private String performanceTrend;
    }

    /**
     * 预测建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionRecommendation {
        /**
         * 建议类型
         */
        private String recommendationType;

        /**
         * 建议操作
         */
        private String recommendedAction;

        /**
         * 建议强度
         */
        private BigDecimal recommendationStrength;

        /**
         * 时间框架
         */
        private String timeFrame;

        /**
         * 建议理由
         */
        private List<String> reasons;

        /**
         * 风险提示
         */
        private List<String> riskWarnings;

        /**
         * 执行建议
         */
        private String executionAdvice;

        public BigDecimal getRecommendationStrength() {
            return recommendationStrength;
        }

        public String getRecommendedAction() {
            return recommendedAction;
        }
    }

    /**
     * 预测警告
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PredictionWarning {
        /**
         * 警告类型
         */
        private String warningType;

        /**
         * 警告等级
         */
        private String warningLevel;

        /**
         * 警告描述
         */
        private String warningDescription;

        /**
         * 影响程度
         */
        private BigDecimal impactSeverity;

        /**
         * 应对措施
         */
        private List<String> countermeasures;

        public String getWarningLevel() {
            return warningLevel;
        }
    }

    /**
     * 获取预测摘要
     */
    public String getPredictionSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append(String.format("交易对: %s, ", symbol != null ? symbol : "未知"));
        summary.append(String.format("当前价格: %s, ", currentPrice != null ? currentPrice.toString() : "N/A"));
        summary.append(String.format("置信度: %s", confidenceLevel != null ? confidenceLevel.getDescription() : "未知"));
        
        if (shortTermPrediction != null && shortTermPrediction.getPredictedPrice() != null) {
            summary.append(String.format(", 短期预测: %s", shortTermPrediction.getPredictedPrice().toString()));
        }
        
        return summary.toString();
    }

    /**
     * 检查是否为高置信度预测
     */
    public boolean isHighConfidencePrediction() {
        return confidenceLevel == PredictionConfidenceLevel.HIGH || 
               confidenceLevel == PredictionConfidenceLevel.VERY_HIGH;
    }

    /**
     * 检查是否为低置信度预测
     */
    public boolean isLowConfidencePrediction() {
        return confidenceLevel == PredictionConfidenceLevel.LOW || 
               confidenceLevel == PredictionConfidenceLevel.VERY_LOW;
    }

    /**
     * 获取最佳预测价格
     */
    public BigDecimal getBestPredictedPrice() {
        if (shortTermPrediction != null && shortTermPrediction.getPredictedPrice() != null) {
            return shortTermPrediction.getPredictedPrice();
        }
        if (mediumTermPrediction != null && mediumTermPrediction.getPredictedPrice() != null) {
            return mediumTermPrediction.getPredictedPrice();
        }
        if (longTermPrediction != null && longTermPrediction.getPredictedPrice() != null) {
            return longTermPrediction.getPredictedPrice();
        }
        return null;
    }

    /**
     * 获取主要建议
     */
    public String getMainRecommendation() {
        if (recommendations == null || recommendations.isEmpty()) {
            return "暂无建议";
        }
        
        PredictionRecommendation topRecommendation = recommendations.stream()
            .max((r1, r2) -> {
                if (r1.getRecommendationStrength() == null && r2.getRecommendationStrength() == null) return 0;
                if (r1.getRecommendationStrength() == null) return -1;
                if (r2.getRecommendationStrength() == null) return 1;
                return r1.getRecommendationStrength().compareTo(r2.getRecommendationStrength());
            })
            .orElse(recommendations.get(0));
        
        return topRecommendation.getRecommendedAction();
    }

    /**
     * 检查是否有重要警告
     */
    public boolean hasImportantWarnings() {
        return warnings != null && !warnings.isEmpty() &&
               warnings.stream().anyMatch(w -> "高".equals(w.getWarningLevel()) || "严重".equals(w.getWarningLevel()));
    }

    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (riskAssessment == null) {
            return "风险等级未评估";
        }
        return riskAssessment.getOverallRiskLevel();
    }

    /**
     * 获取技术信号
     */
    public String getTechnicalSignal() {
        if (technicalAnalysisPrediction == null || 
            technicalAnalysisPrediction.getTechnicalIndicatorAnalysis() == null) {
            return "技术信号不可用";
        }
        return technicalAnalysisPrediction.getTechnicalIndicatorAnalysis().getOverallTechnicalSignal();
    }

    /**
     * 获取基本面评分
     */
    public BigDecimal getFundamentalScore() {
        if (fundamentalAnalysisPrediction == null || 
            fundamentalAnalysisPrediction.getProjectAssessment() == null) {
            return null;
        }
        return fundamentalAnalysisPrediction.getProjectAssessment().getOverallScore();
    }

    /**
     * 获取市场情绪
     */
    public String getMarketSentiment() {
        if (sentimentAnalysisPrediction == null) {
            return "市场情绪不可用";
        }
        return sentimentAnalysisPrediction.getCurrentMarketSentiment();
    }

    /**
     * 获取机器学习置信度
     */
    public BigDecimal getMachineLearningConfidence() {
        if (machineLearningPrediction == null) {
            return null;
        }
        return machineLearningPrediction.getMlConfidence();
    }

    /**
     * 检查预测是否可靠
     */
    public boolean isPredictionReliable() {
        return isHighConfidencePrediction() && 
               !hasImportantWarnings() &&
               (modelEvaluation == null || 
                modelEvaluation.getModelAccuracy() == null ||
                modelEvaluation.getModelAccuracy().compareTo(new BigDecimal("0.7")) >= 0);
    }

    /**
     * 获取预测一致性
     */
    public String getPredictionConsistency() {
        int positiveSignals = 0;
        int totalSignals = 0;
        
        // 检查技术分析信号
        if (technicalAnalysisPrediction != null) {
            String techSignal = getTechnicalSignal();
            if (techSignal != null && techSignal.contains("买入")) {
                positiveSignals++;
            }
            totalSignals++;
        }
        
        // 检查基本面评分
        BigDecimal fundamentalScore = getFundamentalScore();
        if (fundamentalScore != null) {
            if (fundamentalScore.compareTo(new BigDecimal("0.6")) >= 0) {
                positiveSignals++;
            }
            totalSignals++;
        }
        
        // 检查市场情绪
        String sentiment = getMarketSentiment();
        if (sentiment != null && (sentiment.contains("乐观") || sentiment.contains("积极"))) {
            positiveSignals++;
            totalSignals++;
        } else if (sentiment != null) {
            totalSignals++;
        }
        
        if (totalSignals == 0) {
            return "无法评估";
        }
        
        double consistency = (double) positiveSignals / totalSignals;
        if (consistency >= 0.8) {
            return "高度一致";
        } else if (consistency >= 0.6) {
            return "较为一致";
        } else if (consistency >= 0.4) {
            return "部分一致";
        } else {
            return "不一致";
        }
    }
}