package com.cryptoexchange.dto.response;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户资金费用响应DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserFundingFeeResponse {

    /**
     * 资金费用ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 合约符号
     */
    private String symbol;

    /**
     * 持仓方向：1-多头，2-空头
     */
    private Integer positionSide;

    /**
     * 持仓数量
     */
    private BigDecimal positionSize;

    /**
     * 资金费率
     */
    private BigDecimal fundingRate;

    /**
     * 资金费用金额
     */
    private BigDecimal fundingFee;

    /**
     * 标记价格
     */
    private BigDecimal markPrice;

    /**
     * 名义价值
     */
    private BigDecimal notionalValue;

    /**
     * 费用类型：1-支付，2-收取
     */
    private Integer feeType;

    /**
     * 资金费用时间
     */
    private LocalDateTime fundingTime;

    /**
     * 结算状态：1-待结算，2-已结算，3-结算失败
     */
    private Integer settlementStatus;

    /**
     * 结算时间
     */
    private LocalDateTime settlementTime;

    /**
     * 币种
     */
    private String currency;

    /**
     * 杠杆倍数
     */
    private Integer leverage;

    /**
     * 保证金模式：1-逐仓，2-全仓
     */
    private Integer marginMode;

    /**
     * 持仓成本
     */
    private BigDecimal positionCost;

    /**
     * 平均开仓价格
     */
    private BigDecimal avgOpenPrice;

    /**
     * 资金费率周期
     */
    private Integer fundingInterval;

    /**
     * 年化资金费率
     */
    private BigDecimal annualizedRate;

    /**
     * 累计资金费用
     */
    private BigDecimal cumulativeFundingFee;

    /**
     * 交易哈希
     */
    private String transactionHash;

    /**
     * 区块高度
     */
    private Long blockHeight;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}