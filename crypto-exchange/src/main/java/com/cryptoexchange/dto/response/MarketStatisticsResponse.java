package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场统计响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场统计响应")
public class MarketStatisticsResponse {

    @Schema(description = "统计时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsTime;

    @Schema(description = "统计周期")
    private String statisticsPeriod;

    @Schema(description = "总市值")
    private BigDecimal totalMarketCap;

    @Schema(description = "总市值变化")
    private BigDecimal totalMarketCapChange;

    @Schema(description = "总市值变化百分比")
    private BigDecimal totalMarketCapChangePercent;

    @Schema(description = "24小时总成交量")
    private BigDecimal totalVolume24h;

    @Schema(description = "24小时总成交额")
    private BigDecimal totalAmount24h;

    @Schema(description = "24小时成交量变化")
    private BigDecimal volumeChange24h;

    @Schema(description = "24小时成交量变化百分比")
    private BigDecimal volumeChangePercent24h;

    @Schema(description = "活跃交易对数量")
    private Integer activeTradingPairs;

    @Schema(description = "总交易对数量")
    private Integer totalTradingPairs;

    @Schema(description = "新增交易对数量")
    private Integer newTradingPairs;

    @Schema(description = "暂停交易对数量")
    private Integer suspendedTradingPairs;

    @Schema(description = "比特币市值占比")
    private BigDecimal bitcoinDominance;

    @Schema(description = "以太坊市值占比")
    private BigDecimal ethereumDominance;

    @Schema(description = "稳定币市值占比")
    private BigDecimal stablecoinDominance;

    @Schema(description = "DeFi代币市值占比")
    private BigDecimal defiDominance;

    @Schema(description = "市场恐慌贪婪指数")
    private BigDecimal fearGreedIndex;

    @Schema(description = "市场情绪")
    private String marketSentiment;

    @Schema(description = "市场趋势")
    private String marketTrend;

    @Schema(description = "涨跌分布")
    private PriceChangeDistribution priceChangeDistribution;

    @Schema(description = "成交量分布")
    private VolumeDistribution volumeDistribution;

    @Schema(description = "市值分布")
    private MarketCapDistribution marketCapDistribution;

    @Schema(description = "热门交易对")
    private List<HotTradingPair> hotTradingPairs;

    @Schema(description = "涨幅榜")
    private List<TopGainer> topGainers;

    @Schema(description = "跌幅榜")
    private List<TopLoser> topLosers;

    @Schema(description = "成交量榜")
    private List<TopVolume> topVolumes;

    @Schema(description = "新币榜")
    private List<NewListing> newListings;

    @Schema(description = "分类统计")
    private Map<String, CategoryStatistics> categoryStatistics;

    @Schema(description = "地区统计")
    private Map<String, RegionStatistics> regionStatistics;

    @Schema(description = "用户统计")
    private UserStatistics userStatistics;

    @Schema(description = "交易统计")
    private TradingStatistics tradingStatistics;

    @Schema(description = "流动性统计")
    private LiquidityStatistics liquidityStatistics;

    @Schema(description = "风险统计")
    private RiskStatistics riskStatistics;

    @Schema(description = "技术指标")
    private TechnicalIndicators technicalIndicators;

    @Schema(description = "历史对比")
    private HistoricalComparison historicalComparison;

    @Schema(description = "预测指标")
    private PredictionMetrics predictionMetrics;

    @Schema(description = "数据质量")
    private DataQuality dataQuality;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 涨跌分布
     */
    @Data
    @Schema(description = "涨跌分布")
    public static class PriceChangeDistribution {
        
        @Schema(description = "上涨数量")
        private Integer risingCount;
        
        @Schema(description = "下跌数量")
        private Integer fallingCount;
        
        @Schema(description = "持平数量")
        private Integer unchangedCount;
        
        @Schema(description = "上涨比例")
        private BigDecimal risingRatio;
        
        @Schema(description = "下跌比例")
        private BigDecimal fallingRatio;
        
        @Schema(description = "涨幅超过5%数量")
        private Integer rising5PercentCount;
        
        @Schema(description = "涨幅超过10%数量")
        private Integer rising10PercentCount;
        
        @Schema(description = "跌幅超过5%数量")
        private Integer falling5PercentCount;
        
        @Schema(description = "跌幅超过10%数量")
        private Integer falling10PercentCount;
        
        @Schema(description = "平均涨跌幅")
        private BigDecimal averagePriceChange;
        
        @Schema(description = "中位数涨跌幅")
        private BigDecimal medianPriceChange;
    }

    /**
     * 成交量分布
     */
    @Data
    @Schema(description = "成交量分布")
    public static class VolumeDistribution {
        
        @Schema(description = "高成交量数量")
        private Integer highVolumeCount;
        
        @Schema(description = "中成交量数量")
        private Integer mediumVolumeCount;
        
        @Schema(description = "低成交量数量")
        private Integer lowVolumeCount;
        
        @Schema(description = "零成交量数量")
        private Integer zeroVolumeCount;
        
        @Schema(description = "平均成交量")
        private BigDecimal averageVolume;
        
        @Schema(description = "中位数成交量")
        private BigDecimal medianVolume;
        
        @Schema(description = "成交量集中度")
        private BigDecimal volumeConcentration;
        
        @Schema(description = "成交量分布熵")
        private BigDecimal volumeEntropy;
    }

    /**
     * 市值分布
     */
    @Data
    @Schema(description = "市值分布")
    public static class MarketCapDistribution {
        
        @Schema(description = "大盘股数量")
        private Integer largeCapsCount;
        
        @Schema(description = "中盘股数量")
        private Integer midCapsCount;
        
        @Schema(description = "小盘股数量")
        private Integer smallCapsCount;
        
        @Schema(description = "微盘股数量")
        private Integer microCapsCount;
        
        @Schema(description = "大盘股市值占比")
        private BigDecimal largeCapsDominance;
        
        @Schema(description = "中盘股市值占比")
        private BigDecimal midCapsDominance;
        
        @Schema(description = "小盘股市值占比")
        private BigDecimal smallCapsDominance;
        
        @Schema(description = "市值集中度")
        private BigDecimal marketCapConcentration;
    }

    /**
     * 热门交易对
     */
    @Data
    @Schema(description = "热门交易对")
    public static class HotTradingPair {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时价格变化百分比")
        private BigDecimal priceChangePercent24h;
        
        @Schema(description = "热度评分")
        private BigDecimal hotScore;
        
        @Schema(description = "排名")
        private Integer rank;
    }

    /**
     * 涨幅榜
     */
    @Data
    @Schema(description = "涨幅榜")
    public static class TopGainer {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时价格变化")
        private BigDecimal priceChange24h;
        
        @Schema(description = "24小时价格变化百分比")
        private BigDecimal priceChangePercent24h;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "排名")
        private Integer rank;
    }

    /**
     * 跌幅榜
     */
    @Data
    @Schema(description = "跌幅榜")
    public static class TopLoser {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时价格变化")
        private BigDecimal priceChange24h;
        
        @Schema(description = "24小时价格变化百分比")
        private BigDecimal priceChangePercent24h;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "排名")
        private Integer rank;
    }

    /**
     * 成交量榜
     */
    @Data
    @Schema(description = "成交量榜")
    public static class TopVolume {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount24h;
        
        @Schema(description = "24小时价格变化百分比")
        private BigDecimal priceChangePercent24h;
        
        @Schema(description = "排名")
        private Integer rank;
    }

    /**
     * 新币榜
     */
    @Data
    @Schema(description = "新币榜")
    public static class NewListing {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "上线时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime listingTime;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "上线以来价格变化百分比")
        private BigDecimal priceChangeSinceListing;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "关注度")
        private BigDecimal attention;
    }

    /**
     * 分类统计
     */
    @Data
    @Schema(description = "分类统计")
    public static class CategoryStatistics {
        
        @Schema(description = "分类名称")
        private String categoryName;
        
        @Schema(description = "交易对数量")
        private Integer tradingPairCount;
        
        @Schema(description = "总市值")
        private BigDecimal totalMarketCap;
        
        @Schema(description = "24小时总成交量")
        private BigDecimal totalVolume24h;
        
        @Schema(description = "平均价格变化百分比")
        private BigDecimal averagePriceChangePercent;
        
        @Schema(description = "市值占比")
        private BigDecimal marketCapRatio;
        
        @Schema(description = "成交量占比")
        private BigDecimal volumeRatio;
    }

    /**
     * 地区统计
     */
    @Data
    @Schema(description = "地区统计")
    public static class RegionStatistics {
        
        @Schema(description = "地区名称")
        private String regionName;
        
        @Schema(description = "活跃用户数")
        private Integer activeUsers;
        
        @Schema(description = "24小时交易量")
        private BigDecimal tradingVolume24h;
        
        @Schema(description = "交易量占比")
        private BigDecimal volumeRatio;
        
        @Schema(description = "平均交易金额")
        private BigDecimal averageTradeAmount;
        
        @Schema(description = "偏好交易对")
        private List<String> preferredTradingPairs;
    }

    /**
     * 用户统计
     */
    @Data
    @Schema(description = "用户统计")
    public static class UserStatistics {
        
        @Schema(description = "总注册用户数")
        private Long totalRegisteredUsers;
        
        @Schema(description = "24小时活跃用户数")
        private Integer activeUsers24h;
        
        @Schema(description = "7天活跃用户数")
        private Integer activeUsers7d;
        
        @Schema(description = "30天活跃用户数")
        private Integer activeUsers30d;
        
        @Schema(description = "新注册用户数")
        private Integer newRegistrations;
        
        @Schema(description = "用户留存率")
        private BigDecimal userRetentionRate;
        
        @Schema(description = "平均用户交易频次")
        private BigDecimal averageUserTradingFrequency;
    }

    /**
     * 交易统计
     */
    @Data
    @Schema(description = "交易统计")
    public static class TradingStatistics {
        
        @Schema(description = "24小时总交易笔数")
        private Long totalTrades24h;
        
        @Schema(description = "平均交易金额")
        private BigDecimal averageTradeAmount;
        
        @Schema(description = "大额交易笔数")
        private Integer largeTrades;
        
        @Schema(description = "小额交易笔数")
        private Integer smallTrades;
        
        @Schema(description = "买单占比")
        private BigDecimal buyOrderRatio;
        
        @Schema(description = "卖单占比")
        private BigDecimal sellOrderRatio;
        
        @Schema(description = "市价单占比")
        private BigDecimal marketOrderRatio;
        
        @Schema(description = "限价单占比")
        private BigDecimal limitOrderRatio;
    }

    /**
     * 流动性统计
     */
    @Data
    @Schema(description = "流动性统计")
    public static class LiquidityStatistics {
        
        @Schema(description = "平均流动性评分")
        private BigDecimal averageLiquidityScore;
        
        @Schema(description = "高流动性交易对数量")
        private Integer highLiquidityPairs;
        
        @Schema(description = "低流动性交易对数量")
        private Integer lowLiquidityPairs;
        
        @Schema(description = "平均买卖价差")
        private BigDecimal averageSpread;
        
        @Schema(description = "平均订单簿深度")
        private BigDecimal averageOrderBookDepth;
        
        @Schema(description = "流动性提供者数量")
        private Integer liquidityProviders;
    }

    /**
     * 风险统计
     */
    @Data
    @Schema(description = "风险统计")
    public static class RiskStatistics {
        
        @Schema(description = "高风险交易对数量")
        private Integer highRiskPairs;
        
        @Schema(description = "中风险交易对数量")
        private Integer mediumRiskPairs;
        
        @Schema(description = "低风险交易对数量")
        private Integer lowRiskPairs;
        
        @Schema(description = "平均风险评分")
        private BigDecimal averageRiskScore;
        
        @Schema(description = "平均波动率")
        private BigDecimal averageVolatility;
        
        @Schema(description = "异常交易检测数量")
        private Integer anomalousTradeDetections;
    }

    /**
     * 技术指标
     */
    @Data
    @Schema(description = "技术指标")
    public static class TechnicalIndicators {
        
        @Schema(description = "市场RSI")
        private BigDecimal marketRSI;
        
        @Schema(description = "市场MACD")
        private BigDecimal marketMACD;
        
        @Schema(description = "市场布林带位置")
        private BigDecimal marketBollingerPosition;
        
        @Schema(description = "超买交易对数量")
        private Integer overboughtPairs;
        
        @Schema(description = "超卖交易对数量")
        private Integer oversoldPairs;
        
        @Schema(description = "趋势向上交易对数量")
        private Integer upTrendPairs;
        
        @Schema(description = "趋势向下交易对数量")
        private Integer downTrendPairs;
    }

    /**
     * 历史对比
     */
    @Data
    @Schema(description = "历史对比")
    public static class HistoricalComparison {
        
        @Schema(description = "与昨日对比")
        private PeriodComparison yesterdayComparison;
        
        @Schema(description = "与上周对比")
        private PeriodComparison lastWeekComparison;
        
        @Schema(description = "与上月对比")
        private PeriodComparison lastMonthComparison;
        
        @Schema(description = "与去年同期对比")
        private PeriodComparison lastYearComparison;
    }

    /**
     * 周期对比
     */
    @Data
    @Schema(description = "周期对比")
    public static class PeriodComparison {
        
        @Schema(description = "市值变化")
        private BigDecimal marketCapChange;
        
        @Schema(description = "成交量变化")
        private BigDecimal volumeChange;
        
        @Schema(description = "活跃用户变化")
        private BigDecimal activeUsersChange;
        
        @Schema(description = "交易笔数变化")
        private BigDecimal tradesChange;
    }

    /**
     * 预测指标
     */
    @Data
    @Schema(description = "预测指标")
    public static class PredictionMetrics {
        
        @Schema(description = "市场趋势预测")
        private String marketTrendPrediction;
        
        @Schema(description = "预测置信度")
        private BigDecimal predictionConfidence;
        
        @Schema(description = "预测时间范围")
        private String predictionTimeframe;
        
        @Schema(description = "关键支撑位")
        private BigDecimal keySupportLevel;
        
        @Schema(description = "关键阻力位")
        private BigDecimal keyResistanceLevel;
    }

    /**
     * 数据质量
     */
    @Data
    @Schema(description = "数据质量")
    public static class DataQuality {
        
        @Schema(description = "数据完整性")
        private BigDecimal dataCompleteness;
        
        @Schema(description = "数据准确性")
        private BigDecimal dataAccuracy;
        
        @Schema(description = "数据及时性")
        private BigDecimal dataTimeliness;
        
        @Schema(description = "数据一致性")
        private BigDecimal dataConsistency;
        
        @Schema(description = "质量评分")
        private BigDecimal qualityScore;
        
        @Schema(description = "数据异常数量")
        private Integer dataAnomalies;
    }
}