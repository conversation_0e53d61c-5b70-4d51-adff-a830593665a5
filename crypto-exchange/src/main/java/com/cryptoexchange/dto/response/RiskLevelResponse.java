package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 风险等级响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RiskLevelResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总体风险等级
     */
    private String overallRiskLevel;
    
    /**
     * 风险评分
     */
    private BigDecimal riskScore;
    
    /**
     * 总权益
     */
    private BigDecimal totalEquity;
    
    /**
     * 总保证金
     */
    private BigDecimal totalMargin;
    
    /**
     * 总未实现盈亏
     */
    private BigDecimal totalUnrealizedPnl;
    
    /**
     * 保证金率
     */
    private BigDecimal marginRatio;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRatio;
    
    /**
     * 风险提醒阈值
     */
    private BigDecimal riskWarningThreshold;
    
    /**
     * 强平风险阈值
     */
    private BigDecimal liquidationRiskThreshold;
    
    /**
     * 是否接近强平
     */
    private Boolean nearLiquidation;
    
    /**
     * 各持仓风险详情
     */
    private List<PositionRisk> positionRisks;
    
    /**
     * 风险建议
     */
    private List<String> riskSuggestions;
    
    /**
     * 评估时间
     */
    private LocalDateTime assessmentTime;
    
    /**
     * 持仓风险详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PositionRisk {
        /**
         * 合约符号
         */
        private String symbol;
        
        /**
         * 持仓方向
         */
        private String positionSide;
        
        /**
         * 风险等级
         */
        private String riskLevel;
        
        /**
         * 风险评分
         */
        private BigDecimal riskScore;
        
        /**
         * 保证金率
         */
        private BigDecimal marginRatio;
        
        /**
         * 距离强平价格百分比
         */
        private BigDecimal liquidationPercentage;
        
        /**
         * 未实现盈亏
         */
        private BigDecimal unrealizedPnl;
    }
}