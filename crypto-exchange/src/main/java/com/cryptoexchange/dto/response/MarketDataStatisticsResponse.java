package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场数据统计响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketDataStatisticsResponse {
    
    /**
     * 统计时间范围
     */
    private String timeRange;
    
    /**
     * 统计开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 数据生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime generatedTime;
    
    /**
     * 市场概览统计
     */
    private MarketOverviewStats marketOverview;
    
    /**
     * 交易统计
     */
    private TradingStatistics tradingStats;
    
    /**
     * 价格统计
     */
    private PriceStatistics priceStats;
    
    /**
     * 成交量统计
     */
    private VolumeStatistics volumeStats;
    
    /**
     * 波动率统计
     */
    private VolatilityStatistics volatilityStats;
    
    /**
     * 流动性统计
     */
    private LiquidityStatistics liquidityStats;
    
    /**
     * 用户活动统计
     */
    private UserActivityStatistics userActivityStats;
    
    /**
     * 订单簿统计
     */
    private OrderBookStatistics orderBookStats;
    
    /**
     * 交易对统计
     */
    private List<SymbolStatistics> symbolStats;
    
    /**
     * 时间序列统计
     */
    private TimeSeriesStatistics timeSeriesStats;
    
    /**
     * 异常事件统计
     */
    private AnomalyStatistics anomalyStats;
    
    /**
     * 性能统计
     */
    private PerformanceStatistics performanceStats;
    
    /**
     * 数据质量统计
     */
    private DataQualityStatistics dataQualityStats;
    
    /**
     * 市场概览统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketOverviewStats {
        /**
         * 总市值
         */
        private BigDecimal totalMarketCap;
        
        /**
         * 24小时总成交量
         */
        private BigDecimal totalVolume24h;
        
        /**
         * 活跃交易对数量
         */
        private Integer activeSymbolsCount;
        
        /**
         * 上涨交易对数量
         */
        private Integer gainersCount;
        
        /**
         * 下跌交易对数量
         */
        private Integer losersCount;
        
        /**
         * 平盘交易对数量
         */
        private Integer unchangedCount;
        
        /**
         * 平均价格变化
         */
        private BigDecimal avgPriceChange;
        
        /**
         * 市场主导币种
         */
        private String dominantSymbol;
        
        /**
         * 市场情绪指数
         */
        private BigDecimal sentimentIndex;
        
        /**
         * 市场活跃度
         */
        private BigDecimal marketActivity;
    }
    
    /**
     * 交易统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingStatistics {
        /**
         * 总交易笔数
         */
        private Long totalTrades;
        
        /**
         * 买单数量
         */
        private Long buyOrdersCount;
        
        /**
         * 卖单数量
         */
        private Long sellOrdersCount;
        
        /**
         * 市价单数量
         */
        private Long marketOrdersCount;
        
        /**
         * 限价单数量
         */
        private Long limitOrdersCount;
        
        /**
         * 平均交易金额
         */
        private BigDecimal avgTradeAmount;
        
        /**
         * 最大单笔交易金额
         */
        private BigDecimal maxTradeAmount;
        
        /**
         * 最小单笔交易金额
         */
        private BigDecimal minTradeAmount;
        
        /**
         * 交易频率（笔/分钟）
         */
        private BigDecimal tradeFrequency;
        
        /**
         * 成交率
         */
        private BigDecimal fillRate;
        
        /**
         * 撤单率
         */
        private BigDecimal cancelRate;
    }
    
    /**
     * 价格统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceStatistics {
        /**
         * 加权平均价格
         */
        private BigDecimal weightedAvgPrice;
        
        /**
         * 最高价
         */
        private BigDecimal highPrice;
        
        /**
         * 最低价
         */
        private BigDecimal lowPrice;
        
        /**
         * 开盘价
         */
        private BigDecimal openPrice;
        
        /**
         * 收盘价
         */
        private BigDecimal closePrice;
        
        /**
         * 价格变化幅度
         */
        private BigDecimal priceRange;
        
        /**
         * 价格标准差
         */
        private BigDecimal priceStdDev;
        
        /**
         * 价格偏度
         */
        private BigDecimal priceSkewness;
        
        /**
         * 价格峰度
         */
        private BigDecimal priceKurtosis;
        
        /**
         * 价格分布
         */
        private Map<String, Integer> priceDistribution;
    }
    
    /**
     * 成交量统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeStatistics {
        /**
         * 总成交量
         */
        private BigDecimal totalVolume;
        
        /**
         * 买方成交量
         */
        private BigDecimal buyVolume;
        
        /**
         * 卖方成交量
         */
        private BigDecimal sellVolume;
        
        /**
         * 平均成交量
         */
        private BigDecimal avgVolume;
        
        /**
         * 最大成交量
         */
        private BigDecimal maxVolume;
        
        /**
         * 最小成交量
         */
        private BigDecimal minVolume;
        
        /**
         * 成交量标准差
         */
        private BigDecimal volumeStdDev;
        
        /**
         * 成交量加权平均价格
         */
        private BigDecimal vwap;
        
        /**
         * 成交量分布
         */
        private VolumeDistribution volumeDistribution;
        
        /**
         * 大单成交量占比
         */
        private BigDecimal largeTradeVolumeRatio;
    }
    
    /**
     * 成交量分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeDistribution {
        private BigDecimal smallOrdersVolume;
        private BigDecimal mediumOrdersVolume;
        private BigDecimal largeOrdersVolume;
        private BigDecimal smallOrdersRatio;
        private BigDecimal mediumOrdersRatio;
        private BigDecimal largeOrdersRatio;
    }
    
    /**
     * 波动率统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityStatistics {
        /**
         * 历史波动率
         */
        private BigDecimal historicalVolatility;
        
        /**
         * 实现波动率
         */
        private BigDecimal realizedVolatility;
        
        /**
         * 日内波动率
         */
        private BigDecimal intradayVolatility;
        
        /**
         * 波动率分位数
         */
        private Map<String, BigDecimal> volatilityPercentiles;
        
        /**
         * 波动率趋势
         */
        private String volatilityTrend;
        
        /**
         * 波动率聚类
         */
        private Boolean volatilityClustering;
        
        /**
         * 极端波动事件数量
         */
        private Integer extremeVolatilityEvents;
    }
    
    /**
     * 流动性统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityStatistics {
        /**
         * 平均买卖价差
         */
        private BigDecimal avgSpread;
        
        /**
         * 最大买卖价差
         */
        private BigDecimal maxSpread;
        
        /**
         * 最小买卖价差
         */
        private BigDecimal minSpread;
        
        /**
         * 订单簿深度
         */
        private BigDecimal orderBookDepth;
        
        /**
         * 市场冲击成本
         */
        private BigDecimal marketImpactCost;
        
        /**
         * 流动性得分
         */
        private BigDecimal liquidityScore;
        
        /**
         * 流动性提供者数量
         */
        private Integer liquidityProvidersCount;
        
        /**
         * 流动性集中度
         */
        private BigDecimal liquidityConcentration;
    }
    
    /**
     * 用户活动统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserActivityStatistics {
        /**
         * 活跃用户数量
         */
        private Integer activeUsersCount;
        
        /**
         * 新用户数量
         */
        private Integer newUsersCount;
        
        /**
         * 交易用户数量
         */
        private Integer tradingUsersCount;
        
        /**
         * 平均用户交易次数
         */
        private BigDecimal avgTradesPerUser;
        
        /**
         * 平均用户交易金额
         */
        private BigDecimal avgVolumePerUser;
        
        /**
         * 用户留存率
         */
        private BigDecimal userRetentionRate;
        
        /**
         * 用户活跃度分布
         */
        private Map<String, Integer> userActivityDistribution;
    }
    
    /**
     * 订单簿统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderBookStatistics {
        /**
         * 平均订单簿深度
         */
        private BigDecimal avgOrderBookDepth;
        
        /**
         * 买单总量
         */
        private BigDecimal totalBidVolume;
        
        /**
         * 卖单总量
         */
        private BigDecimal totalAskVolume;
        
        /**
         * 订单簿不平衡度
         */
        private BigDecimal orderBookImbalance;
        
        /**
         * 平均订单大小
         */
        private BigDecimal avgOrderSize;
        
        /**
         * 大单占比
         */
        private BigDecimal largeOrderRatio;
        
        /**
         * 订单更新频率
         */
        private BigDecimal orderUpdateFrequency;
    }
    
    /**
     * 交易对统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SymbolStatistics {
        /**
         * 交易对符号
         */
        private String symbol;
        
        /**
         * 交易量
         */
        private BigDecimal volume;
        
        /**
         * 交易笔数
         */
        private Long tradeCount;
        
        /**
         * 价格变化
         */
        private BigDecimal priceChange;
        
        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;
        
        /**
         * 波动率
         */
        private BigDecimal volatility;
        
        /**
         * 流动性得分
         */
        private BigDecimal liquidityScore;
        
        /**
         * 市场份额
         */
        private BigDecimal marketShare;
        
        /**
         * 排名
         */
        private Integer rank;
    }
    
    /**
     * 时间序列统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeSeriesStatistics {
        /**
         * 小时级统计
         */
        private List<HourlyStats> hourlyStats;
        
        /**
         * 日级统计
         */
        private List<DailyStats> dailyStats;
        
        /**
         * 周级统计
         */
        private List<WeeklyStats> weeklyStats;
        
        /**
         * 月级统计
         */
        private List<MonthlyStats> monthlyStats;
        
        /**
         * 趋势分析
         */
        private TrendAnalysis trendAnalysis;
        
        /**
         * 季节性分析
         */
        private SeasonalityAnalysis seasonalityAnalysis;
    }
    
    /**
     * 小时级统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HourlyStats {
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime hour;
        private BigDecimal volume;
        private Long tradeCount;
        private BigDecimal avgPrice;
        private BigDecimal volatility;
    }
    
    /**
     * 日级统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyStats {
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime date;
        private BigDecimal volume;
        private Long tradeCount;
        private BigDecimal high;
        private BigDecimal low;
        private BigDecimal open;
        private BigDecimal close;
    }
    
    /**
     * 周级统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeeklyStats {
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime weekStart;
        private BigDecimal volume;
        private Long tradeCount;
        private BigDecimal priceChange;
        private BigDecimal volatility;
    }
    
    /**
     * 月级统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonthlyStats {
        @JsonFormat(pattern = "yyyy-MM")
        private LocalDateTime month;
        private BigDecimal volume;
        private Long tradeCount;
        private BigDecimal priceChange;
        private BigDecimal volatility;
    }
    
    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        private String overallTrend;
        private BigDecimal trendStrength;
        private String volumeTrend;
        private String volatilityTrend;
        private List<String> trendChangePoints;
    }
    
    /**
     * 季节性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SeasonalityAnalysis {
        private Map<String, BigDecimal> hourlyPattern;
        private Map<String, BigDecimal> dailyPattern;
        private Map<String, BigDecimal> weeklyPattern;
        private Map<String, BigDecimal> monthlyPattern;
        private Boolean hasSeasonality;
        private String seasonalityStrength;
    }
    
    /**
     * 异常事件统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyStatistics {
        /**
         * 异常事件总数
         */
        private Integer totalAnomalies;
        
        /**
         * 价格异常数量
         */
        private Integer priceAnomalies;
        
        /**
         * 成交量异常数量
         */
        private Integer volumeAnomalies;
        
        /**
         * 流动性异常数量
         */
        private Integer liquidityAnomalies;
        
        /**
         * 异常事件详情
         */
        private List<AnomalyEvent> anomalyEvents;
        
        /**
         * 异常检测准确率
         */
        private BigDecimal detectionAccuracy;
    }
    
    /**
     * 异常事件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyEvent {
        private String eventType;
        private String severity;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        private String description;
        private BigDecimal impact;
        private String symbol;
    }
    
    /**
     * 性能统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceStatistics {
        /**
         * 平均响应时间
         */
        private BigDecimal avgResponseTime;
        
        /**
         * 最大响应时间
         */
        private BigDecimal maxResponseTime;
        
        /**
         * 最小响应时间
         */
        private BigDecimal minResponseTime;
        
        /**
         * 吞吐量
         */
        private BigDecimal throughput;
        
        /**
         * 错误率
         */
        private BigDecimal errorRate;
        
        /**
         * 系统可用性
         */
        private BigDecimal availability;
        
        /**
         * 资源使用率
         */
        private Map<String, BigDecimal> resourceUtilization;
    }
    
    /**
     * 数据质量统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataQualityStatistics {
        /**
         * 数据完整性
         */
        private BigDecimal dataCompleteness;
        
        /**
         * 数据准确性
         */
        private BigDecimal dataAccuracy;
        
        /**
         * 数据一致性
         */
        private BigDecimal dataConsistency;
        
        /**
         * 数据及时性
         */
        private BigDecimal dataTimeliness;
        
        /**
         * 数据质量得分
         */
        private BigDecimal qualityScore;
        
        /**
         * 数据质量问题
         */
        private List<DataQualityIssue> qualityIssues;
    }
    
    /**
     * 数据质量问题
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataQualityIssue {
        private String issueType;
        private String severity;
        private String description;
        private Integer occurrenceCount;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime firstOccurrence;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastOccurrence;
    }
}