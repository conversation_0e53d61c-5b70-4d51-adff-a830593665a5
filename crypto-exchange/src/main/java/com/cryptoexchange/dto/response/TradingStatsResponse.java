package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易统计响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易统计响应")
public class TradingStatsResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "统计周期")
    private String period;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "总成交笔数")
    private Long totalTrades;

    @Schema(description = "总成交量")
    private BigDecimal totalVolume;

    @Schema(description = "总成交额")
    private BigDecimal totalAmount;

    @Schema(description = "买单成交量")
    private BigDecimal buyVolume;

    @Schema(description = "卖单成交量")
    private BigDecimal sellVolume;

    @Schema(description = "买单成交额")
    private BigDecimal buyAmount;

    @Schema(description = "卖单成交额")
    private BigDecimal sellAmount;

    @Schema(description = "买卖比例")
    private BigDecimal buySellRatio;

    @Schema(description = "平均成交价")
    private BigDecimal avgPrice;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "价格变化")
    private BigDecimal priceChange;

    @Schema(description = "价格变化百分比")
    private BigDecimal priceChangePercent;

    @Schema(description = "成交量加权平均价格")
    private BigDecimal vwap;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "振幅")
    private BigDecimal amplitude;

    @Schema(description = "换手率")
    private BigDecimal turnoverRate;

    @Schema(description = "活跃度评分")
    private BigDecimal activityScore;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "大单交易统计")
    private LargeOrderStats largeOrderStats;

    @Schema(description = "时段交易统计")
    private List<HourlyStats> hourlyStats;

    @Schema(description = "价格分布统计")
    private PriceDistribution priceDistribution;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 大单交易统计
     */
    @Data
    @Schema(description = "大单交易统计")
    public static class LargeOrderStats {
        
        @Schema(description = "大单阈值")
        private BigDecimal threshold;
        
        @Schema(description = "大单笔数")
        private Long largeOrderCount;
        
        @Schema(description = "大单成交量")
        private BigDecimal largeOrderVolume;
        
        @Schema(description = "大单成交额")
        private BigDecimal largeOrderAmount;
        
        @Schema(description = "大单占比")
        private BigDecimal largeOrderRatio;
        
        @Schema(description = "大买单笔数")
        private Long largeBuyCount;
        
        @Schema(description = "大卖单笔数")
        private Long largeSellCount;
        
        @Schema(description = "大单买卖比")
        private BigDecimal largeBuySellRatio;
    }

    /**
     * 时段统计
     */
    @Data
    @Schema(description = "时段统计")
    public static class HourlyStats {
        
        @Schema(description = "小时")
        private Integer hour;
        
        @Schema(description = "成交笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "成交额")
        private BigDecimal amount;
        
        @Schema(description = "平均价格")
        private BigDecimal avgPrice;
        
        @Schema(description = "活跃度")
        private BigDecimal activity;
    }

    /**
     * 价格分布
     */
    @Data
    @Schema(description = "价格分布")
    public static class PriceDistribution {
        
        @Schema(description = "价格区间数量")
        private Integer intervals;
        
        @Schema(description = "区间大小")
        private BigDecimal intervalSize;
        
        @Schema(description = "分布数据")
        private List<PriceInterval> distribution;
    }

    /**
     * 价格区间
     */
    @Data
    @Schema(description = "价格区间")
    public static class PriceInterval {
        
        @Schema(description = "区间下限")
        private BigDecimal lowerBound;
        
        @Schema(description = "区间上限")
        private BigDecimal upperBound;
        
        @Schema(description = "成交笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }
}