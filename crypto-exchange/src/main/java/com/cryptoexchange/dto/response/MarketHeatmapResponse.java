package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场热力图响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketHeatmapResponse {

    /**
     * 生成时间
     */
    private LocalDateTime generationTime;

    /**
     * 热力图类型
     */
    private HeatmapType heatmapType;

    /**
     * 时间范围
     */
    private String timeRange;

    /**
     * 市场概览
     */
    private MarketOverview marketOverview;

    /**
     * 热力图数据
     */
    private List<HeatmapData> heatmapData;

    /**
     * 板块分析
     */
    private List<SectorAnalysis> sectorAnalyses;

    /**
     * 热点分析
     */
    private HotspotAnalysis hotspotAnalysis;

    /**
     * 冷点分析
     */
    private ColdspotAnalysis coldspotAnalysis;

    /**
     * 相关性分析
     */
    private CorrelationAnalysis correlationAnalysis;

    /**
     * 趋势分析
     */
    private TrendAnalysis trendAnalysis;

    /**
     * 异常检测
     */
    private AnomalyDetection anomalyDetection;

    /**
     * 市场情绪分析
     */
    private MarketSentimentAnalysis marketSentimentAnalysis;

    /**
     * 交易建议
     */
    private List<TradingRecommendation> tradingRecommendations;

    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;

    /**
     * 热力图类型
     */
    public enum HeatmapType {
        PRICE_CHANGE("价格变化", "基于价格变化的热力图"),
        VOLUME_CHANGE("成交量变化", "基于成交量变化的热力图"),
        MARKET_CAP_CHANGE("市值变化", "基于市值变化的热力图"),
        VOLATILITY("波动率", "基于波动率的热力图"),
        TRADING_ACTIVITY("交易活跃度", "基于交易活跃度的热力图"),
        MOMENTUM("动量", "基于动量指标的热力图"),
        LIQUIDITY("流动性", "基于流动性的热力图"),
        SENTIMENT("市场情绪", "基于市场情绪的热力图"),
        RISK_RETURN("风险收益", "基于风险收益比的热力图"),
        COMPREHENSIVE("综合", "综合多个指标的热力图");

        private final String description;
        private final String detail;

        HeatmapType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 市场概览
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketOverview {
        /**
         * 总市值
         */
        private BigDecimal totalMarketCap;

        /**
         * 总成交量
         */
        private BigDecimal totalVolume;

        /**
         * 上涨币种数量
         */
        private Integer risingCoinsCount;

        /**
         * 下跌币种数量
         */
        private Integer fallingCoinsCount;

        /**
         * 平盘币种数量
         */
        private Integer flatCoinsCount;

        /**
         * 市场情绪指数
         */
        private BigDecimal marketSentimentIndex;

        /**
         * 恐慌贪婪指数
         */
        private BigDecimal fearGreedIndex;

        /**
         * 市场主导币种
         */
        private String dominantCoin;

        /**
         * 市场热度等级
         */
        private MarketHeatLevel marketHeatLevel;

        /**
         * 活跃交易对数量
         */
        private Integer activeTradingPairs;
        
        // 手动添加getter方法以解决编译问题
        public MarketHeatLevel getMarketHeatLevel() {
            return marketHeatLevel;
        }

        public Integer getRisingCoinsCount() {
            return risingCoinsCount;
        }

        public Integer getFallingCoinsCount() {
            return fallingCoinsCount;
        }
    }

    /**
     * 市场热度等级
     */
    public enum MarketHeatLevel {
        EXTREMELY_HOT("极热", "市场极度活跃，交易量和波动性都很高"),
        HOT("热", "市场活跃，有较多交易机会"),
        WARM("温", "市场适度活跃，交易相对稳定"),
        COOL("冷", "市场活跃度较低，交易相对清淡"),
        COLD("冷", "市场低迷，交易量和活跃度都很低");

        private final String description;
        private final String detail;

        MarketHeatLevel(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 热力图数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HeatmapData {
        /**
         * 交易对符号
         */
        private String symbol;

        /**
         * 币种名称
         */
        private String coinName;

        /**
         * 当前价格
         */
        private BigDecimal currentPrice;

        /**
         * 价格变化
         */
        private BigDecimal priceChange;

        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;

        /**
         * 成交量
         */
        private BigDecimal volume;

        /**
         * 成交量变化百分比
         */
        private BigDecimal volumeChangePercent;

        /**
         * 市值
         */
        private BigDecimal marketCap;

        /**
         * 市值变化百分比
         */
        private BigDecimal marketCapChangePercent;

        /**
         * 热力值
         */
        private BigDecimal heatValue;

        /**
         * 热力等级
         */
        private HeatLevel heatLevel;

        /**
         * 颜色代码
         */
        private String colorCode;

        /**
         * 板块分类
         */
        private String sector;

        /**
         * 排名
         */
        private Integer ranking;

        /**
         * 交易活跃度
         */
        private BigDecimal tradingActivity;

        /**
         * 波动率
         */
        private BigDecimal volatility;

        /**
         * 流动性指标
         */
        private BigDecimal liquidityIndicator;

        /**
         * 技术指标
         */
        private TechnicalIndicators technicalIndicators;

        public BigDecimal getHeatValue() {
            return heatValue;
        }
    }

    /**
     * 热力等级
     */
    public enum HeatLevel {
        EXTREMELY_HOT("极热", 5, "#FF0000"),
        HOT("热", 4, "#FF6600"),
        WARM("温", 3, "#FFFF00"),
        COOL("冷", 2, "#66FF66"),
        COLD("极冷", 1, "#0066FF");

        private final String description;
        private final int level;
        private final String defaultColor;

        HeatLevel(String description, int level, String defaultColor) {
            this.description = description;
            this.level = level;
            this.defaultColor = defaultColor;
        }

        public String getDescription() {
            return description;
        }

        public int getLevel() {
            return level;
        }

        public String getDefaultColor() {
            return defaultColor;
        }
    }

    /**
     * 技术指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalIndicators {
        /**
         * RSI指标
         */
        private BigDecimal rsi;

        /**
         * MACD指标
         */
        private BigDecimal macd;

        /**
         * 布林带位置
         */
        private BigDecimal bollingerPosition;

        /**
         * 移动平均线趋势
         */
        private String movingAverageTrend;

        /**
         * 支撑位
         */
        private BigDecimal supportLevel;

        /**
         * 阻力位
         */
        private BigDecimal resistanceLevel;

        /**
         * 技术评分
         */
        private BigDecimal technicalScore;
    }

    /**
     * 板块分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SectorAnalysis {
        /**
         * 板块名称
         */
        private String sectorName;

        /**
         * 板块描述
         */
        private String sectorDescription;

        /**
         * 币种数量
         */
        private Integer coinCount;

        /**
         * 总市值
         */
        private BigDecimal totalMarketCap;

        /**
         * 平均价格变化
         */
        private BigDecimal averagePriceChange;

        /**
         * 平均成交量变化
         */
        private BigDecimal averageVolumeChange;

        /**
         * 板块热力值
         */
        private BigDecimal sectorHeatValue;

        /**
         * 板块排名
         */
        private Integer sectorRanking;

        /**
         * 领涨币种
         */
        private List<String> leadingGainers;

        /**
         * 领跌币种
         */
        private List<String> leadingLosers;

        /**
         * 板块趋势
         */
        private String sectorTrend;

        /**
         * 板块强度
         */
        private BigDecimal sectorStrength;

        /**
         * 板块相关性
         */
        private BigDecimal sectorCorrelation;
        
        // 手动添加getter方法以解决编译问题
        public BigDecimal getSectorHeatValue() {
            return sectorHeatValue;
        }
    }

    /**
     * 热点分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotspotAnalysis {
        /**
         * 热点币种
         */
        private List<HotspotCoin> hotspotCoins;

        /**
         * 热点板块
         */
        private List<String> hotspotSectors;

        /**
         * 热点形成原因
         */
        private List<String> hotspotReasons;

        /**
         * 热点持续性预测
         */
        private HotspotPersistenceForecast hotspotPersistenceForecast;

        /**
         * 热点扩散分析
         */
        private HotspotSpreadAnalysis hotspotSpreadAnalysis;
    }

    /**
     * 热点币种
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotspotCoin {
        /**
         * 交易对符号
         */
        private String symbol;

        /**
         * 热点等级
         */
        private HeatLevel hotspotLevel;

        /**
         * 热点强度
         */
        private BigDecimal hotspotIntensity;

        /**
         * 热点持续时间
         */
        private Integer hotspotDuration;

        /**
         * 热点类型
         */
        private String hotspotType;

        /**
         * 关注度
         */
        private BigDecimal attention;

        /**
         * 社交媒体热度
         */
        private BigDecimal socialMediaHeat;
    }

    /**
     * 热点持续性预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotspotPersistenceForecast {
        /**
         * 预测持续时间
         */
        private Integer predictedDuration;

        /**
         * 持续概率
         */
        private BigDecimal persistenceProbability;

        /**
         * 强度变化预测
         */
        private String intensityChangeForecast;

        /**
         * 关键影响因素
         */
        private List<String> keyInfluencingFactors;
    }

    /**
     * 热点扩散分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotspotSpreadAnalysis {
        /**
         * 扩散速度
         */
        private BigDecimal spreadSpeed;

        /**
         * 扩散范围
         */
        private String spreadScope;

        /**
         * 传染效应
         */
        private BigDecimal contagionEffect;

        /**
         * 受影响板块
         */
        private List<String> affectedSectors;

        /**
         * 扩散路径
         */
        private List<String> spreadPath;
    }

    /**
     * 冷点分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColdspotAnalysis {
        /**
         * 冷点币种
         */
        private List<ColdspotCoin> coldspotCoins;

        /**
         * 冷点板块
         */
        private List<String> coldspotSectors;

        /**
         * 冷点形成原因
         */
        private List<String> coldspotReasons;

        /**
         * 复苏可能性分析
         */
        private RecoveryPossibilityAnalysis recoveryPossibilityAnalysis;
    }

    /**
     * 冷点币种
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColdspotCoin {
        /**
         * 交易对符号
         */
        private String symbol;

        /**
         * 冷点等级
         */
        private HeatLevel coldspotLevel;

        /**
         * 冷点强度
         */
        private BigDecimal coldspotIntensity;

        /**
         * 冷点持续时间
         */
        private Integer coldspotDuration;

        /**
         * 流动性状况
         */
        private String liquidityStatus;

        /**
         * 交易活跃度
         */
        private BigDecimal tradingActivity;
    }

    /**
     * 复苏可能性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecoveryPossibilityAnalysis {
        /**
         * 复苏概率
         */
        private BigDecimal recoveryProbability;

        /**
         * 预期复苏时间
         */
        private Integer expectedRecoveryTime;

        /**
         * 复苏驱动因素
         */
        private List<String> recoveryDrivers;

        /**
         * 复苏阻碍因素
         */
        private List<String> recoveryBarriers;
    }

    /**
     * 相关性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationAnalysis {
        /**
         * 整体市场相关性
         */
        private BigDecimal overallMarketCorrelation;

        /**
         * 板块内相关性
         */
        private Map<String, BigDecimal> intraSectoCorrelations;

        /**
         * 板块间相关性
         */
        private Map<String, BigDecimal> interSectorCorrelations;

        /**
         * 高相关性币对
         */
        private List<CorrelationPair> highCorrelationPairs;

        /**
         * 低相关性币对
         */
        private List<CorrelationPair> lowCorrelationPairs;

        /**
         * 相关性变化趋势
         */
        private String correlationTrend;
    }

    /**
     * 相关性币对
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CorrelationPair {
        /**
         * 币种1
         */
        private String coin1;

        /**
         * 币种2
         */
        private String coin2;

        /**
         * 相关系数
         */
        private BigDecimal correlationCoefficient;

        /**
         * 相关性强度
         */
        private String correlationStrength;

        /**
         * 相关性类型
         */
        private String correlationType;
    }

    /**
     * 趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendAnalysis {
        /**
         * 整体市场趋势
         */
        private String overallMarketTrend;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 趋势持续性
         */
        private BigDecimal trendPersistence;

        /**
         * 板块趋势分布
         */
        private Map<String, String> sectorTrendDistribution;

        /**
         * 趋势转折信号
         */
        private List<TrendReversalSignal> trendReversalSignals;

        /**
         * 趋势预测
         */
        private TrendForecast trendForecast;
    }

    /**
     * 趋势转折信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendReversalSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 影响范围
         */
        private String impactScope;

        /**
         * 预期转折时间
         */
        private String expectedReversalTime;

        /**
         * 置信度
         */
        private BigDecimal confidence;
    }

    /**
     * 趋势预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrendForecast {
        /**
         * 短期趋势预测
         */
        private String shortTermTrendForecast;

        /**
         * 中期趋势预测
         */
        private String mediumTermTrendForecast;

        /**
         * 长期趋势预测
         */
        private String longTermTrendForecast;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 关键转折点
         */
        private List<LocalDateTime> keyTurningPoints;
    }

    /**
     * 异常检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyDetection {
        /**
         * 异常币种
         */
        private List<AnomalyCoin> anomalyCoins;

        /**
         * 异常模式
         */
        private List<AnomalyPattern> anomalyPatterns;

        /**
         * 异常严重程度
         */
        private String anomalySeverity;

        /**
         * 异常影响评估
         */
        private AnomalyImpactAssessment anomalyImpactAssessment;
    }

    /**
     * 异常币种
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyCoin {
        /**
         * 交易对符号
         */
        private String symbol;

        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常程度
         */
        private BigDecimal anomalyDegree;

        /**
         * 异常描述
         */
        private String anomalyDescription;

        /**
         * 检测时间
         */
        private LocalDateTime detectionTime;

        /**
         * 可能原因
         */
        private List<String> possibleCauses;
    }

    /**
     * 异常模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyPattern {
        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 影响币种
         */
        private List<String> affectedCoins;

        /**
         * 模式描述
         */
        private String patternDescription;
    }

    /**
     * 异常影响评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyImpactAssessment {
        /**
         * 市场影响程度
         */
        private BigDecimal marketImpactDegree;

        /**
         * 影响持续时间
         */
        private Integer impactDuration;

        /**
         * 传染风险
         */
        private BigDecimal contagionRisk;

        /**
         * 恢复时间预估
         */
        private Integer estimatedRecoveryTime;
    }

    /**
     * 市场情绪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketSentimentAnalysis {
        /**
         * 整体情绪指数
         */
        private BigDecimal overallSentimentIndex;

        /**
         * 情绪分类
         */
        private SentimentClassification sentimentClassification;

        /**
         * 恐慌贪婪指数
         */
        private BigDecimal fearGreedIndex;

        /**
         * 情绪驱动因素
         */
        private List<SentimentDriver> sentimentDrivers;

        /**
         * 情绪变化趋势
         */
        private String sentimentTrend;

        /**
         * 板块情绪分布
         */
        private Map<String, BigDecimal> sectorSentimentDistribution;
    }

    /**
     * 情绪分类
     */
    public enum SentimentClassification {
        EXTREMELY_BULLISH("极度看涨", "市场情绪极度乐观"),
        BULLISH("看涨", "市场情绪乐观"),
        NEUTRAL("中性", "市场情绪中性"),
        BEARISH("看跌", "市场情绪悲观"),
        EXTREMELY_BEARISH("极度看跌", "市场情绪极度悲观");

        private final String description;
        private final String detail;

        SentimentClassification(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 情绪驱动因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SentimentDriver {
        /**
         * 驱动因素名称
         */
        private String driverName;

        /**
         * 影响权重
         */
        private BigDecimal impactWeight;

        /**
         * 影响方向
         */
        private String impactDirection;

        /**
         * 因素强度
         */
        private BigDecimal driverIntensity;

        /**
         * 因素描述
         */
        private String driverDescription;
    }

    /**
     * 交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingRecommendation {
        /**
         * 建议类型
         */
        private RecommendationType recommendationType;

        /**
         * 目标币种
         */
        private String targetCoin;

        /**
         * 建议操作
         */
        private String recommendedAction;

        /**
         * 建议强度
         */
        private BigDecimal recommendationStrength;

        /**
         * 时间框架
         */
        private String timeFrame;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 建议理由
         */
        private List<String> reasons;

        /**
         * 入场价位
         */
        private BigDecimal entryPrice;

        /**
         * 止损价位
         */
        private BigDecimal stopLossPrice;

        /**
         * 止盈价位
         */
        private BigDecimal takeProfitPrice;
    }

    /**
     * 建议类型
     */
    public enum RecommendationType {
        BUY("买入", "建议买入操作"),
        SELL("卖出", "建议卖出操作"),
        HOLD("持有", "建议持有操作"),
        AVOID("避免", "建议避免操作"),
        WATCH("观察", "建议密切观察");

        private final String description;
        private final String detail;

        RecommendationType(String description, String detail) {
            this.description = description;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }

        /**
         * 风险评分
         */
        private BigDecimal riskScore;

        /**
         * 系统性风险
         */
        private BigDecimal systematicRisk;

        /**
         * 非系统性风险
         */
        private BigDecimal unsystematicRisk;

        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;

        /**
         * 波动性风险
         */
        private BigDecimal volatilityRisk;

        /**
         * 集中度风险
         */
        private BigDecimal concentrationRisk;

        /**
         * 风险因素
         */
        private List<RiskFactor> riskFactors;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 风险因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        /**
         * 风险因素名称
         */
        private String riskFactorName;

        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 风险概率
         */
        private BigDecimal riskProbability;

        /**
         * 潜在影响
         */
        private BigDecimal potentialImpact;

        /**
         * 风险描述
         */
        private String riskDescription;

        /**
         * 应对策略
         */
        private List<String> mitigationStrategies;
    }

    /**
     * 获取热力图摘要
     */
    public String getHeatmapSummary() {
        return String.format("类型: %s, 市场热度: %s, 上涨币种: %d, 下跌币种: %d",
            heatmapType != null ? heatmapType.getDescription() : "未知",
            marketOverview != null && marketOverview.getMarketHeatLevel() != null ? 
                marketOverview.getMarketHeatLevel().getDescription() : "未知",
            marketOverview != null && marketOverview.getRisingCoinsCount() != null ? 
                marketOverview.getRisingCoinsCount() : 0,
            marketOverview != null && marketOverview.getFallingCoinsCount() != null ? 
                marketOverview.getFallingCoinsCount() : 0);
    }

    /**
     * 检查是否为热门市场
     */
    public boolean isHotMarket() {
        return marketOverview != null && 
               (marketOverview.getMarketHeatLevel() == MarketHeatLevel.HOT || 
                marketOverview.getMarketHeatLevel() == MarketHeatLevel.EXTREMELY_HOT);
    }

    /**
     * 检查是否为冷门市场
     */
    public boolean isColdMarket() {
        return marketOverview != null && 
               (marketOverview.getMarketHeatLevel() == MarketHeatLevel.COLD || 
                marketOverview.getMarketHeatLevel() == MarketHeatLevel.COOL);
    }

    /**
     * 获取最热币种
     */
    public HeatmapData getHottestCoin() {
        if (heatmapData == null || heatmapData.isEmpty()) {
            return null;
        }
        
        return heatmapData.stream()
            .max((h1, h2) -> {
                if (h1.getHeatValue() == null && h2.getHeatValue() == null) return 0;
                if (h1.getHeatValue() == null) return -1;
                if (h2.getHeatValue() == null) return 1;
                return h1.getHeatValue().compareTo(h2.getHeatValue());
            })
            .orElse(null);
    }

    /**
     * 获取最冷币种
     */
    public HeatmapData getColdestCoin() {
        if (heatmapData == null || heatmapData.isEmpty()) {
            return null;
        }
        
        return heatmapData.stream()
            .min((h1, h2) -> {
                if (h1.getHeatValue() == null && h2.getHeatValue() == null) return 0;
                if (h1.getHeatValue() == null) return 1;
                if (h2.getHeatValue() == null) return -1;
                return h1.getHeatValue().compareTo(h2.getHeatValue());
            })
            .orElse(null);
    }

    /**
     * 获取最佳板块
     */
    public SectorAnalysis getBestSector() {
        if (sectorAnalyses == null || sectorAnalyses.isEmpty()) {
            return null;
        }
        
        return sectorAnalyses.stream()
            .max((s1, s2) -> {
                if (s1.getSectorHeatValue() == null && s2.getSectorHeatValue() == null) return 0;
                if (s1.getSectorHeatValue() == null) return -1;
                if (s2.getSectorHeatValue() == null) return 1;
                return s1.getSectorHeatValue().compareTo(s2.getSectorHeatValue());
            })
            .orElse(null);
    }

    /**
     * 获取最差板块
     */
    public SectorAnalysis getWorstSector() {
        if (sectorAnalyses == null || sectorAnalyses.isEmpty()) {
            return null;
        }
        
        return sectorAnalyses.stream()
            .min((s1, s2) -> {
                if (s1.getSectorHeatValue() == null && s2.getSectorHeatValue() == null) return 0;
                if (s1.getSectorHeatValue() == null) return 1;
                if (s2.getSectorHeatValue() == null) return -1;
                return s1.getSectorHeatValue().compareTo(s2.getSectorHeatValue());
            })
            .orElse(null);
    }

    /**
     * 检查是否有异常
     */
    public boolean hasAnomalies() {
        return anomalyDetection != null && 
               anomalyDetection.getAnomalyCoins() != null &&
               !anomalyDetection.getAnomalyCoins().isEmpty();
    }

    /**
     * 获取市场情绪描述
     */
    public String getMarketSentimentDescription() {
        if (marketSentimentAnalysis == null || 
            marketSentimentAnalysis.getSentimentClassification() == null) {
            return "市场情绪未知";
        }
        return marketSentimentAnalysis.getSentimentClassification().getDescription();
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingRecommendation() {
        if (tradingRecommendations == null || tradingRecommendations.isEmpty()) {
            return "暂无交易建议";
        }
        
        TradingRecommendation topRecommendation = tradingRecommendations.stream()
            .max((r1, r2) -> {
                if (r1.getRecommendationStrength() == null && r2.getRecommendationStrength() == null) return 0;
                if (r1.getRecommendationStrength() == null) return -1;
                if (r2.getRecommendationStrength() == null) return 1;
                return r1.getRecommendationStrength().compareTo(r2.getRecommendationStrength());
            })
            .orElse(tradingRecommendations.get(0));
        
        return String.format("%s %s", 
            topRecommendation.getRecommendedAction(),
            topRecommendation.getTargetCoin());
    }

    /**
     * 获取风险等级
     */
    public String getRiskLevel() {
        if (riskAssessment == null) {
            return "风险等级未评估";
        }
        return riskAssessment.getOverallRiskLevel();
    }

    /**
     * 检查是否需要立即关注
     */
    public boolean requiresImmediateAttention() {
        return hasAnomalies() || 
               (isHotMarket() && marketSentimentAnalysis != null && 
                (marketSentimentAnalysis.getSentimentClassification() == SentimentClassification.EXTREMELY_BULLISH ||
                 marketSentimentAnalysis.getSentimentClassification() == SentimentClassification.EXTREMELY_BEARISH));
    }

    /**
     * 获取热点摘要
     */
    public String getHotspotSummary() {
        if (hotspotAnalysis == null || 
            hotspotAnalysis.getHotspotCoins() == null ||
            hotspotAnalysis.getHotspotCoins().isEmpty()) {
            return "当前无明显热点";
        }
        
        int hotspotCount = hotspotAnalysis.getHotspotCoins().size();
        String topHotspot = hotspotAnalysis.getHotspotCoins().get(0).getSymbol();
        
        return String.format("发现 %d 个热点，主要热点: %s", hotspotCount, topHotspot);
    }

    /**
     * 获取趋势摘要
     */
    public String getTrendSummary() {
        if (trendAnalysis == null) {
            return "趋势分析不可用";
        }
        
        String overallTrend = trendAnalysis.getOverallMarketTrend();
        BigDecimal trendStrength = trendAnalysis.getTrendStrength();
        
        return String.format("整体趋势: %s (强度: %s)",
            overallTrend != null ? overallTrend : "未知",
            trendStrength != null ? trendStrength.toString() : "N/A");
    }

    /**
     * 获取相关性摘要
     */
    public String getCorrelationSummary() {
        if (correlationAnalysis == null) {
            return "相关性分析不可用";
        }
        
        BigDecimal overallCorrelation = correlationAnalysis.getOverallMarketCorrelation();
        String correlationTrend = correlationAnalysis.getCorrelationTrend();
        
        return String.format("市场相关性: %s (趋势: %s)",
            overallCorrelation != null ? overallCorrelation.toString() : "N/A",
            correlationTrend != null ? correlationTrend : "未知");
    }
}