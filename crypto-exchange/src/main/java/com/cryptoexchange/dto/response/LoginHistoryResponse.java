package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 登录历史响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "登录历史响应")
public class LoginHistoryResponse {

    @Schema(description = "登录ID", example = "123456")
    private Long loginId;

    @Schema(description = "登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;

    @Schema(description = "登录IP地址", example = "*************")
    private String ipAddress;

    @Schema(description = "登录地点", example = "北京市朝阳区")
    private String location;

    @Schema(description = "设备类型", example = "PC")
    private String deviceType;

    @Schema(description = "浏览器信息", example = "Chrome 96.0.4664.110")
    private String userAgent;

    @Schema(description = "登录状态", example = "SUCCESS")
    private String loginStatus;

    @Schema(description = "登录方式", example = "PASSWORD")
    private String loginMethod;

    @Schema(description = "会话时长（分钟）", example = "120")
    private Integer sessionDuration;

    @Schema(description = "是否当前会话", example = "false")
    private Boolean isCurrentSession;

    public LoginHistoryResponse() {}

    public LoginHistoryResponse(Long loginId, LocalDateTime loginTime, String ipAddress, 
                              String location, String deviceType, String loginStatus) {
        this.loginId = loginId;
        this.loginTime = loginTime;
        this.ipAddress = ipAddress;
        this.location = location;
        this.deviceType = deviceType;
        this.loginStatus = loginStatus;
    }
}