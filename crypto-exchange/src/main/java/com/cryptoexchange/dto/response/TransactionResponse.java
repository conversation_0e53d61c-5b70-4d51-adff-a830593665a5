package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易记录响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易记录响应")
public class TransactionResponse {

    @Schema(description = "交易记录ID", example = "123456")
    private Long transactionId;

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "币种", example = "BTC")
    private String currency;

    @Schema(description = "交易类型：DEPOSIT-充值，WITHDRAW-提现，TRANSFER_IN-转入，TRANSFER_OUT-转出，TRADE_BUY-买入，TRADE_SELL-卖出，FEE-手续费，REBATE-返佣", example = "DEPOSIT")
    private String type;

    @Schema(description = "交易金额", example = "0.001")
    private BigDecimal amount;

    @Schema(description = "手续费", example = "0.00001")
    private BigDecimal fee;

    @Schema(description = "交易前余额", example = "1.234")
    private BigDecimal balanceBefore;

    @Schema(description = "交易后余额", example = "1.235")
    private BigDecimal balanceAfter;

    @Schema(description = "交易状态：PENDING-待处理，PROCESSING-处理中，SUCCESS-成功，FAILED-失败，CANCELED-已取消", example = "SUCCESS")
    private String status;

    @Schema(description = "交易哈希", example = "0x1234567890abcdef...")
    private String txHash;

    @Schema(description = "区块确认数", example = "6")
    private Integer confirmations;

    @Schema(description = "所需确认数", example = "6")
    private Integer requiredConfirmations;

    @Schema(description = "地址", example = "**********************************")
    private String address;

    @Schema(description = "地址标签", example = "我的钱包")
    private String addressTag;

    @Schema(description = "网络类型", example = "BTC")
    private String network;

    @Schema(description = "备注", example = "交易备注")
    private String memo;

    @Schema(description = "关联订单ID", example = "789012")
    private Long relatedOrderId;

    @Schema(description = "关联交易ID", example = "345678")
    private Long relatedTradeId;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    @Schema(description = "账户类型：SPOT-现货，FUTURES-合约，MARGIN-杠杆", example = "SPOT")
    private String accountType;

    @Schema(description = "业务类型：NORMAL-普通，AIRDROP-空投，STAKING-质押，MINING-挖矿", example = "NORMAL")
    private String businessType;
}