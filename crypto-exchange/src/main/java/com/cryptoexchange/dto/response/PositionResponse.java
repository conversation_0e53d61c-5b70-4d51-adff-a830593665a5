package com.cryptoexchange.dto.response;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 持仓响应
 */
public class PositionResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 持仓方向
     */
    private String side;
    
    /**
     * 持仓数量
     */
    private BigDecimal size;
    
    /**
     * 持仓价值
     */
    private BigDecimal notional;
    
    /**
     * 平均开仓价格
     */
    private BigDecimal avgPrice;
    
    /**
     * 标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedPnl;
    
    /**
     * 已实现盈亏
     */
    private BigDecimal realizedPnl;
    
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    
    /**
     * 保证金
     */
    private BigDecimal margin;
    
    /**
     * 维持保证金
     */
    private BigDecimal maintMargin;
    
    /**
     * 保证金率
     */
    private BigDecimal marginRatio;
    
    /**
     * 强平价格
     */
    private BigDecimal liquidationPrice;
    
    /**
     * 持仓状态
     */
    private String status;
    
    /**
     * 开仓时间
     */
    private LocalDateTime openTime;
    
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    
    public PositionResponse() {}
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public String getSymbol() {
        return symbol;
    }
    
    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }
    
    public String getSide() {
        return side;
    }
    
    public void setSide(String side) {
        this.side = side;
    }
    
    public BigDecimal getSize() {
        return size;
    }
    
    public void setSize(BigDecimal size) {
        this.size = size;
    }
    
    public BigDecimal getNotional() {
        return notional;
    }
    
    public void setNotional(BigDecimal notional) {
        this.notional = notional;
    }
    
    public BigDecimal getAvgPrice() {
        return avgPrice;
    }
    
    public void setAvgPrice(BigDecimal avgPrice) {
        this.avgPrice = avgPrice;
    }
    
    public BigDecimal getMarkPrice() {
        return markPrice;
    }
    
    public void setMarkPrice(BigDecimal markPrice) {
        this.markPrice = markPrice;
    }
    
    public BigDecimal getUnrealizedPnl() {
        return unrealizedPnl;
    }
    
    public void setUnrealizedPnl(BigDecimal unrealizedPnl) {
        this.unrealizedPnl = unrealizedPnl;
    }
    
    public BigDecimal getRealizedPnl() {
        return realizedPnl;
    }
    
    public void setRealizedPnl(BigDecimal realizedPnl) {
        this.realizedPnl = realizedPnl;
    }
    
    public Integer getLeverage() {
        return leverage;
    }
    
    public void setLeverage(Integer leverage) {
        this.leverage = leverage;
    }
    
    public BigDecimal getMargin() {
        return margin;
    }
    
    public void setMargin(BigDecimal margin) {
        this.margin = margin;
    }
    
    public BigDecimal getMaintMargin() {
        return maintMargin;
    }
    
    public void setMaintMargin(BigDecimal maintMargin) {
        this.maintMargin = maintMargin;
    }
    
    public BigDecimal getMarginRatio() {
        return marginRatio;
    }
    
    public void setMarginRatio(BigDecimal marginRatio) {
        this.marginRatio = marginRatio;
    }
    
    public BigDecimal getLiquidationPrice() {
        return liquidationPrice;
    }
    
    public void setLiquidationPrice(BigDecimal liquidationPrice) {
        this.liquidationPrice = liquidationPrice;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public LocalDateTime getOpenTime() {
        return openTime;
    }
    
    public void setOpenTime(LocalDateTime openTime) {
        this.openTime = openTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}