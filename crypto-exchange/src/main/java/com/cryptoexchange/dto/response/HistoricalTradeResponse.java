package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 历史交易响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "历史交易响应")
public class HistoricalTradeResponse {

    @Schema(description = "交易ID")
    private Long tradeId;

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "成交金额")
    private BigDecimal amount;

    @Schema(description = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime tradeTime;

    @Schema(description = "交易时间戳")
    private Long timestamp;

    @Schema(description = "是否为买单成交")
    private Boolean isBuyerMaker;

    @Schema(description = "交易方向")
    private String side;

    @Schema(description = "买单订单ID")
    private String buyOrderId;

    @Schema(description = "卖单订单ID")
    private String sellOrderId;

    @Schema(description = "买方用户ID")
    private Long buyUserId;

    @Schema(description = "卖方用户ID")
    private Long sellUserId;

    @Schema(description = "买方手续费")
    private BigDecimal buyFee;

    @Schema(description = "卖方手续费")
    private BigDecimal sellFee;

    @Schema(description = "买方手续费货币")
    private String buyFeeCurrency;

    @Schema(description = "卖方手续费货币")
    private String sellFeeCurrency;

    @Schema(description = "交易类型")
    private String tradeType;

    @Schema(description = "是否为大单交易")
    private Boolean isLargeOrder;

    @Schema(description = "交易来源")
    private String tradeSource;

    @Schema(description = "市场影响")
    private BigDecimal marketImpact;

    @Schema(description = "流动性标记")
    private String liquidityFlag;

    @Schema(description = "交易序号")
    private Long sequenceNumber;

    @Schema(description = "区块高度")
    private Long blockHeight;

    @Schema(description = "交易哈希")
    private String transactionHash;

    @Schema(description = "确认状态")
    private String confirmationStatus;

    @Schema(description = "确认数")
    private Integer confirmations;

    @Schema(description = "网络费用")
    private BigDecimal networkFee;

    @Schema(description = "滑点")
    private BigDecimal slippage;

    @Schema(description = "执行延迟（毫秒）")
    private Long executionLatency;

    @Schema(description = "价格偏差")
    private BigDecimal priceDeviation;

    @Schema(description = "交易质量评分")
    private BigDecimal qualityScore;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "合规状态")
    private String complianceStatus;

    @Schema(description = "备注")
    private String remarks;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}