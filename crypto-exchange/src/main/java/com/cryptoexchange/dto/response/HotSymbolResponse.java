package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 热门交易对响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class HotSymbolResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 基础货币
     */
    private String baseAsset;
    
    /**
     * 计价货币
     */
    private String quoteAsset;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume24h;
    
    /**
     * 24小时交易额
     */
    private BigDecimal quoteVolume24h;
    
    /**
     * 24小时交易次数
     */
    private Long tradeCount24h;
    
    /**
     * 热度评分
     */
    private BigDecimal hotScore;
    
    /**
     * 热度排名
     */
    private Integer hotRank;
    
    /**
     * 市值
     */
    private BigDecimal marketCap;
    
    /**
     * 市值排名
     */
    private Integer marketCapRank;
    
    /**
     * 流通市值
     */
    private BigDecimal circulatingMarketCap;
    
    /**
     * 换手率
     */
    private BigDecimal turnoverRate;
    
    /**
     * 涨跌幅排名
     */
    private Integer priceChangeRank;
    
    /**
     * 成交量排名
     */
    private Integer volumeRank;
    
    /**
     * 关注度
     */
    private Long watchCount;
    
    /**
     * 是否新币
     */
    private Boolean isNew;
    
    /**
     * 上市时间
     */
    private LocalDateTime listTime;
    
    /**
     * 标签
     */
    private String tags;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}