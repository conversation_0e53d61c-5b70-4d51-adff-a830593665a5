package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货K线响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesKlineResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * K线间隔
     */
    private String interval;
    
    /**
     * K线数据列表
     */
    private List<KlineData> klines;
    
    /**
     * K线类型
     */
    private String klineType;
    
    /**
     * 数据时间
     */
    private LocalDateTime timestamp;
    
    /**
     * K线数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class KlineData {
        /**
         * 开盘时间
         */
        private LocalDateTime openTime;
        
        /**
         * 开盘价
         */
        private BigDecimal openPrice;
        
        /**
         * 最高价
         */
        private BigDecimal highPrice;
        
        /**
         * 最低价
         */
        private BigDecimal lowPrice;
        
        /**
         * 收盘价
         */
        private BigDecimal closePrice;
        
        /**
         * 成交量
         */
        private BigDecimal volume;
        
        /**
         * 收盘时间
         */
        private LocalDateTime closeTime;
        
        /**
         * 成交额
         */
        private BigDecimal quoteVolume;
        
        /**
         * 交易次数
         */
        private Long tradeCount;
        
        /**
         * 主动买入成交量
         */
        private BigDecimal takerBuyVolume;
        
        /**
         * 主动买入成交额
         */
        private BigDecimal takerBuyQuoteVolume;
        
        /**
         * 是否K线结束
         */
        private Boolean isClosed;
        
        /**
         * 标记价格 (标记价格K线时使用)
         */
        private BigDecimal markPrice;
        
        /**
         * 指数价格 (指数价格K线时使用)
         */
        private BigDecimal indexPrice;
        
        /**
         * 溢价指数 (溢价指数K线时使用)
         */
        private BigDecimal premiumIndex;
    }
}