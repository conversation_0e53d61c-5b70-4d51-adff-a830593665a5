package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 交易对配置响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SymbolConfigResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 基础货币
     */
    private String baseCurrency;
    
    /**
     * 计价货币
     */
    private String quoteCurrency;
    
    /**
     * 交易对状态
     */
    private SymbolStatus status;
    
    /**
     * 交易对类型
     */
    private SymbolType symbolType;
    
    /**
     * 配置创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdTime;
    
    /**
     * 配置更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedTime;
    
    /**
     * 交易配置
     */
    private TradingConfig tradingConfig;
    
    /**
     * 价格配置
     */
    private PriceConfig priceConfig;
    
    /**
     * 数量配置
     */
    private QuantityConfig quantityConfig;
    
    /**
     * 费率配置
     */
    private FeeConfig feeConfig;
    
    /**
     * 风险配置
     */
    private RiskConfig riskConfig;
    
    /**
     * 市场数据配置
     */
    private MarketDataConfig marketDataConfig;
    
    /**
     * 显示配置
     */
    private DisplayConfig displayConfig;
    
    /**
     * API配置
     */
    private ApiConfig apiConfig;
    
    /**
     * 合规配置
     */
    private ComplianceConfig complianceConfig;
    
    /**
     * 流动性配置
     */
    private LiquidityConfig liquidityConfig;
    
    /**
     * 监控配置
     */
    private MonitoringConfig monitoringConfig;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extendedProperties;
    
    /**
     * 交易对状态枚举
     */
    public enum SymbolStatus {
        ACTIVE,
        INACTIVE,
        SUSPENDED,
        DELISTED,
        PRE_TRADING,
        POST_TRADING,
        MAINTENANCE
    }
    
    /**
     * 交易对类型枚举
     */
    public enum SymbolType {
        SPOT,
        FUTURES,
        PERPETUAL,
        OPTIONS,
        MARGIN,
        LEVERAGED_TOKEN,
        INDEX
    }
    
    /**
     * 交易配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingConfig {
        /**
         * 是否允许交易
         */
        private Boolean tradingEnabled;
        
        /**
         * 是否允许买入
         */
        private Boolean buyEnabled;
        
        /**
         * 是否允许卖出
         */
        private Boolean sellEnabled;
        
        /**
         * 交易时间段
         */
        private List<TradingSession> tradingSessions;
        
        /**
         * 订单类型限制
         */
        private List<String> allowedOrderTypes;
        
        /**
         * 最大订单数量限制
         */
        private Integer maxOrdersPerUser;
        
        /**
         * 最大持仓限制
         */
        private BigDecimal maxPositionSize;
        
        /**
         * 交易冷却时间（毫秒）
         */
        private Long tradingCooldown;
        
        /**
         * 是否支持止损止盈
         */
        private Boolean stopOrdersEnabled;
        
        /**
         * 是否支持条件订单
         */
        private Boolean conditionalOrdersEnabled;
    }
    
    /**
     * 交易时间段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingSession {
        private String sessionName;
        private String startTime;
        private String endTime;
        private String timezone;
        private List<String> weekdays;
        private Boolean isActive;
    }
    
    /**
     * 价格配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceConfig {
        /**
         * 价格精度（小数位数）
         */
        private Integer pricePrecision;
        
        /**
         * 最小价格变动单位
         */
        private BigDecimal tickSize;
        
        /**
         * 最小价格
         */
        private BigDecimal minPrice;
        
        /**
         * 最大价格
         */
        private BigDecimal maxPrice;
        
        /**
         * 价格过滤器
         */
        private List<PriceFilter> priceFilters;
        
        /**
         * 价格保护机制
         */
        private PriceProtection priceProtection;
        
        /**
         * 参考价格源
         */
        private String referencePriceSource;
    }
    
    /**
     * 价格过滤器
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceFilter {
        private String filterType;
        private BigDecimal minValue;
        private BigDecimal maxValue;
        private BigDecimal stepSize;
        private Boolean isActive;
    }
    
    /**
     * 价格保护机制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceProtection {
        private Boolean enabled;
        private BigDecimal maxPriceDeviation;
        private String referencePrice;
        private Integer protectionDuration;
        private String action;
    }
    
    /**
     * 数量配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuantityConfig {
        /**
         * 数量精度（小数位数）
         */
        private Integer quantityPrecision;
        
        /**
         * 最小数量变动单位
         */
        private BigDecimal stepSize;
        
        /**
         * 最小订单数量
         */
        private BigDecimal minQuantity;
        
        /**
         * 最大订单数量
         */
        private BigDecimal maxQuantity;
        
        /**
         * 最小订单金额
         */
        private BigDecimal minNotional;
        
        /**
         * 最大订单金额
         */
        private BigDecimal maxNotional;
        
        /**
         * 数量过滤器
         */
        private List<QuantityFilter> quantityFilters;
    }
    
    /**
     * 数量过滤器
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QuantityFilter {
        private String filterType;
        private BigDecimal minValue;
        private BigDecimal maxValue;
        private BigDecimal stepSize;
        private Boolean isActive;
    }
    
    /**
     * 费率配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeConfig {
        /**
         * 基础交易费率
         */
        private BigDecimal baseTradingFee;
        
        /**
         * Maker费率
         */
        private BigDecimal makerFee;
        
        /**
         * Taker费率
         */
        private BigDecimal takerFee;
        
        /**
         * 费率类型
         */
        private String feeType;
        
        /**
         * 费率等级
         */
        private List<FeeLevel> feeLevels;
        
        /**
         * 费率折扣
         */
        private List<FeeDiscount> feeDiscounts;
        
        /**
         * 最小费用
         */
        private BigDecimal minFee;
        
        /**
         * 最大费用
         */
        private BigDecimal maxFee;
    }
    
    /**
     * 费率等级
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeLevel {
        private String levelName;
        private BigDecimal volumeThreshold;
        private BigDecimal makerFee;
        private BigDecimal takerFee;
        private String requirements;
    }
    
    /**
     * 费率折扣
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeDiscount {
        private String discountType;
        private BigDecimal discountRate;
        private String condition;
        private LocalDateTime validFrom;
        private LocalDateTime validTo;
    }
    
    /**
     * 风险配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskConfig {
        /**
         * 风险等级
         */
        private String riskLevel;
        
        /**
         * 最大杠杆倍数
         */
        private BigDecimal maxLeverage;
        
        /**
         * 保证金要求
         */
        private BigDecimal marginRequirement;
        
        /**
         * 强制平仓阈值
         */
        private BigDecimal liquidationThreshold;
        
        /**
         * 价格波动限制
         */
        private BigDecimal priceVolatilityLimit;
        
        /**
         * 风险监控规则
         */
        private List<RiskRule> riskRules;
        
        /**
         * 熔断机制
         */
        private CircuitBreaker circuitBreaker;
    }
    
    /**
     * 风险规则
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskRule {
        private String ruleName;
        private String ruleType;
        private String condition;
        private String action;
        private BigDecimal threshold;
        private Boolean isActive;
    }
    
    /**
     * 熔断机制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CircuitBreaker {
        private Boolean enabled;
        private BigDecimal priceChangeThreshold;
        private Integer timeWindow;
        private Integer cooldownPeriod;
        private String action;
    }
    
    /**
     * 市场数据配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketDataConfig {
        /**
         * K线数据间隔
         */
        private List<String> klineIntervals;
        
        /**
         * 深度数据层级
         */
        private List<Integer> depthLevels;
        
        /**
         * 实时数据推送频率
         */
        private Integer pushFrequency;
        
        /**
         * 历史数据保留期
         */
        private Integer dataRetentionDays;
        
        /**
         * 数据源配置
         */
        private List<DataSource> dataSources;
        
        /**
         * 数据质量检查
         */
        private DataQualityCheck dataQualityCheck;
    }
    
    /**
     * 数据源
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataSource {
        private String sourceName;
        private String sourceType;
        private Integer priority;
        private Boolean isActive;
        private String endpoint;
        private Map<String, String> parameters;
    }
    
    /**
     * 数据质量检查
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataQualityCheck {
        private Boolean enabled;
        private List<String> checkRules;
        private String action;
        private Integer tolerance;
    }
    
    /**
     * 显示配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DisplayConfig {
        /**
         * 显示名称
         */
        private String displayName;
        
        /**
         * 显示顺序
         */
        private Integer displayOrder;
        
        /**
         * 是否在列表中显示
         */
        private Boolean showInList;
        
        /**
         * 是否推荐
         */
        private Boolean isRecommended;
        
        /**
         * 标签
         */
        private List<String> tags;
        
        /**
         * 图标URL
         */
        private String iconUrl;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 分类
         */
        private String category;
    }
    
    /**
     * API配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ApiConfig {
        /**
         * API访问限制
         */
        private RateLimit rateLimit;
        
        /**
         * 支持的API版本
         */
        private List<String> supportedVersions;
        
        /**
         * WebSocket配置
         */
        private WebSocketConfig webSocketConfig;
        
        /**
         * REST API配置
         */
        private RestApiConfig restApiConfig;
    }
    
    /**
     * 访问限制
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateLimit {
        private Integer requestsPerSecond;
        private Integer requestsPerMinute;
        private Integer requestsPerHour;
        private Integer requestsPerDay;
        private String limitType;
    }
    
    /**
     * WebSocket配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebSocketConfig {
        private Boolean enabled;
        private List<String> supportedStreams;
        private Integer maxConnections;
        private Integer heartbeatInterval;
    }
    
    /**
     * REST API配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RestApiConfig {
        private Boolean enabled;
        private List<String> supportedEndpoints;
        private Integer timeout;
        private Integer retryAttempts;
    }
    
    /**
     * 合规配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplianceConfig {
        /**
         * 监管要求
         */
        private List<String> regulatoryRequirements;
        
        /**
         * KYC要求
         */
        private String kycRequirement;
        
        /**
         * AML检查
         */
        private Boolean amlCheckRequired;
        
        /**
         * 地区限制
         */
        private List<String> restrictedRegions;
        
        /**
         * 合规等级
         */
        private String complianceLevel;
        
        /**
         * 报告要求
         */
        private List<String> reportingRequirements;
    }
    
    /**
     * 流动性配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityConfig {
        /**
         * 最小流动性要求
         */
        private BigDecimal minLiquidity;
        
        /**
         * 流动性提供商
         */
        private List<String> liquidityProviders;
        
        /**
         * 做市商激励
         */
        private MarketMakerIncentive marketMakerIncentive;
        
        /**
         * 流动性监控
         */
        private LiquidityMonitoring liquidityMonitoring;
    }
    
    /**
     * 做市商激励
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketMakerIncentive {
        private Boolean enabled;
        private BigDecimal rebateRate;
        private String qualificationCriteria;
        private BigDecimal minVolume;
        private BigDecimal minSpread;
    }
    
    /**
     * 流动性监控
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityMonitoring {
        private Boolean enabled;
        private BigDecimal warningThreshold;
        private BigDecimal criticalThreshold;
        private String action;
        private Integer checkInterval;
    }
    
    /**
     * 监控配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonitoringConfig {
        /**
         * 性能监控
         */
        private PerformanceMonitoring performanceMonitoring;
        
        /**
         * 异常检测
         */
        private AnomalyDetection anomalyDetection;
        
        /**
         * 告警配置
         */
        private List<AlertConfig> alertConfigs;
        
        /**
         * 日志配置
         */
        private LoggingConfig loggingConfig;
    }
    
    /**
     * 性能监控
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceMonitoring {
        private Boolean enabled;
        private List<String> metrics;
        private Integer collectionInterval;
        private Integer retentionPeriod;
    }
    
    /**
     * 异常检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyDetection {
        private Boolean enabled;
        private List<String> detectionRules;
        private BigDecimal sensitivity;
        private String action;
    }
    
    /**
     * 告警配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertConfig {
        private String alertType;
        private String condition;
        private BigDecimal threshold;
        private String severity;
        private List<String> recipients;
        private Boolean isActive;
    }
    
    /**
     * 日志配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoggingConfig {
        private String logLevel;
        private List<String> logCategories;
        private Boolean enableAuditLog;
        private Integer logRetentionDays;
    }
}