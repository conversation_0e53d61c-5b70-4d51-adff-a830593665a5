package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 成交量排行响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "成交量排行响应")
public class VolumeRankingResponse {

    @Schema(description = "排名")
    private Integer rank;

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币")
    private String baseCurrency;

    @Schema(description = "计价货币")
    private String quoteCurrency;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "24小时成交量")
    private BigDecimal volume24h;

    @Schema(description = "24小时成交额")
    private BigDecimal amount24h;

    @Schema(description = "24小时涨跌幅")
    private BigDecimal changePercent24h;

    @Schema(description = "7天成交量")
    private BigDecimal volume7d;

    @Schema(description = "7天成交额")
    private BigDecimal amount7d;

    @Schema(description = "30天成交量")
    private BigDecimal volume30d;

    @Schema(description = "30天成交额")
    private BigDecimal amount30d;

    @Schema(description = "成交量变化百分比")
    private BigDecimal volumeChangePercent;

    @Schema(description = "成交额变化百分比")
    private BigDecimal amountChangePercent;

    @Schema(description = "平均成交价")
    private BigDecimal avgPrice;

    @Schema(description = "最高价")
    private BigDecimal highPrice;

    @Schema(description = "最低价")
    private BigDecimal lowPrice;

    @Schema(description = "开盘价")
    private BigDecimal openPrice;

    @Schema(description = "收盘价")
    private BigDecimal closePrice;

    @Schema(description = "成交笔数")
    private Long tradeCount;

    @Schema(description = "买单成交量")
    private BigDecimal buyVolume;

    @Schema(description = "卖单成交量")
    private BigDecimal sellVolume;

    @Schema(description = "买卖比例")
    private BigDecimal buySellRatio;

    @Schema(description = "大单成交量")
    private BigDecimal largeOrderVolume;

    @Schema(description = "大单成交比例")
    private BigDecimal largeOrderRatio;

    @Schema(description = "活跃用户数")
    private Integer activeUsers;

    @Schema(description = "新增用户数")
    private Integer newUsers;

    @Schema(description = "换手率")
    private BigDecimal turnoverRate;

    @Schema(description = "市值")
    private BigDecimal marketCap;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "市值排名")
    private Integer marketCapRank;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "成交密度")
    private BigDecimal tradeDensity;

    @Schema(description = "价格稳定性")
    private BigDecimal priceStability;

    @Schema(description = "资金流向")
    private String moneyFlowDirection;

    @Schema(description = "资金流入量")
    private BigDecimal moneyInflow;

    @Schema(description = "资金流出量")
    private BigDecimal moneyOutflow;

    @Schema(description = "净资金流")
    private BigDecimal netMoneyFlow;

    @Schema(description = "机构持仓比例")
    private BigDecimal institutionalHolding;

    @Schema(description = "散户持仓比例")
    private BigDecimal retailHolding;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "社交媒体提及次数")
    private Integer socialMentions;

    @Schema(description = "新闻报道数量")
    private Integer newsCount;

    @Schema(description = "技术分析评分")
    private BigDecimal technicalScore;

    @Schema(description = "基本面评分")
    private BigDecimal fundamentalScore;

    @Schema(description = "综合评分")
    private BigDecimal overallScore;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "统计周期")
    private String period;

    @Schema(description = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}