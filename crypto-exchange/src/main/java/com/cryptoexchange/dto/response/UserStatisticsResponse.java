package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 用户统计响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "用户统计响应")
public class UserStatisticsResponse {

    @Schema(description = "总用户数", example = "10000")
    private Long totalUsers;

    @Schema(description = "活跃用户数", example = "5000")
    private Long activeUsers;

    @Schema(description = "新注册用户数", example = "100")
    private Long newUsers;

    @Schema(description = "今日新增用户数", example = "50")
    private Long todayNewUsers;

    @Schema(description = "本周新增用户数", example = "300")
    private Long weekNewUsers;

    @Schema(description = "本月新增用户数", example = "1200")
    private Long monthNewUsers;

    @Schema(description = "已验证KYC用户数", example = "8000")
    private Long verifiedUsers;

    @Schema(description = "VIP用户数", example = "500")
    private Long vipUsers;

    @Schema(description = "冻结用户数", example = "10")
    private Long frozenUsers;

    @Schema(description = "在线用户数", example = "1000")
    private Long onlineUsers;

    @Schema(description = "总交易用户数", example = "7000")
    private Long tradingUsers;

    @Schema(description = "今日交易用户数", example = "2000")
    private Long todayTradingUsers;

    @Schema(description = "总资产价值", example = "1000000.50")
    private BigDecimal totalAssetValue;

    @Schema(description = "平均资产价值", example = "100.50")
    private BigDecimal avgAssetValue;

    @Schema(description = "总交易量", example = "5000000.00")
    private BigDecimal totalTradeVolume;

    @Schema(description = "总交易次数", example = "50000")
    private Long totalTradeCount;

    @Schema(description = "总手续费收入", example = "25000.75")
    private BigDecimal totalFeeIncome;

    @Schema(description = "用户增长率", example = "5.2")
    private BigDecimal userGrowthRate;

    @Schema(description = "用户活跃率", example = "50.0")
    private BigDecimal userActiveRate;

    @Schema(description = "用户留存率", example = "80.5")
    private BigDecimal userRetentionRate;

    @Schema(description = "各等级用户分布")
    private Map<String, Long> userLevelDistribution;

    @Schema(description = "各国家用户分布")
    private Map<String, Long> userCountryDistribution;

    @Schema(description = "各注册渠道用户分布")
    private Map<String, Long> userChannelDistribution;

    @Schema(description = "用户年龄分布")
    private Map<String, Long> userAgeDistribution;

    @Schema(description = "用户性别分布")
    private Map<String, Long> userGenderDistribution;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "统计时间范围开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsStartTime;

    @Schema(description = "统计时间范围结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsEndTime;

    @Schema(description = "数据版本", example = "v1.0")
    private String dataVersion;

    public UserStatisticsResponse() {
        this.lastUpdateTime = LocalDateTime.now();
        this.dataVersion = "v1.0";
    }

    public UserStatisticsResponse(Long totalUsers, Long activeUsers, Long newUsers) {
        this();
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.newUsers = newUsers;
        
        // 计算活跃率
        if (totalUsers != null && totalUsers > 0 && activeUsers != null) {
            this.userActiveRate = BigDecimal.valueOf(activeUsers * 100.0 / totalUsers)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 计算用户增长率
     */
    public void calculateGrowthRate(Long previousPeriodUsers) {
        if (previousPeriodUsers != null && previousPeriodUsers > 0 && newUsers != null) {
            this.userGrowthRate = BigDecimal.valueOf(newUsers * 100.0 / previousPeriodUsers)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }
}