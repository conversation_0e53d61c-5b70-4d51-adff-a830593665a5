package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 期货最近交易响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesRecentTradeResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 交易列表
     */
    private List<RecentTrade> trades;
    
    /**
     * 总交易数量
     */
    private Integer totalTrades;
    
    /**
     * 数据时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 最近交易信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RecentTrade {
        /**
         * 交易ID
         */
        private Long tradeId;
        
        /**
         * 价格
         */
        private BigDecimal price;
        
        /**
         * 数量
         */
        private BigDecimal quantity;
        
        /**
         * 成交金额
         */
        private BigDecimal quoteQty;
        
        /**
         * 交易时间
         */
        private LocalDateTime time;
        
        /**
         * 是否为买方成交
         */
        private Boolean isBuyerMaker;
        
        /**
         * 交易方向 (BUY/SELL)
         */
        private String side;
        
        /**
         * 是否为大单
         */
        private Boolean isLargeOrder;
        
        /**
         * 交易类型
         */
        private String tradeType;
    }
}