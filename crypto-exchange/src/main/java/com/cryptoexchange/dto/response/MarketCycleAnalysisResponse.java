package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 市场周期分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketCycleAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 当前市场周期阶段
     */
    private MarketCyclePhase currentPhase;

    /**
     * 周期成熟度 (0-100)
     */
    private BigDecimal cycleMaturity;

    /**
     * 周期分析
     */
    private CycleAnalysis cycleAnalysis;

    /**
     * 周期指标
     */
    private CycleIndicators cycleIndicators;

    /**
     * 周期转换信号
     */
    private CycleTransitionSignals cycleTransitionSignals;

    /**
     * 周期预测
     */
    private CycleForecast cycleForecast;

    /**
     * 历史周期对比
     */
    private HistoricalCycleComparison historicalCycleComparison;

    /**
     * 周期交易策略
     */
    private CycleTradingStrategy cycleTradingStrategy;

    /**
     * 周期风险评估
     */
    private CycleRiskAssessment cycleRiskAssessment;

    /**
     * 宏观经济影响
     */
    private MacroeconomicImpact macroeconomicImpact;

    /**
     * 市场情绪周期
     */
    private MarketSentimentCycle marketSentimentCycle;

    /**
     * 技术周期分析
     */
    private TechnicalCycleAnalysis technicalCycleAnalysis;

    /**
     * 市场周期阶段
     */
    public enum MarketCyclePhase {
        ACCUMULATION("积累期", 1, "价格底部，聪明资金开始积累"),
        MARKUP("上升期", 2, "价格上涨，趋势确立"),
        DISTRIBUTION("分发期", 3, "价格高位，机构开始分发"),
        MARKDOWN("下跌期", 4, "价格下跌，市场恐慌"),
        TRANSITION("过渡期", 5, "周期转换，方向不明");

        private final String description;
        private final Integer phase;
        private final String detail;

        MarketCyclePhase(String description, Integer phase, String detail) {
            this.description = description;
            this.phase = phase;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public Integer getPhase() {
            return phase;
        }

        public String getDetail() {
            return detail;
        }
    }

    /**
     * 周期分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleAnalysis {
        /**
         * 主要周期长度（天）
         */
        private Integer primaryCycleLength;

        /**
         * 次要周期长度（天）
         */
        private Integer secondaryCycleLength;

        /**
         * 当前周期进度 (0-100%)
         */
        private BigDecimal currentCycleProgress;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 周期可靠性
         */
        private BigDecimal cycleReliability;

        /**
         * 周期同步性
         */
        private BigDecimal cycleSynchronization;

        /**
         * 周期偏差
         */
        private BigDecimal cycleDeviation;

        /**
         * 嵌套周期
         */
        private List<NestedCycle> nestedCycles;
    }

    /**
     * 嵌套周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NestedCycle {
        /**
         * 周期类型
         */
        private String cycleType;

        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 当前位置
         */
        private BigDecimal currentPosition;

        /**
         * 周期幅度
         */
        private BigDecimal cycleAmplitude;

        /**
         * 周期相位
         */
        private String cyclePhase;
    }

    /**
     * 周期指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleIndicators {
        /**
         * 周期振荡器
         */
        private Map<String, BigDecimal> cycleOscillators;

        /**
         * 动量指标
         */
        private Map<String, BigDecimal> momentumIndicators;

        /**
         * 成交量周期
         */
        private VolumeCycle volumeCycle;

        /**
         * 波动率周期
         */
        private VolatilityCycle volatilityCycle;

        /**
         * 相对强弱周期
         */
        private RelativeStrengthCycle relativeStrengthCycle;

        /**
         * 周期确认指标
         */
        private List<String> cycleConfirmationIndicators;
    }

    /**
     * 成交量周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeCycle {
        /**
         * 成交量周期阶段
         */
        private String volumeCyclePhase;

        /**
         * 成交量趋势
         */
        private String volumeTrend;

        /**
         * 成交量周期强度
         */
        private BigDecimal volumeCycleStrength;

        /**
         * 成交量异常
         */
        private List<String> volumeAnomalies;
    }

    /**
     * 波动率周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityCycle {
        /**
         * 波动率周期阶段
         */
        private String volatilityCyclePhase;

        /**
         * 波动率趋势
         */
        private String volatilityTrend;

        /**
         * 波动率周期强度
         */
        private BigDecimal volatilityCycleStrength;

        /**
         * 波动率预期
         */
        private BigDecimal volatilityExpectation;
    }

    /**
     * 相对强弱周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelativeStrengthCycle {
        /**
         * 相对强弱阶段
         */
        private String relativeStrengthPhase;

        /**
         * 相对表现
         */
        private BigDecimal relativePerformance;

        /**
         * 强弱周期位置
         */
        private BigDecimal strengthCyclePosition;

        /**
         * 轮动概率
         */
        private BigDecimal rotationProbability;
    }

    /**
     * 周期转换信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleTransitionSignals {
        /**
         * 转换信号列表
         */
        private List<TransitionSignal> transitionSignals;

        /**
         * 转换概率
         */
        private BigDecimal transitionProbability;

        /**
         * 转换时间窗口
         */
        private String transitionTimeWindow;

        /**
         * 转换确认条件
         */
        private List<String> transitionConfirmationConditions;

        /**
         * 早期转换警告
         */
        private List<String> earlyTransitionWarnings;
    }

    /**
     * 转换信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransitionSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 目标阶段
         */
        private String targetPhase;

        /**
         * 信号时间
         */
        private LocalDateTime signalTime;

        /**
         * 信号描述
         */
        private String signalDescription;

        /**
         * 可靠性
         */
        private BigDecimal reliability;
    }

    /**
     * 周期预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleForecast {
        /**
         * 下一阶段预测
         */
        private String nextPhasePredict;

        /**
         * 转换时间预测
         */
        private LocalDateTime transitionTimePredict;

        /**
         * 周期顶部预测
         */
        private CyclePeakPredict cyclePeakPredict;

        /**
         * 周期底部预测
         */
        private CycleTroughPredict cycleTroughPredict;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 关键时间节点
         */
        private List<LocalDateTime> keyTimeNodes;

        /**
         * 预测模型
         */
        private String forecastModel;
    }

    /**
     * 周期顶部预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CyclePeakPredict {
        /**
         * 预测顶部价格
         */
        private BigDecimal predictedPeakPrice;

        /**
         * 预测顶部时间
         */
        private LocalDateTime predictedPeakTime;

        /**
         * 顶部概率
         */
        private BigDecimal peakProbability;

        /**
         * 顶部确认信号
         */
        private List<String> peakConfirmationSignals;
    }

    /**
     * 周期底部预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleTroughPredict {
        /**
         * 预测底部价格
         */
        private BigDecimal predictedTroughPrice;

        /**
         * 预测底部时间
         */
        private LocalDateTime predictedTroughTime;

        /**
         * 底部概率
         */
        private BigDecimal troughProbability;

        /**
         * 底部确认信号
         */
        private List<String> troughConfirmationSignals;
    }

    /**
     * 历史周期对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalCycleComparison {
        /**
         * 相似历史周期
         */
        private List<HistoricalCycle> similarHistoricalCycles;

        /**
         * 周期匹配度
         */
        private BigDecimal cycleMatchDegree;

        /**
         * 历史表现统计
         */
        private Map<String, BigDecimal> historicalPerformanceStats;

        /**
         * 平均周期长度
         */
        private Integer averageCycleLength;

        /**
         * 历史成功率
         */
        private BigDecimal historicalSuccessRate;
    }

    /**
     * 历史周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalCycle {
        /**
         * 周期开始时间
         */
        private LocalDateTime cycleStartTime;

        /**
         * 周期结束时间
         */
        private LocalDateTime cycleEndTime;

        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 周期幅度
         */
        private BigDecimal cycleAmplitude;

        /**
         * 相似度
         */
        private BigDecimal similarity;

        /**
         * 周期特征
         */
        private String cycleCharacteristics;
    }

    /**
     * 周期交易策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleTradingStrategy {
        /**
         * 当前阶段策略
         */
        private String currentPhaseStrategy;

        /**
         * 推荐操作
         */
        private List<String> recommendedActions;

        /**
         * 仓位建议
         */
        private BigDecimal positionSuggestion;

        /**
         * 风险控制
         */
        private List<String> riskControlMeasures;

        /**
         * 策略有效期
         */
        private String strategyValidity;

        /**
         * 策略成功率
         */
        private BigDecimal strategySuccessRate;
    }

    /**
     * 周期风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CycleRiskAssessment {
        /**
         * 周期风险等级
         */
        private String cycleRiskLevel;

        /**
         * 阶段转换风险
         */
        private BigDecimal phaseTransitionRisk;

        /**
         * 周期失效风险
         */
        private BigDecimal cycleFailureRisk;

        /**
         * 时间风险
         */
        private BigDecimal timeRisk;

        /**
         * 幅度风险
         */
        private BigDecimal amplitudeRisk;

        /**
         * 风险缓解措施
         */
        private List<String> riskMitigationMeasures;
    }

    /**
     * 宏观经济影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MacroeconomicImpact {
        /**
         * 经济周期阶段
         */
        private String economicCyclePhase;

        /**
         * 货币政策影响
         */
        private String monetaryPolicyImpact;

        /**
         * 通胀影响
         */
        private String inflationImpact;

        /**
         * 利率影响
         */
        private String interestRateImpact;

        /**
         * 地缘政治影响
         */
        private String geopoliticalImpact;

        /**
         * 宏观风险因素
         */
        private List<String> macroRiskFactors;
    }

    /**
     * 市场情绪周期
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketSentimentCycle {
        /**
         * 情绪周期阶段
         */
        private String sentimentCyclePhase;

        /**
         * 恐慌贪婪指数
         */
        private BigDecimal fearGreedIndex;

        /**
         * 情绪极值
         */
        private String sentimentExtreme;

        /**
         * 情绪转换信号
         */
        private List<String> sentimentTransitionSignals;

        /**
         * 群体心理分析
         */
        private String crowdPsychologyAnalysis;
    }

    /**
     * 技术周期分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TechnicalCycleAnalysis {
        /**
         * 艾略特波浪
         */
        private ElliottWaveAnalysis elliottWaveAnalysis;

        /**
         * 江恩理论
         */
        private GannTheoryAnalysis gannTheoryAnalysis;

        /**
         * 斐波那契周期
         */
        private FibonacciCycleAnalysis fibonacciCycleAnalysis;

        /**
         * 时间周期
         */
        private TimeCycleAnalysis timeCycleAnalysis;

        /**
         * 价格周期
         */
        private PriceCycleAnalysis priceCycleAnalysis;
    }

    /**
     * 艾略特波浪分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ElliottWaveAnalysis {
        /**
         * 当前波浪位置
         */
        private String currentWavePosition;

        /**
         * 波浪计数
         */
        private String waveCount;

        /**
         * 波浪目标
         */
        private List<BigDecimal> waveTargets;

        /**
         * 波浪完成度
         */
        private BigDecimal waveCompleteness;
    }

    /**
     * 江恩理论分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GannTheoryAnalysis {
        /**
         * 江恩角度线
         */
        private List<String> gannAngles;

        /**
         * 时间价格平方
         */
        private String timePriceSquare;

        /**
         * 江恩周期
         */
        private List<String> gannCycles;

        /**
         * 关键时间点
         */
        private List<LocalDateTime> keyTimePoints;
    }

    /**
     * 斐波那契周期分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FibonacciCycleAnalysis {
        /**
         * 斐波那契时间
         */
        private List<Integer> fibonacciTimes;

        /**
         * 斐波那契价格
         */
        private List<BigDecimal> fibonacciPrices;

        /**
         * 黄金分割点
         */
        private List<BigDecimal> goldenRatioPoints;

        /**
         * 斐波那契扩展
         */
        private List<BigDecimal> fibonacciExtensions;
    }

    /**
     * 时间周期分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeCycleAnalysis {
        /**
         * 主要时间周期
         */
        private List<Integer> majorTimeCycles;

        /**
         * 次要时间周期
         */
        private List<Integer> minorTimeCycles;

        /**
         * 周期共振
         */
        private List<String> cycleResonance;

        /**
         * 时间窗口
         */
        private List<String> timeWindows;
    }

    /**
     * 价格周期分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceCycleAnalysis {
        /**
         * 价格周期长度
         */
        private Integer priceCycleLength;

        /**
         * 价格周期幅度
         */
        private BigDecimal priceCycleAmplitude;

        /**
         * 价格周期相位
         */
        private String priceCyclePhase;

        /**
         * 价格周期预测
         */
        private List<BigDecimal> priceCycleForecast;
    }

    /**
     * 获取周期摘要
     */
    public String getCycleSummary() {
        return String.format("市场周期: %s (成熟度: %s%%) - %s",
            currentPhase != null ? currentPhase.getDescription() : "未知",
            cycleMaturity != null ? cycleMaturity.toString() : "N/A",
            currentPhase != null ? currentPhase.getDetail() : "无详细信息");
    }

    /**
     * 检查是否处于买入阶段
     */
    public boolean isInBuyingPhase() {
        return currentPhase != null && 
               (currentPhase == MarketCyclePhase.ACCUMULATION || 
                currentPhase == MarketCyclePhase.MARKUP);
    }

    /**
     * 检查是否处于卖出阶段
     */
    public boolean isInSellingPhase() {
        return currentPhase != null && 
               (currentPhase == MarketCyclePhase.DISTRIBUTION || 
                currentPhase == MarketCyclePhase.MARKDOWN);
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (cycleTradingStrategy == null) {
            return "暂无交易建议";
        }
        
        String strategy = cycleTradingStrategy.getCurrentPhaseStrategy();
        if (strategy != null) {
            return strategy;
        }
        
        List<String> actions = cycleTradingStrategy.getRecommendedActions();
        if (actions != null && !actions.isEmpty()) {
            return String.join(", ", actions);
        }
        
        return "无明确交易建议";
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (cycleRiskAssessment == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = cycleRiskAssessment.getCycleRiskLevel();
        if (riskLevel == null) {
            return "风险等级未知";
        }
        
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：周期可能转换，建议谨慎交易";
            case "MEDIUM":
                return "中等风险：注意周期变化，适度交易";
            case "LOW":
                return "低风险：周期相对稳定，可正常交易";
            default:
                return "风险等级: " + riskLevel;
        }
    }

    /**
     * 检查是否有转换信号
     */
    public boolean hasTransitionSignals() {
        return cycleTransitionSignals != null && 
               cycleTransitionSignals.getTransitionSignals() != null &&
               !cycleTransitionSignals.getTransitionSignals().isEmpty();
    }

    /**
     * 获取周期进度描述
     */
    public String getCycleProgressDescription() {
        if (cycleAnalysis == null || cycleAnalysis.getCurrentCycleProgress() == null) {
            return "周期进度未知";
        }
        
        double progress = cycleAnalysis.getCurrentCycleProgress().doubleValue();
        if (progress < 25) {
            return "周期初期 (" + progress + "%)";
        } else if (progress < 50) {
            return "周期前期 (" + progress + "%)";
        } else if (progress < 75) {
            return "周期中后期 (" + progress + "%)";
        } else {
            return "周期末期 (" + progress + "%)";
        }
    }

    /**
     * 获取下一阶段预测
     */
    public String getNextPhasePredict() {
        if (cycleForecast == null || cycleForecast.getNextPhasePredict() == null) {
            return "下一阶段预测不可用";
        }
        
        return cycleForecast.getNextPhasePredict();
    }

    /**
     * 获取预测置信度描述
     */
    public String getForecastConfidenceDescription() {
        if (cycleForecast == null || cycleForecast.getForecastConfidence() == null) {
            return "预测置信度未知";
        }
        
        double confidence = cycleForecast.getForecastConfidence().doubleValue();
        if (confidence >= 0.8) {
            return "高置信度 (" + (confidence * 100) + "%)";
        } else if (confidence >= 0.6) {
            return "中等置信度 (" + (confidence * 100) + "%)";
        } else {
            return "低置信度 (" + (confidence * 100) + "%)";
        }
    }
}