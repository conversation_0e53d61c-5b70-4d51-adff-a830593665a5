package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 恐慌贪婪指数响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FearGreedIndexResponse {
    
    /**
     * 当前指数值 (0-100)
     */
    private BigDecimal currentValue;
    
    /**
     * 指数等级
     */
    private FearGreedLevel level;
    
    /**
     * 指数分类
     */
    private String classification;
    
    /**
     * 计算时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime calculationTime;
    
    /**
     * 数据更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;
    
    /**
     * 指数变化趋势
     */
    private IndexTrend trend;
    
    /**
     * 24小时变化
     */
    private BigDecimal change24h;
    
    /**
     * 7天变化
     */
    private BigDecimal change7d;
    
    /**
     * 30天变化
     */
    private BigDecimal change30d;
    
    /**
     * 指数组成部分
     */
    private IndexComponents components;
    
    /**
     * 历史数据
     */
    private List<HistoricalData> historicalData;
    
    /**
     * 统计分析
     */
    private StatisticalAnalysis statistics;
    
    /**
     * 市场影响分析
     */
    private MarketImpactAnalysis marketImpact;
    
    /**
     * 指数解读
     */
    private IndexInterpretation interpretation;
    
    /**
     * 交易建议
     */
    private TradingRecommendation tradingRecommendation;
    
    /**
     * 风险评估
     */
    private RiskAssessment riskAssessment;
    
    /**
     * 预测分析
     */
    private ForecastAnalysis forecast;
    
    /**
     * 警报设置
     */
    private List<IndexAlert> alerts;
    
    /**
     * 恐慌贪婪等级枚举
     */
    public enum FearGreedLevel {
        EXTREME_FEAR("极度恐慌", 0, 25),
        FEAR("恐慌", 25, 45),
        NEUTRAL("中性", 45, 55),
        GREED("贪婪", 55, 75),
        EXTREME_GREED("极度贪婪", 75, 100);
        
        private final String description;
        private final int minValue;
        private final int maxValue;
        
        FearGreedLevel(String description, int minValue, int maxValue) {
            this.description = description;
            this.minValue = minValue;
            this.maxValue = maxValue;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getMinValue() {
            return minValue;
        }
        
        public int getMaxValue() {
            return maxValue;
        }
    }
    
    /**
     * 指数趋势
     */
    public enum IndexTrend {
        RAPIDLY_INCREASING,
        INCREASING,
        STABLE,
        DECREASING,
        RAPIDLY_DECREASING
    }
    
    /**
     * 指数组成部分
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexComponents {
        /**
         * 波动率组件 (25%权重)
         */
        private ComponentData volatility;
        
        /**
         * 市场动量/成交量组件 (25%权重)
         */
        private ComponentData marketMomentum;
        
        /**
         * 社交媒体组件 (15%权重)
         */
        private ComponentData socialMedia;
        
        /**
         * 调查数据组件 (15%权重)
         */
        private ComponentData surveys;
        
        /**
         * 比特币主导地位组件 (10%权重)
         */
        private ComponentData bitcoinDominance;
        
        /**
         * 谷歌趋势组件 (10%权重)
         */
        private ComponentData googleTrends;
        
        /**
         * 组件权重分布
         */
        private Map<String, BigDecimal> weightDistribution;
    }
    
    /**
     * 组件数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComponentData {
        private String name;
        private BigDecimal value;
        private BigDecimal normalizedValue;
        private BigDecimal weight;
        private BigDecimal contribution;
        private String status;
        private String trend;
        private String description;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdate;
    }
    
    /**
     * 历史数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalData {
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
        private BigDecimal value;
        private FearGreedLevel level;
        private String classification;
        private BigDecimal change;
        private String note;
    }
    
    /**
     * 统计分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatisticalAnalysis {
        private BigDecimal average30d;
        private BigDecimal average90d;
        private BigDecimal average365d;
        private BigDecimal standardDeviation;
        private BigDecimal volatility;
        private BigDecimal currentPercentile;
        private BigDecimal maxValue;
        private BigDecimal minValue;
        private String distributionAnalysis;
        private List<String> extremeEvents;
    }
    
    /**
     * 市场影响分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketImpactAnalysis {
        private PriceCorrelation priceCorrelation;
        private VolumeCorrelation volumeCorrelation;
        private VolatilityCorrelation volatilityCorrelation;
        private String marketPhase;
        private BigDecimal predictivePower;
        private String historicalAccuracy;
        private List<String> significantEvents;
    }
    
    /**
     * 价格相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceCorrelation {
        private BigDecimal correlationCoefficient;
        private String correlationStrength;
        private BigDecimal lagDays;
        private String relationship;
        private BigDecimal rSquared;
    }
    
    /**
     * 成交量相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeCorrelation {
        private BigDecimal correlationCoefficient;
        private String correlationStrength;
        private String relationship;
        private BigDecimal significance;
    }
    
    /**
     * 波动率相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityCorrelation {
        private BigDecimal correlationCoefficient;
        private String correlationStrength;
        private String relationship;
        private BigDecimal predictiveValue;
    }
    
    /**
     * 指数解读
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexInterpretation {
        private String currentInterpretation;
        private String marketSentiment;
        private String investorBehavior;
        private String marketCondition;
        private List<String> keyInsights;
        private String contrarian;
        private String momentum;
        private String riskLevel;
        private String opportunity;
    }
    
    /**
     * 交易建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingRecommendation {
        private String overallRecommendation;
        private String strategy;
        private String timing;
        private BigDecimal positionSize;
        private String riskManagement;
        private List<String> entrySignals;
        private List<String> exitSignals;
        private String timeframe;
        private String contrarian;
        private String momentum;
    }
    
    /**
     * 风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskAssessment {
        private String overallRisk;
        private BigDecimal riskScore;
        private String volatilityRisk;
        private String liquidityRisk;
        private String sentimentRisk;
        private List<RiskFactor> riskFactors;
        private List<String> mitigationStrategies;
        private String riskWarning;
    }
    
    /**
     * 风险因素
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        private String factor;
        private String description;
        private String severity;
        private BigDecimal probability;
        private String impact;
        private String mitigation;
    }
    
    /**
     * 预测分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastAnalysis {
        private List<IndexForecast> forecasts;
        private String methodology;
        private BigDecimal confidence;
        private List<String> assumptions;
        private List<String> scenarios;
        private String disclaimer;
    }
    
    /**
     * 指数预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexForecast {
        private String timeframe;
        private BigDecimal predictedValue;
        private FearGreedLevel predictedLevel;
        private BigDecimal confidence;
        private String scenario;
        private List<String> catalysts;
        private String reasoning;
    }
    
    /**
     * 指数警报
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IndexAlert {
        private String alertType;
        private String condition;
        private BigDecimal threshold;
        private String severity;
        private String message;
        private String action;
        private Boolean isActive;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdTime;
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime triggeredTime;
    }
}