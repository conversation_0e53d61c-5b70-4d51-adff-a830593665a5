package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易对响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SymbolResponse {
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 基础货币
     */
    private String baseAsset;
    
    /**
     * 计价货币
     */
    private String quoteAsset;
    
    /**
     * 交易对状态 (TRADING, HALT, BREAK)
     */
    private String status;
    
    /**
     * 交易对类型 (SPOT, FUTURES, OPTION)
     */
    private String type;
    
    /**
     * 最小订单数量
     */
    private BigDecimal minQty;
    
    /**
     * 最大订单数量
     */
    private BigDecimal maxQty;
    
    /**
     * 数量步长
     */
    private BigDecimal stepSize;
    
    /**
     * 最小价格
     */
    private BigDecimal minPrice;
    
    /**
     * 最大价格
     */
    private BigDecimal maxPrice;
    
    /**
     * 价格步长
     */
    private BigDecimal tickSize;
    
    /**
     * 最小名义价值
     */
    private BigDecimal minNotional;
    
    /**
     * 基础资产精度
     */
    private Integer baseAssetPrecision;
    
    /**
     * 计价资产精度
     */
    private Integer quoteAssetPrecision;
    
    /**
     * 价格精度
     */
    private Integer pricePrecision;
    
    /**
     * 数量精度
     */
    private Integer quantityPrecision;
    
    /**
     * 是否允许市价单
     */
    private Boolean allowMarketOrder;
    
    /**
     * 是否允许限价单
     */
    private Boolean allowLimitOrder;
    
    /**
     * 是否允许止损单
     */
    private Boolean allowStopOrder;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * Maker手续费率
     */
    private BigDecimal makerFeeRate;
    
    /**
     * Taker手续费率
     */
    private BigDecimal takerFeeRate;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume24h;
    
    /**
     * 24小时交易额
     */
    private BigDecimal quoteVolume24h;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange24h;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent24h;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 最佳买价
     */
    private BigDecimal bidPrice;
    
    /**
     * 最佳卖价
     */
    private BigDecimal askPrice;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 上市时间
     */
    private LocalDateTime listTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}