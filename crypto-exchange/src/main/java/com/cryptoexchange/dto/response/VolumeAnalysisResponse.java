package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 成交量分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolumeAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 成交量健康度评分 (0-100)
     */
    private BigDecimal volumeHealthScore;

    /**
     * 成交量等级
     */
    private VolumeLevel volumeLevel;

    /**
     * 基础成交量指标
     */
    private BasicVolumeMetrics basicVolumeMetrics;

    /**
     * 成交量趋势分析
     */
    private VolumeTrendAnalysis volumeTrendAnalysis;

    /**
     * 成交量分布分析
     */
    private VolumeDistributionAnalysis volumeDistributionAnalysis;

    /**
     * 成交量价格关系
     */
    private VolumePriceRelationship volumePriceRelationship;

    /**
     * 成交量模式识别
     */
    private VolumePatternRecognition volumePatternRecognition;

    /**
     * 异常成交量检测
     */
    private AbnormalVolumeDetection abnormalVolumeDetection;

    /**
     * 成交量预测
     */
    private VolumeForecast volumeForecast;

    /**
     * 流动性指标
     */
    private LiquidityIndicators liquidityIndicators;

    /**
     * 市场参与度分析
     */
    private MarketParticipationAnalysis marketParticipationAnalysis;

    /**
     * 成交量质量评估
     */
    private VolumeQualityAssessment volumeQualityAssessment;

    /**
     * 历史对比
     */
    private HistoricalComparison historicalComparison;

    /**
     * 成交量等级
     */
    public enum VolumeLevel {
        EXCELLENT("优秀", 5, "成交量充足，市场活跃"),
        GOOD("良好", 4, "成交量良好，流动性充足"),
        MODERATE("中等", 3, "成交量中等，基本满足交易需求"),
        POOR("较差", 2, "成交量较低，流动性不足"),
        VERY_POOR("极差", 1, "成交量极低，市场不活跃");

        private final String description;
        private final Integer level;
        private final String detail;

        VolumeLevel(String description, Integer level, String detail) {
            this.description = description;
            this.level = level;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }

        public String getDetail() {
            return detail;
        }

        public static VolumeLevel fromScore(BigDecimal score) {
            if (score == null) return MODERATE;
            
            double value = score.doubleValue();
            if (value >= 80) return EXCELLENT;
            else if (value >= 60) return GOOD;
            else if (value >= 40) return MODERATE;
            else if (value >= 20) return POOR;
            else return VERY_POOR;
        }
    }

    /**
     * 基础成交量指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasicVolumeMetrics {
        /**
         * 24小时成交量
         */
        private BigDecimal volume24h;

        /**
         * 24小时成交额
         */
        private BigDecimal turnover24h;

        /**
         * 平均成交量
         */
        private BigDecimal averageVolume;

        /**
         * 成交量标准差
         */
        private BigDecimal volumeStandardDeviation;

        /**
         * 成交量变异系数
         */
        private BigDecimal volumeVariationCoefficient;

        /**
         * 最大单笔成交量
         */
        private BigDecimal maxSingleVolume;

        /**
         * 最小单笔成交量
         */
        private BigDecimal minSingleVolume;

        /**
         * 成交笔数
         */
        private Long tradeCount;

        /**
         * 平均每笔成交量
         */
        private BigDecimal averageVolumePerTrade;
    }

    /**
     * 成交量趋势分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeTrendAnalysis {
        /**
         * 短期趋势（1小时）
         */
        private String shortTermTrend;

        /**
         * 中期趋势（4小时）
         */
        private String mediumTermTrend;

        /**
         * 长期趋势（24小时）
         */
        private String longTermTrend;

        /**
         * 趋势强度
         */
        private BigDecimal trendStrength;

        /**
         * 成交量增长率
         */
        private BigDecimal volumeGrowthRate;

        /**
         * 成交量动量
         */
        private BigDecimal volumeMomentum;

        /**
         * 趋势持续性
         */
        private BigDecimal trendPersistence;

        /**
         * 趋势转折点
         */
        private List<LocalDateTime> trendTurningPoints;
    }

    /**
     * 成交量分布分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeDistributionAnalysis {
        /**
         * 时间分布
         */
        private Map<String, BigDecimal> timeDistribution;

        /**
         * 价格区间分布
         */
        private Map<String, BigDecimal> priceRangeDistribution;

        /**
         * 订单规模分布
         */
        private Map<String, BigDecimal> orderSizeDistribution;

        /**
         * 买卖方向分布
         */
        private Map<String, BigDecimal> buySellDistribution;

        /**
         * 成交量集中度
         */
        private BigDecimal volumeConcentration;

        /**
         * 分布均匀性
         */
        private BigDecimal distributionUniformity;

        /**
         * 峰值时段
         */
        private List<String> peakPeriods;

        /**
         * 低谷时段
         */
        private List<String> lowPeriods;
    }

    /**
     * 成交量价格关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumePriceRelationship {
        /**
         * 量价相关性
         */
        private BigDecimal volumePriceCorrelation;

        /**
         * 量价背离
         */
        private String volumePriceDivergence;

        /**
         * 价涨量增确认
         */
        private Boolean priceUpVolumeUpConfirmation;

        /**
         * 价跌量增警告
         */
        private Boolean priceDownVolumeUpWarning;

        /**
         * 突破确认
         */
        private String breakoutConfirmation;

        /**
         * 支撑阻力确认
         */
        private String supportResistanceConfirmation;

        /**
         * 量价关系强度
         */
        private BigDecimal relationshipStrength;

        /**
         * 关系稳定性
         */
        private BigDecimal relationshipStability;
    }

    /**
     * 成交量模式识别
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumePatternRecognition {
        /**
         * 识别到的模式
         */
        private List<String> identifiedPatterns;

        /**
         * 模式强度
         */
        private Map<String, BigDecimal> patternStrength;

        /**
         * 模式完成度
         */
        private Map<String, BigDecimal> patternCompleteness;

        /**
         * 经典量能模式
         */
        private List<String> classicalVolumePatterns;

        /**
         * 异常模式
         */
        private List<String> abnormalPatterns;

        /**
         * 模式预测
         */
        private Map<String, String> patternPredictions;

        /**
         * 模式可靠性
         */
        private Map<String, BigDecimal> patternReliability;
    }

    /**
     * 异常成交量检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalVolumeDetection {
        /**
         * 异常事件列表
         */
        private List<AbnormalVolumeEvent> abnormalEvents;

        /**
         * 成交量激增
         */
        private List<VolumeSurge> volumeSurges;

        /**
         * 成交量枯竭
         */
        private List<VolumeDryUp> volumeDryUps;

        /**
         * 异常交易检测
         */
        private List<String> suspiciousTrading;

        /**
         * 操纵嫌疑
         */
        private List<String> manipulationSuspicion;

        /**
         * 异常严重程度
         */
        private String abnormalitySeverity;

        /**
         * 风险警告
         */
        private List<String> riskWarnings;
    }

    /**
     * 异常成交量事件
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AbnormalVolumeEvent {
        /**
         * 事件类型
         */
        private String eventType;

        /**
         * 发生时间
         */
        private LocalDateTime eventTime;

        /**
         * 异常程度
         */
        private BigDecimal abnormalityDegree;

        /**
         * 持续时间
         */
        private String duration;

        /**
         * 影响评估
         */
        private String impactAssessment;

        /**
         * 可能原因
         */
        private List<String> possibleCauses;
    }

    /**
     * 成交量激增
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeSurge {
        /**
         * 激增时间
         */
        private LocalDateTime surgeTime;

        /**
         * 激增倍数
         */
        private BigDecimal surgeMultiplier;

        /**
         * 激增持续时间
         */
        private String surgeDuration;

        /**
         * 价格影响
         */
        private BigDecimal priceImpact;

        /**
         * 激增原因
         */
        private String surgeReason;
    }

    /**
     * 成交量枯竭
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeDryUp {
        /**
         * 枯竭时间
         */
        private LocalDateTime dryUpTime;

        /**
         * 枯竭程度
         */
        private BigDecimal dryUpDegree;

        /**
         * 枯竭持续时间
         */
        private String dryUpDuration;

        /**
         * 流动性影响
         */
        private String liquidityImpact;

        /**
         * 枯竭原因
         */
        private String dryUpReason;
    }

    /**
     * 成交量预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeForecast {
        /**
         * 短期预测（1小时）
         */
        private BigDecimal shortTermForecast;

        /**
         * 中期预测（4小时）
         */
        private BigDecimal mediumTermForecast;

        /**
         * 长期预测（24小时）
         */
        private BigDecimal longTermForecast;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 预测模型
         */
        private String forecastModel;

        /**
         * 影响因素
         */
        private List<String> influencingFactors;

        /**
         * 预测准确率
         */
        private BigDecimal forecastAccuracy;
    }

    /**
     * 流动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityIndicators {
        /**
         * 流动性比率
         */
        private BigDecimal liquidityRatio;

        /**
         * 换手率
         */
        private BigDecimal turnoverRate;

        /**
         * 市场深度
         */
        private BigDecimal marketDepth;

        /**
         * 买卖价差
         */
        private BigDecimal bidAskSpread;

        /**
         * 流动性成本
         */
        private BigDecimal liquidityCost;

        /**
         * 流动性稳定性
         */
        private BigDecimal liquidityStability;

        /**
         * 流动性效率
         */
        private BigDecimal liquidityEfficiency;
    }

    /**
     * 市场参与度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketParticipationAnalysis {
        /**
         * 活跃交易者数量
         */
        private Integer activeTraderCount;

        /**
         * 新增交易者数量
         */
        private Integer newTraderCount;

        /**
         * 大户参与度
         */
        private BigDecimal whaleParticipation;

        /**
         * 散户参与度
         */
        private BigDecimal retailParticipation;

        /**
         * 机构参与度
         */
        private BigDecimal institutionalParticipation;

        /**
         * 参与度多样性
         */
        private BigDecimal participationDiversity;

        /**
         * 市场集中度
         */
        private BigDecimal marketConcentration;
    }

    /**
     * 成交量质量评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeQualityAssessment {
        /**
         * 真实成交量比例
         */
        private BigDecimal genuineVolumeRatio;

        /**
         * 刷量嫌疑
         */
        private BigDecimal washTradingSuspicion;

        /**
         * 成交量质量评分
         */
        private BigDecimal volumeQualityScore;

        /**
         * 有效成交量
         */
        private BigDecimal effectiveVolume;

        /**
         * 噪音成交量
         */
        private BigDecimal noiseVolume;

        /**
         * 质量改善建议
         */
        private List<String> qualityImprovementSuggestions;
    }

    /**
     * 历史对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalComparison {
        /**
         * 历史平均水平
         */
        private BigDecimal historicalAverage;

        /**
         * 历史最高水平
         */
        private BigDecimal historicalHigh;

        /**
         * 历史最低水平
         */
        private BigDecimal historicalLow;

        /**
         * 当前排名
         */
        private Integer currentRanking;

        /**
         * 同期对比
         */
        private BigDecimal yearOverYearComparison;

        /**
         * 趋势对比
         */
        private String trendComparison;

        /**
         * 历史模式匹配
         */
        private List<String> historicalPatternMatches;
    }

    /**
     * 获取成交量状态摘要
     */
    public String getVolumeSummary() {
        return String.format("成交量等级: %s (评分: %s) - %s",
            volumeLevel != null ? volumeLevel.getDescription() : "未知",
            volumeHealthScore != null ? volumeHealthScore.toString() : "N/A",
            volumeLevel != null ? volumeLevel.getDetail() : "无详细信息");
    }

    /**
     * 检查是否适合大额交易
     */
    public boolean isSuitableForLargeOrders() {
        return volumeLevel != null && 
               (volumeLevel == VolumeLevel.EXCELLENT || volumeLevel == VolumeLevel.GOOD);
    }

    /**
     * 获取流动性状态
     */
    public String getLiquidityStatus() {
        if (liquidityIndicators == null || liquidityIndicators.getLiquidityRatio() == null) {
            return "流动性状态未知";
        }
        
        double ratio = liquidityIndicators.getLiquidityRatio().doubleValue();
        if (ratio >= 0.8) {
            return "流动性充足";
        } else if (ratio >= 0.6) {
            return "流动性良好";
        } else if (ratio >= 0.4) {
            return "流动性一般";
        } else {
            return "流动性不足";
        }
    }

    /**
     * 获取主要风险提示
     */
    public String getMainRiskAlert() {
        if (abnormalVolumeDetection == null) {
            return "无异常检测数据";
        }
        
        String severity = abnormalVolumeDetection.getAbnormalitySeverity();
        if (severity == null) {
            return "异常程度未知";
        }
        
        switch (severity.toUpperCase()) {
            case "HIGH":
                return "高风险：检测到严重异常成交量，建议暂停交易";
            case "MEDIUM":
                return "中等风险：成交量存在异常，请谨慎交易";
            case "LOW":
                return "低风险：成交量基本正常";
            default:
                return "异常程度: " + severity;
        }
    }

    /**
     * 检查是否有异常成交量
     */
    public boolean hasAbnormalVolume() {
        return abnormalVolumeDetection != null && 
               abnormalVolumeDetection.getAbnormalEvents() != null &&
               !abnormalVolumeDetection.getAbnormalEvents().isEmpty();
    }

    /**
     * 获取成交量趋势描述
     */
    public String getVolumeTrendDescription() {
        if (volumeTrendAnalysis == null) {
            return "趋势分析不可用";
        }
        
        return String.format("短期: %s, 中期: %s, 长期: %s",
            volumeTrendAnalysis.getShortTermTrend() != null ? volumeTrendAnalysis.getShortTermTrend() : "未知",
            volumeTrendAnalysis.getMediumTermTrend() != null ? volumeTrendAnalysis.getMediumTermTrend() : "未知",
            volumeTrendAnalysis.getLongTermTrend() != null ? volumeTrendAnalysis.getLongTermTrend() : "未知");
    }
}