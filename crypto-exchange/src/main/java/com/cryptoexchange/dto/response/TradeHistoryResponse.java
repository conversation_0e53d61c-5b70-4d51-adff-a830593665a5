package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易历史响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易历史响应")
public class TradeHistoryResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "时间间隔")
    private String interval;

    @Schema(description = "总记录数")
    private Long totalCount;

    @Schema(description = "当前页码")
    private Integer currentPage;

    @Schema(description = "每页大小")
    private Integer pageSize;

    @Schema(description = "总页数")
    private Integer totalPages;

    @Schema(description = "是否有下一页")
    private Boolean hasNext;

    @Schema(description = "是否有上一页")
    private Boolean hasPrevious;

    @Schema(description = "交易记录列表")
    private List<TradeRecord> trades;

    @Schema(description = "统计信息")
    private TradeStatistics statistics;

    @Schema(description = "价格分析")
    private PriceAnalysis priceAnalysis;

    @Schema(description = "成交量分析")
    private VolumeAnalysis volumeAnalysis;

    @Schema(description = "时间分布")
    private TimeDistribution timeDistribution;

    @Schema(description = "大单分析")
    private LargeTradeAnalysis largeTradeAnalysis;

    @Schema(description = "市场微观结构")
    private MarketMicrostructure marketMicrostructure;

    @Schema(description = "流动性指标")
    private LiquidityMetrics liquidityMetrics;

    @Schema(description = "异常交易检测")
    private AnomalousTradeDetection anomalousTradeDetection;

    @Schema(description = "交易模式分析")
    private TradingPatternAnalysis tradingPatternAnalysis;

    @Schema(description = "市场影响分析")
    private MarketImpactAnalysis marketImpactAnalysis;

    @Schema(description = "数据质量")
    private DataQuality dataQuality;

    @Schema(description = "查询时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime queryTime;

    @Schema(description = "数据来源")
    private String dataSource;

    @Schema(description = "数据版本")
    private String dataVersion;

    @Schema(description = "缓存状态")
    private String cacheStatus;

    @Schema(description = "查询耗时(毫秒)")
    private Long queryDuration;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 交易记录
     */
    @Data
    @Schema(description = "交易记录")
    public static class TradeRecord {
        
        @Schema(description = "交易ID")
        private String tradeId;
        
        @Schema(description = "订单ID")
        private String orderId;
        
        @Schema(description = "交易价格")
        private BigDecimal price;
        
        @Schema(description = "交易数量")
        private BigDecimal quantity;
        
        @Schema(description = "交易金额")
        private BigDecimal amount;
        
        @Schema(description = "交易方向")
        private String side;
        
        @Schema(description = "交易类型")
        private String tradeType;
        
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeTime;
        
        @Schema(description = "时间戳")
        private Long timestamp;
        
        @Schema(description = "是否为买方成交")
        private Boolean isBuyerMaker;
        
        @Schema(description = "是否为最优价格成交")
        private Boolean isBestMatch;
        
        @Schema(description = "手续费")
        private BigDecimal fee;
        
        @Schema(description = "手续费币种")
        private String feeAsset;
        
        @Schema(description = "手续费率")
        private BigDecimal feeRate;
        
        @Schema(description = "买方用户ID")
        private String buyerUserId;
        
        @Schema(description = "卖方用户ID")
        private String sellerUserId;
        
        @Schema(description = "是否为大单")
        private Boolean isLargeOrder;
        
        @Schema(description = "市场影响")
        private BigDecimal marketImpact;
        
        @Schema(description = "价格偏离度")
        private BigDecimal priceDeviation;
        
        @Schema(description = "流动性消耗")
        private BigDecimal liquidityConsumed;
        
        @Schema(description = "交易延迟")
        private Long tradeLatency;
        
        @Schema(description = "交易来源")
        private String tradeSource;
        
        @Schema(description = "交易标签")
        private List<String> tradeTags;
    }

    /**
     * 交易统计
     */
    @Data
    @Schema(description = "交易统计")
    public static class TradeStatistics {
        
        @Schema(description = "总交易笔数")
        private Long totalTrades;
        
        @Schema(description = "总成交量")
        private BigDecimal totalVolume;
        
        @Schema(description = "总成交额")
        private BigDecimal totalAmount;
        
        @Schema(description = "平均交易价格")
        private BigDecimal averagePrice;
        
        @Schema(description = "加权平均价格")
        private BigDecimal weightedAveragePrice;
        
        @Schema(description = "最高价")
        private BigDecimal highPrice;
        
        @Schema(description = "最低价")
        private BigDecimal lowPrice;
        
        @Schema(description = "开盘价")
        private BigDecimal openPrice;
        
        @Schema(description = "收盘价")
        private BigDecimal closePrice;
        
        @Schema(description = "价格变化")
        private BigDecimal priceChange;
        
        @Schema(description = "价格变化百分比")
        private BigDecimal priceChangePercent;
        
        @Schema(description = "买单笔数")
        private Long buyTradeCount;
        
        @Schema(description = "卖单笔数")
        private Long sellTradeCount;
        
        @Schema(description = "买单成交量")
        private BigDecimal buyVolume;
        
        @Schema(description = "卖单成交量")
        private BigDecimal sellVolume;
        
        @Schema(description = "买卖比例")
        private BigDecimal buySellRatio;
        
        @Schema(description = "平均交易大小")
        private BigDecimal averageTradeSize;
        
        @Schema(description = "最大交易大小")
        private BigDecimal maxTradeSize;
        
        @Schema(description = "最小交易大小")
        private BigDecimal minTradeSize;
        
        @Schema(description = "交易大小标准差")
        private BigDecimal tradeSizeStdDev;
        
        @Schema(description = "交易频率")
        private BigDecimal tradeFrequency;
        
        @Schema(description = "平均交易间隔")
        private BigDecimal averageTradeInterval;
    }

    /**
     * 价格分析
     */
    @Data
    @Schema(description = "价格分析")
    public static class PriceAnalysis {
        
        @Schema(description = "价格趋势")
        private String priceTrend;
        
        @Schema(description = "趋势强度")
        private BigDecimal trendStrength;
        
        @Schema(description = "价格波动率")
        private BigDecimal volatility;
        
        @Schema(description = "价格振幅")
        private BigDecimal amplitude;
        
        @Schema(description = "价格分布")
        private List<PriceDistribution> priceDistribution;
        
        @Schema(description = "支撑位")
        private List<BigDecimal> supportLevels;
        
        @Schema(description = "阻力位")
        private List<BigDecimal> resistanceLevels;
        
        @Schema(description = "突破点")
        private List<BreakoutPoint> breakoutPoints;
        
        @Schema(description = "价格回归均值")
        private BigDecimal meanReversion;
        
        @Schema(description = "价格动量")
        private BigDecimal priceMomentum;
    }

    /**
     * 价格分布
     */
    @Data
    @Schema(description = "价格分布")
    public static class PriceDistribution {
        
        @Schema(description = "价格区间")
        private String priceRange;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 突破点
     */
    @Data
    @Schema(description = "突破点")
    public static class BreakoutPoint {
        
        @Schema(description = "突破价格")
        private BigDecimal breakoutPrice;
        
        @Schema(description = "突破时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime breakoutTime;
        
        @Schema(description = "突破方向")
        private String breakoutDirection;
        
        @Schema(description = "突破强度")
        private BigDecimal breakoutStrength;
        
        @Schema(description = "突破成交量")
        private BigDecimal breakoutVolume;
    }

    /**
     * 成交量分析
     */
    @Data
    @Schema(description = "成交量分析")
    public static class VolumeAnalysis {
        
        @Schema(description = "成交量趋势")
        private String volumeTrend;
        
        @Schema(description = "成交量波动率")
        private BigDecimal volumeVolatility;
        
        @Schema(description = "成交量分布")
        private List<VolumeDistribution> volumeDistribution;
        
        @Schema(description = "异常成交量")
        private List<VolumeAnomaly> volumeAnomalies;
        
        @Schema(description = "成交量价格相关性")
        private BigDecimal volumePriceCorrelation;
        
        @Schema(description = "成交量集中度")
        private BigDecimal volumeConcentration;
        
        @Schema(description = "平均成交量")
        private BigDecimal averageVolume;
        
        @Schema(description = "成交量标准差")
        private BigDecimal volumeStdDev;
    }

    /**
     * 成交量分布
     */
    @Data
    @Schema(description = "成交量分布")
    public static class VolumeDistribution {
        
        @Schema(description = "成交量区间")
        private String volumeRange;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 成交量异常
     */
    @Data
    @Schema(description = "成交量异常")
    public static class VolumeAnomaly {
        
        @Schema(description = "异常时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime anomalyTime;
        
        @Schema(description = "异常成交量")
        private BigDecimal anomalyVolume;
        
        @Schema(description = "正常成交量")
        private BigDecimal normalVolume;
        
        @Schema(description = "异常倍数")
        private BigDecimal anomalyMultiple;
        
        @Schema(description = "异常类型")
        private String anomalyType;
    }

    /**
     * 时间分布
     */
    @Data
    @Schema(description = "时间分布")
    public static class TimeDistribution {
        
        @Schema(description = "小时分布")
        private List<HourlyDistribution> hourlyDistribution;
        
        @Schema(description = "日分布")
        private List<DailyDistribution> dailyDistribution;
        
        @Schema(description = "周分布")
        private List<WeeklyDistribution> weeklyDistribution;
        
        @Schema(description = "活跃时段")
        private List<String> activeTimeSlots;
        
        @Schema(description = "交易高峰期")
        private List<String> peakTradingHours;
    }

    /**
     * 小时分布
     */
    @Data
    @Schema(description = "小时分布")
    public static class HourlyDistribution {
        
        @Schema(description = "小时")
        private Integer hour;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "平均价格")
        private BigDecimal averagePrice;
    }

    /**
     * 日分布
     */
    @Data
    @Schema(description = "日分布")
    public static class DailyDistribution {
        
        @Schema(description = "日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime date;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "平均价格")
        private BigDecimal averagePrice;
    }

    /**
     * 周分布
     */
    @Data
    @Schema(description = "周分布")
    public static class WeeklyDistribution {
        
        @Schema(description = "星期几")
        private Integer dayOfWeek;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "成交量")
        private BigDecimal volume;
        
        @Schema(description = "平均价格")
        private BigDecimal averagePrice;
    }

    /**
     * 大单分析
     */
    @Data
    @Schema(description = "大单分析")
    public static class LargeTradeAnalysis {
        
        @Schema(description = "大单定义阈值")
        private BigDecimal largeTradeThreshold;
        
        @Schema(description = "大单笔数")
        private Long largeTradeCount;
        
        @Schema(description = "大单成交量")
        private BigDecimal largeTradeVolume;
        
        @Schema(description = "大单占比")
        private BigDecimal largeTradeRatio;
        
        @Schema(description = "大单平均大小")
        private BigDecimal averageLargeTradeSize;
        
        @Schema(description = "大单价格影响")
        private BigDecimal largeTradePriceImpact;
        
        @Schema(description = "大单时间分布")
        private List<LargeTradeTimeDistribution> largeTradeTimeDistribution;
    }

    /**
     * 大单时间分布
     */
    @Data
    @Schema(description = "大单时间分布")
    public static class LargeTradeTimeDistribution {
        
        @Schema(description = "时间段")
        private String timeSlot;
        
        @Schema(description = "大单笔数")
        private Long largeTradeCount;
        
        @Schema(description = "大单成交量")
        private BigDecimal largeTradeVolume;
    }

    /**
     * 市场微观结构
     */
    @Data
    @Schema(description = "市场微观结构")
    public static class MarketMicrostructure {
        
        @Schema(description = "买卖价差")
        private BigDecimal bidAskSpread;
        
        @Schema(description = "有效价差")
        private BigDecimal effectiveSpread;
        
        @Schema(description = "实现价差")
        private BigDecimal realizedSpread;
        
        @Schema(description = "价格发现效率")
        private BigDecimal priceDiscoveryEfficiency;
        
        @Schema(description = "信息不对称性")
        private BigDecimal informationAsymmetry;
        
        @Schema(description = "订单流毒性")
        private BigDecimal orderFlowToxicity;
    }

    /**
     * 流动性指标
     */
    @Data
    @Schema(description = "流动性指标")
    public static class LiquidityMetrics {
        
        @Schema(description = "流动性比率")
        private BigDecimal liquidityRatio;
        
        @Schema(description = "市场深度")
        private BigDecimal marketDepth;
        
        @Schema(description = "价格影响")
        private BigDecimal priceImpact;
        
        @Schema(description = "弹性指标")
        private BigDecimal resilienceMetric;
        
        @Schema(description = "流动性成本")
        private BigDecimal liquidityCost;
        
        @Schema(description = "流动性风险")
        private BigDecimal liquidityRisk;
    }

    /**
     * 异常交易检测
     */
    @Data
    @Schema(description = "异常交易检测")
    public static class AnomalousTradeDetection {
        
        @Schema(description = "异常交易数量")
        private Long anomalousTradeCount;
        
        @Schema(description = "异常交易列表")
        private List<AnomalousTrade> anomalousTrades;
        
        @Schema(description = "异常类型统计")
        private List<AnomalyTypeStatistics> anomalyTypeStatistics;
    }

    /**
     * 异常交易
     */
    @Data
    @Schema(description = "异常交易")
    public static class AnomalousTrade {
        
        @Schema(description = "交易ID")
        private String tradeId;
        
        @Schema(description = "异常类型")
        private String anomalyType;
        
        @Schema(description = "异常评分")
        private BigDecimal anomalyScore;
        
        @Schema(description = "异常原因")
        private String anomalyReason;
        
        @Schema(description = "交易时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime tradeTime;
    }

    /**
     * 异常类型统计
     */
    @Data
    @Schema(description = "异常类型统计")
    public static class AnomalyTypeStatistics {
        
        @Schema(description = "异常类型")
        private String anomalyType;
        
        @Schema(description = "异常数量")
        private Long anomalyCount;
        
        @Schema(description = "异常占比")
        private BigDecimal anomalyPercentage;
    }

    /**
     * 交易模式分析
     */
    @Data
    @Schema(description = "交易模式分析")
    public static class TradingPatternAnalysis {
        
        @Schema(description = "识别的交易模式")
        private List<TradingPattern> identifiedPatterns;
        
        @Schema(description = "模式置信度")
        private BigDecimal patternConfidence;
        
        @Schema(description = "模式持续时间")
        private String patternDuration;
    }

    /**
     * 交易模式
     */
    @Data
    @Schema(description = "交易模式")
    public static class TradingPattern {
        
        @Schema(description = "模式名称")
        private String patternName;
        
        @Schema(description = "模式类型")
        private String patternType;
        
        @Schema(description = "模式强度")
        private BigDecimal patternStrength;
        
        @Schema(description = "模式描述")
        private String patternDescription;
    }

    /**
     * 市场影响分析
     */
    @Data
    @Schema(description = "市场影响分析")
    public static class MarketImpactAnalysis {
        
        @Schema(description = "平均市场影响")
        private BigDecimal averageMarketImpact;
        
        @Schema(description = "最大市场影响")
        private BigDecimal maxMarketImpact;
        
        @Schema(description = "市场影响分布")
        private List<MarketImpactDistribution> marketImpactDistribution;
        
        @Schema(description = "影响持续时间")
        private BigDecimal impactDuration;
    }

    /**
     * 市场影响分布
     */
    @Data
    @Schema(description = "市场影响分布")
    public static class MarketImpactDistribution {
        
        @Schema(description = "影响范围")
        private String impactRange;
        
        @Schema(description = "交易笔数")
        private Long tradeCount;
        
        @Schema(description = "占比")
        private BigDecimal percentage;
    }

    /**
     * 数据质量
     */
    @Data
    @Schema(description = "数据质量")
    public static class DataQuality {
        
        @Schema(description = "数据完整性")
        private BigDecimal dataCompleteness;
        
        @Schema(description = "数据准确性")
        private BigDecimal dataAccuracy;
        
        @Schema(description = "数据一致性")
        private BigDecimal dataConsistency;
        
        @Schema(description = "数据及时性")
        private BigDecimal dataTimeliness;
        
        @Schema(description = "缺失数据数量")
        private Long missingDataCount;
        
        @Schema(description = "异常数据数量")
        private Long anomalousDataCount;
        
        @Schema(description = "数据质量评分")
        private BigDecimal dataQualityScore;
    }
}