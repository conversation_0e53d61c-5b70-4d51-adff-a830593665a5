package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 市场概览响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "市场概览响应")
public class MarketOverviewResponse {

    @Schema(description = "总市值")
    private BigDecimal totalMarketCap;

    @Schema(description = "24小时总成交量")
    private BigDecimal total24hVolume;

    @Schema(description = "24小时总成交额")
    private BigDecimal total24hAmount;

    @Schema(description = "活跃交易对数量")
    private Integer activeSymbolCount;

    @Schema(description = "总交易对数量")
    private Integer totalSymbolCount;

    @Schema(description = "24小时涨跌幅")
    private BigDecimal marketChangePercent;

    @Schema(description = "市场情绪指数")
    private BigDecimal sentimentIndex;

    @Schema(description = "恐慌贪婪指数")
    private BigDecimal fearGreedIndex;

    @Schema(description = "比特币市值占比")
    private BigDecimal btcDominance;

    @Schema(description = "以太坊市值占比")
    private BigDecimal ethDominance;

    @Schema(description = "稳定币市值占比")
    private BigDecimal stablecoinDominance;

    @Schema(description = "DeFi代币市值")
    private BigDecimal defiMarketCap;

    @Schema(description = "NFT市场总值")
    private BigDecimal nftMarketCap;

    @Schema(description = "新上线代币数量")
    private Integer newListingCount;

    @Schema(description = "热门交易对")
    private List<HotSymbol> hotSymbols;

    @Schema(description = "涨幅榜前10")
    private List<TopGainer> topGainers;

    @Schema(description = "跌幅榜前10")
    private List<TopLoser> topLosers;

    @Schema(description = "成交量榜前10")
    private List<TopVolume> topVolumes;

    @Schema(description = "市场分类统计")
    private List<CategoryStats> categoryStats;

    @Schema(description = "全球交易所排名")
    private Integer exchangeRanking;

    @Schema(description = "平台总用户数")
    private Long totalUsers;

    @Schema(description = "24小时活跃用户数")
    private Long activeUsers24h;

    @Schema(description = "24小时新注册用户数")
    private Long newUsers24h;

    @Schema(description = "平台总资产")
    private BigDecimal totalAssets;

    @Schema(description = "风险提示等级")
    private String riskLevel;

    @Schema(description = "系统状态")
    private String systemStatus;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 热门交易对
     */
    @Data
    @Schema(description = "热门交易对")
    public static class HotSymbol {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal price;
        
        @Schema(description = "24小时涨跌幅")
        private BigDecimal changePercent;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume;
        
        @Schema(description = "热度评分")
        private BigDecimal hotScore;
    }

    /**
     * 涨幅榜
     */
    @Data
    @Schema(description = "涨幅榜")
    public static class TopGainer {
        
        @Schema(description = "排名")
        private Integer rank;
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal price;
        
        @Schema(description = "24小时涨幅")
        private BigDecimal changePercent;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume;
    }

    /**
     * 跌幅榜
     */
    @Data
    @Schema(description = "跌幅榜")
    public static class TopLoser {
        
        @Schema(description = "排名")
        private Integer rank;
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal price;
        
        @Schema(description = "24小时跌幅")
        private BigDecimal changePercent;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume;
    }

    /**
     * 成交量榜
     */
    @Data
    @Schema(description = "成交量榜")
    public static class TopVolume {
        
        @Schema(description = "排名")
        private Integer rank;
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "当前价格")
        private BigDecimal price;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount;
    }

    /**
     * 分类统计
     */
    @Data
    @Schema(description = "分类统计")
    public static class CategoryStats {
        
        @Schema(description = "分类名称")
        private String categoryName;
        
        @Schema(description = "分类代码")
        private String categoryCode;
        
        @Schema(description = "交易对数量")
        private Integer symbolCount;
        
        @Schema(description = "总市值")
        private BigDecimal marketCap;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时涨跌幅")
        private BigDecimal changePercent;
    }
}