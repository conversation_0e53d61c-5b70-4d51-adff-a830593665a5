package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 交易所信息响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易所信息响应")
public class ExchangeInfoResponse {

    @Schema(description = "交易所名称")
    private String exchangeName;

    @Schema(description = "交易所代码")
    private String exchangeCode;

    @Schema(description = "交易所版本")
    private String version;

    @Schema(description = "API版本")
    private String apiVersion;

    @Schema(description = "交易所状态")
    private String status;

    @Schema(description = "交易所描述")
    private String description;

    @Schema(description = "官方网站")
    private String officialWebsite;

    @Schema(description = "支持的交易类型")
    private List<String> supportedTradeTypes;

    @Schema(description = "支持的订单类型")
    private List<String> supportedOrderTypes;

    @Schema(description = "支持的时间周期")
    private List<String> supportedTimeframes;

    @Schema(description = "支持的货币列表")
    private List<AssetInfo> supportedAssets;

    @Schema(description = "交易对列表")
    private List<TradingPairInfo> tradingPairs;

    @Schema(description = "交易规则")
    private TradingRules tradingRules;

    @Schema(description = "费率信息")
    private FeeStructure feeStructure;

    @Schema(description = "API限制")
    private ApiLimits apiLimits;

    @Schema(description = "系统配置")
    private SystemConfiguration systemConfiguration;

    @Schema(description = "安全设置")
    private SecuritySettings securitySettings;

    @Schema(description = "合规信息")
    private ComplianceInfo complianceInfo;

    @Schema(description = "服务条款")
    private TermsOfService termsOfService;

    @Schema(description = "联系信息")
    private ContactInfo contactInfo;

    @Schema(description = "社交媒体")
    private SocialMediaInfo socialMediaInfo;

    @Schema(description = "技术信息")
    private TechnicalInfo technicalInfo;

    @Schema(description = "运营统计")
    private OperationalStatistics operationalStatistics;

    @Schema(description = "维护信息")
    private MaintenanceInfo maintenanceInfo;

    @Schema(description = "公告信息")
    private List<AnnouncementInfo> announcements;

    @Schema(description = "服务器时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime serverTime;

    @Schema(description = "时区")
    private String timezone;

    @Schema(description = "最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateTime;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 资产信息
     */
    @Data
    @Schema(description = "资产信息")
    public static class AssetInfo {
        
        @Schema(description = "资产代码")
        private String assetCode;
        
        @Schema(description = "资产名称")
        private String assetName;
        
        @Schema(description = "资产类型")
        private String assetType;
        
        @Schema(description = "是否支持充值")
        private Boolean depositEnabled;
        
        @Schema(description = "是否支持提现")
        private Boolean withdrawEnabled;
        
        @Schema(description = "是否支持交易")
        private Boolean tradingEnabled;
        
        @Schema(description = "最小充值金额")
        private BigDecimal minDepositAmount;
        
        @Schema(description = "最小提现金额")
        private BigDecimal minWithdrawAmount;
        
        @Schema(description = "提现手续费")
        private BigDecimal withdrawFee;
        
        @Schema(description = "充值确认数")
        private Integer depositConfirmations;
        
        @Schema(description = "提现确认数")
        private Integer withdrawConfirmations;
        
        @Schema(description = "网络列表")
        private List<NetworkInfo> networks;
    }

    /**
     * 网络信息
     */
    @Data
    @Schema(description = "网络信息")
    public static class NetworkInfo {
        
        @Schema(description = "网络名称")
        private String networkName;
        
        @Schema(description = "网络代码")
        private String networkCode;
        
        @Schema(description = "是否支持充值")
        private Boolean depositEnabled;
        
        @Schema(description = "是否支持提现")
        private Boolean withdrawEnabled;
        
        @Schema(description = "最小充值金额")
        private BigDecimal minDepositAmount;
        
        @Schema(description = "最小提现金额")
        private BigDecimal minWithdrawAmount;
        
        @Schema(description = "提现手续费")
        private BigDecimal withdrawFee;
        
        @Schema(description = "充值确认数")
        private Integer depositConfirmations;
        
        @Schema(description = "提现确认数")
        private Integer withdrawConfirmations;
    }

    /**
     * 交易对信息
     */
    @Data
    @Schema(description = "交易对信息")
    public static class TradingPairInfo {
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "基础货币")
        private String baseAsset;
        
        @Schema(description = "计价货币")
        private String quoteAsset;
        
        @Schema(description = "交易状态")
        private String status;
        
        @Schema(description = "价格精度")
        private Integer pricePrecision;
        
        @Schema(description = "数量精度")
        private Integer quantityPrecision;
        
        @Schema(description = "最小交易数量")
        private BigDecimal minTradeQuantity;
        
        @Schema(description = "最大交易数量")
        private BigDecimal maxTradeQuantity;
        
        @Schema(description = "最小交易金额")
        private BigDecimal minTradeAmount;
        
        @Schema(description = "最大交易金额")
        private BigDecimal maxTradeAmount;
        
        @Schema(description = "支持的订单类型")
        private List<String> supportedOrderTypes;
    }

    /**
     * 交易规则
     */
    @Data
    @Schema(description = "交易规则")
    public static class TradingRules {
        
        @Schema(description = "交易时间")
        private TradingHours tradingHours;
        
        @Schema(description = "最大订单数量")
        private Integer maxOrderCount;
        
        @Schema(description = "最大持仓数量")
        private Integer maxPositionCount;
        
        @Schema(description = "价格保护机制")
        private PriceProtection priceProtection;
        
        @Schema(description = "风险控制")
        private RiskControl riskControl;
        
        @Schema(description = "交易限制")
        private Map<String, Object> tradingRestrictions;
    }

    /**
     * 交易时间
     */
    @Data
    @Schema(description = "交易时间")
    public static class TradingHours {
        
        @Schema(description = "是否24小时交易")
        private Boolean is24Hours;
        
        @Schema(description = "开市时间")
        private String openTime;
        
        @Schema(description = "闭市时间")
        private String closeTime;
        
        @Schema(description = "时区")
        private String timezone;
        
        @Schema(description = "休市日")
        private List<String> holidays;
    }

    /**
     * 价格保护
     */
    @Data
    @Schema(description = "价格保护")
    public static class PriceProtection {
        
        @Schema(description = "最大价格偏差")
        private BigDecimal maxPriceDeviation;
        
        @Schema(description = "价格熔断机制")
        private Boolean circuitBreakerEnabled;
        
        @Schema(description = "熔断阈值")
        private BigDecimal circuitBreakerThreshold;
        
        @Schema(description = "熔断时间")
        private Integer circuitBreakerDuration;
    }

    /**
     * 风险控制
     */
    @Data
    @Schema(description = "风险控制")
    public static class RiskControl {
        
        @Schema(description = "最大杠杆倍数")
        private BigDecimal maxLeverage;
        
        @Schema(description = "强制平仓比例")
        private BigDecimal liquidationRatio;
        
        @Schema(description = "保证金要求")
        private BigDecimal marginRequirement;
        
        @Schema(description = "风险等级限制")
        private Map<String, Object> riskLevelLimits;
    }

    /**
     * 费率结构
     */
    @Data
    @Schema(description = "费率结构")
    public static class FeeStructure {
        
        @Schema(description = "现货交易费率")
        private TradingFees spotTradingFees;
        
        @Schema(description = "杠杆交易费率")
        private TradingFees marginTradingFees;
        
        @Schema(description = "期货交易费率")
        private TradingFees futuresTradingFees;
        
        @Schema(description = "充值费率")
        private Map<String, BigDecimal> depositFees;
        
        @Schema(description = "提现费率")
        private Map<String, BigDecimal> withdrawFees;
        
        @Schema(description = "VIP费率折扣")
        private List<VipFeeDiscount> vipFeeDiscounts;
    }

    /**
     * 交易费率
     */
    @Data
    @Schema(description = "交易费率")
    public static class TradingFees {
        
        @Schema(description = "Maker费率")
        private BigDecimal makerFee;
        
        @Schema(description = "Taker费率")
        private BigDecimal takerFee;
        
        @Schema(description = "最小费用")
        private BigDecimal minFee;
        
        @Schema(description = "最大费用")
        private BigDecimal maxFee;
    }

    /**
     * VIP费率折扣
     */
    @Data
    @Schema(description = "VIP费率折扣")
    public static class VipFeeDiscount {
        
        @Schema(description = "VIP等级")
        private String vipLevel;
        
        @Schema(description = "30天交易量要求")
        private BigDecimal volumeRequirement;
        
        @Schema(description = "持币要求")
        private BigDecimal holdingRequirement;
        
        @Schema(description = "Maker费率")
        private BigDecimal makerFee;
        
        @Schema(description = "Taker费率")
        private BigDecimal takerFee;
    }

    /**
     * API限制
     */
    @Data
    @Schema(description = "API限制")
    public static class ApiLimits {
        
        @Schema(description = "请求频率限制")
        private RateLimit rateLimit;
        
        @Schema(description = "订单频率限制")
        private RateLimit orderRateLimit;
        
        @Schema(description = "权重限制")
        private WeightLimit weightLimit;
        
        @Schema(description = "IP限制")
        private IpLimit ipLimit;
    }

    /**
     * 频率限制
     */
    @Data
    @Schema(description = "频率限制")
    public static class RateLimit {
        
        @Schema(description = "时间窗口（秒）")
        private Integer timeWindow;
        
        @Schema(description = "最大请求数")
        private Integer maxRequests;
        
        @Schema(description = "限制类型")
        private String limitType;
    }

    /**
     * 权重限制
     */
    @Data
    @Schema(description = "权重限制")
    public static class WeightLimit {
        
        @Schema(description = "时间窗口（秒）")
        private Integer timeWindow;
        
        @Schema(description = "最大权重")
        private Integer maxWeight;
        
        @Schema(description = "接口权重映射")
        private Map<String, Integer> endpointWeights;
    }

    /**
     * IP限制
     */
    @Data
    @Schema(description = "IP限制")
    public static class IpLimit {
        
        @Schema(description = "是否启用IP白名单")
        private Boolean whitelistEnabled;
        
        @Schema(description = "最大IP数量")
        private Integer maxIpCount;
        
        @Schema(description = "地区限制")
        private List<String> regionRestrictions;
    }

    /**
     * 系统配置
     */
    @Data
    @Schema(description = "系统配置")
    public static class SystemConfiguration {
        
        @Schema(description = "系统精度")
        private Integer systemPrecision;
        
        @Schema(description = "默认时区")
        private String defaultTimezone;
        
        @Schema(description = "支持的语言")
        private List<String> supportedLanguages;
        
        @Schema(description = "默认语言")
        private String defaultLanguage;
        
        @Schema(description = "数据保留期")
        private Integer dataRetentionDays;
        
        @Schema(description = "系统参数")
        private Map<String, Object> systemParameters;
    }

    /**
     * 安全设置
     */
    @Data
    @Schema(description = "安全设置")
    public static class SecuritySettings {
        
        @Schema(description = "是否启用2FA")
        private Boolean twoFactorAuthEnabled;
        
        @Schema(description = "密码复杂度要求")
        private PasswordPolicy passwordPolicy;
        
        @Schema(description = "会话超时时间")
        private Integer sessionTimeout;
        
        @Schema(description = "登录失败锁定")
        private LoginLockPolicy loginLockPolicy;
        
        @Schema(description = "加密算法")
        private String encryptionAlgorithm;
    }

    /**
     * 密码策略
     */
    @Data
    @Schema(description = "密码策略")
    public static class PasswordPolicy {
        
        @Schema(description = "最小长度")
        private Integer minLength;
        
        @Schema(description = "最大长度")
        private Integer maxLength;
        
        @Schema(description = "是否需要大写字母")
        private Boolean requireUppercase;
        
        @Schema(description = "是否需要小写字母")
        private Boolean requireLowercase;
        
        @Schema(description = "是否需要数字")
        private Boolean requireNumbers;
        
        @Schema(description = "是否需要特殊字符")
        private Boolean requireSpecialChars;
    }

    /**
     * 登录锁定策略
     */
    @Data
    @Schema(description = "登录锁定策略")
    public static class LoginLockPolicy {
        
        @Schema(description = "最大失败次数")
        private Integer maxFailedAttempts;
        
        @Schema(description = "锁定时间（分钟）")
        private Integer lockoutDuration;
        
        @Schema(description = "是否启用验证码")
        private Boolean captchaEnabled;
    }

    /**
     * 合规信息
     */
    @Data
    @Schema(description = "合规信息")
    public static class ComplianceInfo {
        
        @Schema(description = "监管许可证")
        private List<RegulatoryLicense> licenses;
        
        @Schema(description = "合规等级")
        private String complianceLevel;
        
        @Schema(description = "KYC要求")
        private KycRequirements kycRequirements;
        
        @Schema(description = "AML政策")
        private String amlPolicy;
        
        @Schema(description = "数据保护")
        private DataProtection dataProtection;
    }

    /**
     * 监管许可证
     */
    @Data
    @Schema(description = "监管许可证")
    public static class RegulatoryLicense {
        
        @Schema(description = "许可证类型")
        private String licenseType;
        
        @Schema(description = "许可证号")
        private String licenseNumber;
        
        @Schema(description = "发证机构")
        private String issuingAuthority;
        
        @Schema(description = "发证日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime issueDate;
        
        @Schema(description = "到期日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime expiryDate;
        
        @Schema(description = "适用地区")
        private List<String> applicableRegions;
    }

    /**
     * KYC要求
     */
    @Data
    @Schema(description = "KYC要求")
    public static class KycRequirements {
        
        @Schema(description = "基础KYC要求")
        private String basicKycRequirement;
        
        @Schema(description = "高级KYC要求")
        private String advancedKycRequirement;
        
        @Schema(description = "企业KYC要求")
        private String corporateKycRequirement;
        
        @Schema(description = "KYC等级限制")
        private Map<String, Object> kycLevelLimits;
    }

    /**
     * 数据保护
     */
    @Data
    @Schema(description = "数据保护")
    public static class DataProtection {
        
        @Schema(description = "隐私政策")
        private String privacyPolicy;
        
        @Schema(description = "数据加密")
        private String dataEncryption;
        
        @Schema(description = "数据备份")
        private String dataBackup;
        
        @Schema(description = "数据删除政策")
        private String dataDeletionPolicy;
    }

    /**
     * 服务条款
     */
    @Data
    @Schema(description = "服务条款")
    public static class TermsOfService {
        
        @Schema(description = "服务条款版本")
        private String version;
        
        @Schema(description = "生效日期")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime effectiveDate;
        
        @Schema(description = "服务条款URL")
        private String termsUrl;
        
        @Schema(description = "隐私政策URL")
        private String privacyPolicyUrl;
        
        @Schema(description = "风险披露URL")
        private String riskDisclosureUrl;
    }

    /**
     * 联系信息
     */
    @Data
    @Schema(description = "联系信息")
    public static class ContactInfo {
        
        @Schema(description = "客服邮箱")
        private String supportEmail;
        
        @Schema(description = "客服电话")
        private String supportPhone;
        
        @Schema(description = "在线客服")
        private String liveChat;
        
        @Schema(description = "工作时间")
        private String businessHours;
        
        @Schema(description = "紧急联系方式")
        private String emergencyContact;
        
        @Schema(description = "办公地址")
        private String officeAddress;
    }

    /**
     * 社交媒体信息
     */
    @Data
    @Schema(description = "社交媒体信息")
    public static class SocialMediaInfo {
        
        @Schema(description = "Twitter")
        private String twitter;
        
        @Schema(description = "Telegram")
        private String telegram;
        
        @Schema(description = "Discord")
        private String discord;
        
        @Schema(description = "Reddit")
        private String reddit;
        
        @Schema(description = "Medium")
        private String medium;
        
        @Schema(description = "YouTube")
        private String youtube;
        
        @Schema(description = "LinkedIn")
        private String linkedin;
    }

    /**
     * 技术信息
     */
    @Data
    @Schema(description = "技术信息")
    public static class TechnicalInfo {
        
        @Schema(description = "系统架构")
        private String systemArchitecture;
        
        @Schema(description = "数据库类型")
        private String databaseType;
        
        @Schema(description = "缓存系统")
        private String cacheSystem;
        
        @Schema(description = "消息队列")
        private String messageQueue;
        
        @Schema(description = "负载均衡")
        private String loadBalancer;
        
        @Schema(description = "CDN提供商")
        private String cdnProvider;
        
        @Schema(description = "云服务提供商")
        private String cloudProvider;
    }

    /**
     * 运营统计
     */
    @Data
    @Schema(description = "运营统计")
    public static class OperationalStatistics {
        
        @Schema(description = "成立时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime establishedDate;
        
        @Schema(description = "注册用户数")
        private Long totalUsers;
        
        @Schema(description = "日活跃用户数")
        private Integer dailyActiveUsers;
        
        @Schema(description = "支持的国家数")
        private Integer supportedCountries;
        
        @Schema(description = "累计交易量")
        private BigDecimal totalTradingVolume;
        
        @Schema(description = "系统可用性")
        private BigDecimal systemUptime;
    }

    /**
     * 维护信息
     */
    @Data
    @Schema(description = "维护信息")
    public static class MaintenanceInfo {
        
        @Schema(description = "是否在维护中")
        private Boolean isUnderMaintenance;
        
        @Schema(description = "维护开始时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime maintenanceStartTime;
        
        @Schema(description = "预计维护结束时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime estimatedEndTime;
        
        @Schema(description = "维护原因")
        private String maintenanceReason;
        
        @Schema(description = "影响的服务")
        private List<String> affectedServices;
    }

    /**
     * 公告信息
     */
    @Data
    @Schema(description = "公告信息")
    public static class AnnouncementInfo {
        
        @Schema(description = "公告ID")
        private String announcementId;
        
        @Schema(description = "公告标题")
        private String title;
        
        @Schema(description = "公告类型")
        private String type;
        
        @Schema(description = "重要程度")
        private String importance;
        
        @Schema(description = "发布时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime publishTime;
        
        @Schema(description = "公告链接")
        private String announcementUrl;
    }
}