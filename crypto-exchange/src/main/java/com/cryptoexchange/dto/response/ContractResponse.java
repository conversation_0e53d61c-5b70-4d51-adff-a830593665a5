package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ContractResponse {
    
    /**
     * 合约ID
     */
    private Long id;
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 基础资产
     */
    private String baseAsset;
    
    /**
     * 报价资产
     */
    private String quoteAsset;
    
    /**
     * 合约类型
     */
    private String contractType;
    
    /**
     * 合约状态
     */
    private String status;
    
    /**
     * 最小交易数量
     */
    private BigDecimal minQty;
    
    /**
     * 最大交易数量
     */
    private BigDecimal maxQty;
    
    /**
     * 数量精度
     */
    private Integer qtyPrecision;
    
    /**
     * 价格精度
     */
    private Integer pricePrecision;
    
    /**
     * 最小价格变动
     */
    private BigDecimal tickSize;
    
    /**
     * 最小数量变动
     */
    private BigDecimal stepSize;
    
    /**
     * 最大杠杆倍数
     */
    private Integer maxLeverage;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRate;
    
    /**
     * 初始保证金率
     */
    private BigDecimal requiredMarginRate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}