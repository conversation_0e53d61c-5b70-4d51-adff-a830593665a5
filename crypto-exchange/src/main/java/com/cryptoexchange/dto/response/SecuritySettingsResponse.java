package com.cryptoexchange.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 安全设置响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "安全设置响应")
public class SecuritySettingsResponse {

    @Schema(description = "是否设置了登录密码", example = "true")
    private Boolean hasLoginPassword;

    @Schema(description = "是否设置了交易密码", example = "false")
    private Boolean hasTradingPassword;

    @Schema(description = "是否绑定了邮箱", example = "true")
    private Boolean hasEmail;

    @Schema(description = "是否绑定了手机号", example = "false")
    private Boolean hasPhone;

    @Schema(description = "是否启用了双因子认证", example = "false")
    private Boolean hasTwoFactorAuth;

    @Schema(description = "邮箱地址（脱敏）", example = "u***@example.com")
    private String maskedEmail;

    @Schema(description = "手机号（脱敏）", example = "138****1234")
    private String maskedPhone;

    @Schema(description = "双因子认证类型", example = "TOTP")
    private String twoFactorAuthType;

    @Schema(description = "最后登录时间")
    private String lastLoginTime;

    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    public SecuritySettingsResponse() {}

    public SecuritySettingsResponse(Boolean hasLoginPassword, Boolean hasTradingPassword, 
                                  Boolean hasEmail, Boolean hasPhone, Boolean hasTwoFactorAuth) {
        this.hasLoginPassword = hasLoginPassword;
        this.hasTradingPassword = hasTradingPassword;
        this.hasEmail = hasEmail;
        this.hasPhone = hasPhone;
        this.hasTwoFactorAuth = hasTwoFactorAuth;
    }
}