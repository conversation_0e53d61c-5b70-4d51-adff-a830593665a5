package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 管理员用户响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "管理员用户响应")
public class AdminUserResponse {

    @Schema(description = "用户ID", example = "123456")
    private Long userId;

    @Schema(description = "用户名", example = "john_doe")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "+1234567890")
    private String phone;

    @Schema(description = "昵称", example = "John")
    private String nickname;

    @Schema(description = "真实姓名", example = "John Doe")
    private String realName;

    @Schema(description = "用户状态", example = "ACTIVE")
    private String status;

    @Schema(description = "用户等级", example = "VIP1")
    private String userLevel;

    @Schema(description = "KYC状态", example = "VERIFIED")
    private String kycStatus;

    @Schema(description = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registerTime;

    @Schema(description = "最后登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后活跃时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActiveTime;

    @Schema(description = "注册IP", example = "***********")
    private String registerIp;

    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    @Schema(description = "国家/地区", example = "CN")
    private String country;

    @Schema(description = "推荐人ID", example = "123455")
    private Long referrerId;

    @Schema(description = "推荐人用户名", example = "referrer_user")
    private String referrerUsername;

    @Schema(description = "推荐码", example = "ABC123")
    private String referralCode;

    @Schema(description = "总资产价值", example = "10000.50")
    private BigDecimal totalAssetValue;

    @Schema(description = "总交易量", example = "50000.00")
    private BigDecimal totalTradeVolume;

    @Schema(description = "总交易次数", example = "150")
    private Integer totalTradeCount;

    @Schema(description = "总手续费", example = "25.75")
    private BigDecimal totalFee;

    @Schema(description = "风险等级", example = "LOW")
    private String riskLevel;

    @Schema(description = "是否VIP", example = "true")
    private Boolean isVip;

    @Schema(description = "是否机器人", example = "false")
    private Boolean isBot;

    @Schema(description = "是否被冻结", example = "false")
    private Boolean isFrozen;

    @Schema(description = "冻结原因")
    private String frozenReason;

    @Schema(description = "冻结时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime frozenTime;

    @Schema(description = "解冻时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime unfrozenTime;

    @Schema(description = "用户标签")
    private List<String> tags;

    @Schema(description = "备注信息")
    private String remarks;

    @Schema(description = "扩展信息")
    private Map<String, Object> extraInfo;

    @Schema(description = "创建人", example = "admin")
    private String createdBy;

    @Schema(description = "更新人", example = "admin")
    private String updatedBy;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    public AdminUserResponse() {}

    public AdminUserResponse(Long userId, String username, String email, String status, 
                           LocalDateTime registerTime, BigDecimal totalAssetValue) {
        this.userId = userId;
        this.username = username;
        this.email = email;
        this.status = status;
        this.registerTime = registerTime;
        this.totalAssetValue = totalAssetValue;
        this.isVip = false;
        this.isBot = false;
        this.isFrozen = false;
    }
}