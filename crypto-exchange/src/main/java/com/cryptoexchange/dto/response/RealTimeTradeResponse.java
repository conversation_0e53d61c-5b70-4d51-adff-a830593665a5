package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 实时交易响应
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RealTimeTradeResponse {
    
    /**
     * 交易ID
     */
    private String tradeId;
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * 交易价格
     */
    private BigDecimal price;
    
    /**
     * 交易数量
     */
    private BigDecimal quantity;
    
    /**
     * 交易金额
     */
    private BigDecimal amount;
    
    /**
     * 交易时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime tradeTime;
    
    /**
     * 交易方向
     */
    private TradeDirection direction;
    
    /**
     * 交易类型
     */
    private TradeType tradeType;
    
    /**
     * 买方订单ID
     */
    private String buyOrderId;
    
    /**
     * 卖方订单ID
     */
    private String sellOrderId;
    
    /**
     * 买方用户ID
     */
    private String buyUserId;
    
    /**
     * 卖方用户ID
     */
    private String sellUserId;
    
    /**
     * 交易手续费
     */
    private TradeFee tradeFee;
    
    /**
     * 市场数据
     */
    private MarketData marketData;
    
    /**
     * 交易统计
     */
    private TradeStatistics tradeStats;
    
    /**
     * 流动性信息
     */
    private LiquidityInfo liquidityInfo;
    
    /**
     * 交易影响
     */
    private TradeImpact tradeImpact;
    
    /**
     * 风险指标
     */
    private RiskMetrics riskMetrics;
    
    /**
     * 交易质量
     */
    private TradeQuality tradeQuality;
    
    /**
     * 延迟信息
     */
    private LatencyInfo latencyInfo;
    
    /**
     * 交易标签
     */
    private List<String> tradeTags;
    
    /**
     * 交易元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 交易方向
     */
    public enum TradeDirection {
        BUY("买入"),
        SELL("卖出");
        
        private final String description;
        
        TradeDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 交易类型
     */
    public enum TradeType {
        MARKET("市价交易"),
        LIMIT("限价交易"),
        STOP("止损交易"),
        STOP_LIMIT("止损限价交易"),
        ICEBERG("冰山交易"),
        TWAP("时间加权平均价格交易"),
        VWAP("成交量加权平均价格交易"),
        ALGORITHMIC("算法交易"),
        CROSS("交叉交易"),
        BLOCK("大宗交易");
        
        private final String description;
        
        TradeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 交易手续费
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeFee {
        /**
         * 买方手续费
         */
        private BigDecimal buyerFee;
        
        /**
         * 卖方手续费
         */
        private BigDecimal sellerFee;
        
        /**
         * 手续费币种
         */
        private String feeCurrency;
        
        /**
         * 手续费率
         */
        private BigDecimal feeRate;
        
        /**
         * 手续费类型
         */
        private FeeType feeType;
        
        /**
         * 折扣信息
         */
        private FeeDiscount discount;
    }
    
    /**
     * 手续费类型
     */
    public enum FeeType {
        MAKER("挂单手续费"),
        TAKER("吃单手续费"),
        FLAT("固定手续费"),
        PERCENTAGE("百分比手续费");
        
        private final String description;
        
        FeeType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 手续费折扣
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeDiscount {
        private BigDecimal discountRate;
        private String discountReason;
        private BigDecimal originalFee;
        private BigDecimal discountedFee;
        private BigDecimal savedAmount;
    }
    
    /**
     * 市场数据
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketData {
        /**
         * 最新价格
         */
        private BigDecimal lastPrice;
        
        /**
         * 24小时最高价
         */
        private BigDecimal high24h;
        
        /**
         * 24小时最低价
         */
        private BigDecimal low24h;
        
        /**
         * 24小时成交量
         */
        private BigDecimal volume24h;
        
        /**
         * 24小时价格变化
         */
        private BigDecimal priceChange24h;
        
        /**
         * 24小时价格变化百分比
         */
        private BigDecimal priceChangePercent24h;
        
        /**
         * 最佳买价
         */
        private BigDecimal bestBid;
        
        /**
         * 最佳卖价
         */
        private BigDecimal bestAsk;
        
        /**
         * 买卖价差
         */
        private BigDecimal spread;
        
        /**
         * 市场深度
         */
        private BigDecimal marketDepth;
    }
    
    /**
     * 交易统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeStatistics {
        /**
         * 今日交易笔数
         */
        private Long todayTradeCount;
        
        /**
         * 今日交易量
         */
        private BigDecimal todayVolume;
        
        /**
         * 平均交易大小
         */
        private BigDecimal avgTradeSize;
        
        /**
         * 最大交易大小
         */
        private BigDecimal maxTradeSize;
        
        /**
         * 最小交易大小
         */
        private BigDecimal minTradeSize;
        
        /**
         * 交易频率
         */
        private BigDecimal tradeFrequency;
        
        /**
         * 大单交易占比
         */
        private BigDecimal largeTradeRatio;
        
        /**
         * 买卖比例
         */
        private BigDecimal buySellRatio;
    }
    
    /**
     * 流动性信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityInfo {
        /**
         * 流动性得分
         */
        private BigDecimal liquidityScore;
        
        /**
         * 订单簿深度
         */
        private BigDecimal orderBookDepth;
        
        /**
         * 市场冲击
         */
        private BigDecimal marketImpact;
        
        /**
         * 流动性提供者
         */
        private String liquidityProvider;
        
        /**
         * 流动性类型
         */
        private LiquidityType liquidityType;
        
        /**
         * 流动性质量
         */
        private LiquidityQuality liquidityQuality;
    }
    
    /**
     * 流动性类型
     */
    public enum LiquidityType {
        NATURAL("自然流动性"),
        ARTIFICIAL("人工流动性"),
        MIXED("混合流动性");
        
        private final String description;
        
        LiquidityType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 流动性质量
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityQuality {
        private BigDecimal tightness;
        private BigDecimal depth;
        private BigDecimal resilience;
        private BigDecimal immediacy;
        private BigDecimal overallQuality;
    }
    
    /**
     * 交易影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeImpact {
        /**
         * 价格影响
         */
        private BigDecimal priceImpact;
        
        /**
         * 成交量影响
         */
        private BigDecimal volumeImpact;
        
        /**
         * 波动率影响
         */
        private BigDecimal volatilityImpact;
        
        /**
         * 流动性影响
         */
        private BigDecimal liquidityImpact;
        
        /**
         * 市场情绪影响
         */
        private BigDecimal sentimentImpact;
        
        /**
         * 影响持续时间
         */
        private Long impactDuration;
        
        /**
         * 影响等级
         */
        private ImpactLevel impactLevel;
    }
    
    /**
     * 影响等级
     */
    public enum ImpactLevel {
        LOW("低影响"),
        MEDIUM("中等影响"),
        HIGH("高影响"),
        EXTREME("极端影响");
        
        private final String description;
        
        ImpactLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 风险指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskMetrics {
        /**
         * 风险等级
         */
        private RiskLevel riskLevel;
        
        /**
         * 风险得分
         */
        private BigDecimal riskScore;
        
        /**
         * 价格风险
         */
        private BigDecimal priceRisk;
        
        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;
        
        /**
         * 对手方风险
         */
        private BigDecimal counterpartyRisk;
        
        /**
         * 操作风险
         */
        private BigDecimal operationalRisk;
        
        /**
         * 风险警告
         */
        private List<String> riskWarnings;
    }
    
    /**
     * 风险等级
     */
    public enum RiskLevel {
        LOW("低风险"),
        MEDIUM("中等风险"),
        HIGH("高风险"),
        CRITICAL("严重风险");
        
        private final String description;
        
        RiskLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 交易质量
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeQuality {
        /**
         * 执行质量得分
         */
        private BigDecimal executionQuality;
        
        /**
         * 价格改善
         */
        private BigDecimal priceImprovement;
        
        /**
         * 滑点
         */
        private BigDecimal slippage;
        
        /**
         * 执行效率
         */
        private BigDecimal executionEfficiency;
        
        /**
         * 成本效益
         */
        private BigDecimal costEffectiveness;
        
        /**
         * 质量等级
         */
        private QualityLevel qualityLevel;
    }
    
    /**
     * 质量等级
     */
    public enum QualityLevel {
        EXCELLENT("优秀"),
        GOOD("良好"),
        AVERAGE("一般"),
        POOR("较差");
        
        private final String description;
        
        QualityLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 延迟信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LatencyInfo {
        /**
         * 订单接收延迟
         */
        private Long orderReceiveLatency;
        
        /**
         * 撮合延迟
         */
        private Long matchingLatency;
        
        /**
         * 执行延迟
         */
        private Long executionLatency;
        
        /**
         * 确认延迟
         */
        private Long confirmationLatency;
        
        /**
         * 总延迟
         */
        private Long totalLatency;
        
        /**
         * 延迟等级
         */
        private LatencyLevel latencyLevel;
    }
    
    /**
     * 延迟等级
     */
    public enum LatencyLevel {
        ULTRA_LOW("超低延迟"),
        LOW("低延迟"),
        NORMAL("正常延迟"),
        HIGH("高延迟"),
        EXTREME("极高延迟");
        
        private final String description;
        
        LatencyLevel(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}