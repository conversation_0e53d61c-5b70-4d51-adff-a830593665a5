package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 余额变动响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BalanceChangeResponse {
    
    /**
     * 变动记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 钱包类型
     */
    private String walletType;
    
    /**
     * 变动类型 (DEPOSIT, WITHDRAW, TRANSFER_IN, TRANSFER_OUT, TRADE_BUY, TRADE_SELL, FEE, REWARD, PENALTY)
     */
    private String changeType;
    
    /**
     * 变动金额
     */
    private BigDecimal changeAmount;
    
    /**
     * 变动前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 变动后余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 变动前可用余额
     */
    private BigDecimal availableBalanceBefore;
    
    /**
     * 变动后可用余额
     */
    private BigDecimal availableBalanceAfter;
    
    /**
     * 变动前冻结余额
     */
    private BigDecimal frozenBalanceBefore;
    
    /**
     * 变动后冻结余额
     */
    private BigDecimal frozenBalanceAfter;
    
    /**
     * 关联订单ID
     */
    private Long orderId;
    
    /**
     * 关联交易ID
     */
    private Long tradeId;
    
    /**
     * 关联转账ID
     */
    private Long transferId;
    
    /**
     * 关联充值ID
     */
    private Long depositId;
    
    /**
     * 关联提现ID
     */
    private Long withdrawId;
    
    /**
     * 交易对（如果是交易相关）
     */
    private String symbol;
    
    /**
     * 交易方向（如果是交易相关）
     */
    private String side;
    
    /**
     * 变动原因
     */
    private String reason;
    
    /**
     * 变动描述
     */
    private String description;
    
    /**
     * 变动时间
     */
    private LocalDateTime changeTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 业务流水号
     */
    private String businessId;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 手续费货币
     */
    private String feeCurrency;
    
    /**
     * 状态 (PENDING, COMPLETED, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 操作人
     */
    private String operator;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 用户代理
     */
    private String userAgent;
    
    /**
     * 是否为内部操作
     */
    private Boolean isInternal;
    
    /**
     * 风险等级
     */
    private String riskLevel;
}