package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 流动性分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiquidityAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 流动性等级
     */
    private LiquidityLevel liquidityLevel;

    /**
     * 综合流动性指数 (0-100)
     */
    private BigDecimal overallLiquidityIndex;

    /**
     * 市场深度分析
     */
    private MarketDepthAnalysis marketDepthAnalysis;

    /**
     * 价差分析
     */
    private SpreadAnalysis spreadAnalysis;

    /**
     * 交易量分析
     */
    private VolumeAnalysis volumeAnalysis;

    /**
     * 订单簿分析
     */
    private OrderBookAnalysis orderBookAnalysis;

    /**
     * 流动性提供者分析
     */
    private LiquidityProviderAnalysis liquidityProviderAnalysis;

    /**
     * 时间维度分析
     */
    private TemporalAnalysis temporalAnalysis;

    /**
     * 流动性风险评估
     */
    private LiquidityRiskAssessment riskAssessment;

    /**
     * 流动性预测
     */
    private LiquidityForecast liquidityForecast;

    /**
     * 改善建议
     */
    private List<LiquidityImprovement> improvementSuggestions;

    /**
     * 基准对比
     */
    private BenchmarkComparison benchmarkComparison;

    /**
     * 流动性等级
     */
    public enum LiquidityLevel {
        EXCELLENT("优秀", 5, "流动性极佳，交易成本低"),
        GOOD("良好", 4, "流动性良好，适合大部分交易"),
        MODERATE("中等", 3, "流动性中等，需注意交易规模"),
        POOR("较差", 2, "流动性较差，存在滑点风险"),
        VERY_POOR("极差", 1, "流动性极差，不建议大额交易");

        private final String description;
        private final Integer level;
        private final String detail;

        LiquidityLevel(String description, Integer level, String detail) {
            this.description = description;
            this.level = level;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public Integer getLevel() {
            return level;
        }

        public String getDetail() {
            return detail;
        }

        public static LiquidityLevel fromIndex(BigDecimal index) {
            if (index == null) return MODERATE;
            
            double value = index.doubleValue();
            if (value >= 80) return EXCELLENT;
            else if (value >= 60) return GOOD;
            else if (value >= 40) return MODERATE;
            else if (value >= 20) return POOR;
            else return VERY_POOR;
        }
    }

    /**
     * 市场深度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketDepthAnalysis {
        /**
         * 买方深度
         */
        private BigDecimal bidDepth;

        /**
         * 卖方深度
         */
        private BigDecimal askDepth;

        /**
         * 总深度
         */
        private BigDecimal totalDepth;

        /**
         * 有效深度（在合理价格范围内）
         */
        private BigDecimal effectiveDepth;

        /**
         * 深度分布均匀性
         */
        private BigDecimal depthUniformity;

        /**
         * 深度稳定性
         */
        private BigDecimal depthStability;

        /**
         * 各价格层级深度
         */
        private Map<String, BigDecimal> depthByLevel;

        /**
         * 深度集中度
         */
        private BigDecimal depthConcentration;
    }

    /**
     * 价差分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SpreadAnalysis {
        /**
         * 当前买卖价差
         */
        private BigDecimal currentSpread;

        /**
         * 价差百分比
         */
        private BigDecimal spreadPercentage;

        /**
         * 平均价差
         */
        private BigDecimal averageSpread;

        /**
         * 价差波动性
         */
        private BigDecimal spreadVolatility;

        /**
         * 最小价差
         */
        private BigDecimal minimumSpread;

        /**
         * 最大价差
         */
        private BigDecimal maximumSpread;

        /**
         * 价差趋势
         */
        private String spreadTrend;

        /**
         * 价差效率指数
         */
        private BigDecimal spreadEfficiencyIndex;
    }

    /**
     * 交易量分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnalysis {
        /**
         * 24小时交易量
         */
        private BigDecimal volume24h;

        /**
         * 平均交易规模
         */
        private BigDecimal averageTradeSize;

        /**
         * 交易频率
         */
        private BigDecimal tradeFrequency;

        /**
         * 大额交易占比
         */
        private BigDecimal largeTradeRatio;

        /**
         * 交易量稳定性
         */
        private BigDecimal volumeStability;

        /**
         * 交易量趋势
         */
        private String volumeTrend;

        /**
         * 换手率
         */
        private BigDecimal turnoverRate;

        /**
         * 活跃度指数
         */
        private BigDecimal activityIndex;
    }

    /**
     * 订单簿分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderBookAnalysis {
        /**
         * 订单数量
         */
        private Integer totalOrders;

        /**
         * 买单数量
         */
        private Integer bidOrders;

        /**
         * 卖单数量
         */
        private Integer askOrders;

        /**
         * 平均订单规模
         */
        private BigDecimal averageOrderSize;

        /**
         * 订单簿厚度
         */
        private BigDecimal orderBookThickness;

        /**
         * 订单更新频率
         */
        private BigDecimal orderUpdateFrequency;

        /**
         * 订单生存时间
         */
        private BigDecimal averageOrderLifetime;

        /**
         * 订单簿不平衡度
         */
        private BigDecimal orderBookImbalance;
    }

    /**
     * 流动性提供者分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityProviderAnalysis {
        /**
         * 做市商数量
         */
        private Integer marketMakerCount;

        /**
         * 做市商活跃度
         */
        private BigDecimal marketMakerActivity;

        /**
         * 流动性集中度
         */
        private BigDecimal liquidityConcentration;

        /**
         * 顶级提供者占比
         */
        private BigDecimal topProviderShare;

        /**
         * 提供者稳定性
         */
        private BigDecimal providerStability;

        /**
         * 新增提供者数量
         */
        private Integer newProviderCount;

        /**
         * 流动性竞争度
         */
        private BigDecimal liquidityCompetition;
    }

    /**
     * 时间维度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TemporalAnalysis {
        /**
         * 1小时流动性变化
         */
        private BigDecimal oneHourChange;

        /**
         * 4小时流动性变化
         */
        private BigDecimal fourHourChange;

        /**
         * 24小时流动性变化
         */
        private BigDecimal twentyFourHourChange;

        /**
         * 流动性周期性
         */
        private String liquidityCyclicality;

        /**
         * 峰值时段
         */
        private List<String> peakPeriods;

        /**
         * 低谷时段
         */
        private List<String> lowPeriods;

        /**
         * 流动性持续性
         */
        private BigDecimal liquidityPersistence;
    }

    /**
     * 流动性风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityRiskAssessment {
        /**
         * 流动性风险等级
         */
        private String riskLevel;

        /**
         * 流动性缺口风险
         */
        private BigDecimal liquidityGapRisk;

        /**
         * 市场冲击风险
         */
        private BigDecimal marketImpactRisk;

        /**
         * 流动性枯竭概率
         */
        private BigDecimal liquidityDryUpProbability;

        /**
         * 极端情况下的流动性
         */
        private BigDecimal stressLiquidity;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationSuggestions;
    }

    /**
     * 流动性预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityForecast {
        /**
         * 短期预测（1小时）
         */
        private BigDecimal shortTermForecast;

        /**
         * 中期预测（4小时）
         */
        private BigDecimal mediumTermForecast;

        /**
         * 长期预测（24小时）
         */
        private BigDecimal longTermForecast;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 预测趋势
         */
        private String forecastTrend;

        /**
         * 关键影响因素
         */
        private List<String> keyInfluencingFactors;
    }

    /**
     * 流动性改善建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityImprovement {
        /**
         * 改善类型
         */
        private String improvementType;

        /**
         * 具体建议
         */
        private String suggestion;

        /**
         * 预期效果
         */
        private String expectedEffect;

        /**
         * 实施难度
         */
        private String implementationDifficulty;

        /**
         * 优先级
         */
        private Integer priority;

        /**
         * 预期改善幅度
         */
        private BigDecimal expectedImprovement;
    }

    /**
     * 基准对比
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BenchmarkComparison {
        /**
         * 行业平均水平
         */
        private BigDecimal industryAverage;

        /**
         * 相对行业排名
         */
        private Integer industryRanking;

        /**
         * 同类资产对比
         */
        private Map<String, BigDecimal> peerComparison;

        /**
         * 历史最佳水平
         */
        private BigDecimal historicalBest;

        /**
         * 改善空间
         */
        private BigDecimal improvementPotential;
    }

    /**
     * 获取流动性状态摘要
     */
    public String getLiquiditySummary() {
        return String.format("流动性等级: %s (指数: %s) - %s",
            liquidityLevel != null ? liquidityLevel.getDescription() : "未知",
            overallLiquidityIndex != null ? overallLiquidityIndex.toString() : "N/A",
            liquidityLevel != null ? liquidityLevel.getDetail() : "无详细信息");
    }

    /**
     * 检查是否适合大额交易
     */
    public boolean isSuitableForLargeOrders() {
        return liquidityLevel != null && 
               (liquidityLevel == LiquidityLevel.EXCELLENT || liquidityLevel == LiquidityLevel.GOOD);
    }

    /**
     * 获取流动性健康度
     */
    public String getLiquidityHealth() {
        if (overallLiquidityIndex == null) {
            return "未知";
        }
        
        double index = overallLiquidityIndex.doubleValue();
        if (index >= 80) {
            return "健康";
        } else if (index >= 60) {
            return "良好";
        } else if (index >= 40) {
            return "一般";
        } else if (index >= 20) {
            return "较差";
        } else {
            return "不健康";
        }
    }

    /**
     * 获取主要风险提示
     */
    public String getMainRiskAlert() {
        if (riskAssessment == null || riskAssessment.getRiskLevel() == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = riskAssessment.getRiskLevel();
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：建议谨慎交易，避免大额订单";
            case "MEDIUM":
                return "中等风险：注意市场深度，控制交易规模";
            case "LOW":
                return "低风险：流动性充足，可正常交易";
            default:
                return "风险等级: " + riskLevel;
        }
    }
}