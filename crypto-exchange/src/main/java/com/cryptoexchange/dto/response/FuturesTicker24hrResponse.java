package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 期货24小时价格统计响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FuturesTicker24hrResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 24小时价格变化
     */
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent;
    
    /**
     * 加权平均价格
     */
    private BigDecimal weightedAvgPrice;
    
    /**
     * 前一日收盘价
     */
    private BigDecimal prevClosePrice;
    
    /**
     * 最新价格
     */
    private BigDecimal lastPrice;
    
    /**
     * 最新成交量
     */
    private BigDecimal lastQty;
    
    /**
     * 最佳买价
     */
    private BigDecimal bidPrice;
    
    /**
     * 最佳买量
     */
    private BigDecimal bidQty;
    
    /**
     * 最佳卖价
     */
    private BigDecimal askPrice;
    
    /**
     * 最佳卖量
     */
    private BigDecimal askQty;
    
    /**
     * 开盘价
     */
    private BigDecimal openPrice;
    
    /**
     * 最高价
     */
    private BigDecimal highPrice;
    
    /**
     * 最低价
     */
    private BigDecimal lowPrice;
    
    /**
     * 24小时成交量
     */
    private BigDecimal volume;
    
    /**
     * 24小时成交额
     */
    private BigDecimal quoteVolume;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime openTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime closeTime;
    
    /**
     * 首次交易ID
     */
    private Long firstId;
    
    /**
     * 最后交易ID
     */
    private Long lastId;
    
    /**
     * 交易次数
     */
    private Long count;
    
    /**
     * 标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 指数价格
     */
    private BigDecimal indexPrice;
    
    /**
     * 资金费率
     */
    private BigDecimal fundingRate;
    
    /**
     * 下次资金费率时间
     */
    private LocalDateTime nextFundingTime;
    
    /**
     * 未平仓合约数量
     */
    private BigDecimal openInterest;
    
    /**
     * 数据时间
     */
    private LocalDateTime timestamp;
}