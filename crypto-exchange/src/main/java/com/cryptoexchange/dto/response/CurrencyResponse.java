package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 货币响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CurrencyResponse {
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 货币名称
     */
    private String name;
    
    /**
     * 货币全称
     */
    private String fullName;
    
    /**
     * 货币类型 (CRYPTO, FIAT)
     */
    private String type;
    
    /**
     * 货币状态 (ACTIVE, INACTIVE, MAINTENANCE)
     */
    private String status;
    
    /**
     * 是否支持充值
     */
    private Boolean depositEnabled;
    
    /**
     * 是否支持提现
     */
    private Boolean withdrawEnabled;
    
    /**
     * 是否支持交易
     */
    private Boolean tradingEnabled;
    
    /**
     * 精度（小数位数）
     */
    private Integer precision;
    
    /**
     * 最小充值金额
     */
    private BigDecimal minDepositAmount;
    
    /**
     * 最小提现金额
     */
    private BigDecimal minWithdrawAmount;
    
    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 提现手续费类型 (FIXED, PERCENTAGE)
     */
    private String withdrawFeeType;
    
    /**
     * 最小提现手续费
     */
    private BigDecimal minWithdrawFee;
    
    /**
     * 最大提现手续费
     */
    private BigDecimal maxWithdrawFee;
    
    /**
     * 支持的网络列表
     */
    private List<NetworkInfo> supportedNetworks;
    
    /**
     * 货币图标URL
     */
    private String iconUrl;
    
    /**
     * 货币描述
     */
    private String description;
    
    /**
     * 官方网站
     */
    private String website;
    
    /**
     * 白皮书URL
     */
    private String whitepaper;
    
    /**
     * 区块浏览器URL
     */
    private String explorer;
    
    /**
     * 市值排名
     */
    private Integer marketCapRank;
    
    /**
     * 当前价格（USDT计价）
     */
    private BigDecimal currentPrice;
    
    /**
     * 24小时价格变化百分比
     */
    private BigDecimal priceChangePercent24h;
    
    /**
     * 市值
     */
    private BigDecimal marketCap;
    
    /**
     * 24小时交易量
     */
    private BigDecimal volume24h;
    
    /**
     * 流通供应量
     */
    private BigDecimal circulatingSupply;
    
    /**
     * 总供应量
     */
    private BigDecimal totalSupply;
    
    /**
     * 最大供应量
     */
    private BigDecimal maxSupply;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 是否为热门货币
     */
    private Boolean isHot;
    
    /**
     * 是否为新币
     */
    private Boolean isNew;
    
    /**
     * 排序权重
     */
    private Integer sortWeight;
    
    /**
     * 网络信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class NetworkInfo {
        /**
         * 网络代码
         */
        private String network;
        
        /**
         * 网络名称
         */
        private String networkName;
        
        /**
         * 网络状态
         */
        private String status;
        
        /**
         * 是否支持充值
         */
        private Boolean depositEnabled;
        
        /**
         * 是否支持提现
         */
        private Boolean withdrawEnabled;
        
        /**
         * 最小充值金额
         */
        private BigDecimal minDepositAmount;
        
        /**
         * 最小提现金额
         */
        private BigDecimal minWithdrawAmount;
        
        /**
         * 提现手续费
         */
        private BigDecimal withdrawFee;
        
        /**
         * 确认数要求
         */
        private Integer confirmations;
        
        /**
         * 是否需要标签
         */
        private Boolean requiresTag;
        
        /**
         * 网络精度
         */
        private Integer networkPrecision;
        
        /**
         * 合约地址（如果是代币）
         */
        private String contractAddress;
    }
}