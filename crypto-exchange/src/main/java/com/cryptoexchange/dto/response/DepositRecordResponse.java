package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 充值记录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DepositRecordResponse {
    
    /**
     * 充值记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 充值货币
     */
    private String currency;
    
    /**
     * 充值金额
     */
    private BigDecimal amount;
    
    /**
     * 充值地址
     */
    private String address;
    
    /**
     * 交易哈希
     */
    private String txHash;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 充值状态 (PENDING, CONFIRMED, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
    
    /**
     * 确认数
     */
    private Integer confirmations;
    
    /**
     * 所需确认数
     */
    private Integer requiredConfirmations;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际到账金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 充值时间
     */
    private LocalDateTime depositTime;
    
    /**
     * 确认时间
     */
    private LocalDateTime confirmTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 区块高度
     */
    private Long blockHeight;
    
    /**
     * 标签/备忘录
     */
    private String tag;
    
    /**
     * 充值来源
     */
    private String source;
    
    /**
     * 风险等级
     */
    private String riskLevel;
    
    /**
     * 钱包类型
     */
    private String walletType;
    
    /**
     * 充值前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 充值后余额
     */
    private BigDecimal balanceAfter;
}