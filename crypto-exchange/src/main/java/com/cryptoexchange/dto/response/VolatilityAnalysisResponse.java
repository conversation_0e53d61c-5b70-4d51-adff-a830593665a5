package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 波动率分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VolatilityAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 当前波动率
     */
    private BigDecimal currentVolatility;

    /**
     * 波动率等级
     */
    private VolatilityLevel volatilityLevel;

    /**
     * 历史波动率分析
     */
    private HistoricalVolatilityAnalysis historicalVolatilityAnalysis;

    /**
     * 隐含波动率分析
     */
    private ImpliedVolatilityAnalysis impliedVolatilityAnalysis;

    /**
     * 已实现波动率分析
     */
    private RealizedVolatilityAnalysis realizedVolatilityAnalysis;

    /**
     * 波动率预测
     */
    private VolatilityForecast volatilityForecast;

    /**
     * 波动率模型分析
     */
    private VolatilityModelAnalysis volatilityModelAnalysis;

    /**
     * 波动率聚类分析
     */
    private VolatilityClusterAnalysis volatilityClusterAnalysis;

    /**
     * 波动率风险评估
     */
    private VolatilityRiskAssessment volatilityRiskAssessment;

    /**
     * 波动率交易策略
     */
    private VolatilityTradingStrategy volatilityTradingStrategy;

    /**
     * 波动率比较分析
     */
    private VolatilityComparativeAnalysis volatilityComparativeAnalysis;

    /**
     * 波动率异常检测
     */
    private VolatilityAnomalyDetection volatilityAnomalyDetection;

    /**
     * 波动率等级
     */
    public enum VolatilityLevel {
        EXTREMELY_LOW("极低", 0.0, 0.1, "市场极度平静"),
        LOW("低", 0.1, 0.2, "市场相对平静"),
        MODERATE("中等", 0.2, 0.4, "正常波动水平"),
        HIGH("高", 0.4, 0.6, "市场活跃"),
        VERY_HIGH("很高", 0.6, 0.8, "市场高度活跃"),
        EXTREMELY_HIGH("极高", 0.8, 1.0, "市场极度活跃"),
        CRISIS("危机", 1.0, Double.MAX_VALUE, "市场恐慌状态");

        private final String description;
        private final double minValue;
        private final double maxValue;
        private final String interpretation;

        VolatilityLevel(String description, double minValue, double maxValue, String interpretation) {
            this.description = description;
            this.minValue = minValue;
            this.maxValue = maxValue;
            this.interpretation = interpretation;
        }

        public String getDescription() {
            return description;
        }

        public double getMinValue() {
            return minValue;
        }

        public double getMaxValue() {
            return maxValue;
        }

        public String getInterpretation() {
            return interpretation;
        }

        public static VolatilityLevel fromValue(double value) {
            for (VolatilityLevel level : values()) {
                if (value >= level.minValue && value < level.maxValue) {
                    return level;
                }
            }
            return CRISIS;
        }
    }

    /**
     * 历史波动率分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoricalVolatilityAnalysis {
        /**
         * 不同时间窗口的历史波动率
         */
        private Map<String, BigDecimal> historicalVolatilities;

        /**
         * 波动率趋势
         */
        private String volatilityTrend;

        /**
         * 波动率统计
         */
        private VolatilityStatistics volatilityStatistics;

        /**
         * 波动率分布
         */
        private VolatilityDistribution volatilityDistribution;

        /**
         * 波动率周期性
         */
        private VolatilityCyclicality volatilityCyclicality;

        /**
         * 波动率持续性
         */
        private VolatilityPersistence volatilityPersistence;
    }

    /**
     * 波动率统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityStatistics {
        /**
         * 平均波动率
         */
        private BigDecimal meanVolatility;

        /**
         * 波动率中位数
         */
        private BigDecimal medianVolatility;

        /**
         * 波动率标准差
         */
        private BigDecimal volatilityStandardDeviation;

        /**
         * 最大波动率
         */
        private BigDecimal maxVolatility;

        /**
         * 最小波动率
         */
        private BigDecimal minVolatility;

        /**
         * 波动率偏度
         */
        private BigDecimal volatilitySkewness;

        /**
         * 波动率峰度
         */
        private BigDecimal volatilityKurtosis;

        /**
         * 波动率分位数
         */
        private Map<String, BigDecimal> volatilityQuantiles;
    }

    /**
     * 波动率分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityDistribution {
        /**
         * 分布类型
         */
        private String distributionType;

        /**
         * 分布参数
         */
        private Map<String, BigDecimal> distributionParameters;

        /**
         * 拟合优度
         */
        private BigDecimal goodnessOfFit;

        /**
         * 分布直方图
         */
        private List<HistogramBin> histogram;

        /**
         * 正态性检验
         */
        private NormalityTest normalityTest;
    }

    /**
     * 直方图区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistogramBin {
        /**
         * 区间下界
         */
        private BigDecimal lowerBound;

        /**
         * 区间上界
         */
        private BigDecimal upperBound;

        /**
         * 频数
         */
        private Integer frequency;

        /**
         * 频率
         */
        private BigDecimal probability;
    }

    /**
     * 正态性检验
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NormalityTest {
        /**
         * Shapiro-Wilk检验统计量
         */
        private BigDecimal shapiroWilkStatistic;

        /**
         * Shapiro-Wilk检验P值
         */
        private BigDecimal shapiroWilkPValue;

        /**
         * Kolmogorov-Smirnov检验统计量
         */
        private BigDecimal ksStatistic;

        /**
         * Kolmogorov-Smirnov检验P值
         */
        private BigDecimal ksPValue;

        /**
         * 是否符合正态分布
         */
        private boolean isNormal;
    }

    /**
     * 波动率周期性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityCyclicality {
        /**
         * 是否存在周期性
         */
        private boolean hasCyclicality;

        /**
         * 主要周期长度
         */
        private Integer primaryCycleLength;

        /**
         * 次要周期长度
         */
        private List<Integer> secondaryCycleLengths;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 周期相位
         */
        private BigDecimal cyclePhase;

        /**
         * 季节性效应
         */
        private Map<String, BigDecimal> seasonalEffects;
    }

    /**
     * 波动率持续性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityPersistence {
        /**
         * 自相关系数
         */
        private List<BigDecimal> autocorrelations;

        /**
         * 半衰期
         */
        private BigDecimal halfLife;

        /**
         * 持续性指数
         */
        private BigDecimal persistenceIndex;

        /**
         * 均值回归速度
         */
        private BigDecimal meanReversionSpeed;

        /**
         * 长期均值
         */
        private BigDecimal longTermMean;
    }

    /**
     * 隐含波动率分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ImpliedVolatilityAnalysis {
        /**
         * 当前隐含波动率
         */
        private BigDecimal currentImpliedVolatility;

        /**
         * 隐含波动率曲面
         */
        private VolatilitySurface volatilitySurface;

        /**
         * 隐含波动率偏斜
         */
        private VolatilitySkew volatilitySkew;

        /**
         * 隐含波动率期限结构
         */
        private VolatilityTermStructure volatilityTermStructure;

        /**
         * VIX指数分析
         */
        private VixAnalysis vixAnalysis;

        /**
         * 隐含波动率预测
         */
        private List<VolatilityForecastPoint> impliedVolatilityForecast;
    }

    /**
     * 波动率曲面
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilitySurface {
        /**
         * 执行价格网格
         */
        private List<BigDecimal> strikeGrid;

        /**
         * 到期时间网格
         */
        private List<Integer> maturityGrid;

        /**
         * 波动率矩阵
         */
        private Map<String, Map<String, BigDecimal>> volatilityMatrix;

        /**
         * 曲面拟合质量
         */
        private BigDecimal surfaceFitQuality;

        /**
         * 曲面参数
         */
        private Map<String, BigDecimal> surfaceParameters;
    }

    /**
     * 波动率偏斜
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilitySkew {
        /**
         * 偏斜系数
         */
        private BigDecimal skewCoefficient;

        /**
         * 偏斜方向
         */
        private String skewDirection;

        /**
         * 偏斜强度
         */
        private BigDecimal skewStrength;

        /**
         * 风险逆转指标
         */
        private BigDecimal riskReversalIndicator;

        /**
         * 蝶式价差指标
         */
        private BigDecimal butterflyIndicator;
    }

    /**
     * 波动率期限结构
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityTermStructure {
        /**
         * 期限结构点
         */
        private List<TermStructurePoint> termStructurePoints;

        /**
         * 期限结构形状
         */
        private String termStructureShape;

        /**
         * 期限结构斜率
         */
        private BigDecimal termStructureSlope;

        /**
         * 期限结构曲率
         */
        private BigDecimal termStructureCurvature;

        /**
         * 期限结构稳定性
         */
        private BigDecimal termStructureStability;
    }

    /**
     * 期限结构点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TermStructurePoint {
        /**
         * 到期天数
         */
        private Integer daysToMaturity;

        /**
         * 隐含波动率
         */
        private BigDecimal impliedVolatility;

        /**
         * 置信区间
         */
        private ConfidenceInterval confidenceInterval;

        /**
         * 流动性指标
         */
        private BigDecimal liquidityIndicator;
    }

    /**
     * 置信区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfidenceInterval {
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;

        /**
         * 下界
         */
        private BigDecimal lowerBound;

        /**
         * 上界
         */
        private BigDecimal upperBound;
    }

    /**
     * VIX指数分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VixAnalysis {
        /**
         * 当前VIX值
         */
        private BigDecimal currentVix;

        /**
         * VIX历史分位数
         */
        private BigDecimal vixPercentile;

        /**
         * VIX趋势
         */
        private String vixTrend;

        /**
         * VIX均值回归信号
         */
        private String vixMeanReversionSignal;

        /**
         * VIX极值警告
         */
        private List<String> vixExtremeWarnings;
    }

    /**
     * 已实现波动率分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RealizedVolatilityAnalysis {
        /**
         * 当前已实现波动率
         */
        private BigDecimal currentRealizedVolatility;

        /**
         * 已实现波动率历史
         */
        private List<RealizedVolatilityPoint> realizedVolatilityHistory;

        /**
         * 已实现波动率vs隐含波动率
         */
        private VolatilityComparison realizedVsImplied;

        /**
         * 波动率风险溢价
         */
        private BigDecimal volatilityRiskPremium;

        /**
         * 已实现波动率预测
         */
        private List<VolatilityForecastPoint> realizedVolatilityForecast;
    }

    /**
     * 已实现波动率点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RealizedVolatilityPoint {
        /**
         * 时间点
         */
        private LocalDateTime timestamp;

        /**
         * 已实现波动率
         */
        private BigDecimal realizedVolatility;

        /**
         * 计算窗口
         */
        private Integer calculationWindow;

        /**
         * 频率调整
         */
        private String frequencyAdjustment;
    }

    /**
     * 波动率比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityComparison {
        /**
         * 已实现波动率
         */
        private BigDecimal realizedVolatility;

        /**
         * 隐含波动率
         */
        private BigDecimal impliedVolatility;

        /**
         * 波动率差异
         */
        private BigDecimal volatilityDifference;

        /**
         * 波动率比率
         */
        private BigDecimal volatilityRatio;

        /**
         * 比较结论
         */
        private String comparisonConclusion;

        /**
         * 交易信号
         */
        private String tradingSignal;
    }

    /**
     * 波动率预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityForecast {
        /**
         * 短期预测
         */
        private List<VolatilityForecastPoint> shortTermForecast;

        /**
         * 中期预测
         */
        private List<VolatilityForecastPoint> mediumTermForecast;

        /**
         * 长期预测
         */
        private List<VolatilityForecastPoint> longTermForecast;

        /**
         * 预测模型
         */
        private String forecastModel;

        /**
         * 预测准确性
         */
        private BigDecimal forecastAccuracy;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 模型参数
         */
        private Map<String, BigDecimal> modelParameters;
    }

    /**
     * 波动率预测点
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityForecastPoint {
        /**
         * 预测时间
         */
        private LocalDateTime forecastTime;

        /**
         * 预测波动率
         */
        private BigDecimal predictedVolatility;

        /**
         * 预测区间
         */
        private ConfidenceInterval predictionInterval;

        /**
         * 预测可靠性
         */
        private BigDecimal predictionReliability;

        /**
         * 预测方向
         */
        private String predictionDirection;
    }

    /**
     * 波动率模型分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityModelAnalysis {
        /**
         * GARCH模型分析
         */
        private GarchModelAnalysis garchModelAnalysis;

        /**
         * 随机波动率模型分析
         */
        private StochasticVolatilityModelAnalysis stochasticVolatilityModelAnalysis;

        /**
         * 跳跃扩散模型分析
         */
        private JumpDiffusionModelAnalysis jumpDiffusionModelAnalysis;

        /**
         * 模型比较
         */
        private ModelComparison modelComparison;

        /**
         * 最优模型
         */
        private String optimalModel;
    }

    /**
     * GARCH模型分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GarchModelAnalysis {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 模型参数
         */
        private Map<String, BigDecimal> modelParameters;

        /**
         * 模型拟合优度
         */
        private BigDecimal modelFitQuality;

        /**
         * AIC信息准则
         */
        private BigDecimal aicCriterion;

        /**
         * BIC信息准则
         */
        private BigDecimal bicCriterion;

        /**
         * 残差分析
         */
        private ResidualAnalysis residualAnalysis;
    }

    /**
     * 残差分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResidualAnalysis {
        /**
         * 残差正态性
         */
        private boolean residualNormality;

        /**
         * 残差自相关
         */
        private List<BigDecimal> residualAutocorrelations;

        /**
         * 残差异方差性
         */
        private boolean residualHeteroscedasticity;

        /**
         * Ljung-Box检验
         */
        private LjungBoxTest ljungBoxTest;
    }

    /**
     * Ljung-Box检验
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LjungBoxTest {
        /**
         * 检验统计量
         */
        private BigDecimal testStatistic;

        /**
         * P值
         */
        private BigDecimal pValue;

        /**
         * 检验结果
         */
        private boolean rejectNullHypothesis;
    }

    /**
     * 随机波动率模型分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StochasticVolatilityModelAnalysis {
        /**
         * 模型类型
         */
        private String modelType;

        /**
         * 波动率因子
         */
        private List<VolatilityFactor> volatilityFactors;

        /**
         * 模型参数
         */
        private Map<String, BigDecimal> modelParameters;

        /**
         * 模型似然值
         */
        private BigDecimal modelLikelihood;

        /**
         * 卡尔曼滤波结果
         */
        private KalmanFilterResult kalmanFilterResult;
    }

    /**
     * 波动率因子
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityFactor {
        /**
         * 因子名称
         */
        private String factorName;

        /**
         * 因子权重
         */
        private BigDecimal factorWeight;

        /**
         * 因子持续性
         */
        private BigDecimal factorPersistence;

        /**
         * 因子波动性
         */
        private BigDecimal factorVolatility;
    }

    /**
     * 卡尔曼滤波结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KalmanFilterResult {
        /**
         * 滤波状态
         */
        private List<BigDecimal> filteredStates;

        /**
         * 预测状态
         */
        private List<BigDecimal> predictedStates;

        /**
         * 状态协方差
         */
        private List<BigDecimal> stateCovariances;

        /**
         * 滤波质量
         */
        private BigDecimal filterQuality;
    }

    /**
     * 跳跃扩散模型分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JumpDiffusionModelAnalysis {
        /**
         * 跳跃强度
         */
        private BigDecimal jumpIntensity;

        /**
         * 跳跃幅度分布
         */
        private JumpSizeDistribution jumpSizeDistribution;

        /**
         * 扩散参数
         */
        private Map<String, BigDecimal> diffusionParameters;

        /**
         * 跳跃检测结果
         */
        private List<JumpDetectionResult> jumpDetectionResults;

        /**
         * 模型拟合质量
         */
        private BigDecimal modelFitQuality;
    }

    /**
     * 跳跃幅度分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JumpSizeDistribution {
        /**
         * 分布类型
         */
        private String distributionType;

        /**
         * 分布参数
         */
        private Map<String, BigDecimal> distributionParameters;

        /**
         * 平均跳跃幅度
         */
        private BigDecimal meanJumpSize;

        /**
         * 跳跃幅度标准差
         */
        private BigDecimal jumpSizeStandardDeviation;
    }

    /**
     * 跳跃检测结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JumpDetectionResult {
        /**
         * 跳跃时间
         */
        private LocalDateTime jumpTime;

        /**
         * 跳跃幅度
         */
        private BigDecimal jumpSize;

        /**
         * 跳跃方向
         */
        private String jumpDirection;

        /**
         * 跳跃显著性
         */
        private BigDecimal jumpSignificance;

        /**
         * 跳跃概率
         */
        private BigDecimal jumpProbability;
    }

    /**
     * 模型比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModelComparison {
        /**
         * 模型评分
         */
        private Map<String, BigDecimal> modelScores;

        /**
         * 预测准确性比较
         */
        private Map<String, BigDecimal> forecastAccuracyComparison;

        /**
         * 模型复杂度比较
         */
        private Map<String, BigDecimal> modelComplexityComparison;

        /**
         * 推荐模型
         */
        private String recommendedModel;

        /**
         * 模型选择理由
         */
        private String modelSelectionReason;
    }

    /**
     * 波动率聚类分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityClusterAnalysis {
        /**
         * 波动率聚类
         */
        private boolean hasVolatilityClustering;

        /**
         * 聚类强度
         */
        private BigDecimal clusteringStrength;

        /**
         * 聚类持续时间
         */
        private List<ClusterDuration> clusterDurations;

        /**
         * 聚类转换概率
         */
        private Map<String, BigDecimal> clusterTransitionProbabilities;

        /**
         * 当前聚类状态
         */
        private String currentClusterState;
    }

    /**
     * 聚类持续时间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ClusterDuration {
        /**
         * 聚类类型
         */
        private String clusterType;

        /**
         * 平均持续时间
         */
        private BigDecimal averageDuration;

        /**
         * 最大持续时间
         */
        private BigDecimal maxDuration;

        /**
         * 最小持续时间
         */
        private BigDecimal minDuration;

        /**
         * 持续时间分布
         */
        private String durationDistribution;
    }

    /**
     * 波动率风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityRiskAssessment {
        /**
         * 波动率风险等级
         */
        private String volatilityRiskLevel;

        /**
         * 风险价值(VaR)
         */
        private BigDecimal valueAtRisk;

        /**
         * 条件风险价值(CVaR)
         */
        private BigDecimal conditionalValueAtRisk;

        /**
         * 最大回撤风险
         */
        private BigDecimal maxDrawdownRisk;

        /**
         * 波动率冲击测试
         */
        private VolatilityStressTest volatilityStressTest;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 波动率冲击测试
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityStressTest {
        /**
         * 冲击情景
         */
        private List<StressScenario> stressScenarios;

        /**
         * 最坏情况损失
         */
        private BigDecimal worstCaseLoss;

        /**
         * 压力测试结果
         */
        private Map<String, BigDecimal> stressTestResults;

        /**
         * 抗压能力评分
         */
        private BigDecimal stressResistanceScore;
    }

    /**
     * 压力情景
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StressScenario {
        /**
         * 情景名称
         */
        private String scenarioName;

        /**
         * 波动率冲击幅度
         */
        private BigDecimal volatilityShock;

        /**
         * 冲击持续时间
         */
        private Integer shockDuration;

        /**
         * 预期损失
         */
        private BigDecimal expectedLoss;

        /**
         * 情景概率
         */
        private BigDecimal scenarioProbability;
    }

    /**
     * 波动率交易策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityTradingStrategy {
        /**
         * 策略类型
         */
        private String strategyType;

        /**
         * 波动率交易信号
         */
        private List<VolatilityTradingSignal> volatilityTradingSignals;

        /**
         * 期权策略建议
         */
        private List<OptionStrategyRecommendation> optionStrategyRecommendations;

        /**
         * 风险管理建议
         */
        private List<String> riskManagementRecommendations;

        /**
         * 策略预期收益
         */
        private BigDecimal expectedStrategyReturn;

        /**
         * 策略风险评估
         */
        private BigDecimal strategyRiskAssessment;
    }

    /**
     * 波动率交易信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityTradingSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 交易方向
         */
        private String tradingDirection;

        /**
         * 信号有效期
         */
        private String signalValidity;

        /**
         * 信号描述
         */
        private String signalDescription;

        /**
         * 信号可靠性
         */
        private BigDecimal signalReliability;
    }

    /**
     * 期权策略建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionStrategyRecommendation {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 最大收益
         */
        private BigDecimal maxProfit;

        /**
         * 最大损失
         */
        private BigDecimal maxLoss;

        /**
         * 盈亏平衡点
         */
        private List<BigDecimal> breakEvenPoints;

        /**
         * 策略适用性
         */
        private String strategySuitability;
    }

    /**
     * 波动率比较分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityComparativeAnalysis {
        /**
         * 同类资产波动率比较
         */
        private List<VolatilityComparison> peerVolatilityComparisons;

        /**
         * 历史波动率排名
         */
        private VolatilityRanking historicalVolatilityRanking;

        /**
         * 波动率相对表现
         */
        private BigDecimal volatilityRelativePerformance;

        /**
         * 波动率基准比较
         */
        private VolatilityBenchmarkComparison volatilityBenchmarkComparison;
    }

    /**
     * 波动率排名
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityRanking {
        /**
         * 当前排名
         */
        private Integer currentRanking;

        /**
         * 总数量
         */
        private Integer totalCount;

        /**
         * 百分位排名
         */
        private BigDecimal percentileRanking;

        /**
         * 排名变化
         */
        private Integer rankingChange;

        /**
         * 排名趋势
         */
        private String rankingTrend;
    }

    /**
     * 波动率基准比较
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityBenchmarkComparison {
        /**
         * 基准波动率
         */
        private BigDecimal benchmarkVolatility;

        /**
         * 相对波动率
         */
        private BigDecimal relativeVolatility;

        /**
         * 波动率贝塔
         */
        private BigDecimal volatilityBeta;

        /**
         * 跟踪误差
         */
        private BigDecimal trackingError;

        /**
         * 信息比率
         */
        private BigDecimal informationRatio;
    }

    /**
     * 波动率异常检测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityAnomalyDetection {
        /**
         * 异常检测结果
         */
        private List<VolatilityAnomaly> volatilityAnomalies;

        /**
         * 异常检测方法
         */
        private String anomalyDetectionMethod;

        /**
         * 异常阈值
         */
        private BigDecimal anomalyThreshold;

        /**
         * 异常频率
         */
        private BigDecimal anomalyFrequency;

        /**
         * 异常影响评估
         */
        private String anomalyImpactAssessment;
    }

    /**
     * 波动率异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityAnomaly {
        /**
         * 异常时间
         */
        private LocalDateTime anomalyTime;

        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常严重程度
         */
        private BigDecimal anomalySeverity;

        /**
         * 异常持续时间
         */
        private Integer anomalyDuration;

        /**
         * 异常描述
         */
        private String anomalyDescription;

        /**
         * 可能原因
         */
        private List<String> possibleCauses;
    }

    /**
     * 获取波动率摘要
     */
    public String getVolatilitySummary() {
        return String.format("当前波动率: %s (%s) - %s",
            currentVolatility != null ? currentVolatility.toString() + "%" : "N/A",
            volatilityLevel != null ? volatilityLevel.getDescription() : "未知",
            volatilityLevel != null ? volatilityLevel.getInterpretation() : "无解释");
    }

    /**
     * 检查是否为高波动率
     */
    public boolean isHighVolatility() {
        return volatilityLevel != null && 
               (volatilityLevel == VolatilityLevel.HIGH ||
                volatilityLevel == VolatilityLevel.VERY_HIGH ||
                volatilityLevel == VolatilityLevel.EXTREMELY_HIGH ||
                volatilityLevel == VolatilityLevel.CRISIS);
    }

    /**
     * 检查是否为低波动率
     */
    public boolean isLowVolatility() {
        return volatilityLevel != null && 
               (volatilityLevel == VolatilityLevel.EXTREMELY_LOW ||
                volatilityLevel == VolatilityLevel.LOW);
    }

    /**
     * 获取波动率趋势
     */
    public String getVolatilityTrend() {
        if (historicalVolatilityAnalysis == null) {
            return "趋势分析不可用";
        }
        
        String trend = historicalVolatilityAnalysis.getVolatilityTrend();
        return trend != null ? trend : "趋势不明确";
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (volatilityTradingStrategy == null || 
            volatilityTradingStrategy.getVolatilityTradingSignals() == null ||
            volatilityTradingStrategy.getVolatilityTradingSignals().isEmpty()) {
            return "暂无交易建议";
        }
        
        VolatilityTradingSignal strongestSignal = volatilityTradingStrategy.getVolatilityTradingSignals()
            .stream()
            .max((s1, s2) -> {
                BigDecimal strength1 = s1.getSignalStrength();
                BigDecimal strength2 = s2.getSignalStrength();
                if (strength1 == null && strength2 == null) return 0;
                if (strength1 == null) return -1;
                if (strength2 == null) return 1;
                return strength1.compareTo(strength2);
            })
            .orElse(null);
        
        if (strongestSignal != null) {
            return String.format("%s - %s (强度: %s)",
                strongestSignal.getSignalType(),
                strongestSignal.getTradingDirection(),
                strongestSignal.getSignalStrength());
        }
        
        return "无明确交易信号";
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (volatilityRiskAssessment == null) {
            return "风险评估不可用";
        }
        
        String riskLevel = volatilityRiskAssessment.getVolatilityRiskLevel();
        if (riskLevel == null) {
            return "风险等级未知";
        }
        
        switch (riskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：波动率异常，建议谨慎交易";
            case "MEDIUM":
                return "中等风险：波动率较高，注意风险控制";
            case "LOW":
                return "低风险：波动率正常，可正常交易";
            default:
                return "风险等级: " + riskLevel;
        }
    }

    /**
     * 检查是否有异常波动
     */
    public boolean hasVolatilityAnomalies() {
        return volatilityAnomalyDetection != null && 
               volatilityAnomalyDetection.getVolatilityAnomalies() != null &&
               !volatilityAnomalyDetection.getVolatilityAnomalies().isEmpty();
    }

    /**
     * 获取预测置信度描述
     */
    public String getForecastConfidenceDescription() {
        if (volatilityForecast == null || volatilityForecast.getForecastConfidence() == null) {
            return "预测置信度未知";
        }
        
        double confidence = volatilityForecast.getForecastConfidence().doubleValue();
        if (confidence >= 0.8) {
            return "高置信度 (" + (confidence * 100) + "%)";
        } else if (confidence >= 0.6) {
            return "中等置信度 (" + (confidence * 100) + "%)";
        } else {
            return "低置信度 (" + (confidence * 100) + "%)";
        }
    }

    /**
     * 获取最优期权策略
     */
    public String getOptimalOptionStrategy() {
        if (volatilityTradingStrategy == null || 
            volatilityTradingStrategy.getOptionStrategyRecommendations() == null ||
            volatilityTradingStrategy.getOptionStrategyRecommendations().isEmpty()) {
            return "暂无期权策略建议";
        }
        
        OptionStrategyRecommendation bestStrategy = volatilityTradingStrategy.getOptionStrategyRecommendations().get(0);
        return String.format("%s - %s",
            bestStrategy.getStrategyName(),
            bestStrategy.getStrategySuitability());
    }
}