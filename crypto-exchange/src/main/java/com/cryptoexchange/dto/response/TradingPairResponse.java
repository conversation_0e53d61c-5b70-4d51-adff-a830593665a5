package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易对信息响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "交易对信息响应")
public class TradingPairResponse {

    @Schema(description = "交易对代码")
    private String symbol;

    @Schema(description = "交易对名称")
    private String symbolName;

    @Schema(description = "基础货币代码")
    private String baseAsset;

    @Schema(description = "基础货币名称")
    private String baseAssetName;

    @Schema(description = "计价货币代码")
    private String quoteAsset;

    @Schema(description = "计价货币名称")
    private String quoteAssetName;

    @Schema(description = "交易状态")
    private String status;

    @Schema(description = "交易状态描述")
    private String statusDescription;

    @Schema(description = "是否支持现货交易")
    private Boolean spotTradingEnabled;

    @Schema(description = "是否支持杠杆交易")
    private Boolean marginTradingEnabled;

    @Schema(description = "是否支持期货交易")
    private Boolean futuresTradingEnabled;

    @Schema(description = "是否支持期权交易")
    private Boolean optionsTradingEnabled;

    @Schema(description = "价格精度")
    private Integer pricePrecision;

    @Schema(description = "数量精度")
    private Integer quantityPrecision;

    @Schema(description = "最小交易数量")
    private BigDecimal minTradeQuantity;

    @Schema(description = "最大交易数量")
    private BigDecimal maxTradeQuantity;

    @Schema(description = "最小交易金额")
    private BigDecimal minTradeAmount;

    @Schema(description = "最大交易金额")
    private BigDecimal maxTradeAmount;

    @Schema(description = "最小价格变动")
    private BigDecimal tickSize;

    @Schema(description = "最小数量变动")
    private BigDecimal stepSize;

    @Schema(description = "手续费率")
    private FeeRates feeRates;

    @Schema(description = "当前价格")
    private BigDecimal currentPrice;

    @Schema(description = "24小时价格变化")
    private BigDecimal priceChange24h;

    @Schema(description = "24小时价格变化百分比")
    private BigDecimal priceChangePercent24h;

    @Schema(description = "24小时最高价")
    private BigDecimal highPrice24h;

    @Schema(description = "24小时最低价")
    private BigDecimal lowPrice24h;

    @Schema(description = "24小时成交量")
    private BigDecimal volume24h;

    @Schema(description = "24小时成交额")
    private BigDecimal amount24h;

    @Schema(description = "24小时成交笔数")
    private Long tradeCount24h;

    @Schema(description = "市值")
    private BigDecimal marketCap;

    @Schema(description = "流通市值")
    private BigDecimal circulatingMarketCap;

    @Schema(description = "市值排名")
    private Integer marketCapRank;

    @Schema(description = "流动性评分")
    private BigDecimal liquidityScore;

    @Schema(description = "波动率")
    private BigDecimal volatility;

    @Schema(description = "风险等级")
    private String riskLevel;

    @Schema(description = "风险评分")
    private BigDecimal riskScore;

    @Schema(description = "推荐等级")
    private String recommendationLevel;

    @Schema(description = "热度指数")
    private BigDecimal popularityIndex;

    @Schema(description = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime listingTime;

    @Schema(description = "是否为新币")
    private Boolean isNewListing;

    @Schema(description = "交易限制")
    private TradingRestrictions tradingRestrictions;

    @Schema(description = "技术信息")
    private TechnicalInfo technicalInfo;

    @Schema(description = "市场数据")
    private MarketData marketData;

    @Schema(description = "项目信息")
    private ProjectInfo projectInfo;

    @Schema(description = "社交媒体")
    private SocialMediaInfo socialMediaInfo;

    @Schema(description = "合规信息")
    private ComplianceInfo complianceInfo;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "备注")
    private String remarks;

    /**
     * 手续费率
     */
    @Data
    @Schema(description = "手续费率")
    public static class FeeRates {
        
        @Schema(description = "现货交易手续费率")
        private BigDecimal spotTradingFee;
        
        @Schema(description = "杠杆交易手续费率")
        private BigDecimal marginTradingFee;
        
        @Schema(description = "期货交易手续费率")
        private BigDecimal futuresTradingFee;
        
        @Schema(description = "Maker手续费率")
        private BigDecimal makerFee;
        
        @Schema(description = "Taker手续费率")
        private BigDecimal takerFee;
        
        @Schema(description = "VIP等级折扣")
        private BigDecimal vipDiscount;
        
        @Schema(description = "持币折扣")
        private BigDecimal holdingDiscount;
    }

    /**
     * 交易限制
     */
    @Data
    @Schema(description = "交易限制")
    public static class TradingRestrictions {
        
        @Schema(description = "是否暂停交易")
        private Boolean tradingSuspended;
        
        @Schema(description = "暂停原因")
        private String suspensionReason;
        
        @Schema(description = "预计恢复时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expectedResumeTime;
        
        @Schema(description = "地区限制")
        private List<String> regionRestrictions;
        
        @Schema(description = "KYC要求等级")
        private String kycRequiredLevel;
        
        @Schema(description = "最小持仓要求")
        private BigDecimal minHoldingRequirement;
        
        @Schema(description = "交易时间限制")
        private List<TradingTimeRestriction> tradingTimeRestrictions;
    }

    /**
     * 交易时间限制
     */
    @Data
    @Schema(description = "交易时间限制")
    public static class TradingTimeRestriction {
        
        @Schema(description = "开始时间")
        private String startTime;
        
        @Schema(description = "结束时间")
        private String endTime;
        
        @Schema(description = "时区")
        private String timezone;
        
        @Schema(description = "星期几")
        private List<Integer> daysOfWeek;
    }

    /**
     * 技术信息
     */
    @Data
    @Schema(description = "技术信息")
    public static class TechnicalInfo {
        
        @Schema(description = "区块链网络")
        private String blockchain;
        
        @Schema(description = "合约地址")
        private String contractAddress;
        
        @Schema(description = "代币标准")
        private String tokenStandard;
        
        @Schema(description = "总供应量")
        private BigDecimal totalSupply;
        
        @Schema(description = "流通供应量")
        private BigDecimal circulatingSupply;
        
        @Schema(description = "最大供应量")
        private BigDecimal maxSupply;
        
        @Schema(description = "通胀率")
        private BigDecimal inflationRate;
        
        @Schema(description = "销毁机制")
        private Boolean hasBurnMechanism;
        
        @Schema(description = "质押机制")
        private Boolean hasStakingMechanism;
    }

    /**
     * 市场数据
     */
    @Data
    @Schema(description = "市场数据")
    public static class MarketData {
        
        @Schema(description = "7天价格变化")
        private BigDecimal priceChange7d;
        
        @Schema(description = "30天价格变化")
        private BigDecimal priceChange30d;
        
        @Schema(description = "历史最高价")
        private BigDecimal allTimeHigh;
        
        @Schema(description = "历史最高价时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime allTimeHighTime;
        
        @Schema(description = "历史最低价")
        private BigDecimal allTimeLow;
        
        @Schema(description = "历史最低价时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime allTimeLowTime;
        
        @Schema(description = "52周最高价")
        private BigDecimal high52Week;
        
        @Schema(description = "52周最低价")
        private BigDecimal low52Week;
        
        @Schema(description = "平均日成交量")
        private BigDecimal averageDailyVolume;
        
        @Schema(description = "换手率")
        private BigDecimal turnoverRate;
    }

    /**
     * 项目信息
     */
    @Data
    @Schema(description = "项目信息")
    public static class ProjectInfo {
        
        @Schema(description = "项目名称")
        private String projectName;
        
        @Schema(description = "项目描述")
        private String projectDescription;
        
        @Schema(description = "项目类型")
        private String projectType;
        
        @Schema(description = "项目分类")
        private String projectCategory;
        
        @Schema(description = "官方网站")
        private String officialWebsite;
        
        @Schema(description = "白皮书链接")
        private String whitepaperUrl;
        
        @Schema(description = "GitHub链接")
        private String githubUrl;
        
        @Schema(description = "团队信息")
        private String teamInfo;
        
        @Schema(description = "投资机构")
        private List<String> investors;
        
        @Schema(description = "合作伙伴")
        private List<String> partners;
    }

    /**
     * 社交媒体信息
     */
    @Data
    @Schema(description = "社交媒体信息")
    public static class SocialMediaInfo {
        
        @Schema(description = "Twitter链接")
        private String twitterUrl;
        
        @Schema(description = "Twitter关注者数")
        private Integer twitterFollowers;
        
        @Schema(description = "Telegram链接")
        private String telegramUrl;
        
        @Schema(description = "Telegram成员数")
        private Integer telegramMembers;
        
        @Schema(description = "Discord链接")
        private String discordUrl;
        
        @Schema(description = "Reddit链接")
        private String redditUrl;
        
        @Schema(description = "Medium链接")
        private String mediumUrl;
        
        @Schema(description = "社交媒体活跃度")
        private BigDecimal socialMediaActivity;
        
        @Schema(description = "社区情绪")
        private String communitySentiment;
    }

    /**
     * 合规信息
     */
    @Data
    @Schema(description = "合规信息")
    public static class ComplianceInfo {
        
        @Schema(description = "监管状态")
        private String regulatoryStatus;
        
        @Schema(description = "合规等级")
        private String complianceLevel;
        
        @Schema(description = "审计报告")
        private List<AuditReport> auditReports;
        
        @Schema(description = "法律意见书")
        private String legalOpinion;
        
        @Schema(description = "反洗钱合规")
        private Boolean amlCompliant;
        
        @Schema(description = "KYC合规")
        private Boolean kycCompliant;
        
        @Schema(description = "监管机构")
        private List<String> regulatoryBodies;
    }

    /**
     * 审计报告
     */
    @Data
    @Schema(description = "审计报告")
    public static class AuditReport {
        
        @Schema(description = "审计机构")
        private String auditor;
        
        @Schema(description = "审计时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime auditDate;
        
        @Schema(description = "审计结果")
        private String auditResult;
        
        @Schema(description = "报告链接")
        private String reportUrl;
        
        @Schema(description = "审计评分")
        private BigDecimal auditScore;
    }
}