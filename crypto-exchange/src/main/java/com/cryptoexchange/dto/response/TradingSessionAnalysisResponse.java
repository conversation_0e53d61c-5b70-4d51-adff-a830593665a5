package com.cryptoexchange.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 交易时段分析响应
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradingSessionAnalysisResponse {

    /**
     * 交易对符号
     */
    private String symbol;

    /**
     * 分析时间
     */
    private LocalDateTime analysisTime;

    /**
     * 当前交易时段
     */
    private TradingSession currentTradingSession;

    /**
     * 时段分析结果
     */
    private List<SessionAnalysisResult> sessionAnalysisResults;

    /**
     * 时段比较分析
     */
    private SessionComparativeAnalysis sessionComparativeAnalysis;

    /**
     * 时段模式识别
     */
    private SessionPatternRecognition sessionPatternRecognition;

    /**
     * 时段流动性分析
     */
    private SessionLiquidityAnalysis sessionLiquidityAnalysis;

    /**
     * 时段波动性分析
     */
    private SessionVolatilityAnalysis sessionVolatilityAnalysis;

    /**
     * 时段交易策略
     */
    private SessionTradingStrategy sessionTradingStrategy;

    /**
     * 时段风险评估
     */
    private SessionRiskAssessment sessionRiskAssessment;

    /**
     * 时段预测分析
     */
    private SessionForecastAnalysis sessionForecastAnalysis;

    /**
     * 全球市场影响
     */
    private GlobalMarketImpact globalMarketImpact;

    /**
     * 时段优化建议
     */
    private SessionOptimizationRecommendation sessionOptimizationRecommendation;

    /**
     * 交易时段
     */
    public enum TradingSession {
        ASIAN_MORNING("亚洲早盘", LocalTime.of(0, 0), LocalTime.of(6, 0), "亚洲市场开盘时段"),
        ASIAN_AFTERNOON("亚洲午盘", LocalTime.of(6, 0), LocalTime.of(12, 0), "亚洲市场午盘时段"),
        EUROPEAN_MORNING("欧洲早盘", LocalTime.of(12, 0), LocalTime.of(18, 0), "欧洲市场开盘时段"),
        AMERICAN_EVENING("美洲晚盘", LocalTime.of(18, 0), LocalTime.of(24, 0), "美洲市场交易时段"),
        OVERLAP_ASIAN_EUROPEAN("亚欧重叠", LocalTime.of(12, 0), LocalTime.of(14, 0), "亚洲欧洲市场重叠"),
        OVERLAP_EUROPEAN_AMERICAN("欧美重叠", LocalTime.of(18, 0), LocalTime.of(20, 0), "欧洲美洲市场重叠"),
        LOW_ACTIVITY("低活跃期", LocalTime.of(22, 0), LocalTime.of(2, 0), "全球市场低活跃时段");

        private final String description;
        private final LocalTime startTime;
        private final LocalTime endTime;
        private final String detail;

        TradingSession(String description, LocalTime startTime, LocalTime endTime, String detail) {
            this.description = description;
            this.startTime = startTime;
            this.endTime = endTime;
            this.detail = detail;
        }

        public String getDescription() {
            return description;
        }

        public LocalTime getStartTime() {
            return startTime;
        }

        public LocalTime getEndTime() {
            return endTime;
        }

        public String getDetail() {
            return detail;
        }

        public static TradingSession fromTime(LocalTime time) {
            for (TradingSession session : values()) {
                if (isTimeInSession(time, session)) {
                    return session;
                }
            }
            return LOW_ACTIVITY;
        }

        private static boolean isTimeInSession(LocalTime time, TradingSession session) {
            LocalTime start = session.getStartTime();
            LocalTime end = session.getEndTime();
            
            if (start.isBefore(end)) {
                return !time.isBefore(start) && time.isBefore(end);
            } else {
                // 跨越午夜的时段
                return !time.isBefore(start) || time.isBefore(end);
            }
        }
    }

    /**
     * 时段分析结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionAnalysisResult {
        /**
         * 交易时段
         */
        private TradingSession tradingSession;

        /**
         * 时段统计
         */
        private SessionStatistics sessionStatistics;

        /**
         * 价格行为分析
         */
        private SessionPriceBehavior sessionPriceBehavior;

        /**
         * 成交量分析
         */
        private SessionVolumeAnalysis sessionVolumeAnalysis;

        /**
         * 时段表现评分
         */
        private BigDecimal sessionPerformanceScore;

        /**
         * 时段特征
         */
        private List<String> sessionCharacteristics;

        /**
         * 时段优势
         */
        private List<String> sessionAdvantages;

        /**
         * 时段劣势
         */
        private List<String> sessionDisadvantages;
    }

    /**
     * 时段统计
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionStatistics {
        /**
         * 平均价格变动
         */
        private BigDecimal averagePriceChange;

        /**
         * 平均成交量
         */
        private BigDecimal averageVolume;

        /**
         * 平均波动率
         */
        private BigDecimal averageVolatility;

        /**
         * 平均点差
         */
        private BigDecimal averageSpread;

        /**
         * 交易次数
         */
        private Integer tradeCount;

        /**
         * 盈利交易比例
         */
        private BigDecimal profitableTradeRatio;

        /**
         * 最大单笔收益
         */
        private BigDecimal maxSingleProfit;

        /**
         * 最大单笔损失
         */
        private BigDecimal maxSingleLoss;

        /**
         * 平均持仓时间
         */
        private BigDecimal averageHoldingTime;
    }

    /**
     * 时段价格行为
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionPriceBehavior {
        /**
         * 开盘价格行为
         */
        private PriceBehaviorPattern openingBehavior;

        /**
         * 收盘价格行为
         */
        private PriceBehaviorPattern closingBehavior;

        /**
         * 时段内趋势
         */
        private String intraSessionTrend;

        /**
         * 价格反转频率
         */
        private BigDecimal priceReversalFrequency;

        /**
         * 突破概率
         */
        private BigDecimal breakoutProbability;

        /**
         * 假突破概率
         */
        private BigDecimal falseBreakoutProbability;

        /**
         * 支撑阻力有效性
         */
        private BigDecimal supportResistanceEffectiveness;
    }

    /**
     * 价格行为模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceBehaviorPattern {
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式强度
         */
        private BigDecimal patternStrength;

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 历史成功率
         */
        private BigDecimal historicalSuccessRate;
    }

    /**
     * 时段成交量分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionVolumeAnalysis {
        /**
         * 成交量分布
         */
        private VolumeDistribution volumeDistribution;

        /**
         * 成交量趋势
         */
        private String volumeTrend;

        /**
         * 成交量异常
         */
        private List<VolumeAnomaly> volumeAnomalies;

        /**
         * 成交量价格关系
         */
        private VolumePriceRelationship volumePriceRelationship;

        /**
         * 流动性指标
         */
        private LiquidityIndicators liquidityIndicators;
    }

    /**
     * 成交量分布
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeDistribution {
        /**
         * 时段内成交量分布
         */
        private Map<String, BigDecimal> hourlyVolumeDistribution;

        /**
         * 成交量集中度
         */
        private BigDecimal volumeConcentration;

        /**
         * 峰值成交量时间
         */
        private LocalTime peakVolumeTime;

        /**
         * 低谷成交量时间
         */
        private LocalTime troughVolumeTime;

        /**
         * 成交量变异系数
         */
        private BigDecimal volumeVariationCoefficient;
    }

    /**
     * 成交量异常
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumeAnomaly {
        /**
         * 异常时间
         */
        private LocalDateTime anomalyTime;

        /**
         * 异常类型
         */
        private String anomalyType;

        /**
         * 异常程度
         */
        private BigDecimal anomalyDegree;

        /**
         * 异常成交量
         */
        private BigDecimal anomalyVolume;

        /**
         * 正常成交量
         */
        private BigDecimal normalVolume;

        /**
         * 可能原因
         */
        private List<String> possibleCauses;
    }

    /**
     * 成交量价格关系
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolumePriceRelationship {
        /**
         * 相关系数
         */
        private BigDecimal correlationCoefficient;

        /**
         * 关系强度
         */
        private String relationshipStrength;

        /**
         * 关系类型
         */
        private String relationshipType;

        /**
         * 价量背离信号
         */
        private List<String> divergenceSignals;

        /**
         * 价量确认信号
         */
        private List<String> confirmationSignals;
    }

    /**
     * 流动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityIndicators {
        /**
         * 买卖价差
         */
        private BigDecimal bidAskSpread;

        /**
         * 市场深度
         */
        private BigDecimal marketDepth;

        /**
         * 价格冲击
         */
        private BigDecimal priceImpact;

        /**
         * 流动性比率
         */
        private BigDecimal liquidityRatio;

        /**
         * 交易成本
         */
        private BigDecimal tradingCost;
    }

    /**
     * 时段比较分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionComparativeAnalysis {
        /**
         * 最佳交易时段
         */
        private TradingSession bestTradingSession;

        /**
         * 最差交易时段
         */
        private TradingSession worstTradingSession;

        /**
         * 时段排名
         */
        private List<SessionRanking> sessionRankings;

        /**
         * 时段相关性分析
         */
        private SessionCorrelationAnalysis sessionCorrelationAnalysis;

        /**
         * 时段转换分析
         */
        private SessionTransitionAnalysis sessionTransitionAnalysis;

        public TradingSession getBestTradingSession() {
            return bestTradingSession;
        }
    }

    /**
     * 时段排名
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionRanking {
        /**
         * 交易时段
         */
        private TradingSession tradingSession;

        /**
         * 排名
         */
        private Integer ranking;

        /**
         * 综合评分
         */
        private BigDecimal overallScore;

        /**
         * 盈利能力评分
         */
        private BigDecimal profitabilityScore;

        /**
         * 风险评分
         */
        private BigDecimal riskScore;

        /**
         * 流动性评分
         */
        private BigDecimal liquidityScore;

        /**
         * 排名变化
         */
        private Integer rankingChange;
    }

    /**
     * 时段相关性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionCorrelationAnalysis {
        /**
         * 时段间相关性矩阵
         */
        private Map<String, Map<String, BigDecimal>> sessionCorrelationMatrix;

        /**
         * 高相关时段组
         */
        private List<List<TradingSession>> highCorrelationGroups;

        /**
         * 独立时段
         */
        private List<TradingSession> independentSessions;

        /**
         * 相关性稳定性
         */
        private BigDecimal correlationStability;
    }

    /**
     * 时段转换分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionTransitionAnalysis {
        /**
         * 转换效应
         */
        private List<TransitionEffect> transitionEffects;

        /**
         * 最佳转换时机
         */
        private List<OptimalTransitionTiming> optimalTransitionTimings;

        /**
         * 转换风险
         */
        private Map<String, BigDecimal> transitionRisks;

        /**
         * 转换策略建议
         */
        private List<String> transitionStrategyRecommendations;
    }

    /**
     * 转换效应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransitionEffect {
        /**
         * 源时段
         */
        private TradingSession fromSession;

        /**
         * 目标时段
         */
        private TradingSession toSession;

        /**
         * 转换效应强度
         */
        private BigDecimal transitionEffectStrength;

        /**
         * 价格影响
         */
        private BigDecimal priceImpact;

        /**
         * 成交量影响
         */
        private BigDecimal volumeImpact;

        /**
         * 波动性影响
         */
        private BigDecimal volatilityImpact;
    }

    /**
     * 最佳转换时机
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimalTransitionTiming {
        /**
         * 转换时间
         */
        private LocalTime transitionTime;

        /**
         * 转换类型
         */
        private String transitionType;

        /**
         * 成功概率
         */
        private BigDecimal successProbability;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险水平
         */
        private BigDecimal riskLevel;
    }

    /**
     * 时段模式识别
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionPatternRecognition {
        /**
         * 识别的模式
         */
        private List<SessionPattern> identifiedPatterns;

        /**
         * 模式强度分析
         */
        private PatternStrengthAnalysis patternStrengthAnalysis;

        /**
         * 模式预测
         */
        private PatternForecast patternForecast;

        /**
         * 模式交易信号
         */
        private List<PatternTradingSignal> patternTradingSignals;

        public List<PatternTradingSignal> getPatternTradingSignals() {
            return patternTradingSignals;
        }
    }

    /**
     * 时段模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionPattern {
        /**
         * 模式名称
         */
        private String patternName;

        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 模式描述
         */
        private String patternDescription;

        /**
         * 模式频率
         */
        private BigDecimal patternFrequency;

        /**
         * 模式可靠性
         */
        private BigDecimal patternReliability;

        /**
         * 历史表现
         */
        private PatternHistoricalPerformance historicalPerformance;

        /**
         * 模式条件
         */
        private List<String> patternConditions;
    }

    /**
     * 模式历史表现
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternHistoricalPerformance {
        /**
         * 成功率
         */
        private BigDecimal successRate;

        /**
         * 平均收益
         */
        private BigDecimal averageReturn;

        /**
         * 最大收益
         */
        private BigDecimal maxReturn;

        /**
         * 最大损失
         */
        private BigDecimal maxLoss;

        /**
         * 风险收益比
         */
        private BigDecimal riskReturnRatio;

        /**
         * 出现次数
         */
        private Integer occurrenceCount;
    }

    /**
     * 模式强度分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternStrengthAnalysis {
        /**
         * 整体模式强度
         */
        private BigDecimal overallPatternStrength;

        /**
         * 强模式列表
         */
        private List<String> strongPatterns;

        /**
         * 弱模式列表
         */
        private List<String> weakPatterns;

        /**
         * 模式稳定性
         */
        private BigDecimal patternStability;

        /**
         * 模式演化趋势
         */
        private String patternEvolutionTrend;
    }

    /**
     * 模式预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternForecast {
        /**
         * 下一时段预测模式
         */
        private String nextSessionPredictedPattern;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        /**
         * 模式持续概率
         */
        private BigDecimal patternContinuationProbability;

        /**
         * 模式转换概率
         */
        private Map<String, BigDecimal> patternTransitionProbabilities;

        /**
         * 预测时间范围
         */
        private String forecastTimeRange;
    }

    /**
     * 模式交易信号
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PatternTradingSignal {
        /**
         * 信号类型
         */
        private String signalType;

        /**
         * 信号强度
         */
        private BigDecimal signalStrength;

        /**
         * 交易方向
         */
        private String tradingDirection;

        /**
         * 入场时机
         */
        private String entryTiming;

        /**
         * 出场时机
         */
        private String exitTiming;

        /**
         * 止损建议
         */
        private BigDecimal stopLossRecommendation;

        /**
         * 止盈建议
         */
        private BigDecimal takeProfitRecommendation;
    }

    /**
     * 时段流动性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionLiquidityAnalysis {
        /**
         * 流动性等级
         */
        private String liquidityLevel;

        /**
         * 流动性指标
         */
        private LiquidityMetrics liquidityMetrics;

        /**
         * 流动性变化趋势
         */
        private String liquidityTrend;

        /**
         * 流动性风险评估
         */
        private LiquidityRiskAssessment liquidityRiskAssessment;

        /**
         * 流动性提供者分析
         */
        private LiquidityProviderAnalysis liquidityProviderAnalysis;

        public String getLiquidityLevel() {
            return liquidityLevel;
        }

        public String getLiquidityTrend() {
            return liquidityTrend;
        }
    }

    /**
     * 流动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityMetrics {
        /**
         * 平均买卖价差
         */
        private BigDecimal averageBidAskSpread;

        /**
         * 市场深度
         */
        private BigDecimal marketDepth;

        /**
         * 订单簿厚度
         */
        private BigDecimal orderBookThickness;

        /**
         * 价格冲击成本
         */
        private BigDecimal priceImpactCost;

        /**
         * 流动性比率
         */
        private BigDecimal liquidityRatio;

        /**
         * 成交量加权平均价差
         */
        private BigDecimal volumeWeightedAverageSpread;
    }

    /**
     * 流动性风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityRiskAssessment {
        /**
         * 流动性风险等级
         */
        private String liquidityRiskLevel;

        /**
         * 流动性缺口风险
         */
        private BigDecimal liquidityGapRisk;

        /**
         * 市场冲击风险
         */
        private BigDecimal marketImpactRisk;

        /**
         * 流动性枯竭概率
         */
        private BigDecimal liquidityDryUpProbability;

        /**
         * 风险缓解建议
         */
        private List<String> riskMitigationRecommendations;
    }

    /**
     * 流动性提供者分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiquidityProviderAnalysis {
        /**
         * 主要流动性提供者
         */
        private List<String> majorLiquidityProviders;

        /**
         * 流动性集中度
         */
        private BigDecimal liquidityConcentration;

        /**
         * 提供者行为分析
         */
        private Map<String, String> providerBehaviorAnalysis;

        /**
         * 流动性稳定性
         */
        private BigDecimal liquidityStability;
    }

    /**
     * 时段波动性分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionVolatilityAnalysis {
        /**
         * 波动性等级
         */
        private String volatilityLevel;

        /**
         * 波动性指标
         */
        private VolatilityMetrics volatilityMetrics;

        /**
         * 波动性模式
         */
        private VolatilityPattern volatilityPattern;

        /**
         * 波动性预测
         */
        private VolatilityForecast volatilityForecast;

        /**
         * 波动性风险评估
         */
        private VolatilityRiskAssessment volatilityRiskAssessment;

        public String getVolatilityLevel() {
            return volatilityLevel;
        }

        public VolatilityMetrics getVolatilityMetrics() {
            return volatilityMetrics;
        }
    }

    /**
     * 波动性指标
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityMetrics {
        /**
         * 历史波动率
         */
        private BigDecimal historicalVolatility;

        /**
         * 已实现波动率
         */
        private BigDecimal realizedVolatility;

        /**
         * 波动率范围
         */
        private BigDecimal volatilityRange;

        /**
         * 波动率偏度
         */
        private BigDecimal volatilitySkewness;

        /**
         * 波动率峰度
         */
        private BigDecimal volatilityKurtosis;

        public BigDecimal getHistoricalVolatility() {
            return historicalVolatility;
        }
    }

    /**
     * 波动性模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityPattern {
        /**
         * 模式类型
         */
        private String patternType;

        /**
         * 波动性聚类
         */
        private boolean volatilityClustering;

        /**
         * 波动性持续性
         */
        private BigDecimal volatilityPersistence;

        /**
         * 波动性周期性
         */
        private VolatilityCyclicality volatilityCyclicality;
    }

    /**
     * 波动性周期性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityCyclicality {
        /**
         * 是否存在周期性
         */
        private boolean hasCyclicality;

        /**
         * 周期长度
         */
        private Integer cycleLength;

        /**
         * 周期强度
         */
        private BigDecimal cycleStrength;

        /**
         * 当前周期位置
         */
        private BigDecimal currentCyclePosition;
    }

    /**
     * 波动性预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityForecast {
        /**
         * 下一时段波动率预测
         */
        private BigDecimal nextSessionVolatilityForecast;

        /**
         * 预测置信区间
         */
        private ConfidenceInterval forecastConfidenceInterval;

        /**
         * 预测准确性
         */
        private BigDecimal forecastAccuracy;

        /**
         * 波动率趋势预测
         */
        private String volatilityTrendForecast;

        public String getVolatilityTrendForecast() {
            return volatilityTrendForecast;
        }
    }

    /**
     * 置信区间
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfidenceInterval {
        /**
         * 置信水平
         */
        private BigDecimal confidenceLevel;

        /**
         * 下界
         */
        private BigDecimal lowerBound;

        /**
         * 上界
         */
        private BigDecimal upperBound;
    }

    /**
     * 波动性风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VolatilityRiskAssessment {
        /**
         * 波动性风险等级
         */
        private String volatilityRiskLevel;

        /**
         * 极端波动概率
         */
        private BigDecimal extremeVolatilityProbability;

        /**
         * 波动性冲击风险
         */
        private BigDecimal volatilityShockRisk;

        /**
         * 风险调整收益
         */
        private BigDecimal riskAdjustedReturn;

        /**
         * 风险等级
         */
        private String riskLevel;

        public String getRiskLevel() {
            return riskLevel;
        }
    }

    /**
     * 时段交易策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionTradingStrategy {
        /**
         * 推荐策略
         */
        private List<TradingStrategyRecommendation> recommendedStrategies;

        /**
         * 时段特定策略
         */
        private Map<TradingSession, String> sessionSpecificStrategies;

        /**
         * 策略优化建议
         */
        private List<String> strategyOptimizationRecommendations;

        /**
         * 风险管理建议
         */
        private List<String> riskManagementRecommendations;

        public List<TradingStrategyRecommendation> getRecommendedStrategies() {
            return recommendedStrategies;
        }

        public Map<TradingSession, String> getSessionSpecificStrategies() {
            return sessionSpecificStrategies;
        }
    }

    /**
     * 交易策略建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradingStrategyRecommendation {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略类型
         */
        private String strategyType;

        /**
         * 适用时段
         */
        private List<TradingSession> applicableSessions;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 预期收益
         */
        private BigDecimal expectedReturn;

        /**
         * 风险水平
         */
        private BigDecimal riskLevel;

        /**
         * 成功概率
         */
        private BigDecimal successProbability;

        /**
         * 策略参数
         */
        private Map<String, String> strategyParameters;

        public String getStrategyName() {
            return strategyName;
        }

        public String getStrategyDescription() {
            return strategyDescription;
        }

        public BigDecimal getSuccessProbability() {
            return successProbability;
        }
    }

    /**
     * 时段风险评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionRiskAssessment {
        /**
         * 整体风险等级
         */
        private String overallRiskLevel;

        /**
         * 时段风险分析
         */
        private Map<TradingSession, SessionRiskAnalysis> sessionRiskAnalysis;

        /**
         * 风险因子识别
         */
        private List<RiskFactor> riskFactors;

        /**
         * 风险缓解策略
         */
        private List<String> riskMitigationStrategies;

        public Map<TradingSession, SessionRiskAnalysis> getSessionRiskAnalysis() {
            return sessionRiskAnalysis;
        }

        public String getOverallRiskLevel() {
            return overallRiskLevel;
        }
    }

    /**
     * 时段风险分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionRiskAnalysis {
        /**
         * 风险等级
         */
        private String riskLevel;

        /**
         * 最大回撤风险
         */
        private BigDecimal maxDrawdownRisk;

        /**
         * 流动性风险
         */
        private BigDecimal liquidityRisk;

        /**
         * 波动性风险
         */
        private BigDecimal volatilityRisk;

        /**
         * 市场风险
         */
        private BigDecimal marketRisk;

        /**
         * 风险调整收益
         */
        private BigDecimal riskAdjustedReturn;

        public String getRiskLevel() {
            return riskLevel;
        }
    }

    /**
     * 风险因子
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactor {
        /**
         * 风险因子名称
         */
        private String factorName;

        /**
         * 风险因子类型
         */
        private String factorType;

        /**
         * 风险程度
         */
        private BigDecimal riskDegree;

        /**
         * 影响时段
         */
        private List<TradingSession> affectedSessions;

        /**
         * 风险描述
         */
        private String riskDescription;

        /**
         * 缓解措施
         */
        private List<String> mitigationMeasures;
    }

    /**
     * 时段预测分析
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionForecastAnalysis {
        /**
         * 下一时段预测
         */
        private SessionForecast nextSessionForecast;

        /**
         * 短期预测
         */
        private List<SessionForecast> shortTermForecasts;

        /**
         * 预测模型评估
         */
        private ForecastModelEvaluation forecastModelEvaluation;

        /**
         * 预测置信度
         */
        private BigDecimal forecastConfidence;

        public SessionForecast getNextSessionForecast() {
            return nextSessionForecast;
        }
    }

    /**
     * 时段预测
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionForecast {
        /**
         * 预测时段
         */
        private TradingSession forecastSession;

        /**
         * 预测价格变动
         */
        private BigDecimal predictedPriceChange;

        /**
         * 预测成交量
         */
        private BigDecimal predictedVolume;

        /**
         * 预测波动率
         */
        private BigDecimal predictedVolatility;

        /**
         * 预测趋势方向
         */
        private String predictedTrendDirection;

        /**
         * 预测置信区间
         */
        private ConfidenceInterval predictionConfidenceInterval;

        public TradingSession getForecastSession() {
            return forecastSession;
        }

        public String getPredictedTrendDirection() {
            return predictedTrendDirection;
        }

        public BigDecimal getPredictedPriceChange() {
            return predictedPriceChange;
        }
    }

    /**
     * 预测模型评估
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ForecastModelEvaluation {
        /**
         * 模型准确性
         */
        private BigDecimal modelAccuracy;

        /**
         * 模型精确度
         */
        private BigDecimal modelPrecision;

        /**
         * 模型召回率
         */
        private BigDecimal modelRecall;

        /**
         * F1分数
         */
        private BigDecimal f1Score;

        /**
         * 模型稳定性
         */
        private BigDecimal modelStability;
    }

    /**
     * 全球市场影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GlobalMarketImpact {
        /**
         * 主要市场影响
         */
        private Map<String, MarketImpact> majorMarketImpacts;

        /**
         * 时区效应
         */
        private TimezoneEffect timezoneEffect;

        /**
         * 跨市场相关性
         */
        private CrossMarketCorrelation crossMarketCorrelation;

        /**
         * 全球事件影响
         */
        private List<GlobalEventImpact> globalEventImpacts;

        public Map<String, MarketImpact> getMajorMarketImpacts() {
            return majorMarketImpacts;
        }
    }

    /**
     * 市场影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MarketImpact {
        /**
         * 市场名称
         */
        private String marketName;

        /**
         * 影响强度
         */
        private BigDecimal impactStrength;

        /**
         * 影响方向
         */
        private String impactDirection;

        /**
         * 影响时段
         */
        private List<TradingSession> impactedSessions;

        /**
         * 影响描述
         */
        private String impactDescription;

        public String getMarketName() {
            return marketName;
        }

        public BigDecimal getImpactStrength() {
            return impactStrength;
        }

        public String getImpactDirection() {
            return impactDirection;
        }
    }

    /**
     * 时区效应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimezoneEffect {
        /**
         * 主要时区影响
         */
        private Map<String, BigDecimal> majorTimezoneImpacts;

        /**
         * 时区转换效应
         */
        private List<TimezoneTransitionEffect> timezoneTransitionEffects;

        /**
         * 夏令时影响
         */
        private DaylightSavingTimeImpact daylightSavingTimeImpact;
    }

    /**
     * 时区转换效应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimezoneTransitionEffect {
        /**
         * 源时区
         */
        private String fromTimezone;

        /**
         * 目标时区
         */
        private String toTimezone;

        /**
         * 转换效应强度
         */
        private BigDecimal transitionEffectStrength;

        /**
         * 转换时间
         */
        private LocalTime transitionTime;
    }

    /**
     * 夏令时影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DaylightSavingTimeImpact {
        /**
         * 是否受夏令时影响
         */
        private boolean isDaylightSavingTimeAffected;

        /**
         * 影响程度
         */
        private BigDecimal impactDegree;

        /**
         * 调整建议
         */
        private List<String> adjustmentRecommendations;
    }

    /**
     * 跨市场相关性
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrossMarketCorrelation {
        /**
         * 相关性矩阵
         */
        private Map<String, Map<String, BigDecimal>> correlationMatrix;

        /**
         * 强相关市场
         */
        private List<String> stronglyCorrelatedMarkets;

        /**
         * 相关性稳定性
         */
        private BigDecimal correlationStability;

        /**
         * 相关性变化趋势
         */
        private String correlationChangeTrend;
    }

    /**
     * 全球事件影响
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GlobalEventImpact {
        /**
         * 事件名称
         */
        private String eventName;

        /**
         * 事件类型
         */
        private String eventType;

        /**
         * 事件时间
         */
        private LocalDateTime eventTime;

        /**
         * 影响强度
         */
        private BigDecimal impactStrength;

        /**
         * 影响持续时间
         */
        private Integer impactDuration;

        /**
         * 受影响时段
         */
        private List<TradingSession> affectedSessions;
    }

    /**
     * 时段优化建议
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionOptimizationRecommendation {
        /**
         * 优化目标
         */
        private String optimizationObjective;

        /**
         * 推荐时段组合
         */
        private List<TradingSession> recommendedSessionCombination;

        /**
         * 时段权重分配
         */
        private Map<TradingSession, BigDecimal> sessionWeightAllocation;

        /**
         * 优化策略
         */
        private List<OptimizationStrategy> optimizationStrategies;

        /**
         * 预期改进效果
         */
        private ExpectedImprovementEffect expectedImprovementEffect;

        public String getOptimizationObjective() {
            return optimizationObjective;
        }

        public List<TradingSession> getRecommendedSessionCombination() {
            return recommendedSessionCombination;
        }
    }

    /**
     * 优化策略
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptimizationStrategy {
        /**
         * 策略名称
         */
        private String strategyName;

        /**
         * 策略描述
         */
        private String strategyDescription;

        /**
         * 实施步骤
         */
        private List<String> implementationSteps;

        /**
         * 预期效果
         */
        private String expectedEffect;

        /**
         * 实施难度
         */
        private String implementationDifficulty;
    }

    /**
     * 预期改进效果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpectedImprovementEffect {
        /**
         * 收益改进
         */
        private BigDecimal returnImprovement;

        /**
         * 风险降低
         */
        private BigDecimal riskReduction;

        /**
         * 夏普比率改进
         */
        private BigDecimal sharpeRatioImprovement;

        /**
         * 最大回撤改进
         */
        private BigDecimal maxDrawdownImprovement;

        /**
         * 胜率改进
         */
        private BigDecimal winRateImprovement;
    }

    /**
     * 获取时段分析摘要
     */
    public String getSessionAnalysisSummary() {
        return String.format("当前时段: %s - %s",
            currentTradingSession != null ? currentTradingSession.getDescription() : "未知",
            currentTradingSession != null ? currentTradingSession.getDetail() : "无详细信息");
    }

    /**
     * 获取最佳交易时段
     */
    public TradingSession getBestTradingSession() {
        if (sessionComparativeAnalysis == null) {
            return null;
        }
        return sessionComparativeAnalysis.getBestTradingSession();
    }

    /**
     * 获取当前时段建议
     */
    public String getCurrentSessionAdvice() {
        if (sessionTradingStrategy == null || 
            sessionTradingStrategy.getSessionSpecificStrategies() == null) {
            return "暂无时段建议";
        }
        
        String strategy = sessionTradingStrategy.getSessionSpecificStrategies()
            .get(currentTradingSession);
        
        return strategy != null ? strategy : "当前时段无特定策略";
    }

    /**
     * 检查是否为高风险时段
     */
    public boolean isHighRiskSession() {
        if (sessionRiskAssessment == null || currentTradingSession == null) {
            return false;
        }
        
        SessionRiskAnalysis riskAnalysis = sessionRiskAssessment.getSessionRiskAnalysis()
            .get(currentTradingSession);
        
        if (riskAnalysis == null) {
            return false;
        }
        
        String riskLevel = riskAnalysis.getRiskLevel();
        return "HIGH".equalsIgnoreCase(riskLevel) || "VERY_HIGH".equalsIgnoreCase(riskLevel);
    }

    /**
     * 获取流动性状况
     */
    public String getLiquidityStatus() {
        if (sessionLiquidityAnalysis == null) {
            return "流动性分析不可用";
        }
        
        String liquidityLevel = sessionLiquidityAnalysis.getLiquidityLevel();
        String liquidityTrend = sessionLiquidityAnalysis.getLiquidityTrend();
        
        return String.format("流动性等级: %s, 趋势: %s",
            liquidityLevel != null ? liquidityLevel : "未知",
            liquidityTrend != null ? liquidityTrend : "未知");
    }

    /**
     * 获取波动性状况
     */
    public String getVolatilityStatus() {
        if (sessionVolatilityAnalysis == null) {
            return "波动性分析不可用";
        }
        
        String volatilityLevel = sessionVolatilityAnalysis.getVolatilityLevel();
        
        if (sessionVolatilityAnalysis.getVolatilityMetrics() != null) {
            BigDecimal historicalVol = sessionVolatilityAnalysis.getVolatilityMetrics()
                .getHistoricalVolatility();
            
            return String.format("波动性等级: %s, 历史波动率: %s%%",
                volatilityLevel != null ? volatilityLevel : "未知",
                historicalVol != null ? historicalVol.toString() : "N/A");
        }
        
        return String.format("波动性等级: %s",
            volatilityLevel != null ? volatilityLevel : "未知");
    }

    /**
     * 获取主要交易建议
     */
    public String getMainTradingAdvice() {
        if (sessionTradingStrategy == null || 
            sessionTradingStrategy.getRecommendedStrategies() == null ||
            sessionTradingStrategy.getRecommendedStrategies().isEmpty()) {
            return "暂无交易建议";
        }
        
        TradingStrategyRecommendation topStrategy = sessionTradingStrategy.getRecommendedStrategies().get(0);
        return String.format("%s - %s (成功概率: %s%%)",
            topStrategy.getStrategyName(),
            topStrategy.getStrategyDescription(),
            topStrategy.getSuccessProbability() != null ? 
                topStrategy.getSuccessProbability().toString() : "N/A");
    }

    /**
     * 获取下一时段预测
     */
    public String getNextSessionPrediction() {
        if (sessionForecastAnalysis == null || 
            sessionForecastAnalysis.getNextSessionForecast() == null) {
            return "下一时段预测不可用";
        }
        
        SessionForecast nextForecast = sessionForecastAnalysis.getNextSessionForecast();
        return String.format("下一时段: %s, 预测趋势: %s, 预测价格变动: %s%%",
            nextForecast.getForecastSession() != null ? 
                nextForecast.getForecastSession().getDescription() : "未知",
            nextForecast.getPredictedTrendDirection() != null ? 
                nextForecast.getPredictedTrendDirection() : "未知",
            nextForecast.getPredictedPriceChange() != null ? 
                nextForecast.getPredictedPriceChange().toString() : "N/A");
    }

    /**
     * 检查是否有模式信号
     */
    public boolean hasPatternSignals() {
        return sessionPatternRecognition != null && 
               sessionPatternRecognition.getPatternTradingSignals() != null &&
               !sessionPatternRecognition.getPatternTradingSignals().isEmpty();
    }

    /**
     * 获取风险警告
     */
    public String getRiskWarning() {
        if (sessionRiskAssessment == null) {
            return "风险评估不可用";
        }
        
        String overallRiskLevel = sessionRiskAssessment.getOverallRiskLevel();
        if (overallRiskLevel == null) {
            return "风险等级未知";
        }
        
        switch (overallRiskLevel.toUpperCase()) {
            case "HIGH":
                return "高风险：当前时段风险较高，建议谨慎交易";
            case "MEDIUM":
                return "中等风险：注意风险控制，适度交易";
            case "LOW":
                return "低风险：当前时段相对安全，可正常交易";
            default:
                return "风险等级: " + overallRiskLevel;
        }
    }

    /**
     * 获取优化建议摘要
     */
    public String getOptimizationAdviceSummary() {
        if (sessionOptimizationRecommendation == null) {
            return "暂无优化建议";
        }
        
        String objective = sessionOptimizationRecommendation.getOptimizationObjective();
        List<TradingSession> recommendedSessions = sessionOptimizationRecommendation
            .getRecommendedSessionCombination();
        
        if (recommendedSessions != null && !recommendedSessions.isEmpty()) {
            String sessionNames = recommendedSessions.stream()
                .map(TradingSession::getDescription)
                .reduce((a, b) -> a + ", " + b)
                .orElse("无");
            
            return String.format("优化目标: %s, 推荐时段: %s",
                objective != null ? objective : "未指定",
                sessionNames);
        }
        
        return String.format("优化目标: %s",
            objective != null ? objective : "未指定");
    }

    /**
     * 检查是否为重叠时段
     */
    public boolean isOverlapSession() {
        return currentTradingSession != null && 
               (currentTradingSession == TradingSession.OVERLAP_ASIAN_EUROPEAN ||
                currentTradingSession == TradingSession.OVERLAP_EUROPEAN_AMERICAN);
    }

    /**
     * 获取全球市场影响摘要
     */
    public String getGlobalMarketImpactSummary() {
        if (globalMarketImpact == null || 
            globalMarketImpact.getMajorMarketImpacts() == null) {
            return "全球市场影响分析不可用";
        }
        
        Map<String, MarketImpact> impacts = globalMarketImpact.getMajorMarketImpacts();
        if (impacts.isEmpty()) {
            return "当前无重大市场影响";
        }
        
        // 找到影响最强的市场
        MarketImpact strongestImpact = impacts.values().stream()
            .max((i1, i2) -> {
                BigDecimal strength1 = i1.getImpactStrength();
                BigDecimal strength2 = i2.getImpactStrength();
                if (strength1 == null && strength2 == null) return 0;
                if (strength1 == null) return -1;
                if (strength2 == null) return 1;
                return strength1.compareTo(strength2);
            })
            .orElse(null);
        
        if (strongestImpact != null) {
            return String.format("主要影响: %s (%s, 强度: %s)",
                strongestImpact.getMarketName(),
                strongestImpact.getImpactDirection(),
                strongestImpact.getImpactStrength());
        }
        
        return "存在多个市场影响因素";
    }
}