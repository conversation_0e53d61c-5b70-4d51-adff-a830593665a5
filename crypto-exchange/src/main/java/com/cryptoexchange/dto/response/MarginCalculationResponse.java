package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保证金计算响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MarginCalculationResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 持仓方向
     */
    private String positionSide;
    
    /**
     * 持仓数量
     */
    private BigDecimal quantity;
    
    /**
     * 开仓价格
     */
    private BigDecimal entryPrice;
    
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    
    /**
     * 保证金模式
     */
    private String marginType;
    
    /**
     * 初始保证金
     */
    private BigDecimal initialMargin;
    
    /**
     * 维持保证金
     */
    private BigDecimal maintMargin;
    
    /**
     * 持仓价值
     */
    private BigDecimal positionValue;
    
    /**
     * 保证金率
     */
    private BigDecimal marginRatio;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRatio;
    
    /**
     * 强平价格
     */
    private BigDecimal liquidationPrice;
    
    /**
     * 破产价格
     */
    private BigDecimal bankruptcyPrice;
    
    /**
     * 未实现盈亏
     */
    private BigDecimal unrealizedPnl;
    
    /**
     * 标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 计算时间
     */
    private LocalDateTime calculationTime;
}