package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 强平价格响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LiquidationPriceResponse {
    
    /**
     * 合约符号
     */
    private String symbol;
    
    /**
     * 持仓方向
     */
    private String positionSide;
    
    /**
     * 持仓数量
     */
    private BigDecimal quantity;
    
    /**
     * 开仓价格
     */
    private BigDecimal entryPrice;
    
    /**
     * 杠杆倍数
     */
    private Integer leverage;
    
    /**
     * 保证金模式
     */
    private String marginType;
    
    /**
     * 强平价格
     */
    private BigDecimal liquidationPrice;
    
    /**
     * 破产价格
     */
    private BigDecimal bankruptcyPrice;
    
    /**
     * 维持保证金
     */
    private BigDecimal maintMargin;
    
    /**
     * 维持保证金率
     */
    private BigDecimal maintMarginRatio;
    
    /**
     * 当前标记价格
     */
    private BigDecimal markPrice;
    
    /**
     * 距离强平价格的百分比
     */
    private BigDecimal liquidationPercentage;
    
    /**
     * 风险等级
     */
    private String riskLevel;
    
    /**
     * 计算时间
     */
    private LocalDateTime calculationTime;
}