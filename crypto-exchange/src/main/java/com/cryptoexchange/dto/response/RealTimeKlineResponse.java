package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 实时K线数据响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class RealTimeKlineResponse {
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 事件时间
     */
    private Long eventTime;
    
    /**
     * 交易对符号
     */
    private String symbol;
    
    /**
     * K线数据
     */
    private KlineData kline;
    
    /**
     * 数据来源
     */
    private String source;
    
    /**
     * 延迟时间（毫秒）
     */
    private Long latency;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * K线数据
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class KlineData {
        /**
         * K线开始时间
         */
        private Long startTime;
        
        /**
         * K线结束时间
         */
        private Long endTime;
        
        /**
         * 交易对符号
         */
        private String symbol;
        
        /**
         * K线间隔
         */
        private String interval;
        
        /**
         * 首次交易ID
         */
        private Long firstTradeId;
        
        /**
         * 最后交易ID
         */
        private Long lastTradeId;
        
        /**
         * 开盘价
         */
        private BigDecimal openPrice;
        
        /**
         * 收盘价
         */
        private BigDecimal closePrice;
        
        /**
         * 最高价
         */
        private BigDecimal highPrice;
        
        /**
         * 最低价
         */
        private BigDecimal lowPrice;
        
        /**
         * 交易量
         */
        private BigDecimal volume;
        
        /**
         * 交易次数
         */
        private Long tradeCount;
        
        /**
         * K线是否结束
         */
        private Boolean isClosed;
        
        /**
         * 交易额
         */
        private BigDecimal quoteVolume;
        
        /**
         * 主动买入交易量
         */
        private BigDecimal takerBuyVolume;
        
        /**
         * 主动买入交易额
         */
        private BigDecimal takerBuyQuoteVolume;
        
        /**
         * 价格变化
         */
        private BigDecimal priceChange;
        
        /**
         * 价格变化百分比
         */
        private BigDecimal priceChangePercent;
        
        /**
         * 加权平均价格
         */
        private BigDecimal weightedAvgPrice;
        
        /**
         * 振幅
         */
        private BigDecimal amplitude;
    }
}