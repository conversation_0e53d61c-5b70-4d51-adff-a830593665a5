package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 总资产响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TotalAssetsResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 总资产价值（USDT计价）
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 现货钱包总价值
     */
    private BigDecimal spotWalletValue;
    
    /**
     * 期货钱包总价值
     */
    private BigDecimal futuresWalletValue;
    
    /**
     * 理财钱包总价值
     */
    private BigDecimal savingsWalletValue;
    
    /**
     * 质押钱包总价值
     */
    private BigDecimal stakingWalletValue;
    
    /**
     * 24小时资产变化
     */
    private BigDecimal change24h;
    
    /**
     * 24小时资产变化百分比
     */
    private BigDecimal changePercent24h;
    
    /**
     * 7天资产变化
     */
    private BigDecimal change7d;
    
    /**
     * 7天资产变化百分比
     */
    private BigDecimal changePercent7d;
    
    /**
     * 30天资产变化
     */
    private BigDecimal change30d;
    
    /**
     * 30天资产变化百分比
     */
    private BigDecimal changePercent30d;
    
    /**
     * 资产分布列表
     */
    private List<AssetDistribution> assetDistributions;
    
    /**
     * 钱包类型分布
     */
    private List<WalletTypeDistribution> walletDistributions;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 计价货币
     */
    private String quoteCurrency;
    
    /**
     * 资产分布内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AssetDistribution {
        /**
         * 货币代码
         */
        private String currency;
        
        /**
         * 总余额
         */
        private BigDecimal totalBalance;
        
        /**
         * 可用余额
         */
        private BigDecimal availableBalance;
        
        /**
         * 冻结余额
         */
        private BigDecimal frozenBalance;
        
        /**
         * 资产价值（USDT计价）
         */
        private BigDecimal assetValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 当前价格
         */
        private BigDecimal currentPrice;
    }
    
    /**
     * 钱包类型分布内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class WalletTypeDistribution {
        /**
         * 钱包类型
         */
        private String walletType;
        
        /**
         * 钱包名称
         */
        private String walletName;
        
        /**
         * 钱包价值
         */
        private BigDecimal walletValue;
        
        /**
         * 占总资产比例
         */
        private BigDecimal percentage;
        
        /**
         * 资产数量
         */
        private Integer assetCount;
    }
}