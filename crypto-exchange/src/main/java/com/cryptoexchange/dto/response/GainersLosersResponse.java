package com.cryptoexchange.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 涨跌幅排行响应DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "涨跌幅排行响应")
public class GainersLosersResponse {

    @Schema(description = "统计周期")
    private String period;

    @Schema(description = "统计时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statisticsTime;

    @Schema(description = "涨幅榜")
    private List<GainerItem> gainers;

    @Schema(description = "跌幅榜")
    private List<LoserItem> losers;

    @Schema(description = "涨停数量")
    private Integer limitUpCount;

    @Schema(description = "跌停数量")
    private Integer limitDownCount;

    @Schema(description = "平盘数量")
    private Integer flatCount;

    @Schema(description = "总交易对数量")
    private Integer totalSymbolCount;

    @Schema(description = "上涨交易对数量")
    private Integer risingCount;

    @Schema(description = "下跌交易对数量")
    private Integer fallingCount;

    @Schema(description = "市场情绪指数")
    private BigDecimal marketSentiment;

    @Schema(description = "平均涨跌幅")
    private BigDecimal avgChangePercent;

    @Schema(description = "涨跌比例")
    private BigDecimal riseToFallRatio;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 涨幅榜项目
     */
    @Data
    @Schema(description = "涨幅榜项目")
    public static class GainerItem {
        
        @Schema(description = "排名")
        private Integer rank;
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "交易对名称")
        private String symbolName;
        
        @Schema(description = "基础货币")
        private String baseCurrency;
        
        @Schema(description = "计价货币")
        private String quoteCurrency;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "开盘价")
        private BigDecimal openPrice;
        
        @Schema(description = "最高价")
        private BigDecimal highPrice;
        
        @Schema(description = "最低价")
        private BigDecimal lowPrice;
        
        @Schema(description = "价格变化")
        private BigDecimal priceChange;
        
        @Schema(description = "涨幅百分比")
        private BigDecimal changePercent;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount24h;
        
        @Schema(description = "换手率")
        private BigDecimal turnoverRate;
        
        @Schema(description = "市值")
        private BigDecimal marketCap;
        
        @Schema(description = "流通市值")
        private BigDecimal circulatingMarketCap;
        
        @Schema(description = "是否为新币")
        private Boolean isNewListing;
        
        @Schema(description = "上线天数")
        private Integer listingDays;
        
        @Schema(description = "涨幅持续时间（小时）")
        private Integer gainDuration;
        
        @Schema(description = "突破阻力位")
        private Boolean breakoutResistance;
        
        @Schema(description = "技术指标评分")
        private BigDecimal technicalScore;
        
        @Schema(description = "资金流入")
        private BigDecimal moneyFlow;
    }

    /**
     * 跌幅榜项目
     */
    @Data
    @Schema(description = "跌幅榜项目")
    public static class LoserItem {
        
        @Schema(description = "排名")
        private Integer rank;
        
        @Schema(description = "交易对代码")
        private String symbol;
        
        @Schema(description = "交易对名称")
        private String symbolName;
        
        @Schema(description = "基础货币")
        private String baseCurrency;
        
        @Schema(description = "计价货币")
        private String quoteCurrency;
        
        @Schema(description = "当前价格")
        private BigDecimal currentPrice;
        
        @Schema(description = "开盘价")
        private BigDecimal openPrice;
        
        @Schema(description = "最高价")
        private BigDecimal highPrice;
        
        @Schema(description = "最低价")
        private BigDecimal lowPrice;
        
        @Schema(description = "价格变化")
        private BigDecimal priceChange;
        
        @Schema(description = "跌幅百分比")
        private BigDecimal changePercent;
        
        @Schema(description = "24小时成交量")
        private BigDecimal volume24h;
        
        @Schema(description = "24小时成交额")
        private BigDecimal amount24h;
        
        @Schema(description = "换手率")
        private BigDecimal turnoverRate;
        
        @Schema(description = "市值")
        private BigDecimal marketCap;
        
        @Schema(description = "流通市值")
        private BigDecimal circulatingMarketCap;
        
        @Schema(description = "跌幅持续时间（小时）")
        private Integer lossDuration;
        
        @Schema(description = "跌破支撑位")
        private Boolean breakdownSupport;
        
        @Schema(description = "技术指标评分")
        private BigDecimal technicalScore;
        
        @Schema(description = "资金流出")
        private BigDecimal moneyFlow;
        
        @Schema(description = "风险等级")
        private String riskLevel;
    }
}