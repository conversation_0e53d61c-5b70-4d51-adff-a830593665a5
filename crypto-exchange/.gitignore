# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Application specific
logs/
*.log.*
temp/
tmp/
uploads/
files/

# Configuration files with sensitive data
application-local.yml
application-prod.yml
*.env
.env.*

# Database
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# Docker
.dockerignore

# SSL certificates
*.pem
*.key
*.crt
*.p12
*.jks

# Backup files
*.bak
*.backup
*.old

# Test coverage
.coverage
coverage/
*.lcov

# Node.js (if using frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Generated documentation
docs/generated/

# JProfiler
*.jprofiler

# JMeter
*.jmx.log

# Spring Boot
spring-boot-*.log

# H2 Database
*.h2.db
*.trace.db

# Flyway
*.sql.bak

# Local development
dev-tools/
scripts/local/

# IDE specific
.metadata
.recommenders

# OS generated files
.directory

# JetBrains
.idea_modules/

# CMake
CMakeLists.txt.user
CMakeCache.txt
CMakeFiles
CMakeScripts
Testing
Makefile
cmake_install.cmake
install_manifest.txt
compile_commands.json
CTestTestfile.cmake
_deps

# Ignore application-specific directories
data/
storage/
cache/
sessions/

# Ignore sensitive configuration
config/local/
config/production/
secrets/
keys/
certs/

# Ignore generated API documentation
api-docs/
swagger-ui/

# Ignore performance test results
performance-tests/results/

# Ignore local scripts
scripts/deploy-local.sh
scripts/backup-local.sh

# Ignore IDE workspace files
*.code-workspace

# Ignore local environment files
.env.local
.env.development
.env.test
.env.production

# Ignore temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Ignore compiled TypeScript (if any)
*.js.map
*.d.ts

# Ignore package-lock.json for libraries
# package-lock.json

# Ignore yarn.lock for npm projects
# yarn.lock

# Ignore local configuration overrides
config/override/
config/custom/

# Ignore monitoring and metrics data
metrics/
monitoring/

# Ignore local database dumps
*.sql.gz
*.dump

# Ignore local SSL certificates
ssl/
certificates/

# Ignore local backup directories
backups/
archives/

# Ignore IDE-specific files
.project
.classpath
.c9/
*.launch
.settings/
.loadpath
.recommenders

# Ignore OS-specific files
.fuse_hidden*
.nfs*

# Ignore local development tools
tools/local/
utils/local/

# Ignore generated reports
reports/
*.html
*.xml

# Ignore local scripts
run-local.sh
start-local.sh
stop-local.sh

# Ignore application logs
application*.log
spring*.log
root*.log

# Ignore test outputs
test-output/
test-results/

# Ignore local Maven settings
.m2/settings-local.xml

# Ignore local Gradle settings
gradle-local.properties

# Ignore IntelliJ IDEA files
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/misc.xml
.idea/modules.xml
.idea/scopes/scope_settings.xml
.idea/vcs.xml
.idea/jsLibraryMappings.xml
.idea/datasources.xml
.idea/datasources/
.idea/sqlDataSources.xml
.idea/dynamic.xml
.idea/uiDesigner.xml
.idea/dbnavigator.xml

# Ignore Eclipse files
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.externalToolBuilders/
*.launch
.pydevproject
.cproject
.autotools
.factorypath
.buildpath
.target
.tern-project
.texlipse
.springBeans
.recommenders/
.apt_generated/
.cache-main
.scala_dependencies
.worksheet