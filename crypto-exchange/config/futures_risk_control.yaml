# 合约交易风险控制配置
# 版本: 1.0.0
# 创建时间: 2024-01-01
# 用途: 合约和杠杆交易风险控制参数配置

# ========================================
# 全局风险控制参数
# ========================================
global_risk:
  # 系统级风险开关
  risk_control_enabled: true
  
  # 紧急停止交易开关
  emergency_stop: false
  
  # 全局最大杠杆倍数
  max_global_leverage: 125
  
  # 系统维护模式
  maintenance_mode: false
  
  # 风险检查间隔（秒）
  risk_check_interval: 5
  
  # 强平引擎并发数
  liquidation_concurrency: 10

# ========================================
# 保证金率风险等级
# ========================================
margin_ratio_levels:
  # 安全等级
  safe:
    min_ratio: 0.20  # 20%
    max_ratio: 1.00  # 100%
    color: "green"
    description: "安全"
    
  # 警告等级
  warning:
    min_ratio: 0.10  # 10%
    max_ratio: 0.20  # 20%
    color: "yellow"
    description: "警告"
    actions:
      - "发送风险提醒"
      - "限制开新仓"
    
  # 危险等级
  danger:
    min_ratio: 0.05  # 5%
    max_ratio: 0.10  # 10%
    color: "orange"
    description: "危险"
    actions:
      - "发送紧急通知"
      - "禁止开新仓"
      - "建议减仓"
    
  # 强平等级
  liquidation:
    min_ratio: 0.00  # 0%
    max_ratio: 0.05  # 5%
    color: "red"
    description: "强平"
    actions:
      - "触发强平"
      - "冻结账户"

# ========================================
# 合约风险参数
# ========================================
contract_risk_params:
  # BTC永续合约
  "BTC-USDT-PERP":
    max_leverage: 125
    maintenance_margin_rate: 0.004  # 0.4%
    initial_margin_rate: 0.008      # 0.8%
    max_position_value: 10000000    # 1000万USDT
    max_order_value: 1000000        # 100万USDT
    price_deviation_limit: 0.05     # 5%
    funding_rate_limit: 0.00375     # 0.375%
    
  # ETH永续合约
  "ETH-USDT-PERP":
    max_leverage: 100
    maintenance_margin_rate: 0.005  # 0.5%
    initial_margin_rate: 0.010      # 1.0%
    max_position_value: 5000000     # 500万USDT
    max_order_value: 500000         # 50万USDT
    price_deviation_limit: 0.05     # 5%
    funding_rate_limit: 0.00375     # 0.375%
    
  # BNB永续合约
  "BNB-USDT-PERP":
    max_leverage: 75
    maintenance_margin_rate: 0.0065 # 0.65%
    initial_margin_rate: 0.013      # 1.3%
    max_position_value: 2000000     # 200万USDT
    max_order_value: 200000         # 20万USDT
    price_deviation_limit: 0.05     # 5%
    funding_rate_limit: 0.00375     # 0.375%

# ========================================
# 用户风险等级配置
# ========================================
user_risk_levels:
  # 新手用户
  beginner:
    max_leverage: 10
    max_position_value: 10000       # 1万USDT
    max_daily_loss: 1000            # 1000USDT
    required_margin_ratio: 0.15     # 15%
    
  # 普通用户
  intermediate:
    max_leverage: 50
    max_position_value: 100000      # 10万USDT
    max_daily_loss: 10000           # 1万USDT
    required_margin_ratio: 0.10     # 10%
    
  # 专业用户
  advanced:
    max_leverage: 100
    max_position_value: 1000000     # 100万USDT
    max_daily_loss: 50000           # 5万USDT
    required_margin_ratio: 0.08     # 8%
    
  # VIP用户
  vip:
    max_leverage: 125
    max_position_value: 10000000    # 1000万USDT
    max_daily_loss: 200000          # 20万USDT
    required_margin_ratio: 0.05     # 5%

# ========================================
# 强平配置
# ========================================
liquidation_config:
  # 强平引擎配置
  engine:
    enabled: true
    check_interval: 1               # 检查间隔（秒）
    batch_size: 100                 # 批处理大小
    max_retry: 3                    # 最大重试次数
    timeout: 30                     # 超时时间（秒）
    
  # 强平手续费
  liquidation_fee_rate: 0.005       # 0.5%
  
  # 保险基金费率
  insurance_fund_rate: 0.001        # 0.1%
  
  # 强平价格偏移
  price_offset: 0.001               # 0.1%
  
  # ADL自动减仓配置
  adl:
    enabled: true
    trigger_threshold: 0.8          # 保险基金使用率阈值
    max_reduction_ratio: 0.5        # 最大减仓比例
    priority_calculation: "pnl_ratio" # 优先级计算方式

# ========================================
# 资金费率配置
# ========================================
funding_rate_config:
  # 计算间隔（秒）
  calculation_interval: 3600        # 1小时
  
  # 结算间隔（秒）
  settlement_interval: 28800        # 8小时
  
  # 最大资金费率
  max_funding_rate: 0.00375         # 0.375%
  
  # 最小资金费率
  min_funding_rate: -0.00375        # -0.375%
  
  # 基础利率
  base_interest_rate: 0.0001        # 0.01%
  
  # 溢价指数平滑系数
  premium_smoothing_factor: 0.1

# ========================================
# 价格保护机制
# ========================================
price_protection:
  # 价格偏离保护
  deviation_protection:
    enabled: true
    max_deviation: 0.05             # 5%
    reference_price: "mark_price"    # 参考价格类型
    
  # 价格熔断机制
  circuit_breaker:
    enabled: true
    trigger_threshold: 0.10         # 10%
    cooling_period: 300             # 冷却期（秒）
    
  # 标记价格配置
  mark_price:
    update_interval: 1              # 更新间隔（秒）
    price_sources:
      - "binance"
      - "okx"
      - "bybit"
    weight_calculation: "volume_weighted"

# ========================================
# 监控和告警配置
# ========================================
monitoring:
  # 系统监控指标
  metrics:
    # 保证金率监控
    margin_ratio:
      enabled: true
      check_interval: 5             # 检查间隔（秒）
      alert_threshold: 0.10         # 告警阈值
      
    # 持仓集中度监控
    position_concentration:
      enabled: true
      check_interval: 60            # 检查间隔（秒）
      max_single_user_ratio: 0.20   # 单用户最大持仓比例
      max_single_contract_ratio: 0.50 # 单合约最大持仓比例
      
    # 资金费率监控
    funding_rate:
      enabled: true
      check_interval: 300           # 检查间隔（秒）
      alert_threshold: 0.003        # 告警阈值
      
    # 保险基金监控
    insurance_fund:
      enabled: true
      check_interval: 60            # 检查间隔（秒）
      min_balance_ratio: 0.10       # 最小余额比例
      
  # 告警配置
  alerts:
    # 邮件告警
    email:
      enabled: true
      smtp_host: "smtp.example.com"
      smtp_port: 587
      username: "<EMAIL>"
      recipients:
        - "<EMAIL>"
        - "<EMAIL>"
        
    # 短信告警
    sms:
      enabled: true
      provider: "aliyun"
      recipients:
        - "+86138****8888"
        
    # 钉钉告警
    dingtalk:
      enabled: true
      webhook_url: "https://oapi.dingtalk.com/robot/send?access_token=xxx"
      
    # Slack告警
    slack:
      enabled: false
      webhook_url: "https://hooks.slack.com/services/xxx"

# ========================================
# 限流配置
# ========================================
rate_limiting:
  # API限流
  api:
    # 下单限流
    order_placement:
      requests_per_second: 10
      burst_size: 20
      
    # 撤单限流
    order_cancellation:
      requests_per_second: 20
      burst_size: 40
      
    # 查询限流
    query:
      requests_per_second: 100
      burst_size: 200
      
  # 用户操作限流
  user_operations:
    # 每日最大订单数
    max_daily_orders: 10000
    
    # 每分钟最大订单数
    max_orders_per_minute: 100
    
    # 最大持仓数量
    max_positions: 50

# ========================================
# 数据备份和恢复
# ========================================
backup_recovery:
  # 实时数据备份
  real_time_backup:
    enabled: true
    backup_interval: 60             # 备份间隔（秒）
    retention_days: 7               # 保留天数
    
  # 关键数据快照
  snapshot:
    enabled: true
    snapshot_interval: 3600         # 快照间隔（秒）
    retention_days: 30              # 保留天数
    
  # 灾难恢复
  disaster_recovery:
    enabled: true
    backup_location: "remote"       # 备份位置
    rto: 300                        # 恢复时间目标（秒）
    rpo: 60                         # 恢复点目标（秒）

# ========================================
# 合规和审计
# ========================================
compliance:
  # 交易记录审计
  trade_audit:
    enabled: true
    log_level: "detailed"
    retention_days: 2555            # 7年
    
  # 风险事件记录
  risk_event_logging:
    enabled: true
    log_level: "all"
    retention_days: 1095            # 3年
    
  # 监管报告
  regulatory_reporting:
    enabled: true
    report_frequency: "daily"
    report_format: "json"
    
# ========================================
# 性能优化
# ========================================
performance:
  # 缓存配置
  cache:
    # Redis缓存
    redis:
      enabled: true
      ttl: 300                      # 缓存过期时间（秒）
      max_memory: "2gb"
      
    # 本地缓存
    local:
      enabled: true
      max_size: 10000
      ttl: 60
      
  # 数据库优化
  database:
    # 连接池配置
    connection_pool:
      max_connections: 100
      min_connections: 10
      connection_timeout: 30
      
    # 查询优化
    query_optimization:
      enabled: true
      slow_query_threshold: 1000    # 慢查询阈值（毫秒）
      
  # 消息队列
  message_queue:
    # Kafka配置
    kafka:
      enabled: true
      batch_size: 1000
      linger_ms: 10
      compression_type: "lz4"

# ========================================
# 环境配置
# ========================================
environment:
  # 开发环境
  development:
    risk_control_enabled: false
    max_leverage: 10
    liquidation_enabled: false
    
  # 测试环境
  testing:
    risk_control_enabled: true
    max_leverage: 50
    liquidation_enabled: true
    
  # 生产环境
  production:
    risk_control_enabled: true
    max_leverage: 125
    liquidation_enabled: true
    strict_mode: true