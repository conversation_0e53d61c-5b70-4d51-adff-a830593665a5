version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: crypto-exchange-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: crypto_exchange
      MYSQL_USER: crypto
      MYSQL_PASSWORD: crypto123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=1G
      --innodb-log-file-size=256M
      --max-connections=1000
    networks:
      - crypto-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: crypto-exchange-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - crypto-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: crypto-exchange-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - crypto-network

  # 后端应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crypto-exchange-app
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: crypto
      SPRING_DATASOURCE_PASSWORD: crypto123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      APP_FILE_MINIO_ENDPOINT: http://minio:9000
      APP_FILE_MINIO_ACCESS_KEY: minioadmin
      APP_FILE_MINIO_SECRET_KEY: minioadmin123
      JAVA_OPTS: -Xms512m -Xmx1024m -XX:+UseG1GC
    ports:
      - "8080:8080"
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    depends_on:
      - mysql
      - redis
      - minio
    networks:
      - crypto-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: crypto-exchange-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./config/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - crypto-network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: crypto-exchange-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - crypto-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: crypto-exchange-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus
    networks:
      - crypto-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  crypto-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16