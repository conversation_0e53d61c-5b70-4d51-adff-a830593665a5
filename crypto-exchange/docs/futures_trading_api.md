# 合约和杠杆交易API文档

## 概述

本文档描述了加密货币交易所合约和杠杆交易系统的API接口设计，包括永续合约、交割合约、杠杆交易等核心功能。

## 目录

1. [认证和授权](#认证和授权)
2. [合约信息API](#合约信息api)
3. [账户管理API](#账户管理api)
4. [订单管理API](#订单管理api)
5. [持仓管理API](#持仓管理api)
6. [交易记录API](#交易记录api)
7. [资金费率API](#资金费率api)
8. [风险控制API](#风险控制api)
9. [WebSocket推送](#websocket推送)
10. [错误码说明](#错误码说明)

## 认证和授权

所有API请求都需要通过JWT Token进行身份验证。

### 请求头

```http
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

### 权限等级

- **READ**: 查询权限
- **TRADE**: 交易权限
- **WITHDRAW**: 提现权限
- **ADMIN**: 管理员权限

## 合约信息API

### 1. 获取合约列表

**接口地址**: `GET /api/v1/futures/contracts`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| status | int | 否 | 合约状态：1-正常，2-维护中 |
| contractType | int | 否 | 合约类型：1-永续合约，2-交割合约 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "symbol": "BTC-USDT-PERP",
      "baseSymbol": "BTC",
      "quoteSymbol": "USDT",
      "contractType": 1,
      "contractSize": "1.********",
      "tickSize": "0.********",
      "minQuantity": "0.00100000",
      "maxQuantity": "1000.********",
      "maxLeverage": "125.00",
      "maintenanceMarginRate": "0.004000",
      "initialMarginRate": "0.008000",
      "makerFeeRate": "0.000200",
      "takerFeeRate": "0.000400",
      "fundingInterval": 28800,
      "maxFundingRate": "0.003750",
      "settlementCurrency": "USDT",
      "pricePrecision": 1,
      "quantityPrecision": 3,
      "status": 1
    }
  ]
}
```

### 2. 获取合约详情

**接口地址**: `GET /api/v1/futures/contracts/{contractId}`

**权限要求**: READ

**路径参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 是 | 合约ID |

## 账户管理API

### 1. 获取保证金账户信息

**接口地址**: `GET /api/v1/futures/account`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| accountType | int | 否 | 账户类型：1-逐仓，2-全仓 |
| contractId | long | 否 | 合约ID（逐仓时使用） |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "accountType": 2,
      "contractId": null,
      "contractSymbol": null,
      "marginBalance": "10000.********",
      "availableBalance": "8500.********",
      "frozenBalance": "500.********",
      "positionMargin": "1000.********",
      "orderMargin": "500.********",
      "unrealizedPnl": "150.********",
      "realizedPnl": "250.********",
      "totalWalletBalance": "10400.********",
      "marginRatio": "0.096154",
      "riskLevel": 1,
      "maxWithdrawAmount": "8500.********"
    }
  ]
}
```

### 2. 调整杠杆倍数

**接口地址**: `POST /api/v1/futures/leverage`

**权限要求**: TRADE

**请求参数**:

```json
{
  "contractId": 1,
  "leverage": "10.00",
  "marginMode": 1
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 是 | 合约ID |
| leverage | decimal | 是 | 杠杆倍数 |
| marginMode | int | 是 | 保证金模式：1-逐仓，2-全仓 |

### 3. 调整保证金

**接口地址**: `POST /api/v1/futures/margin`

**权限要求**: TRADE

**请求参数**:

```json
{
  "positionId": 1,
  "amount": "100.********",
  "type": 1
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| positionId | long | 是 | 持仓ID |
| amount | decimal | 是 | 调整金额 |
| type | int | 是 | 调整类型：1-增加，2-减少 |

## 订单管理API

### 1. 下单

**接口地址**: `POST /api/v1/futures/orders`

**权限要求**: TRADE

**请求参数**:

```json
{
  "contractId": 1,
  "orderType": 1,
  "orderSide": 1,
  "positionSide": 1,
  "price": "50000.00",
  "quantity": "0.01000000",
  "leverage": "10.00",
  "marginMode": 1,
  "reduceOnly": false,
  "timeInForce": 1,
  "clientOrderId": "my_order_123"
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 是 | 合约ID |
| orderType | int | 是 | 订单类型：1-限价单，2-市价单 |
| orderSide | int | 是 | 订单方向：1-开多，2-开空，3-平多，4-平空 |
| positionSide | int | 是 | 持仓方向：1-多头，2-空头 |
| price | decimal | 否 | 委托价格（限价单必填） |
| quantity | decimal | 是 | 委托数量 |
| leverage | decimal | 是 | 杠杆倍数 |
| marginMode | int | 是 | 保证金模式：1-逐仓，2-全仓 |
| reduceOnly | boolean | 否 | 是否只减仓 |
| timeInForce | int | 否 | 有效期类型：1-GTC，2-IOC，3-FOK |
| clientOrderId | string | 否 | 客户端订单ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderId": 123456,
    "orderNo": "FO202401010001",
    "clientOrderId": "my_order_123",
    "status": 1
  }
}
```

### 2. 取消订单

**接口地址**: `DELETE /api/v1/futures/orders/{orderId}`

**权限要求**: TRADE

**路径参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| orderId | long | 是 | 订单ID |

### 3. 查询订单

**接口地址**: `GET /api/v1/futures/orders`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 否 | 合约ID |
| status | int | 否 | 订单状态 |
| startTime | long | 否 | 开始时间戳 |
| endTime | long | 否 | 结束时间戳 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认20 |

## 持仓管理API

### 1. 获取持仓信息

**接口地址**: `GET /api/v1/futures/positions`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 否 | 合约ID |
| positionSide | int | 否 | 持仓方向 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "contractId": 1,
      "contractSymbol": "BTC-USDT-PERP",
      "positionSide": 1,
      "positionSize": "0.********",
      "availableSize": "0.********",
      "avgEntryPrice": "50000.********",
      "markPrice": "51000.********",
      "liquidationPrice": "45454.********",
      "marginAmount": "400.********",
      "unrealizedPnl": "100.********",
      "realizedPnl": "0.********",
      "leverage": "10.00",
      "marginMode": 1,
      "marginRatio": "0.078431",
      "riskLevel": 1
    }
  ]
}
```

### 2. 平仓

**接口地址**: `POST /api/v1/futures/positions/{positionId}/close`

**权限要求**: TRADE

**路径参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| positionId | long | 是 | 持仓ID |

**请求参数**:

```json
{
  "quantity": "0.05000000",
  "orderType": 2
}
```

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| quantity | decimal | 否 | 平仓数量（不填则全部平仓） |
| orderType | int | 是 | 订单类型：1-限价单，2-市价单 |
| price | decimal | 否 | 平仓价格（限价单必填） |

## 交易记录API

### 1. 获取交易记录

**接口地址**: `GET /api/v1/futures/trades`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 否 | 合约ID |
| orderId | long | 否 | 订单ID |
| startTime | long | 否 | 开始时间戳 |
| endTime | long | 否 | 结束时间戳 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认20 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 100,
    "page": 1,
    "size": 20,
    "records": [
      {
        "id": 1,
        "tradeNo": "FT202401010001",
        "orderId": 123456,
        "orderNo": "FO202401010001",
        "contractSymbol": "BTC-USDT-PERP",
        "price": "50000.********",
        "quantity": "0.01000000",
        "fee": "0.20000000",
        "realizedPnl": "0.********",
        "isMaker": true,
        "tradeType": 1,
        "createTime": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

## 资金费率API

### 1. 获取资金费率历史

**接口地址**: `GET /api/v1/futures/funding-rates`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 是 | 合约ID |
| startTime | long | 否 | 开始时间戳 |
| endTime | long | 否 | 结束时间戳 |
| limit | int | 否 | 返回数量，默认100 |

### 2. 获取用户资金费用记录

**接口地址**: `GET /api/v1/futures/funding-fees`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 否 | 合约ID |
| startTime | long | 否 | 开始时间戳 |
| endTime | long | 否 | 结束时间戳 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认20 |

## 风险控制API

### 1. 获取强平记录

**接口地址**: `GET /api/v1/futures/liquidations`

**权限要求**: READ

**请求参数**:

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| contractId | long | 否 | 合约ID |
| startTime | long | 否 | 开始时间戳 |
| endTime | long | 否 | 结束时间戳 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认20 |

### 2. 获取风险等级

**接口地址**: `GET /api/v1/futures/risk-level`

**权限要求**: READ

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overallRiskLevel": 1,
    "marginRatio": "0.150000",
    "totalMarginBalance": "10000.********",
    "totalUnrealizedPnl": "150.********",
    "positions": [
      {
        "contractSymbol": "BTC-USDT-PERP",
        "riskLevel": 1,
        "marginRatio": "0.078431",
        "liquidationPrice": "45454.********"
      }
    ]
  }
}
```

## WebSocket推送

### 连接地址

```
wss://api.example.com/ws/futures
```

### 认证

连接后需要发送认证消息：

```json
{
  "method": "auth",
  "params": {
    "token": "JWT_TOKEN"
  }
}
```

### 订阅频道

#### 1. 账户更新

```json
{
  "method": "subscribe",
  "params": {
    "channel": "account"
  }
}
```

#### 2. 持仓更新

```json
{
  "method": "subscribe",
  "params": {
    "channel": "position"
  }
}
```

#### 3. 订单更新

```json
{
  "method": "subscribe",
  "params": {
    "channel": "order"
  }
}
```

#### 4. 交易更新

```json
{
  "method": "subscribe",
  "params": {
    "channel": "trade"
  }
}
```

### 推送消息格式

#### 账户更新推送

```json
{
  "channel": "account",
  "data": {
    "accountType": 2,
    "marginBalance": "10000.********",
    "availableBalance": "8500.********",
    "unrealizedPnl": "150.********",
    "marginRatio": "0.096154",
    "riskLevel": 1,
    "updateTime": *************
  }
}
```

#### 持仓更新推送

```json
{
  "channel": "position",
  "data": {
    "positionId": 1,
    "contractSymbol": "BTC-USDT-PERP",
    "positionSide": 1,
    "positionSize": "0.********",
    "avgEntryPrice": "50000.********",
    "markPrice": "51000.********",
    "unrealizedPnl": "100.********",
    "marginRatio": "0.078431",
    "riskLevel": 1,
    "updateTime": *************
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求频率限制 |
| 500 | 服务器内部错误 |
| 10001 | 合约不存在 |
| 10002 | 合约已暂停交易 |
| 10003 | 杠杆倍数超出限制 |
| 10004 | 保证金不足 |
| 10005 | 持仓不存在 |
| 10006 | 订单不存在 |
| 10007 | 订单状态不允许取消 |
| 10008 | 价格超出限制 |
| 10009 | 数量超出限制 |
| 10010 | 账户被冻结 |
| 10011 | 风险等级过高 |
| 10012 | 强平中，无法操作 |

## 风险控制说明

### 1. 保证金率计算

```
保证金率 = (保证金余额 + 未实现盈亏) / 持仓价值
```

### 2. 强平触发条件

- 保证金率 ≤ 维持保证金率时触发强平
- 系统会自动平仓以降低风险

### 3. 风险等级

- **安全**: 保证金率 > 20%
- **警告**: 10% < 保证金率 ≤ 20%
- **危险**: 5% < 保证金率 ≤ 10%
- **强平**: 保证金率 ≤ 5%

### 4. ADL自动减仓

当保险基金不足以覆盖强平亏损时，系统会对盈利最多的反向持仓进行自动减仓。

## 注意事项

1. 所有价格和数量字段都使用字符串格式，避免精度丢失
2. 时间戳使用毫秒级Unix时间戳
3. API请求频率限制：每秒最多100次请求
4. WebSocket连接会定期发送心跳包，客户端需要响应
5. 强烈建议在生产环境中使用止损订单控制风险
6. 合约交易具有高风险，请谨慎操作

## 更新日志

- **v1.0.0** (2024-01-01): 初始版本发布
  - 支持永续合约交易
  - 支持逐仓和全仓模式
  - 支持资金费率机制
  - 支持风险控制和强平机制