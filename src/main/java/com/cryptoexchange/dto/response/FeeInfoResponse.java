package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 手续费信息响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FeeInfoResponse {
    
    /**
     * 手续费配置ID
     */
    private Long id;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 手续费类型 (DEPOSIT, WITHDRAW, TRADE)
     */
    private String feeType;
    
    /**
     * 网络类型（充值/提现）
     */
    private String network;
    
    /**
     * 手续费金额
     */
    private BigDecimal feeAmount;
    
    /**
     * 手续费计算方式 (FIXED, PERCENTAGE)
     */
    private String feeCalculationType;
    
    /**
     * 最小手续费
     */
    private BigDecimal minFee;
    
    /**
     * 最大手续费
     */
    private BigDecimal maxFee;
    
    /**
     * 手续费率（百分比）
     */
    private BigDecimal feeRate;
    
    /**
     * 用户等级
     */
    private String userLevel;
    
    /**
     * VIP等级
     */
    private String vipLevel;
    
    /**
     * 交易对（交易手续费）
     */
    private String symbol;
    
    /**
     * Maker手续费率
     */
    private BigDecimal makerFeeRate;
    
    /**
     * Taker手续费率
     */
    private BigDecimal takerFeeRate;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
    
    /**
     * 失效时间
     */
    private LocalDateTime expiryTime;
    
    /**
     * 手续费阶梯（VIP等级相关）
     */
    private List<FeeTier> feeTiers;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 手续费阶梯内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FeeTier {
        /**
         * 阶梯等级
         */
        private String tier;
        
        /**
         * 阶梯名称
         */
        private String tierName;
        
        /**
         * 最小交易量（30天）
         */
        private BigDecimal minVolume;
        
        /**
         * 最小持仓量
         */
        private BigDecimal minHolding;
        
        /**
         * Maker手续费率
         */
        private BigDecimal makerFeeRate;
        
        /**
         * Taker手续费率
         */
        private BigDecimal takerFeeRate;
        
        /**
         * 提现手续费折扣
         */
        private BigDecimal withdrawFeeDiscount;
    }
}
