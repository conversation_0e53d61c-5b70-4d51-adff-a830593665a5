package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 余额变动响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BalanceChangeResponse {
    
    /**
     * 变动记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 变动类型 (DEPOSIT, WITHDRAW, TRANSFER, TRADE, FEE, FREEZE, UNFREEZE)
     */
    private String type;
    
    /**
     * 变动金额（正数为增加，负数为减少）
     */
    private BigDecimal amount;
    
    /**
     * 变动前余额
     */
    private BigDecimal balanceBefore;
    
    /**
     * 变动后余额
     */
    private BigDecimal balanceAfter;
    
    /**
     * 变动前冻结余额
     */
    private BigDecimal frozenBefore;
    
    /**
     * 变动后冻结余额
     */
    private BigDecimal frozenAfter;
    
    /**
     * 钱包类型
     */
    private String walletType;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 关联订单ID
     */
    private Long orderId;
    
    /**
     * 关联交易ID
     */
    private String tradeId;
    
    /**
     * 变动时间
     */
    private LocalDateTime changeTime;
    
    /**
     * 变动原因
     */
    private String reason;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 操作人员ID
     */
    private Long operatorId;
    
    /**
     * 操作人员姓名
     */
    private String operatorName;
    
    /**
     * 交易哈希（区块链相关）
     */
    private String txHash;
    
    /**
     * 网络类型（区块链相关）
     */
    private String network;
}
