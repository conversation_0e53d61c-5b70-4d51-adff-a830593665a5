package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 网络配置响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NetworkConfigResponse {
    
    /**
     * 网络配置ID
     */
    private Long id;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 网络代码
     */
    private String network;
    
    /**
     * 网络名称
     */
    private String networkName;
    
    /**
     * 网络全称
     */
    private String networkFullName;
    
    /**
     * 网络状态 (ACTIVE, INACTIVE, MAINTENANCE)
     */
    private String status;
    
    /**
     * 是否支持充值
     */
    private Boolean depositEnabled;
    
    /**
     * 是否支持提现
     */
    private Boolean withdrawEnabled;
    
    /**
     * 最小充值金额
     */
    private BigDecimal minDepositAmount;
    
    /**
     * 最小提现金额
     */
    private BigDecimal minWithdrawAmount;
    
    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 提现手续费类型 (FIXED, PERCENTAGE)
     */
    private String withdrawFeeType;
    
    /**
     * 充值确认数
     */
    private Integer depositConfirmations;
    
    /**
     * 提现确认数
     */
    private Integer withdrawConfirmations;
    
    /**
     * 是否需要标签/备忘录
     */
    private Boolean requiresTag;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 地址格式正则表达式
     */
    private String addressRegex;
    
    /**
     * 标签格式正则表达式
     */
    private String tagRegex;
    
    /**
     * 区块浏览器URL模板
     */
    private String explorerUrl;
    
    /**
     * 交易浏览器URL模板
     */
    private String txExplorerUrl;
    
    /**
     * 地址浏览器URL模板
     */
    private String addressExplorerUrl;
    
    /**
     * 网络类型 (MAINNET, TESTNET)
     */
    private String networkType;
    
    /**
     * 链ID（EVM兼容链）
     */
    private Long chainId;
    
    /**
     * 合约地址（代币）
     */
    private String contractAddress;
    
    /**
     * 小数位数
     */
    private Integer decimals;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
