package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 汇率响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ExchangeRateResponse {
    
    /**
     * 汇率ID
     */
    private Long id;
    
    /**
     * 源货币
     */
    private String fromCurrency;
    
    /**
     * 目标货币
     */
    private String toCurrency;
    
    /**
     * 汇率
     */
    private BigDecimal rate;
    
    /**
     * 反向汇率
     */
    private BigDecimal reverseRate;
    
    /**
     * 买入价
     */
    private BigDecimal bidPrice;
    
    /**
     * 卖出价
     */
    private BigDecimal askPrice;
    
    /**
     * 中间价
     */
    private BigDecimal midPrice;
    
    /**
     * 24小时变化
     */
    private BigDecimal change24h;
    
    /**
     * 24小时变化百分比
     */
    private BigDecimal changePercent24h;
    
    /**
     * 24小时最高价
     */
    private BigDecimal high24h;
    
    /**
     * 24小时最低价
     */
    private BigDecimal low24h;
    
    /**
     * 24小时成交量
     */
    private BigDecimal volume24h;
    
    /**
     * 数据来源
     */
    private String source;
    
    /**
     * 数据提供商
     */
    private String provider;
    
    /**
     * 汇率类型 (SPOT, AVERAGE, WEIGHTED)
     */
    private String rateType;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 数据时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 是否实时数据
     */
    private Boolean isRealTime;
    
    /**
     * 数据延迟（毫秒）
     */
    private Long delay;
    
    /**
     * 精度
     */
    private Integer precision;
    
    /**
     * 备注
     */
    private String remark;
}
