package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 钱包统计响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletStatisticsResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 统计周期
     */
    private String period;
    
    /**
     * 总资产价值（USDT）
     */
    private BigDecimal totalAssetValue;
    
    /**
     * 可用余额总价值
     */
    private BigDecimal availableBalance;
    
    /**
     * 冻结余额总价值
     */
    private BigDecimal frozenBalance;
    
    /**
     * 总充值金额
     */
    private BigDecimal totalDeposits;
    
    /**
     * 总提现金额
     */
    private BigDecimal totalWithdrawals;
    
    /**
     * 净流入金额
     */
    private BigDecimal netInflow;
    
    /**
     * 交易总量
     */
    private BigDecimal totalTradingVolume;
    
    /**
     * 交易手续费总额
     */
    private BigDecimal totalTradingFees;
    
    /**
     * 盈亏金额
     */
    private BigDecimal profitLoss;
    
    /**
     * 盈亏百分比
     */
    private BigDecimal profitLossPercentage;
    
    /**
     * 持仓币种数量
     */
    private Integer holdingCurrencyCount;
    
    /**
     * 活跃交易对数量
     */
    private Integer activeTradingPairs;
    
    /**
     * 充值次数
     */
    private Integer depositCount;
    
    /**
     * 提现次数
     */
    private Integer withdrawCount;
    
    /**
     * 交易次数
     */
    private Integer tradeCount;
    
    /**
     * 最大单日盈利
     */
    private BigDecimal maxDailyProfit;
    
    /**
     * 最大单日亏损
     */
    private BigDecimal maxDailyLoss;
    
    /**
     * 平均日交易量
     */
    private BigDecimal avgDailyVolume;
    
    /**
     * 资产分布（按币种）
     */
    private Map<String, BigDecimal> assetDistribution;
    
    /**
     * 收益率历史
     */
    private List<DailyReturn> dailyReturns;
    
    /**
     * 风险指标
     */
    private RiskMetrics riskMetrics;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 统计开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 统计结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 日收益率内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DailyReturn {
        private LocalDateTime date;
        private BigDecimal returnRate;
        private BigDecimal cumulativeReturn;
        private BigDecimal portfolioValue;
    }
    
    /**
     * 风险指标内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RiskMetrics {
        /**
         * 夏普比率
         */
        private BigDecimal sharpeRatio;
        
        /**
         * 最大回撤
         */
        private BigDecimal maxDrawdown;
        
        /**
         * 波动率
         */
        private BigDecimal volatility;
        
        /**
         * VaR（风险价值）
         */
        private BigDecimal valueAtRisk;
        
        /**
         * 风险等级
         */
        private String riskLevel;
    }
}
