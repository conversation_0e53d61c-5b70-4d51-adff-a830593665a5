package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现记录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawRecordResponse {
    
    /**
     * 提现记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 提现货币
     */
    private String currency;
    
    /**
     * 提现金额
     */
    private BigDecimal amount;
    
    /**
     * 提现地址
     */
    private String address;
    
    /**
     * 交易哈希
     */
    private String txHash;
    
    /**
     * 网络类型
     */
    private String network;
    
    /**
     * 提现状态 (PENDING, PROCESSING, COMPLETED, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际提现金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;
    
    /**
     * 处理时间
     */
    private LocalDateTime processTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 地址标签
     */
    private String addressTag;
    
    /**
     * 确认数
     */
    private Integer confirmations;
    
    /**
     * 所需确认数
     */
    private Integer requiredConfirmations;
    
    /**
     * 区块高度
     */
    private Long blockHeight;
    
    /**
     * 区块哈希
     */
    private String blockHash;
}
