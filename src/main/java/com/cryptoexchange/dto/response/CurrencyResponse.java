package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 货币响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CurrencyResponse {
    
    /**
     * 货币ID
     */
    private Long id;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 货币名称
     */
    private String name;
    
    /**
     * 货币符号
     */
    private String symbol;
    
    /**
     * 小数位数
     */
    private Integer decimals;
    
    /**
     * 是否启用
     */
    private Boolean isActive;
    
    /**
     * 是否支持充值
     */
    private Boolean depositEnabled;
    
    /**
     * 是否支持提现
     */
    private Boolean withdrawEnabled;
    
    /**
     * 是否支持交易
     */
    private Boolean tradingEnabled;
    
    /**
     * 最小充值金额
     */
    private BigDecimal minDepositAmount;
    
    /**
     * 最小提现金额
     */
    private BigDecimal minWithdrawAmount;
    
    /**
     * 最大提现金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 日提现限额
     */
    private BigDecimal dailyWithdrawLimit;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 提现手续费类型 (FIXED, PERCENTAGE)
     */
    private String withdrawFeeType;
    
    /**
     * 充值确认数
     */
    private Integer depositConfirmations;
    
    /**
     * 提现确认数
     */
    private Integer withdrawConfirmations;
    
    /**
     * 支持的网络列表
     */
    private List<NetworkInfo> networks;
    
    /**
     * 货币类型 (CRYPTO, FIAT)
     */
    private String currencyType;
    
    /**
     * 货币分类 (MAJOR, ALTCOIN, STABLECOIN, DEFI, NFT)
     */
    private String category;
    
    /**
     * 排序权重
     */
    private Integer sortOrder;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 网络信息内部类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class NetworkInfo {
        /**
         * 网络代码
         */
        private String network;
        
        /**
         * 网络名称
         */
        private String networkName;
        
        /**
         * 网络状态
         */
        private String status;
        
        /**
         * 最小充值金额
         */
        private BigDecimal minDeposit;
        
        /**
         * 最小提现金额
         */
        private BigDecimal minWithdraw;
        
        /**
         * 提现手续费
         */
        private BigDecimal withdrawFee;
        
        /**
         * 确认数要求
         */
        private Integer confirmations;
        
        /**
         * 是否需要标签
         */
        private Boolean requiresTag;
        
        /**
         * 是否支持充值
         */
        private Boolean depositEnabled;
        
        /**
         * 是否支持提现
         */
        private Boolean withdrawEnabled;
    }
}
