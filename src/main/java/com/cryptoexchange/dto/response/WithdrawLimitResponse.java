package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现限额响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WithdrawLimitResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 货币代码
     */
    private String currency;
    
    /**
     * 用户等级
     */
    private String userLevel;
    
    /**
     * VIP等级
     */
    private String vipLevel;
    
    /**
     * 单笔最小提现金额
     */
    private BigDecimal minWithdrawAmount;
    
    /**
     * 单笔最大提现金额
     */
    private BigDecimal maxWithdrawAmount;
    
    /**
     * 日提现限额
     */
    private BigDecimal dailyLimit;
    
    /**
     * 今日已提现金额
     */
    private BigDecimal todayWithdrawn;
    
    /**
     * 今日剩余额度
     */
    private BigDecimal todayRemaining;
    
    /**
     * 月提现限额
     */
    private BigDecimal monthlyLimit;
    
    /**
     * 本月已提现金额
     */
    private BigDecimal monthlyWithdrawn;
    
    /**
     * 本月剩余额度
     */
    private BigDecimal monthlyRemaining;
    
    /**
     * 年提现限额
     */
    private BigDecimal yearlyLimit;
    
    /**
     * 今年已提现金额
     */
    private BigDecimal yearlyWithdrawn;
    
    /**
     * 今年剩余额度
     */
    private BigDecimal yearlyRemaining;
    
    /**
     * 是否需要KYC验证
     */
    private Boolean requiresKyc;
    
    /**
     * KYC验证状态
     */
    private String kycStatus;
    
    /**
     * 是否需要2FA验证
     */
    private Boolean requires2fa;
    
    /**
     * 2FA验证状态
     */
    private String twoFactorStatus;
    
    /**
     * 是否需要邮箱验证
     */
    private Boolean requiresEmailVerification;
    
    /**
     * 邮箱验证状态
     */
    private String emailVerificationStatus;
    
    /**
     * 是否需要手机验证
     */
    private Boolean requiresPhoneVerification;
    
    /**
     * 手机验证状态
     */
    private String phoneVerificationStatus;
    
    /**
     * 提现手续费
     */
    private BigDecimal withdrawFee;
    
    /**
     * 提现手续费类型 (FIXED, PERCENTAGE)
     */
    private String withdrawFeeType;
    
    /**
     * 限额重置时间
     */
    private LocalDateTime limitResetTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String remark;
}
