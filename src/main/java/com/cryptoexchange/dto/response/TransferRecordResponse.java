package com.cryptoexchange.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 转账记录响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransferRecordResponse {
    
    /**
     * 转账记录ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 转账货币
     */
    private String currency;
    
    /**
     * 转账金额
     */
    private BigDecimal amount;
    
    /**
     * 转出账户类型
     */
    private String fromAccountType;
    
    /**
     * 转入账户类型
     */
    private String toAccountType;
    
    /**
     * 收款用户ID（内部转账）
     */
    private Long toUserId;
    
    /**
     * 收款用户名（内部转账）
     */
    private String toUsername;
    
    /**
     * 转账状态 (PENDING, PROCESSING, SUCCESS, FAILED, CANCELLED)
     */
    private String status;
    
    /**
     * 手续费
     */
    private BigDecimal fee;
    
    /**
     * 实际转账金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 转账时间
     */
    private LocalDateTime transferTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注
     */
    private String memo;
    
    /**
     * 转账类型 (INTERNAL, EXTERNAL)
     */
    private String transferType;
    
    /**
     * 交易哈希（外部转账）
     */
    private String txHash;
    
    /**
     * 网络类型（外部转账）
     */
    private String network;
    
    /**
     * 确认数（外部转账）
     */
    private Integer confirmations;
    
    /**
     * 所需确认数（外部转账）
     */
    private Integer requiredConfirmations;
}
