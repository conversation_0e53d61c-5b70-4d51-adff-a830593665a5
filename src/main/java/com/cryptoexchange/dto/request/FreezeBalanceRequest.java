package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 冻结余额请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "冻结余额请求")
public class FreezeBalanceRequest {

    @Schema(description = "币种", example = "BTC")
    @NotBlank(message = "币种不能为空")
    private String currency;

    @Schema(description = "冻结金额", example = "0.001")
    @NotNull(message = "冻结金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "冻结金额必须大于0")
    private BigDecimal amount;

    @Schema(description = "冻结原因", example = "风控冻结")
    @NotBlank(message = "冻结原因不能为空")
    private String reason;

    @Schema(description = "钱包类型", example = "SPOT")
    private String walletType = "SPOT";

    @Schema(description = "业务类型", example = "RISK_CONTROL")
    private String businessType;

    @Schema(description = "关联订单ID")
    private Long orderId;

    @Schema(description = "操作人员ID")
    private Long operatorId;

    @Schema(description = "备注")
    private String remark;

    public String getCurrency() {
        return currency;
    }
}
