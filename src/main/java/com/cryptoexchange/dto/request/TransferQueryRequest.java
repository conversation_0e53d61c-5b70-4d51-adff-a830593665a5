package com.cryptoexchange.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 转账查询请求DTO
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "转账查询请求")
public class TransferQueryRequest {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "币种", example = "USDT")
    private String currency;

    @Schema(description = "转账类型", example = "INTERNAL")
    private String type;

    @Schema(description = "开始时间", example = "2023-01-01 00:00:00")
    private String startTime;

    @Schema(description = "结束时间", example = "2023-12-31 23:59:59")
    private String endTime;

    @Schema(description = "转出账户类型", example = "SPOT")
    private String fromAccountType;

    @Schema(description = "转入账户类型", example = "FUTURES")
    private String toAccountType;

    @Schema(description = "最小金额")
    private String minAmount;

    @Schema(description = "最大金额")
    private String maxAmount;

    @Schema(description = "状态", example = "SUCCESS")
    private String status;

    @Schema(description = "页码")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页大小")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 20;

    @Schema(description = "排序字段", example = "createTime")
    private String sortBy = "createTime";

    @Schema(description = "排序方向", example = "DESC")
    private String sortOrder = "DESC";
}
