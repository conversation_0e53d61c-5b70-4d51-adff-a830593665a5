# Makefile for Crypto Exchange Go Backend

# Variables
APP_NAME=crypto-exchange
BIN_DIR=bin
CMD_DIR=cmd/server
DOCKER_IMAGE=$(APP_NAME):latest
DOCKER_COMPOSE_FILE=docker-compose.yml

# Go related variables
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt
GOVET=$(GOCMD) vet
GOLINT=golangci-lint

# Build flags
LDFLAGS=-ldflags "-s -w"
BUILD_FLAGS=-a -installsuffix cgo

# Default target
.PHONY: all
all: clean deps fmt vet test build

# Install dependencies
.PHONY: deps
deps:
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	$(GOFMT) -s -w .

# Vet code
.PHONY: vet
vet:
	@echo "Vetting code..."
	$(GOVET) ./...

# Lint code
.PHONY: lint
lint:
	@echo "Linting code..."
	$(GOLINT) run

# Run tests
.PHONY: test
test:
	@echo "Running tests..."
	$(GOTEST) -v -race -coverprofile=coverage.out ./...

# Run tests with coverage
.PHONY: test-coverage
test-coverage: test
	@echo "Generating coverage report..."
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Build the application
.PHONY: build
build:
	@echo "Building application..."
	@mkdir -p $(BIN_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BIN_DIR)/$(APP_NAME) ./$(CMD_DIR)

# Build for current OS
.PHONY: build-local
build-local:
	@echo "Building application for local OS..."
	@mkdir -p $(BIN_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BIN_DIR)/$(APP_NAME) ./$(CMD_DIR)

# Build for Windows
.PHONY: build-windows
build-windows:
	@echo "Building application for Windows..."
	@mkdir -p $(BIN_DIR)
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BIN_DIR)/$(APP_NAME).exe ./$(CMD_DIR)

# Build for macOS
.PHONY: build-darwin
build-darwin:
	@echo "Building application for macOS..."
	@mkdir -p $(BIN_DIR)
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(BUILD_FLAGS) $(LDFLAGS) -o $(BIN_DIR)/$(APP_NAME)-darwin ./$(CMD_DIR)

# Build for all platforms
.PHONY: build-all
build-all: build build-windows build-darwin

# Run the application
.PHONY: run
run:
	@echo "Running application..."
	$(GOCMD) run ./$(CMD_DIR)

# Run with live reload (requires air)
.PHONY: dev
dev:
	@echo "Starting development server with live reload..."
	air

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BIN_DIR)
	@rm -f coverage.out coverage.html

# Docker commands
.PHONY: docker-build
docker-build:
	@echo "Building Docker image..."
	docker build -t $(DOCKER_IMAGE) .

.PHONY: docker-run
docker-run:
	@echo "Running Docker container..."
	docker run -d --name $(APP_NAME) -p 8080:8080 $(DOCKER_IMAGE)

.PHONY: docker-stop
docker-stop:
	@echo "Stopping Docker container..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

.PHONY: docker-logs
docker-logs:
	@echo "Showing Docker container logs..."
	docker logs -f $(APP_NAME)

# Docker Compose commands
.PHONY: up
up:
	@echo "Starting services with Docker Compose..."
	docker-compose -f $(DOCKER_COMPOSE_FILE) up -d

.PHONY: down
down:
	@echo "Stopping services with Docker Compose..."
	docker-compose -f $(DOCKER_COMPOSE_FILE) down

.PHONY: logs
logs:
	@echo "Showing Docker Compose logs..."
	docker-compose -f $(DOCKER_COMPOSE_FILE) logs -f

.PHONY: restart
restart: down up

# Database commands
.PHONY: db-init
db-init:
	@echo "Initializing database..."
	mysql -h localhost -u root -p -e "CREATE DATABASE IF NOT EXISTS crypto_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
	mysql -h localhost -u root -p crypto_exchange < sql/init.sql

.PHONY: db-migrate
db-migrate:
	@echo "Running database migrations..."
	$(GOCMD) run ./$(CMD_DIR) -migrate

.PHONY: db-reset
db-reset:
	@echo "Resetting database..."
	mysql -h localhost -u root -p -e "DROP DATABASE IF EXISTS crypto_exchange;"
	make db-init

# Generate swagger docs
.PHONY: swagger
swagger:
	@echo "Generating Swagger documentation..."
	swag init -g ./$(CMD_DIR)/main.go -o ./docs

# Install development tools
.PHONY: install-tools
install-tools:
	@echo "Installing development tools..."
	$(GOCMD) install github.com/cosmtrek/air@latest
	$(GOCMD) install github.com/swaggo/swag/cmd/swag@latest
	$(GOCMD) install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Security scan
.PHONY: security
security:
	@echo "Running security scan..."
	gosec ./...

# Benchmark tests
.PHONY: bench
bench:
	@echo "Running benchmark tests..."
	$(GOTEST) -bench=. -benchmem ./...

# Profile the application
.PHONY: profile
profile:
	@echo "Profiling application..."
	$(GOCMD) tool pprof http://localhost:8080/debug/pprof/profile

# Check for updates
.PHONY: update
update:
	@echo "Checking for dependency updates..."
	$(GOCMD) list -u -m all

# Vendor dependencies
.PHONY: vendor
vendor:
	@echo "Vendoring dependencies..."
	$(GOMOD) vendor

# Help
.PHONY: help
help:
	@echo "Available commands:"
	@echo "  all          - Run clean, deps, fmt, vet, test, build"
	@echo "  deps         - Install dependencies"
	@echo "  fmt          - Format code"
	@echo "  vet          - Vet code"
	@echo "  lint         - Lint code"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  build        - Build application"
	@echo "  build-local  - Build for current OS"
	@echo "  build-all    - Build for all platforms"
	@echo "  run          - Run application"
	@echo "  dev          - Run with live reload"
	@echo "  clean        - Clean build artifacts"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo "  docker-stop  - Stop Docker container"
	@echo "  up           - Start services with Docker Compose"
	@echo "  down         - Stop services with Docker Compose"
	@echo "  logs         - Show Docker Compose logs"
	@echo "  restart      - Restart services"
	@echo "  db-init      - Initialize database"
	@echo "  db-migrate   - Run database migrations"
	@echo "  db-reset     - Reset database"
	@echo "  swagger      - Generate Swagger docs"
	@echo "  install-tools- Install development tools"
	@echo "  security     - Run security scan"
	@echo "  bench        - Run benchmark tests"
	@echo "  help         - Show this help"