# 加密货币交易所后端系统

基于 Go 语言开发的高性能加密货币交易所后端系统，采用微服务架构设计，支持现货交易、期货交易等多种交易模式。

## 🚀 特性

- **高性能**: 基于 Gin 框架，支持高并发请求处理
- **安全可靠**: JWT 认证、密码加密、API 限流等安全机制
- **可扩展**: 模块化设计，易于扩展和维护
- **实时交易**: WebSocket 支持实时行情推送
- **多币种**: 支持多种加密货币交易对
- **风控系统**: 完善的风险控制和资金安全机制

## 📋 技术栈

- **语言**: Go 1.21+
- **框架**: Gin Web Framework
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **ORM**: GORM
- **认证**: JWT
- **日志**: Logrus
- **文档**: Swagger
- **容器**: Docker & Docker Compose
- **对象存储**: MinIO

## 🏗️ 项目结构

```
crypto-exchange-go/
├── cmd/
│   └── server/
│       └── main.go              # 应用入口
├── configs/
│   ├── config.go               # 配置加载
│   └── config.yaml             # 配置文件
├── internal/
│   ├── api/                    # API 路由
│   │   ├── auth/               # 认证相关 API
│   │   ├── user/               # 用户管理 API
│   │   ├── wallet/             # 钱包管理 API
│   │   ├── trading/            # 交易相关 API
│   │   └── router.go           # 路由配置
│   ├── domain/
│   │   └── entity/             # 实体模型
│   ├── dto/
│   │   ├── request/            # 请求 DTO
│   │   └── response/           # 响应 DTO
│   ├── infrastructure/
│   │   ├── database/           # 数据库连接
│   │   └── redis/              # Redis 连接
│   └── middleware/             # 中间件
├── pkg/
│   ├── auth/                   # JWT 认证
│   ├── errors/                 # 错误处理
│   ├── logger/                 # 日志工具
│   └── utils/                  # 工具函数
├── sql/
│   └── init.sql                # 数据库初始化脚本
├── docker-compose.yml          # Docker 编排文件
├── Dockerfile                  # Docker 镜像构建文件
├── go.mod                      # Go 模块文件
└── README.md                   # 项目说明
```

## 🛠️ 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+
- Docker & Docker Compose (可选)

### 1. 克隆项目

```bash
git clone <repository-url>
cd crypto-exchange-go
```

### 2. 安装依赖

```bash
go mod download
```

### 3. 配置环境

复制配置文件并修改相关配置：

```bash
cp configs/config.yaml.example configs/config.yaml
```

编辑 `configs/config.yaml` 文件，配置数据库、Redis 等连接信息。

### 4. 初始化数据库

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE crypto_exchange CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入初始化脚本
mysql -u root -p crypto_exchange < sql/init.sql
```

### 5. 运行应用

```bash
# 开发模式
go run cmd/server/main.go

# 或者构建后运行
go build -o bin/server cmd/server/main.go
./bin/server
```

### 6. 使用 Docker Compose (推荐)

```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

## 📚 API 文档

启动应用后，访问 Swagger 文档：

```
http://localhost:8080/swagger/index.html
```

### 主要 API 端点

#### 认证相关
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出

#### 用户管理
- `GET /api/v1/user/profile` - 获取用户信息
- `PUT /api/v1/user/profile` - 更新用户信息
- `POST /api/v1/user/change-password` - 修改密码

#### 钱包管理
- `GET /api/v1/wallet/list` - 获取钱包列表
- `GET /api/v1/wallet/:currency` - 获取指定币种钱包
- `POST /api/v1/wallet/transfer` - 内部转账

#### 交易相关
- `POST /api/v1/trading/order` - 创建订单
- `DELETE /api/v1/trading/order/:id` - 取消订单
- `GET /api/v1/trading/orders` - 获取订单列表
- `GET /api/v1/trading/trades` - 获取交易记录

#### 行情数据
- `GET /api/v1/market/ticker/:symbol` - 获取行情数据
- `GET /api/v1/market/depth/:symbol` - 获取深度数据
- `GET /api/v1/market/klines/:symbol` - 获取K线数据

## 🔧 配置说明

### 环境变量

可以通过环境变量覆盖配置文件中的设置：

```bash
# 数据库配置
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=password
export DB_NAME=crypto_exchange

# Redis 配置
export REDIS_HOST=localhost
export REDIS_PORT=6379
export REDIS_PASSWORD=

# JWT 配置
export JWT_SECRET=your-secret-key
export JWT_REFRESH_SECRET=your-refresh-secret

# 服务器配置
export SERVER_PORT=8080
export GIN_MODE=release
```

### 配置文件结构

```yaml
server:
  port: 8080
  mode: debug
  read_timeout: 60
  write_timeout: 60
  idle_timeout: 60

database:
  host: localhost
  port: 3306
  user: root
  password: password
  dbname: crypto_exchange
  # ... 其他配置

redis:
  host: localhost
  port: 6379
  password: ""
  # ... 其他配置

# ... 其他配置项
```

## 🧪 测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./pkg/utils

# 运行测试并显示覆盖率
go test -cover ./...
```

## 📦 部署

### Docker 部署

```bash
# 构建镜像
docker build -t crypto-exchange:latest .

# 运行容器
docker run -d \
  --name crypto-exchange \
  -p 8080:8080 \
  -e DB_HOST=your-db-host \
  -e REDIS_HOST=your-redis-host \
  crypto-exchange:latest
```

### 生产环境部署

1. 设置环境变量
2. 配置反向代理 (Nginx)
3. 设置 SSL 证书
4. 配置监控和日志收集
5. 设置自动重启和健康检查

## 🔒 安全考虑

- 使用 HTTPS 加密传输
- JWT 令牌定期刷新
- API 请求限流
- 密码加密存储
- 输入参数验证
- SQL 注入防护
- XSS 攻击防护

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- 项目维护者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/crypto-exchange-go]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者们！

## 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: MySQL 8.0 + GORM
- **缓存**: Redis + go-redis
- **认证**: JWT (golang-jwt)
- **配置**: Viper
- **API文档**: Swagger (gin-swagger)
- **WebSocket**: Gorilla WebSocket
- **加密**: golang.org/x/crypto
- **数值计算**: shopspring/decimal
- **验证**: go-playground/validator

## 项目结构

```
crypto-exchange-go/
├── cmd/                    # 应用程序入口
│   └── server/
│       └── main.go
├── internal/               # 内部应用代码
│   ├── api/               # API处理器
│   │   ├── auth/
│   │   ├── futures/
│   │   ├── market/
│   │   ├── trading/
│   │   ├── user/
│   │   └── wallet/
│   ├── config/            # 配置
│   ├── domain/            # 领域模型
│   │   ├── entity/        # 实体
│   │   ├── repository/    # 仓储接口
│   │   └── service/       # 服务接口
│   ├── infrastructure/    # 基础设施
│   │   ├── database/      # 数据库
│   │   ├── redis/         # Redis
│   │   └── websocket/     # WebSocket
│   ├── middleware/        # 中间件
│   └── dto/              # 数据传输对象
│       ├── request/
│       └── response/
├── pkg/                   # 公共库
│   ├── auth/             # 认证工具
│   ├── errors/           # 错误处理
│   ├── logger/           # 日志
│   └── utils/            # 工具函数
├── configs/              # 配置文件
├── docs/                 # 文档
├── scripts/              # 脚本
├── sql/                  # SQL文件
├── docker-compose.yml    # Docker编排
├── Dockerfile           # Docker镜像
├── Makefile            # 构建脚本
└── go.mod              # Go模块
```

## 核心功能模块

### 1. 用户管理 (User Management)
- 用户注册/登录
- 身份验证 (JWT)
- 用户信息管理
- 安全设置

### 2. 钱包管理 (Wallet Management)
- 多币种钱包
- 充值/提现
- 资产查询
- 交易记录

### 3. 现货交易 (Spot Trading)
- 订单管理 (限价单、市价单)
- 交易撮合
- 交易历史
- 资产冻结/解冻

### 4. 期货交易 (Futures Trading)
- 期货合约管理
- 杠杆交易
- 仓位管理
- 强制平仓
- 资金费率
- 保险基金

### 5. 市场数据 (Market Data)
- 实时价格推送
- K线数据
- 深度数据
- 24h统计
- WebSocket推送

### 6. 风控系统 (Risk Management)
- 交易限制
- 风险监控
- 异常检测

## 快速开始

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 7.0+

### 安装依赖
```bash
go mod tidy
```

### 配置数据库
1. 创建数据库
2. 执行sql目录下的初始化脚本
3. 修改configs/config.yaml中的数据库配置

### 运行项目
```bash
# 开发模式
go run cmd/server/main.go

# 或使用Makefile
make run
```

### API文档
启动项目后访问: http://localhost:8080/swagger/index.html

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t crypto-exchange-go .

# 使用docker-compose
docker-compose up -d
```

## 开发指南

### 代码规范
- 遵循Go官方代码规范
- 使用gofmt格式化代码
- 添加必要的注释
- 编写单元测试

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

MIT License