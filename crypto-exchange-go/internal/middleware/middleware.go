package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"crypto-exchange-go/configs"
	"crypto-exchange-go/internal/infrastructure/redis"
	"crypto-exchange-go/pkg/auth"
	"crypto-exchange-go/pkg/errors"

	"github.com/gin-gonic/gin"
)

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")

		// 设置CORS头
		c.<PERSON>er("Access-Control-Allow-Origin", origin)
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma")
		c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")

		// 处理预检请求
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// JWTAuth JWT认证中间件
func JWTAuth(jwtConfig configs.JWTConfig) gin.HandlerFunc {
	jwtService := auth.NewJWTService(jwtConfig)

	return func(c *gin.Context) {
		// 从Header中获取token
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    errors.CodeUnauthorized,
				"message": "Missing authorization header",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    errors.CodeUnauthorized,
				"message": "Invalid authorization header format",
			})
			c.Abort()
			return
		}

		// 验证token
		claims, err := jwtService.ParseToken(parts[1])
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    errors.CodeUnauthorized,
				"message": "Invalid token",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)

		c.Next()
	}
}

// RateLimit 限流中间件
func RateLimit(redisService *redis.RedisService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取客户端IP
		clientIP := c.ClientIP()
		key := fmt.Sprintf("rate_limit:%s", clientIP)

		// 检查限流
		ctx := context.Background()
		countStr, err := redisService.Get(ctx, key)
		if err != nil && err.Error() != "redis: nil" {
			// Redis错误，允许请求通过
			c.Next()
			return
		}

		count := 0
		if countStr != "" {
			count, _ = strconv.Atoi(countStr)
		}

		// 限制每分钟100次请求
		if count >= 100 {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    errors.CodeTooManyRequests,
				"message": "Too many requests",
			})
			c.Abort()
			return
		}

		// 增加计数
		err = redisService.Incr(ctx, key)
		if err == nil {
			redisService.Expire(ctx, key, time.Minute)
		} else {
			// Redis错误，记录日志但允许请求通过
			fmt.Printf("Rate limit redis error: %v\n", err)
		}

		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID
		requestID := fmt.Sprintf("%d", time.Now().UnixNano())
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// Security 安全头中间件
func Security() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}

// Timeout 超时中间件
func Timeout(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// GetUserID 从上下文中获取用户ID
func GetUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	if id, ok := userID.(uint); ok {
		return id, true
	}
	return 0, false
}

// GetUsername 从上下文中获取用户名
func GetUsername(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}
	if name, ok := username.(string); ok {
		return name, true
	}
	return "", false
}

// GetEmail 从上下文中获取邮箱
func GetEmail(c *gin.Context) (string, bool) {
	email, exists := c.Get("email")
	if !exists {
		return "", false
	}
	if mail, ok := email.(string); ok {
		return mail, true
	}
	return "", false
}

// Pagination 分页中间件
func Pagination() gin.HandlerFunc {
	return func(c *gin.Context) {
		page := 1
		pageSize := 20

		if p := c.Query("page"); p != "" {
			if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
				page = parsed
			}
		}

		if ps := c.Query("page_size"); ps != "" {
			if parsed, err := strconv.Atoi(ps); err == nil && parsed > 0 && parsed <= 100 {
				pageSize = parsed
			}
		}

		c.Set("page", page)
		c.Set("page_size", pageSize)
		c.Set("offset", (page-1)*pageSize)
		c.Set("limit", pageSize)

		c.Next()
	}
}

// GetPagination 从上下文中获取分页信息
func GetPagination(c *gin.Context) (page, pageSize, offset, limit int) {
	pageVal, _ := c.Get("page")
	pageSizeVal, _ := c.Get("page_size")
	offsetVal, _ := c.Get("offset")
	limitVal, _ := c.Get("limit")
	return pageVal.(int), pageSizeVal.(int), offsetVal.(int), limitVal.(int)
}
