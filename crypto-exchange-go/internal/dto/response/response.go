package response

import (
	"time"

	"github.com/shopspring/decimal"
)

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code" example:"200"`
	Message string      `json:"message" example:"success"`
	Data    interface{} `json:"data,omitempty"`
	Time    int64       `json:"time" example:"1640995200"`
}

// Success 成功响应
func Success(data interface{}) Response {
	return Response{
		Code:    200,
		Message: "success",
		Data:    data,
		Time:    time.Now().Unix(),
	}
}

// Error 错误响应
func Error(code int, message string) Response {
	return Response{
		Code:    code,
		Message: message,
		Time:    time.Now().Unix(),
	}
}

// AuthResponse 认证响应
type AuthResponse struct {
	Token     string       `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	TokenType string       `json:"token_type" example:"Bearer"`
	ExpiresIn int          `json:"expires_in" example:"3600"`
	User      UserResponse `json:"user"`
}

// TokenResponse 令牌响应
type TokenResponse struct {
	Token     string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
	TokenType string `json:"token_type" example:"Bearer"`
	ExpiresIn int    `json:"expires_in" example:"3600"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID       uint   `json:"id" example:"1"`
	UUID     string `json:"uuid" example:"550e8400-e29b-41d4-a716-************"`
	Email    string `json:"email" example:"<EMAIL>"`
	Username string `json:"username" example:"username"`
	Nickname string `json:"nickname" example:"nickname"`
	Status   int    `json:"status" example:"1"`
}

// WalletResponse 钱包响应
type WalletResponse struct {
	ID              uint            `json:"id" example:"1"`
	UserID          uint            `json:"user_id" example:"1"`
	CurrencyID      uint            `json:"currency_id" example:"1"`
	CurrencyCode    string          `json:"currency_code" example:"BTC"`
	CurrencyName    string          `json:"currency_name" example:"Bitcoin"`
	AvailableAmount decimal.Decimal `json:"available_amount" example:"1.23456789"`
	FrozenAmount    decimal.Decimal `json:"frozen_amount" example:"0.12345678"`
	TotalAmount     decimal.Decimal `json:"total_amount" example:"1.35802467"`
	Address         string          `json:"address,omitempty" example:"**********************************"`
	CreatedAt       time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt       time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// CurrencyResponse 货币响应
type CurrencyResponse struct {
	ID                uint            `json:"id" example:"1"`
	Code              string          `json:"code" example:"BTC"`
	Name              string          `json:"name" example:"Bitcoin"`
	Symbol            string          `json:"symbol" example:"₿"`
	Decimals          int             `json:"decimals" example:"8"`
	IsActive          bool            `json:"is_active" example:"true"`
	DepositEnabled    bool            `json:"deposit_enabled" example:"true"`
	WithdrawEnabled   bool            `json:"withdraw_enabled" example:"true"`
	MinDepositAmount  decimal.Decimal `json:"min_deposit_amount" example:"0.001"`
	MinWithdrawAmount decimal.Decimal `json:"min_withdraw_amount" example:"0.001"`
	WithdrawFee       decimal.Decimal `json:"withdraw_fee" example:"0.0005"`
	CreatedAt         time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt         time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// OrderResponse 订单响应
type OrderResponse struct {
	ID           uint            `json:"id" example:"1"`
	UUID         string          `json:"uuid" example:"550e8400-e29b-41d4-a716-************"`
	UserID       uint            `json:"user_id" example:"1"`
	Symbol       string          `json:"symbol" example:"BTCUSDT"`
	Side         string          `json:"side" example:"buy"`
	Type         string          `json:"type" example:"limit"`
	Quantity     decimal.Decimal `json:"quantity" example:"1.0"`
	Price        decimal.Decimal `json:"price" example:"50000.0"`
	Amount       decimal.Decimal `json:"amount" example:"50000.0"`
	FilledQty    decimal.Decimal `json:"filled_qty" example:"0.5"`
	FilledAmount decimal.Decimal `json:"filled_amount" example:"25000.0"`
	Status       string          `json:"status" example:"partial_filled"`
	CreatedAt    time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
	UpdatedAt    time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// TradeResponse 交易响应
type TradeResponse struct {
	ID        uint            `json:"id" example:"1"`
	OrderID   uint            `json:"order_id" example:"1"`
	Symbol    string          `json:"symbol" example:"BTCUSDT"`
	Side      string          `json:"side" example:"buy"`
	Quantity  decimal.Decimal `json:"quantity" example:"0.5"`
	Price     decimal.Decimal `json:"price" example:"50000.0"`
	Amount    decimal.Decimal `json:"amount" example:"25000.0"`
	Fee       decimal.Decimal `json:"fee" example:"25.0"`
	CreatedAt time.Time       `json:"created_at" example:"2023-01-01T00:00:00Z"`
}

// MarketDataResponse 市场数据响应
type MarketDataResponse struct {
	Symbol     string          `json:"symbol" example:"BTCUSDT"`
	Price      decimal.Decimal `json:"price" example:"50000.0"`
	Change24h  decimal.Decimal `json:"change_24h" example:"1000.0"`
	ChangeRate decimal.Decimal `json:"change_rate" example:"0.02"`
	Volume24h  decimal.Decimal `json:"volume_24h" example:"1000.0"`
	High24h    decimal.Decimal `json:"high_24h" example:"51000.0"`
	Low24h     decimal.Decimal `json:"low_24h" example:"49000.0"`
	UpdatedAt  time.Time       `json:"updated_at" example:"2023-01-01T00:00:00Z"`
}

// KlineResponse K线响应
type KlineResponse struct {
	Timestamp int64           `json:"timestamp" example:"1640995200"`
	Open      decimal.Decimal `json:"open" example:"50000.0"`
	High      decimal.Decimal `json:"high" example:"51000.0"`
	Low       decimal.Decimal `json:"low" example:"49000.0"`
	Close     decimal.Decimal `json:"close" example:"50500.0"`
	Volume    decimal.Decimal `json:"volume" example:"100.0"`
}

// DepthResponse 深度响应
type DepthResponse struct {
	Symbol string          `json:"symbol" example:"BTCUSDT"`
	Bids   [][]interface{} `json:"bids" example:"[[\"50000.0\", \"1.0\"], [\"49999.0\", \"2.0\"]]"`
	Asks   [][]interface{} `json:"asks" example:"[[\"50001.0\", \"1.5\"], [\"50002.0\", \"2.5\"]]"`
}

// PaginationResponse 分页响应
type PaginationResponse struct {
	Page     int         `json:"page" example:"1"`
	PageSize int         `json:"page_size" example:"20"`
	Total    int64       `json:"total" example:"100"`
	Pages    int         `json:"pages" example:"5"`
	Data     interface{} `json:"data"`
}
