package entity

import (
	"time"

	"crypto-exchange-go/pkg/errors"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// WalletType 钱包类型
type WalletType int

const (
	WalletTypeSpot    WalletType = iota // 现货钱包
	WalletTypeMargin                    // 杠杆钱包
	WalletTypeFutures                   // 期货钱包
	WalletTypeEarning                   // 理财钱包
)

// Wallet 钱包实体
type Wallet struct {
	ID               uint            `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID           uint            `json:"user_id" gorm:"not null;index"`
	CurrencyID       uint            `json:"currency_id" gorm:"not null;index"`
	Type             WalletType      `json:"type" gorm:"type:tinyint;not null;index"`
	Balance          decimal.Decimal `json:"balance" gorm:"type:decimal(36,18);default:0;not null"`
	FrozenBalance    decimal.Decimal `json:"frozen_balance" gorm:"type:decimal(36,18);default:0;not null"`
	AvailableBalance decimal.Decimal `json:"available_balance" gorm:"type:decimal(36,18);default:0;not null"`
	Address          string          `json:"address" gorm:"type:varchar(255)"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
	DeletedAt        gorm.DeletedAt  `json:"-" gorm:"index"`

	// 关联关系
	User     User     `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Currency Currency `json:"currency,omitempty" gorm:"foreignKey:CurrencyID"`
}

// TableName 指定表名
func (Wallet) TableName() string {
	return "wallets"
}

// BeforeUpdate GORM钩子：更新前计算可用余额
func (w *Wallet) BeforeUpdate(tx *gorm.DB) error {
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
	return nil
}

// BeforeCreate GORM钩子：创建前计算可用余额
func (w *Wallet) BeforeCreate(tx *gorm.DB) error {
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
	return nil
}

// CanWithdraw 检查是否可以提取指定金额
func (w *Wallet) CanWithdraw(amount decimal.Decimal) bool {
	return w.AvailableBalance.GreaterThanOrEqual(amount)
}

// Freeze 冻结指定金额
func (w *Wallet) Freeze(amount decimal.Decimal) error {
	if !w.CanWithdraw(amount) {
		return errors.ErrInsufficientBalance
	}
	w.FrozenBalance = w.FrozenBalance.Add(amount)
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
	return nil
}

// Unfreeze 解冻指定金额
func (w *Wallet) Unfreeze(amount decimal.Decimal) error {
	if w.FrozenBalance.LessThan(amount) {
		return errors.ErrInsufficientFrozenBalance
	}
	w.FrozenBalance = w.FrozenBalance.Sub(amount)
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
	return nil
}

// Debit 扣除余额（从冻结余额中扣除）
func (w *Wallet) Debit(amount decimal.Decimal) error {
	if w.FrozenBalance.LessThan(amount) {
		return errors.ErrInsufficientFrozenBalance
	}
	w.Balance = w.Balance.Sub(amount)
	w.FrozenBalance = w.FrozenBalance.Sub(amount)
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
	return nil
}

// Credit 增加余额
func (w *Wallet) Credit(amount decimal.Decimal) {
	w.Balance = w.Balance.Add(amount)
	w.AvailableBalance = w.Balance.Sub(w.FrozenBalance)
}

// GetTotalAmount 获取总金额
func (w *Wallet) GetTotalAmount() decimal.Decimal {
	return w.Balance
}
