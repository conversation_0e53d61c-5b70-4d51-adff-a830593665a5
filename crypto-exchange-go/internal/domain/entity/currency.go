package entity

import (
	"time"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

// Currency 货币实体
type Currency struct {
	ID              uint            `json:"id" gorm:"primaryKey;autoIncrement"`
	Code            string          `json:"code" gorm:"type:varchar(10);uniqueIndex;not null"`          // 货币代码，如 BTC, ETH, USDT
	Name            string          `json:"name" gorm:"type:varchar(50);not null"`                      // 货币名称
	Symbol          string          `json:"symbol" gorm:"type:varchar(10);not null"`                    // 货币符号
	Decimals        int             `json:"decimals" gorm:"type:tinyint;default:8;not null"`            // 小数位数
	IsActive        bool            `json:"is_active" gorm:"default:true;not null"`                     // 是否激活
	CanDeposit      bool            `json:"can_deposit" gorm:"default:true;not null"`                   // 是否允许充值
	CanWithdraw     bool            `json:"can_withdraw" gorm:"default:true;not null"`                  // 是否允许提现
	WithdrawFee     decimal.Decimal `json:"withdraw_fee" gorm:"type:decimal(36,18);default:0;not null"` // 提现手续费
	WithdrawFeeType int             `json:"withdraw_fee_type" gorm:"type:tinyint;default:0;not null"`   // 提现手续费类型：0-固定费用，1-百分比
	CreatedAt       time.Time       `json:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at"`
	DeletedAt       gorm.DeletedAt  `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Currency) TableName() string {
	return "currencies"
}

// IsDepositEnabled 检查是否允许充值
func (c *Currency) IsDepositEnabled() bool {
	return c.IsActive && c.CanDeposit
}

// IsWithdrawEnabled 检查是否允许提现
func (c *Currency) IsWithdrawEnabled() bool {
	return c.IsActive && c.CanWithdraw
}

// CalculateWithdrawFee 计算提现手续费
func (c *Currency) CalculateWithdrawFee(amount decimal.Decimal) decimal.Decimal {
	if c.WithdrawFeeType == 0 {
		// 固定费用
		return c.WithdrawFee
	}
	// 百分比费用
	return amount.Mul(c.WithdrawFee)
}
