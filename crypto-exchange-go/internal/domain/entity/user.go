package entity

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserStatus 用户状态
type UserStatus int

const (
	UserStatusInactive  UserStatus = iota // 未激活
	UserStatusActive                      // 激活
	UserStatusSuspended                   // 暂停
	UserStatusBanned                      // 封禁
)

// User 用户实体
type User struct {
	ID                  uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	UUID                string         `json:"uuid" gorm:"type:varchar(36);uniqueIndex;not null"`
	Email               string         `json:"email" gorm:"type:varchar(100);uniqueIndex;not null"`
	Username            string         `json:"username" gorm:"type:varchar(50);uniqueIndex;not null"`
	PasswordHash        string         `json:"-" gorm:"type:varchar(255);not null"`
	TradingPasswordHash string         `json:"-" gorm:"type:varchar(255)"`
	Salt                string         `json:"-" gorm:"type:varchar(32);not null"`
	Nickname            string         `json:"nickname" gorm:"type:varchar(50)"`
	Avatar              string         `json:"avatar" gorm:"type:varchar(255)"`
	Phone               string         `json:"phone" gorm:"type:varchar(20);index"`
	CountryCode         string         `json:"country_code" gorm:"type:varchar(10)"`
	Status              UserStatus     `json:"status" gorm:"type:tinyint;default:0;index"`
	EmailVerified       bool           `json:"email_verified" gorm:"default:false"`
	PhoneVerified       bool           `json:"phone_verified" gorm:"default:false"`
	KYCLevel            int            `json:"kyc_level" gorm:"type:tinyint;default:0"`
	TwoFactorEnabled    bool           `json:"two_factor_enabled" gorm:"default:false"`
	TwoFactorSecret     string         `json:"-" gorm:"type:varchar(32)"`
	LoginIP             string         `json:"login_ip" gorm:"type:varchar(45)"`
	LastLoginAt         *time.Time     `json:"last_login_at"`
	RegistrationIP      string         `json:"registration_ip" gorm:"type:varchar(45)"`
	ReferrerID          *uint          `json:"referrer_id" gorm:"index"`
	ReferralCode        string         `json:"referral_code" gorm:"type:varchar(20);uniqueIndex"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Referrer *User    `json:"referrer,omitempty" gorm:"foreignKey:ReferrerID"`
	Referees []User   `json:"referees,omitempty" gorm:"foreignKey:ReferrerID"`
	Wallets  []Wallet `json:"wallets,omitempty" gorm:"foreignKey:UserID"`
}

// BeforeCreate GORM钩子：创建前生成UUID和推荐码
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.UUID == "" {
		u.UUID = uuid.New().String()
	}
	if u.ReferralCode == "" {
		u.ReferralCode = generateReferralCode()
	}
	return nil
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// IsActive 检查用户是否激活
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsSuspended 检查用户是否被暂停
func (u *User) IsSuspended() bool {
	return u.Status == UserStatusSuspended
}

// IsBanned 检查用户是否被封禁
func (u *User) IsBanned() bool {
	return u.Status == UserStatusBanned
}

// CanTrade 检查用户是否可以交易
func (u *User) CanTrade() bool {
	return u.IsActive() && u.EmailVerified
}

// generateReferralCode 生成推荐码
func generateReferralCode() string {
	// 生成8位随机推荐码
	const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 8)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}
