version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: crypto-exchange-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root123456
      MYSQL_DATABASE: crypto_exchange
      MYSQL_USER: crypto_user
      MYSQL_PASSWORD: crypto_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - crypto-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: crypto-exchange-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass redis123456
    networks:
      - crypto-network

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: crypto-exchange-minio
    restart: unless-stopped
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - crypto-network

  # 应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: crypto-exchange-app
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - GIN_MODE=release
      - CONFIG_PATH=/app/configs/config.yaml
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
      - minio
    networks:
      - crypto-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: crypto-exchange-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - crypto-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local
  nginx_logs:
    driver: local

networks:
  crypto-network:
    driver: bridge