# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files
*.log
logs/

# Environment variables
.env
.env.local
.env.*.local

# Configuration files with sensitive data
configs/config.yaml
configs/config.yml
configs/config.json

# Build output
bin/
build/
dist/

# Temporary files
tmp/
temp/

# Database files
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# Docker volumes
data/
mysql_data/
redis_data/
minio_data/

# SSL certificates
*.pem
*.key
*.crt
*.csr

# Backup files
*.bak
*.backup

# Coverage reports
coverage.txt
coverage.html
coverage.out

# Air live reload
tmp/

# Swagger generated files
docs/

# MinIO data
.minio/

# Node modules (if using any frontend tools)
node_modules/

# Python cache (if using any Python tools)
__pycache__/
*.py[cod]
*$py.class

# Local development files
local/
dev/

# Kubernetes files
*.yaml.bak
*.yml.bak

# Helm charts
charts/

# Terraform files
*.tfstate
*.tfstate.*
.terraform/

# Ansible files
*.retry

# JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr

# Visual Studio Code
.vscode/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local history
.history/

# Profiling data
*.prof
*.pprof

# Memory dumps
*.hprof
*.dump

# Application specific
static/uploads/
uploads/
files/
images/

# Cache directories
cache/
.cache/

# Lock files
*.lock
yarn.lock
package-lock.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/